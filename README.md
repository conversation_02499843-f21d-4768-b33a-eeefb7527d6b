# Footprints Web App

A Flask-based web application for creating and managing Footprintss. Users can create journeys, add events with locations, and share their travel experiences with others.

## Features

- User Authentication (Register, Login, Logout)
- Journey Management (Create, Edit, Delete, View)
- Event Management (Add, Edit, Delete Events)
- Location Management (Add, Edit, Delete Locations)
- Image Upload Support
- Privacy Settings (Private, Public, Hidden)
- Responsive Design with Bootstrap

## Prerequisites

- Python 3.8 or higher
- MySQL Server
- pip (Python package installer)

## Required Python Packages

- bcrypt==4.3.0
- blinker==1.9.0
- click==8.1.8
- colorama==0.4.6
- Flask==3.1.0
- Flask-Bcrypt==1.0.1
- itsdangerous==2.2.0
- Jinja2==3.1.6
- MarkupSafe==3.0.2
- mysql-connector-python==9.2.0
- pillow==11.1.0
- python-dotenv==1.0.1
- Werkzeug==3.1.3

## Installation

1. Clone the repository:

```bash
git clone https://github.com/LUMasterOfAppliedComputing2025S1/COMP639_Project_1_BHE.git
cd COMP639_Project_1_BHE
```

2. Create and activate a virtual environment:

```bash
python -m venv venv
# On Windows
venv\Scripts\activate
# On Unix or MacOS
source venv/bin/activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

4. Set up connect.py for local database connection:

```
DB_USER = "Your_local_db_User"
DB_PASSWORD = "Your_local_db_Password"
DB_HOST = "localhost"
DB_NAME = "your_db_name"
DB_PORT = 3306
```

5. Initialize the database:

```bash
cd database
python init_db.py
```

## Usage

1. Start the application:

```bash
python app.py
```

2. Open your web browser and navigate to:

```
http://localhost:5000
```

3. Register a new account or log in with existing credentials.

4. Start creating your Footprintss!

## Project Structure

```
travel_journal/
├── app.py                          # Application entry point
├── config.py                       # Configuration settings (not found in scan)
├── connect.py                      # Database connection configuration
├── data/                           # Data access layer modules
│ ├── user_data.py                  # User data access functions
│ ├── journey_data.py               # Journey data access functions
│ ├── event_data.py                 # Event data access functions
│ ├── location_data.py              # Location data access functions
│ └── announcement_data.py          # Announcement data access functions
├── database/                       # Database initialization scripts
├── requirements.txt                # Dependencies
├── README.md                       # Project documentation
├── static/                         # Static files
│ ├── css/                          # CSS files
│ ├── js/                           # JavaScript files
│ └── uploads/                      # User-uploaded files
│   ├── profile_images/             # Profile pictures
│   └── event_images/               # Event images
├── templates/                      # Jinja templates
│ ├── base.html                     # Base template with Bootstrap
│ ├── errors.html                   # Error page templates
│ ├── index.html                    # Home page templates
│ ├── admin/                        # Admin panel templates
│ ├── announcement/                 # Announcement templates
│ ├── auth/                         # Authentication templates
│ ├── dashboard/                    # Dashboard templates
│ ├── discovery/                    # Public journey exploration templates
│ ├── event/                        # Event templates
│ ├── account/                      # Profile templates
│ ├── journeys/                     # Journey templates
│ └── components/                   # Reusable template components
├── routes/                         # Route modules
│ ├── auth_routes.py                # Authentication routes
│ ├── profile_routes.py             # Profile routes
│ ├── journeys_routes.py            # Journey routes
│ ├── events_routes.py              # Event routes
│ ├── location_routes.py            # Location routes
│ ├── main_routes.py                # Main routes
│ ├── user_routes.py                # User routes
│ └── announcements_routes.py       # Announcement routes
├── services/                       # Service modules
│ ├── auth_service.py               # Authentication services
│ ├── profile_service.py            # Profile services
│ ├── journeys_service.py           # Journey services
│ ├── events_service.py             # Event services
│ ├── location_service.py           # Location services
│ ├── main_service.py               # Main services
│ ├── user_service.py               # User services
│ └── announcements_service.py      # Announcement services
├── utils/                          # Utility modules
│ ├── __init__.py                   # Package initialization
│ ├── db_utils.py                   # Database utility functions
│ ├── file_utils.py                 # File handling utilities
│ └── security.py                   # Security and authentication utilities
```

## Default User Roles

### The system has three user roles:

- Traveller - Regular user who can create and manage their own journeys
- Editor - Can manage content including locations and announcements
- Admin - Full system access including user management

## User Credential for testing

### Admin Users

| Username | Password     |
| -------- | ------------ |
| admin1   | Password123! |
| admin2   | Password123! |
| admin3   | Password123! |

### Editor Users

| Username | Password     |
| -------- | ------------ |
| editor1  | Password123! |
| editor2  | Password123! |
| editor3  | Password123! |

### Traveller Users

| Username   | Password     |
| ---------- | ------------ |
| traveller1 | Password123! |
| traveller2 | Password123! |
| traveller3 | Password123! |
