from flask import Flask, render_template, g, session
import os
import traceback
from datetime import datetime
from routes import init_routes
from utils.db_utils import init_db_connection
from utils.security import init_bcrypt
from utils.filters import init_filters
from utils.permissions import register_permission_helpers
from werkzeug.exceptions import HTTPException
from utils.logger import get_logger
from services import account_service, message_service, notification_service
from routes.edit_history_routes import edit_history_bp
from routes.file_config_routes import bp as file_config_bp
from utils.file_utils import get_frontend_config, get_safe_image_url

# Initialize logger
logger = get_logger(__name__)

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)

    # Load configuration
    app.config.from_mapping(
        SECRET_KEY=os.environ.get('SECRET_KEY', 'dev_key_change_in_production'),
        MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16MB max upload size
    )

    # Initialize database connection
    init_db_connection(app)

    # Initialize Flask-Bcrypt
    init_bcrypt(app)

    # Initialize custom filters
    init_filters(app)

    # Register permission helpers for templates
    register_permission_helpers(app)

    # Register routes
    init_routes(app)

    # Register blueprints
    app.register_blueprint(edit_history_bp)
    app.register_blueprint(file_config_bp)

    # Application-level before_request handler
    @app.before_request
    def before_request():
        """Set up global variables for all requests"""
        if 'user_id' in session:
            # Get current user object for templates
            g.current_user = account_service.get_user_profile(session['user_id'])

            # Get unread messages count for navbar
            g.unread_messages_count = message_service.get_unread_messages_count(session['user_id'])

            # Get unread notifications count for navbar
            g.unread_notifications_count = notification_service.count_unread_notifications(session['user_id'])
        else:
            g.current_user = None
            g.unread_messages_count = 0
            g.unread_notifications_count = 0

    # Add template context processor
    @app.context_processor
    def inject_template_globals():
        """Add global variables to all templates"""
        file_config = get_frontend_config()
        return {
            'current_user': getattr(g, 'current_user', None),
            'unread_messages_count': getattr(g, 'unread_messages_count', 0),
            'unread_notifications_count': getattr(g, 'unread_notifications_count', 0),
            'file_config': file_config,
            'get_safe_image_url': get_safe_image_url
        }

    # Register error handlers
    @app.errorhandler(Exception)
    def handle_error(error):
        """Handle all errors and render the error page"""

        # If it's an HTTPException (e.g., 404, 403), use its code
        if isinstance(error, HTTPException):
            code = error.code
            message = error.description
        else:
            # Otherwise, it's an internal server error (500)
            code = 500
            message = 'An internal server error occurred. Please try again later.'
            logger.error(f"Unexpected error: {str(error)}\n{traceback.format_exc()}")  # Log full traceback

        # Error messages for specific HTTP errors
        error_messages = {
            400: 'Bad request. Please check your input.',
            401: 'Unauthorized. Please log in.',
            403: 'Forbidden. You do not have permission to access this resource.',
            404: 'The page you are looking for could not be found.',
            405: 'Method not allowed.',
            413: 'File too large. Maximum size is 16MB.',
            429: 'Too many requests. Please try again later.',
            500: 'An internal server error occurred. Please try again later.',
        }

        # Override with a specific message if available
        message = error_messages.get(code, message)

        return render_template('error.html', error_code=code, message=message), code

    # Create folders for file uploads if they don't exist
    os.makedirs(os.path.join(app.static_folder, 'uploads', 'profile_images'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads', 'event_images'), exist_ok=True)
    os.makedirs(os.path.join(app.static_folder, 'uploads', 'journey_covers'), exist_ok=True)

    return app


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
