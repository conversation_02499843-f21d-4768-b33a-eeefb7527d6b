"""
Data Package

This package contains all data access layer modules that handle database
operations for the application. These modules implement the persistence
layer and are called by service layer modules.
"""

# Import all data modules
from . import achievement_data
from . import announcement_data
from . import community_data
from . import country_data
from . import departure_board_data
from . import edit_history_data
from . import event_data
from . import helpdesk_data
from . import journey_data
from . import location_data
from . import notification_data
from . import privacy_settings_data
from . import report_data
from . import subscription_data
from . import user_data

# Define __all__ to make "from data import *" work properly
__all__ = [
    'achievement_data',
    'announcement_data',
    'community_data',
    'country_data',
    'departure_board_data',
    'edit_history_data',
    'event_data',
    'helpdesk_data',
    'journey_data',
    'location_data',
    'notification_data',
    'privacy_settings_data',
    'report_data',
    'subscription_data',
    'user_data',
]
