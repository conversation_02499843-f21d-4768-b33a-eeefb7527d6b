from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def get_achievement_categories() -> List[Dict[str, Any]]:
    """Get all achievement categories.
    
    Returns:
        List[Dict[str, Any]]: List of achievement category records.
    """
    logger.debug("Getting all achievement categories")
    query = """
    SELECT *
    FROM achievement_categories
    ORDER BY name
    """
    results = execute_query(query, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} achievement categories")
    return results or []

def get_achievements(category_id: Optional[int] = None, is_premium: Optional[bool] = None) -> List[Dict[str, Any]]:
    """Get all achievements, optionally filtered by category or premium status.
    
    Args:
        category_id: Optional category ID to filter by.
        is_premium: Optional premium status to filter by.
        
    Returns:
        List[Dict[str, Any]]: List of achievement records.
    """
    logger.debug(f"Getting achievements, category_id: {category_id}, is_premium: {is_premium}")
    
    query = """
    SELECT a.*, c.name as category_name
    FROM achievements a
    JOIN achievement_categories c ON a.category_id = c.id
    """
    
    params = []
    conditions = []
    
    if category_id is not None:
        conditions.append("a.category_id = %s")
        params.append(category_id)
    
    if is_premium is not None:
        conditions.append("a.is_premium = %s")
        params.append(is_premium)
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY a.category_id, a.name"
    
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} achievements")
    return results or []

def get_user_achievements(user_id: int) -> List[Dict[str, Any]]:
    """Get all achievements for a user.
    
    Args:
        user_id: The ID of the user to get achievements for.
        
    Returns:
        List[Dict[str, Any]]: List of user achievement records with achievement details.
    """
    logger.debug(f"Getting achievements for user ID: {user_id}")
    query = """
    SELECT ua.*, a.name as achievement_name, a.description as achievement_description,
           a.badge_filename, a.points, a.is_premium, a.progress_threshold,
           c.name as category_name
    FROM user_achievements ua
    JOIN achievements a ON ua.achievement_id = a.id
    JOIN achievement_categories c ON a.category_id = c.id
    WHERE ua.user_id = %s
    ORDER BY ua.achieved_at DESC NULLS LAST, a.category_id, a.name
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} achievements for user ID: {user_id}")
    return results or []

def get_user_achievement(user_id: int, achievement_id: int) -> Optional[Dict[str, Any]]:
    """Get a specific user achievement.
    
    Args:
        user_id: The ID of the user.
        achievement_id: The ID of the achievement.
        
    Returns:
        Dict[str, Any]: User achievement record with achievement details if found, None otherwise.
    """
    logger.debug(f"Getting achievement ID: {achievement_id} for user ID: {user_id}")
    query = """
    SELECT ua.*, a.name as achievement_name, a.description as achievement_description,
           a.badge_filename, a.points, a.is_premium, a.progress_threshold
    FROM user_achievements ua
    JOIN achievements a ON ua.achievement_id = a.id
    WHERE ua.user_id = %s AND ua.achievement_id = %s
    """
    result = execute_query(query, (user_id, achievement_id), fetch_one=True)
    logger.debug(f"User achievement lookup result: {'Found' if result else 'Not found'}")
    return result

def update_achievement_progress(user_id: int, achievement_id: int, 
                              current_progress: Optional[int] = None,
                              increment: Optional[int] = None) -> Optional[Dict[str, Any]]:
    """Update progress for a user achievement, potentially unlocking it.
    
    Args:
        user_id: The ID of the user.
        achievement_id: The ID of the achievement.
        current_progress: Optional new progress value (absolute).
        increment: Optional progress increment (relative).
        
    Returns:
        Dict[str, Any]: Updated user achievement record if the achievement was unlocked, None otherwise.
    """
    logger.info(f"Updating achievement progress for user ID: {user_id}, achievement ID: {achievement_id}")
    
    # Get achievement details to check threshold
    achievement_query = """
    SELECT a.id, a.progress_threshold
    FROM achievements a
    WHERE a.id = %s
    """
    achievement = execute_query(achievement_query, (achievement_id,), fetch_one=True)
    
    if not achievement:
        logger.error(f"Achievement ID: {achievement_id} not found")
        return None
    
    threshold = achievement['progress_threshold']
    
    # Check if user already has this achievement
    user_achievement_query = """
    SELECT id, current_progress, achieved_at
    FROM user_achievements
    WHERE user_id = %s AND achievement_id = %s
    """
    user_achievement = execute_query(user_achievement_query, (user_id, achievement_id), fetch_one=True)
    
    # If user doesn't have this achievement record yet, create it
    if not user_achievement:
        if current_progress is not None:
            new_progress = current_progress
        elif increment is not None:
            new_progress = increment
        else:
            new_progress = 0
        
        insert_query = """
        INSERT INTO user_achievements (user_id, achievement_id, current_progress)
        VALUES (%s, %s, %s)
        """
        user_achievement_id = execute_query(insert_query, (user_id, achievement_id, new_progress))
        logger.info(f"Created user achievement with ID: {user_achievement_id}")
        
        # Check if achievement should be unlocked immediately
        if threshold is not None and new_progress >= threshold:
            unlock_query = """
            UPDATE user_achievements
            SET achieved_at = CURRENT_TIMESTAMP
            WHERE id = %s
            """
            execute_query(unlock_query, (user_achievement_id,))
            logger.info(f"Unlocked achievement ID: {achievement_id} for user ID: {user_id}")
            
            # Create notification
            create_notification(user_id, achievement_id)
            
            # Return the updated record
            return get_user_achievement(user_id, achievement_id)
        
        return None
    
    # User already has this achievement record
    user_achievement_id = user_achievement['id']
    current = user_achievement['current_progress'] or 0
    already_achieved = user_achievement['achieved_at'] is not None
    
    # If already achieved, no need to update progress
    if already_achieved:
        logger.debug(f"Achievement ID: {achievement_id} already achieved for user ID: {user_id}")
        return None
    
    # Update progress
    if current_progress is not None:
        new_progress = current_progress
    elif increment is not None:
        new_progress = current + increment
    else:
        logger.warning("No progress update specified")
        return None
    
    update_query = """
    UPDATE user_achievements
    SET current_progress = %s
    WHERE id = %s
    """
    execute_query(update_query, (new_progress, user_achievement_id))
    logger.info(f"Updated progress to {new_progress} for user achievement ID: {user_achievement_id}")
    
    # Check if achievement should be unlocked
    if threshold is not None and new_progress >= threshold:
        unlock_query = """
        UPDATE user_achievements
        SET achieved_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """
        execute_query(unlock_query, (user_achievement_id,))
        logger.info(f"Unlocked achievement ID: {achievement_id} for user ID: {user_id}")
        
        # Create notification for achievement
        notification_query = """
        SELECT a.name
        FROM achievements a
        WHERE a.id = %s
        """
        achievement_info = execute_query(notification_query, (achievement_id,), fetch_one=True)
        
        if achievement_info:
            notification_content = f"You earned the achievement: {achievement_info['name']}"
            
            # Create a notification for the user
            from data import notification_data
            notification_data.create_notification(
                user_id=user_id,
                notification_type='achievement',
                content=notification_content,
                related_id=user_achievement_id
            )
        
        # Return the updated record
        return get_user_achievement(user_id, achievement_id)
    
    return None

def get_achievement_leaderboard(limit: int = 10) -> List[Dict[str, Any]]:
    """Get the achievement leaderboard.
    
    Args:
        limit: Maximum number of results to return.
        
    Returns:
        List[Dict[str, Any]]: List of user records with achievement counts and points.
    """
    logger.debug(f"Getting achievement leaderboard with limit: {limit}")
    query = """
    SELECT user_id, username, profile_image, achievements_count, total_points
    FROM achievement_leaderboard
    LIMIT %s
    """
    results = execute_query(query, (limit,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} users on leaderboard")
    return results or []

def get_recent_achievements(limit: int = 10) -> List[Dict[str, Any]]:
    """Get recently earned achievements.
    
    Args:
        limit: Maximum number of results to return.
        
    Returns:
        List[Dict[str, Any]]: List of recently earned achievement records.
    """
    logger.debug(f"Getting recent achievements with limit: {limit}")
    query = """
    SELECT *
    FROM recent_achievements
    LIMIT %s
    """
    results = execute_query(query, (limit,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} recent achievements")
    return results or []