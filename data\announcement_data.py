from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# ===== Basic Announcement Operations =====

def get_announcement(announcement_id: int) -> Optional[Dict[str, Any]]:
    """Get an announcement by ID.
    
    Args:
        announcement_id: The ID of the announcement to retrieve.
        
    Returns:
        Dict[str, Any]: Announcement data including author username if found, None otherwise.
    """
    logger.debug(f"Getting announcement with ID: {announcement_id}")
    query = """
    SELECT a.*, u.username
    FROM announcements a
    JOIN users u ON a.author_id = u.id
    WHERE a.id = %s
    """
    result = execute_query(query, (announcement_id,), fetch_one=True)
    logger.debug(f"Announcement lookup result: {'Found' if result else 'Not found'}")
    return result

def create_announcement(author_id: int, title: str, content: str) -> int:
    """Create a new announcement.
    
    Args:
        author_id: The ID of the user creating the announcement.
        title: The title of the announcement.
        content: The content of the announcement.
        
    Returns:
        int: The ID of the newly created announcement.
    """
    logger.info(f"Creating new announcement by author ID: {author_id}, title: {title}")
    query = """
    INSERT INTO announcements (author_id, title, content, updated_at)
    VALUES (%s, %s, %s, NULL)
    """
    announcement_id = execute_query(query, (author_id, title, content))
    logger.info(f"Created announcement with ID: {announcement_id}")
    return announcement_id

def update_announcement(announcement_id: int, title: Optional[str] = None, 
                       content: Optional[str] = None) -> int:
    """Update an announcement.
    
    Args:
        announcement_id: The ID of the announcement to update.
        title: Optional new title for the announcement.
        content: Optional new content for the announcement.
        
    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating announcement with ID: {announcement_id}")
    updates = []
    params = []
    
    if title is not None:
        updates.append("title = %s")
        params.append(title)
        logger.debug(f"Updating title to: {title}")
    if content is not None:
        updates.append("content = %s")
        updates.append("updated_at = CURRENT_TIMESTAMP")
        params.append(content)
        logger.debug("Updating content")
    
    if not updates:
        logger.warning(f"No updates provided for announcement ID: {announcement_id}")
        return 0
    
    query = f"""
    UPDATE announcements
    SET {', '.join(updates)}
    WHERE id = %s
    """
    params.append(announcement_id)
    rows_affected = execute_query(query, params)
    logger.info(f"Updated announcement ID: {announcement_id}, rows affected: {rows_affected}")
    return rows_affected

def delete_announcement(announcement_id: int) -> int:
    """Delete an announcement.
    
    Args:
        announcement_id: The ID of the announcement to delete.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting announcement with ID: {announcement_id}")
    query = """
    DELETE FROM announcements
    WHERE id = %s
    """
    rows_affected = execute_query(query, (announcement_id,))
    logger.info(f"Deleted announcement with ID: {announcement_id}, rows affected: {rows_affected}")
    return rows_affected

# ===== User Announcement Operations =====

def get_user_announcements(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all announcements for a user with pagination.
    
    Args:
        user_id: The ID of the user to get announcements for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of announcement records.
    """
    logger.debug(f"Getting announcements for user ID: {user_id}, limit: {limit}, offset: {offset}")
    query = """
    SELECT a.*, u.username
    FROM announcements a
    JOIN users u ON a.author_id = u.id
    ORDER BY a.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} announcements for user ID: {user_id}")
    return results or []

def get_user_announcements_count(user_id: int) -> int:
    """Get total count of announcements for a user.
    
    Args:
        user_id: The ID of the user to count announcements for.
        
    Returns:
        int: Total count of announcements.
    """
    logger.debug(f"Counting announcements for user ID: {user_id}")
    query = """
    SELECT COUNT(*) as count
    FROM announcements a
    """
    result = execute_query(query, (), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} announcements for user ID: {user_id}")
    return count

# ===== Read/Unread Status Operations =====

def get_unread_announcements(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get unread announcements with pagination.
    
    Args:
        user_id: The ID of the user to get unread announcements for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of unread announcement records.
    """
    logger.debug(f"Getting unread announcements for user ID: {user_id}, limit: {limit}, offset: {offset}")
    query = """
    SELECT a.*, u.username
    FROM announcements a
    JOIN users u ON a.author_id = u.id
    LEFT JOIN user_announcements ua ON a.id = ua.announcement_id AND ua.user_id = %s
    WHERE ua.announcement_id IS NULL
    ORDER BY a.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} unread announcements for user ID: {user_id}")
    return results or []

def get_unread_announcements_count(user_id: int) -> int:
    """Get total count of unread announcements.
    
    Args:
        user_id: The ID of the user to count unread announcements for.
        
    Returns:
        int: Count of unread announcements.
    """
    logger.debug(f"Counting unread announcements for user ID: {user_id}")
    query = """
    SELECT COUNT(*) as count
    FROM announcements a
    LEFT JOIN user_announcements ua ON a.id = ua.announcement_id AND ua.user_id = %s
    WHERE ua.announcement_id IS NULL
    """
    result = execute_query(query, (user_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} unread announcements for user ID: {user_id}")
    return count

def get_read_announcements(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get read announcements with pagination.
    
    Args:
        user_id: The ID of the user to get read announcements for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of read announcement records.
    """
    logger.debug(f"Getting read announcements for user ID: {user_id}, limit: {limit}, offset: {offset}")
    query = """
    SELECT a.*, u.username
    FROM announcements a
    JOIN users u ON a.author_id = u.id
    JOIN user_announcements ua ON a.id = ua.announcement_id
    WHERE ua.user_id = %s
    ORDER BY a.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} read announcements for user ID: {user_id}")
    return results or []

def get_read_announcements_count(user_id: int) -> int:
    """Get total count of read announcements.
    
    Args:
        user_id: The ID of the user to count read announcements for.
        
    Returns:
        int: Count of read announcements.
    """
    logger.debug(f"Counting read announcements for user ID: {user_id}")
    query = """
    SELECT COUNT(*) as count
    FROM announcements a
    JOIN user_announcements ua ON a.id = ua.announcement_id
    WHERE ua.user_id = %s
    """
    result = execute_query(query, (user_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} read announcements for user ID: {user_id}")
    return count

def mark_announcement_as_read(user_id: int, announcement_id: int) -> int:
    """Mark an announcement as read by a user.
    
    Args:
        user_id: The ID of the user marking the announcement as read.
        announcement_id: The ID of the announcement to mark as read.
        
    Returns:
        int: ID of the newly inserted record or 0 if no insert occurred.
    """
    logger.info(f"Marking announcement ID: {announcement_id} as read by user ID: {user_id}")
    
    # First check if record already exists
    check_query = """
    SELECT 1 FROM user_announcements 
    WHERE user_id = %s AND announcement_id = %s
    """
    exists = execute_query(check_query, (user_id, announcement_id), fetch_one=True)
    
    if exists:
        logger.debug(f"Announcement ID: {announcement_id} was already marked as read by user ID: {user_id}")
        return 0
    
    # If not exists, insert the record
    insert_query = """
    INSERT INTO user_announcements (user_id, announcement_id)
    VALUES (%s, %s)
    """
    inserted_id = execute_query(insert_query, (user_id, announcement_id))
    logger.debug(f"Query result: inserted ID = {inserted_id}")
    
    if inserted_id > 0:
        logger.info(f"Marked announcement ID: {announcement_id} as read by user ID: {user_id}")
    
    return inserted_id

# ===== Admin/Editor Operations =====

def get_all_announcements(limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all announcements with pagination.
    
    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of announcement records.
    """
    logger.debug(f"Getting all announcements with limit: {limit}, offset: {offset}")
    query = """
    SELECT a.*, u.username
    FROM announcements a
    JOIN users u ON a.author_id = u.id
    ORDER BY a.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} announcements")
    return results or []

def get_all_announcements_count() -> int:
    """Get total count of all announcements.
    
    Returns:
        int: Total count of announcements.
    """
    logger.debug("Counting all announcements")
    query = """
    SELECT COUNT(*) as count
    FROM announcements a
    """
    result = execute_query(query, (), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} total announcements")
    return count
