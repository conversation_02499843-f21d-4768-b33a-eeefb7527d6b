from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# Event Likes functions

def like_event(user_id: int, event_id: int) -> int:
    """Like an event.

    Args:
        user_id: ID of the user adding the like.
        event_id: ID of the event to like.

    Returns:
        int: ID of the newly created like, or 0 if already liked.
    """
    logger.info(f"User ID {user_id} liking event ID: {event_id}")

    # Check if user already liked this event
    check_query = """
    SELECT id FROM event_likes
    WHERE user_id = %s AND event_id = %s
    """
    existing = execute_query(check_query, (user_id, event_id), fetch_one=True)

    # Toggle like off
    if existing:
        delete_query = """
        DELETE FROM event_likes
        WHERE id = %s
        """
        execute_query(delete_query, (existing['id'],), commit=True)
        logger.info(f"Removed event like with ID: {existing['id']}")
        return -1


    # Add like
    insert_query = """
    INSERT INTO event_likes (user_id, event_id)
    VALUES (%s, %s)
    """
    like_id = execute_query(insert_query, (user_id, event_id))

    logger.info(f"Created like with ID: {like_id}")

    # Create notification for event owner (unless it's the same user)
    event_owner_query = """
    SELECT j.user_id, e.title, liker.username
    FROM events e
    JOIN journeys j ON e.journey_id = j.id
    JOIN users liker ON liker.id = %s
    WHERE e.id = %s
    """
    owner_info = execute_query(event_owner_query, (user_id, event_id), fetch_one=True)

    if owner_info and owner_info['user_id'] != user_id:
        notification_content = f"{owner_info['username']} liked your event '{owner_info['title']}'"

        from data import notification_data
        notification_data.create_notification(
            user_id=owner_info['user_id'],
            notification_type='like',
            content=notification_content,
            related_id=event_id
        )
        logger.debug(f"Created like notification for event owner {owner_info['user_id']}")

    return like_id

def unlike_event(user_id: int, event_id: int) -> int:
    """Remove a like from an event.

    Args:
        user_id: ID of the user removing the like.
        event_id: ID of the event to unlike.

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"User ID {user_id} unliking event ID: {event_id}")

    query = """
    DELETE FROM event_likes
    WHERE user_id = %s AND event_id = %s
    """
    rows_affected = execute_query(query, (user_id, event_id))

    logger.info(f"Unliked event ID: {event_id}, rows affected: {rows_affected}")
    return rows_affected

def get_event_likes(event_id: int) -> List[Dict[str, Any]]:
    """Get all likes for an event.

    Args:
        event_id: ID of the event to get likes for.

    Returns:
        List[Dict[str, Any]]: List of like records with user information.
    """
    logger.debug(f"Getting likes for event ID: {event_id}")

    query = """
    SELECT el.*, u.username, u.first_name, u.last_name, u.profile_image
    FROM event_likes el
    JOIN users u ON el.user_id = u.id
    WHERE el.event_id = %s
    ORDER BY el.created_at DESC
    """
    results = execute_query(query, (event_id,), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} likes for event ID: {event_id}")
    return results or []

def count_event_likes(event_id: int) -> int:
    """Count likes for an event.

    Args:
        event_id: ID of the event to count likes for.

    Returns:
        int: Count of likes for the event.
    """
    logger.debug(f"Counting likes for event ID: {event_id}")

    query = """
    SELECT COUNT(*) as count
    FROM event_likes
    WHERE event_id = %s
    """
    result = execute_query(query, (event_id,), fetch_one=True)
    count = result['count'] if result else 0

    logger.debug(f"Found {count} likes for event ID: {event_id}")
    return count

def check_user_liked_event(user_id: int, event_id: int) -> bool:
    """Check if a user has liked an event.

    Args:
        user_id: ID of the user to check.
        event_id: ID of the event to check.

    Returns:
        bool: True if the user has liked the event, False otherwise.
    """
    logger.debug(f"Checking if user ID {user_id} liked event ID: {event_id}")

    query = """
    SELECT COUNT(*) as count
    FROM event_likes
    WHERE user_id = %s AND event_id = %s
    """
    result = execute_query(query, (user_id, event_id), fetch_one=True)
    liked = result['count'] > 0 if result else False

    logger.debug(f"User ID {user_id} liked event ID {event_id}: {liked}")
    return liked

def get_user_liked_events(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all events and comments liked by a user with pagination.

    Args:
        user_id: ID of the user to get liked events and comments for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of event and comment records with journey and user information.
    """
    logger.debug(f"Getting liked events and comments for user ID: {user_id}")

    # Query for event likes
    event_likes_query = """
        SELECT
            e.id as event_id,
            e.title,
            e.description,
            j.title as journey_title,
            j.user_id as journey_user_id,
            u.username as journey_username,
            u.profile_image as journey_profile_image,
            (SELECT ei.image_filename FROM event_images ei WHERE ei.event_id = e.id AND ei.is_primary = TRUE LIMIT 1) as event_image,
            l.name as location_name,
            el.created_at as liked_at,
            'event' as like_type,
            NULL as comment_content,
            NULL as comment_username
        FROM event_likes el
        JOIN events e ON el.event_id = e.id
        JOIN journeys j ON e.journey_id = j.id
        JOIN users u ON j.user_id = u.id
        LEFT JOIN locations l ON e.location_id = l.id
        WHERE el.user_id = %s
    """

    # Query for comment likes
    comment_likes_query = """
        SELECT
            e.id as event_id,
            e.title,
            e.description,
            j.title as journey_title,
            j.user_id as journey_user_id,
            u.username as journey_username,
            u.profile_image as journey_profile_image,
            (SELECT ei.image_filename FROM event_images ei WHERE ei.event_id = e.id AND ei.is_primary = TRUE LIMIT 1) as event_image,
            l.name as location_name,
            ci.created_at as liked_at,
            'comment' as like_type,
            ec.content as comment_content,
            comment_user.username as comment_username
        FROM comment_interactions ci
        JOIN event_comments ec ON ci.comment_id = ec.id
        JOIN events e ON ec.event_id = e.id
        JOIN journeys j ON e.journey_id = j.id
        JOIN users u ON j.user_id = u.id
        JOIN users comment_user ON ec.user_id = comment_user.id
        LEFT JOIN locations l ON e.location_id = l.id
        WHERE ci.user_id = %s AND ci.interaction_type = 'like'
    """

    # Combine both queries with UNION ALL and add pagination
    query = f"""
        ({event_likes_query})
        UNION ALL
        ({comment_likes_query})
        ORDER BY liked_at DESC
        LIMIT %s OFFSET %s
    """

    # Log the full query and parameters for debugging
    logger.debug(f"Executing query with parameters: user_id={user_id}, limit={limit}, offset={offset}")

    # Execute the query
    results = execute_query(query, (user_id, user_id, limit, offset), fetch_all=True)

    # Log detailed results for debugging
    if results:
        logger.debug(f"Found {len(results)} liked items:")
        for idx, result in enumerate(results):
            logger.debug(f"Like {idx + 1}: type={result.get('like_type')}, title={result.get('title')}, liked_at={result.get('liked_at')}")
    else:
        logger.debug("No likes found in database")

    return results or []

# Event Comments functions

def add_comment(user_id: int, event_id: int, content: str) -> int:
    """Add a comment to an event.

    Args:
        user_id: ID of the user adding the comment.
        event_id: ID of the event to comment on.
        content: Text content of the comment.

    Returns:
        int: ID of the newly created comment.
    """
    logger.info(f"User ID {user_id} commenting on event ID: {event_id}")

    query = """
    INSERT INTO event_comments (user_id, event_id, content)
    VALUES (%s, %s, %s)
    """
    comment_id = execute_query(query, (user_id, event_id, content))

    logger.info(f"Created comment with ID: {comment_id}")

    # Create notification for event owner (unless it's the same user)
    event_owner_query = """
    SELECT j.user_id, e.title, commenter.username
    FROM events e
    JOIN journeys j ON e.journey_id = j.id
    JOIN users commenter ON commenter.id = %s
    WHERE e.id = %s
    """
    owner_info = execute_query(event_owner_query, (user_id, event_id), fetch_one=True)

    if owner_info and owner_info['user_id'] != user_id:
        notification_content = f"{owner_info['username']} commented on your event '{owner_info['title']}'"

        from data import notification_data
        notification_data.create_notification(
            user_id=owner_info['user_id'],
            notification_type='comment',
            content=notification_content,
            related_id=event_id
        )
        logger.debug(f"Created comment notification for event owner {owner_info['user_id']}")

    return comment_id

def update_comment(comment_id: int, content: str) -> int:
    """Update a comment.

    Args:
        comment_id: ID of the comment to update.
        content: New text content for the comment.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating comment ID: {comment_id}")

    query = """
    UPDATE event_comments
    SET content = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (content, comment_id))

    logger.info(f"Updated comment ID: {comment_id}, rows affected: {rows_affected}")
    return rows_affected

def delete_comment(comment_id: int) -> int:
    """Delete a comment.

    Args:
        comment_id: ID of the comment to delete.

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting comment ID: {comment_id}")

    query = """
    DELETE FROM event_comments
    WHERE id = %s
    """
    rows_affected = execute_query(query, (comment_id,))

    logger.info(f"Deleted comment ID: {comment_id}, rows affected: {rows_affected}")
    return rows_affected

def hide_comment(comment_id: int) -> int:
    """Hide a comment (for moderation) and notify the commenter.

    Args:
        comment_id: ID of the comment to hide.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Hiding comment ID: {comment_id}")

    # Fetch report details related to this comment
    report_query = """
    SELECT id AS report_id, reporter_id, reason
    FROM reports
    WHERE content_type = 'comment'
      AND content_id = %s
      AND (status = 'new' OR status = 'open')
    ORDER BY created_at DESC
    LIMIT 1
    """
    report = execute_query(report_query, (comment_id,), fetch_one=True)

    if not report:
        logger.warning(f"No active report found for comment ID: {comment_id}")
        return 0

    report_id = report['report_id']
    reporter_id = report['reporter_id']
    report_reason = report['reason']

    # Hide the comment
    update_query = """
    UPDATE event_comments
    SET is_hidden = TRUE
    WHERE id = %s
    """
    rows_affected = execute_query(update_query, (comment_id,))
    logger.info(f"Hid comment ID: {comment_id}, rows affected: {rows_affected}")

    # Notify the commenter
    notify_comment_hidden(comment_id, reporter_id, report_reason, report_id)

    return rows_affected


def notify_comment_hidden(comment_id: int, reporter_id: int, report_reason: str, report_id: int) -> None:
    """Notify the user who created the comment that their comment has been hidden due to a report.

    Args:
        comment_id: ID of the comment that was hidden.
        reporter_id: ID of the user who submitted the report.
        report_reason: Reason the comment was reported.
        report_id: ID of the related report.
    """
    logger.debug(f"Notifying commenter that comment ID {comment_id} has been hidden.")

    # Get reporter username, commenter ID, and event ID for proper navigation
    query = """
    SELECT ec.user_id AS commenter_id,
           ec.event_id AS event_id,
           commenter.username AS commenter_username,
           reporter.username AS reporter_username
    FROM event_comments ec
    JOIN users commenter ON commenter.id = ec.user_id
    JOIN users reporter ON reporter.id = %s
    WHERE ec.id = %s
    """
    info = execute_query(query, (reporter_id, comment_id), fetch_one=True)
    logger.debug(f"Fetching info with reporter_id={reporter_id}, comment_id={comment_id}")

    if not info:
        logger.error(f"Failed to retrieve user/comment info for comment ID: {comment_id}")
        return

    # Prevent self-notification if reporter and commenter are the same
    if info['commenter_id'] == reporter_id:
        logger.debug("User reported their own comment — skipping notification.")
        return

    notification_content = (
        f"Your comment was hidden by a staff member. "
    )

    # Send notification to the original commenter
    # Use event_id as related_id so user can navigate to the event where their comment was hidden
    try:
        from data import notification_data
        notification_data.create_notification(
            user_id=info['commenter_id'],
            notification_type='comment',
            content=notification_content,
            related_id=info['event_id']
        )
        logger.debug(f"Notified user {info['commenter_id']} that their comment ID {comment_id} was hidden.")
    except Exception as e:
        logger.error(f"Failed to notify commenter of hidden comment: {e}")

def unhide_comment(comment_id: int) -> int:
    """Unhide a comment (for moderation).

    Args:
        comment_id: ID of the comment to unhide.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Unhiding comment ID: {comment_id}")

    query = """
    UPDATE event_comments
    SET is_hidden = FALSE
    WHERE id = %s
    """
    rows_affected = execute_query(query, (comment_id,))

    logger.info(f"Unhid comment ID: {comment_id}, rows affected: {rows_affected}")
    return rows_affected

def get_comment(comment_id: int) -> Optional[Dict[str, Any]]:
    """Get a comment by ID.

    Args:
        comment_id: ID of the comment to retrieve.

    Returns:
        Dict[str, Any]: Comment data if found, None otherwise.
    """
    logger.debug(f"Getting comment with ID: {comment_id}")

    query = """
    SELECT ec.*, u.username, u.profile_image,
           (SELECT COUNT(*) FROM comment_interactions
            WHERE comment_id = ec.id AND interaction_type = 'like') as like_count,
           (SELECT COUNT(*) FROM comment_interactions
            WHERE comment_id = ec.id AND interaction_type = 'dislike') as dislike_count
    FROM event_comments ec
    JOIN users u ON ec.user_id = u.id
    WHERE ec.id = %s
    """
    result = execute_query(query, (comment_id,), fetch_one=True)

    logger.debug(f"Comment lookup result: {'Found' if result else 'Not found'}")
    return result

def get_event_comments(event_id: int, include_hidden: bool = False) -> List[Dict[str, Any]]:
    """Get all comments for an event.

    Args:
        event_id: ID of the event to get comments for.
        include_hidden: Whether to include hidden comments.

    Returns:
        List[Dict[str, Any]]: List of comment records with user information.
    """
    logger.debug(f"Getting comments for event ID: {event_id}, include_hidden: {include_hidden}")

    query = """
    SELECT ec.*, u.username, u.profile_image,
           (SELECT COUNT(*) FROM comment_interactions
            WHERE comment_id = ec.id AND interaction_type = 'like') as like_count,
           (SELECT COUNT(*) FROM comment_interactions
            WHERE comment_id = ec.id AND interaction_type = 'dislike') as dislike_count
    FROM event_comments ec
    JOIN users u ON ec.user_id = u.id
    WHERE ec.event_id = %s
    """

    if not include_hidden:
        query += " AND ec.is_hidden = FALSE"

    query += " ORDER BY ec.created_at ASC"

    results = execute_query(query, (event_id,), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} comments for event ID: {event_id}")
    return results or []

def count_event_comments(event_id: int, include_hidden: bool = False) -> int:
    """Count comments for an event.

    Args:
        event_id: ID of the event to count comments for.
        include_hidden: Whether to include hidden comments.

    Returns:
        int: Count of comments for the event.
    """
    logger.debug(f"Counting comments for event ID: {event_id}, include_hidden: {include_hidden}")

    query = """
    SELECT COUNT(*) as count
    FROM event_comments
    WHERE event_id = %s
    """

    if not include_hidden:
        query += " AND is_hidden = FALSE"

    result = execute_query(query, (event_id,), fetch_one=True)
    count = result['count'] if result else 0

    logger.debug(f"Found {count} comments for event ID: {event_id}")
    return count

def get_user_comments(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all comments made by a user with pagination.

    Args:
        user_id: ID of the user to get comments for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of comment records with event and journey information.
    """
    logger.debug(f"Getting comments for user ID: {user_id}")

    query = """
    SELECT ec.*, e.title as event_title, j.title as journey_title, j.user_id as journey_user_id,
           (SELECT COUNT(*) FROM comment_interactions
            WHERE comment_id = ec.id AND interaction_type = 'like') as like_count,
           (SELECT COUNT(*) FROM comment_interactions
            WHERE comment_id = ec.id AND interaction_type = 'dislike') as dislike_count
    FROM event_comments ec
    JOIN events e ON ec.event_id = e.id
    JOIN journeys j ON e.journey_id = j.id
    WHERE ec.user_id = %s
    ORDER BY ec.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} comments for user ID: {user_id}")
    return results or []

# Comment Interactions (like/dislike/report)

def interact_with_comment(user_id: int, comment_id: int, interaction_type: str,
                      ) -> int:
    """Add an interaction to a comment (like, dislike, or report).

    Args:
        user_id: ID of the user adding the interaction.
        comment_id: ID of the comment to interact with.
        interaction_type: Type of interaction ('like', 'dislike', 'report').

    Returns:
        int: ID of the newly created interaction, or 0 if already exists.
    """
    logger.info(f"User ID {user_id} {interaction_type}ing comment ID: {comment_id}")

    # Validate interaction type
    valid_types = ['like', 'dislike', 'report']
    if interaction_type not in valid_types:
        logger.error(f"Invalid interaction type: {interaction_type}")
        raise ValueError(f"Invalid interaction type. Must be one of: {valid_types}")

    # Skip database operations for 'report' type since database doesn't support it
    if interaction_type == 'report':
        logger.info(f"Skipping database insert for 'report' interaction type (use reports table instead)")
        return 1  # Return a positive number to indicate success

    # Check if user already interacted with this comment in this way
    check_query = """
    SELECT id FROM comment_interactions
    WHERE user_id = %s AND comment_id = %s AND interaction_type = %s
    """
    existing = execute_query(check_query, (user_id, comment_id, interaction_type), fetch_one=True)

    if existing:
    # Toggle off: delete existing interaction
        delete_query = """
        DELETE FROM comment_interactions
        WHERE id = %s
        """
        execute_query(delete_query, (existing['id'],), commit=True)
        logger.info(f"Removed {interaction_type} interaction with ID: {existing['id']}")
        return -1

    # For likes/dislikes, remove any existing opposite interaction
    if interaction_type in ['like', 'dislike']:
        opposite_type = 'dislike' if interaction_type == 'like' else 'like'
        delete_query = """
        DELETE FROM comment_interactions
        WHERE user_id = %s AND comment_id = %s AND interaction_type = %s
        """
        execute_query(delete_query, (user_id, comment_id, opposite_type))

    # Add interaction
    insert_query = """
    INSERT INTO comment_interactions (user_id, comment_id, interaction_type)
    VALUES (%s, %s, %s)
    """
    interaction_id = execute_query(insert_query, (user_id, comment_id, interaction_type))

    logger.info(f"Created {interaction_type} interaction with ID: {interaction_id}")

    # Create notification for comment owner (unless it's the same user)
    comment_owner_query = """
    SELECT ec.user_id, ec.content, liker.username, e.id as event_id, e.title as event_title
    FROM event_comments ec
    JOIN users liker ON liker.id = %s
    JOIN events e ON e.id = ec.event_id
    WHERE ec.id = %s
    """
    owner_info = execute_query(comment_owner_query, (user_id, comment_id), fetch_one=True)

    if owner_info and owner_info['user_id'] != user_id:
        notification_content = f"{owner_info['username']} {interaction_type}d your comment on event '{owner_info['event_title']}'"

        from data import notification_data
        notification_data.create_notification(
            user_id=owner_info['user_id'],
            notification_type=interaction_type,
            content=notification_content,
            related_id=owner_info['event_id']
        )
        logger.debug(f"Created {interaction_type} notification for comment owner {owner_info['user_id']}")

    return interaction_id

def remove_comment_interaction(user_id: int, comment_id: int, interaction_type: str) -> int:
    """Remove an interaction from a comment.

    Args:
        user_id: ID of the user removing the interaction.
        comment_id: ID of the comment to remove interaction from.
        interaction_type: Type of interaction to remove ('like', 'dislike', 'report').

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"User ID {user_id} removing {interaction_type} from comment ID: {comment_id}")

    query = """
    DELETE FROM comment_interactions
    WHERE user_id = %s AND comment_id = %s AND interaction_type = %s
    """
    rows_affected = execute_query(query, (user_id, comment_id, interaction_type))

    logger.info(f"Removed {interaction_type} from comment ID: {comment_id}, rows affected: {rows_affected}")
    return rows_affected

def check_user_interaction_with_comment(user_id: int, comment_id: int, interaction_type: str) -> bool:
    """Check if a user has interacted with a comment in a specific way.

    Args:
        user_id: ID of the user to check.
        comment_id: ID of the comment to check.
        interaction_type: Type of interaction to check ('like', 'dislike', 'report').

    Returns:
        bool: True if the user has interacted with the comment in that way, False otherwise.
    """
    logger.debug(f"Checking if user ID {user_id} {interaction_type}d comment ID: {comment_id}")

    # For 'report' type, always return False since database doesn't support it
    # Use report_data.has_user_flagged_comment instead
    if interaction_type == 'report':
        logger.debug(f"Returning False for 'report' check (should use reports table instead)")
        return False

    query = """
    SELECT COUNT(*) as count
    FROM comment_interactions
    WHERE user_id = %s AND comment_id = %s AND interaction_type = %s
    """
    result = execute_query(query, (user_id, comment_id, interaction_type), fetch_one=True)
    has_interacted = result['count'] > 0 if result else False

    logger.debug(f"User ID {user_id} {interaction_type}d comment ID {comment_id}: {has_interacted}")
    return has_interacted

def check_user_reported_comment(user_id: int, comment_id: int) -> bool:
    """Check if a user has reported a specific comment.

    Args:
        user_id: ID of the user (reporter).
        comment_id: ID of the comment being reported.

    Returns:
        bool: True if the user has reported the comment, False otherwise.
    """
    logger.debug(f"Checking if user ID {user_id} has reported comment ID: {comment_id}")

    query = """
    SELECT COUNT(*) as count
    FROM reports
    WHERE reporter_id = %s AND content_type = 'comment' AND content_id = %s
    """

    result = execute_query(query, (user_id, comment_id), fetch_one=True)
    has_reported = result['count'] > 0 if result else False

    logger.debug(f"User ID {user_id} reported comment ID {comment_id}: {has_reported}")
    return has_reported


def get_reported_comments(limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all reported comments with pagination.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of reported comment records with counts and details.
    """
    logger.debug(f"Getting reported comments with limit: {limit}, offset: {offset}")

    query = """
    SELECT ec.*, u.username, u.profile_image,
           e.title as event_title, j.title as journey_title, j.user_id as journey_user_id,
           COUNT(r.id) as report_count
    FROM event_comments ec
    JOIN users u ON ec.user_id = u.id
    JOIN events e ON ec.event_id = e.id
    JOIN journeys j ON e.journey_id = j.id
    JOIN reports r ON r.content_id = ec.id AND r.content_type = 'comment'
    GROUP BY ec.id
    ORDER BY report_count DESC, ec.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} reported comments")
    return results or []

def get_comment_reports(comment_id: int) -> List[Dict[str, Any]]:
    """Get all reports for a comment.

    Args:
        comment_id: ID of the comment to get reports for.

    Returns:
        List[Dict[str, Any]]: List of report records with user information.
    """
    logger.debug(f"Getting reports for comment ID: {comment_id}")

    query = """
    SELECT r.*, u.username, u.profile_image
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    WHERE r.content_id = %s AND r.content_type = 'comment'
    ORDER BY r.created_at DESC
    """
    results = execute_query(query, (comment_id,), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} reports for comment ID: {comment_id}")
    return results or []

def report_comment(reporter_id: int, comment_id: int, reason: str) -> int:
    """Report a comment for moderation.

    Args:
        reporter_id: ID of the user reporting the comment.
        comment_id: ID of the comment that was reported.
        reason: Reason for the report.

    Returns:
        int: ID of the newly created report, or 0 if already reported.
    """
    logger.info(f"User ID {reporter_id} reporting comment ID: {comment_id}")

    # Check if already reported by this user
    check_query = """
    SELECT id FROM reports
    WHERE reporter_id = %s AND content_type = 'comment' AND content_id = %s
    """
    existing = execute_query(check_query, (reporter_id, comment_id), fetch_one=True)

    if existing:
        logger.debug(f"User ID {reporter_id} already reported comment ID: {comment_id}")
        return 0

    # Add report
    insert_query = """
    INSERT INTO reports
    (reporter_id, content_type, content_id, reason, status)
    VALUES (%s, 'comment', %s, %s, 'new')
    """
    report_id = execute_query(insert_query, (reporter_id, comment_id, reason))

    logger.info(f"Created comment report with ID: {report_id}")

    # Notify staff and original poster of the report
    notify_moderator_of_report(reporter_id, comment_id, reason, report_id)
    notify_commenter_of_report(comment_id, reporter_id, reason, report_id )

    return report_id

def notify_moderator_of_report(reporter_id: int, comment_id: int, report_reason: str, report_id: int) -> None:
    """Notify moderators of a reported comment.

    Args:
        reporter_id: ID of the user who reported the comment.
        comment_id: ID of the comment that was reported.
        report_reason: Reason for the report.
        report_id: ID of the newly created report.
    """
    logger.debug(f"Notifying moderators of report for comment ID: {comment_id}")

    # Get reporter and comment info
    info_query = """
    SELECT ec.content, ec.user_id,
           reporter.username as reporter_username,
           commenter.username as commenter_username
    FROM event_comments ec
    JOIN users reporter ON reporter.id = %s
    JOIN users commenter ON commenter.id = ec.user_id
    WHERE ec.id = %s
    """
    info = execute_query(info_query, (reporter_id, comment_id), fetch_one=True)

    if not info:
        logger.error(f"Could not find information for comment ID: {comment_id}")
        return

    # Create notification content
    notification_content = (
        f"{info['reporter_username']} reported a comment by {info['commenter_username']}. "
        f"Reason: {report_reason}"
    )

    # Get all staff users (editors, moderators, admins)
    staff_query = """
    SELECT id FROM users
    WHERE role = 'moderator'
    """
    staff_results = execute_query(staff_query, fetch_all=True)

    if not staff_results:
        logger.warning("No moderators found to notify of report")
        return

    # Send notification to all staff - using report_id instead of comment_id
    for staff in staff_results:
        from data import notification_data
        notification_data.create_notification(
            user_id=staff['id'],
            notification_type='report',
            content=notification_content,
            related_id=report_id
        )

    logger.debug(f"Notified {len(staff_results)} moderators about reported comment ID: {comment_id}")


def notify_commenter_of_report(comment_id: int, reporter_id: int, report_reason: str, report_id: int) -> None:
    """Notify the user who created the comment that it has been reported.

    Args:
        comment_id: ID of the comment that was reported.
        reporter_id: ID of the user who submitted the report.
        report_reason: Reason the comment was reported.
        report_id: ID of the report.
    """
    logger.debug(f"Notifying commenter of reported comment ID: {comment_id}")

    # Get reporter username and commenter ID
    query = """
    SELECT ec.user_id as commenter_id,
           commenter.username as commenter_username,
           reporter.username as reporter_username
    FROM event_comments ec
    JOIN users commenter ON commenter.id = ec.user_id
    JOIN users reporter ON reporter.id = %s
    WHERE ec.id = %s
    """
    info = execute_query(query, (reporter_id, comment_id), fetch_one=True)
    logger.debug(f"Fetching info with reporter_id={reporter_id}, comment_id={comment_id}")

    if not info:
        logger.error(f"Failed to retrieve user/comment info for comment ID: {comment_id}")
        return

    notification_content = (
        f"Your comment was reported by {info['reporter_username']}. "
        f"Reason: {report_reason}"
    )

    # Send notification to the original commenter
    from data import notification_data
    notification_data.create_notification(
        user_id=info['commenter_id'],
        notification_type='report',
        content=notification_content,
        related_id=report_id
    )

    logger.debug(f"Notified user {info['commenter_id']} that their comment ID {comment_id} was reported.")


# Private Messages functions

def send_message(sender_id: int, recipient_id: int, content: str) -> int:
    """Send a private message to another user.

    Args:
        sender_id: ID of the user sending the message.
        recipient_id: ID of the user to receive the message.
        content: Text content of the message.

    Returns:
        int: ID of the newly created message.
    """
    logger.info(f"User ID {sender_id} sending message to user ID: {recipient_id}")

    query = """
    INSERT INTO private_messages (sender_id, recipient_id, content)
    VALUES (%s, %s, %s)
    """
    message_id = execute_query(query, (sender_id, recipient_id, content))

    logger.info(f"Created message with ID: {message_id}")

    # Create notification for recipient
    sender_query = """
    SELECT username FROM users WHERE id = %s
    """
    sender = execute_query(sender_query, (sender_id,), fetch_one=True)

    if sender:
        notification_content = f"New message from {sender['username']}"

        from data import notification_data
        notification_data.create_notification(
            user_id=recipient_id,
            notification_type='message',
            content=notification_content,
            related_id=message_id
        )
        logger.debug(f"Created message notification for recipient {recipient_id}")

    return message_id

def get_message(message_id: int) -> Optional[Dict[str, Any]]:
    """Get a message by ID.

    Args:
        message_id: ID of the message to retrieve.

    Returns:
        Dict[str, Any]: Message data if found, None otherwise.
    """
    logger.debug(f"Getting message with ID: {message_id}")

    query = """
    SELECT pm.*,
           sender.username as sender_username, sender.profile_image as sender_image,
           recipient.username as recipient_username, recipient.profile_image as recipient_image
    FROM private_messages pm
    JOIN users sender ON pm.sender_id = sender.id
    JOIN users recipient ON pm.recipient_id = recipient.id
    WHERE pm.id = %s
    """
    result = execute_query(query, (message_id,), fetch_one=True)

    logger.debug(f"Message lookup result: {'Found' if result else 'Not found'}")
    return result

def mark_message_as_read(message_id: int) -> int:
    """Mark a message as read.

    Args:
        message_id: ID of the message to mark as read.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Marking message ID: {message_id} as read")

    query = """
    UPDATE private_messages
    SET is_read = TRUE
    WHERE id = %s
    """
    rows_affected = execute_query(query, (message_id,))

    logger.info(f"Marked message ID: {message_id} as read, rows affected: {rows_affected}")
    return rows_affected

def delete_message(message_id: int) -> int:
    """Delete a message.

    Args:
        message_id: ID of the message to delete.

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting message ID: {message_id}")

    query = """
    DELETE FROM private_messages
    WHERE id = %s
    """
    rows_affected = execute_query(query, (message_id,))

    logger.info(f"Deleted message ID: {message_id}, rows affected: {rows_affected}")
    return rows_affected

def get_user_received_messages(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all messages received by a user with pagination.

    Args:
        user_id: ID of the user to get received messages for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of message records with sender information.
    """
    logger.debug(f"Getting received messages for user ID: {user_id}")

    query = """
    SELECT pm.*, u.username as sender_username, u.profile_image as sender_image
    FROM private_messages pm
    JOIN users u ON pm.sender_id = u.id
    WHERE pm.recipient_id = %s
    ORDER BY pm.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} received messages for user ID: {user_id}")
    return results or []

def get_user_sent_messages(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all messages sent by a user with pagination.

    Args:
        user_id: ID of the user to get sent messages for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of message records with recipient information.
    """
    logger.debug(f"Getting sent messages for user ID: {user_id}")

    query = """
    SELECT pm.*, u.username as recipient_username, u.profile_image as recipient_image
    FROM private_messages pm
    JOIN users u ON pm.recipient_id = u.id
    WHERE pm.sender_id = %s
    ORDER BY pm.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} sent messages for user ID: {user_id}")
    return results or []

def get_conversation(user1_id: int, user2_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get the conversation between two users with pagination.

    Args:
        user1_id: ID of the first user in the conversation.
        user2_id: ID of the second user in the conversation.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of message records in the conversation.
    """
    logger.debug(f"Getting conversation between user ID {user1_id} and user ID {user2_id}")

    query = """
    SELECT pm.*,
           sender.id as sender_id, sender.username as sender_username,
           recipient.id as recipient_id, recipient.username as recipient_username
    FROM private_messages pm
    JOIN users sender ON pm.sender_id = sender.id
    JOIN users recipient ON pm.recipient_id = recipient.id
    WHERE (pm.sender_id = %s AND pm.recipient_id = %s)
       OR (pm.sender_id = %s AND pm.recipient_id = %s)
    ORDER BY pm.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user1_id, user2_id, user2_id, user1_id, limit, offset), fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} messages in conversation")

    # Mark all messages from user2 to user1 as read
    mark_conversation_as_read(user2_id, user1_id)

    return results or []

def mark_conversation_as_read(sender_id: int, recipient_id: int) -> int:
    """Mark all messages from sender to recipient as read.

    Args:
        sender_id: ID of the sender of the messages.
        recipient_id: ID of the recipient of the messages.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Marking all messages from user ID {sender_id} to user ID {recipient_id} as read")

    query = """
    UPDATE private_messages
    SET is_read = TRUE
    WHERE sender_id = %s AND recipient_id = %s AND is_read = FALSE
    """
    rows_affected = execute_query(query, (sender_id, recipient_id))

    logger.info(f"Marked {rows_affected} messages as read")
    return rows_affected

def count_unread_messages(user_id: int) -> int:
    """Count unread messages for a user.

    Args:
        user_id: ID of the user to count unread messages for.

    Returns:
        int: Count of unread messages.
    """
    logger.debug(f"Counting unread messages for user ID: {user_id}")

    query = """
    SELECT COUNT(*) as count
    FROM private_messages
    WHERE recipient_id = %s AND is_read = FALSE
    """
    result = execute_query(query, (user_id,), fetch_one=True)
    count = result['count'] if result else 0

    logger.debug(f"Found {count} unread messages for user ID: {user_id}")
    return count

def get_user_conversations(user_id: int) -> List[Dict[str, Any]]:
    """Get all conversations a user is part of, with the most recent message for each.

    Args:
        user_id: ID of the user to get conversations for.

    Returns:
        List[Dict[str, Any]]: List of conversation records with the other user and the most recent message.
    """
    logger.debug(f"Getting conversations for user ID: {user_id}")

    query = """
        SELECT DISTINCT
            CASE
                WHEN pm.sender_id = %s THEN pm.recipient_id
                ELSE pm.sender_id
            END AS other_user_id,
            CASE
                WHEN pm.sender_id = %s THEN r.username
                ELSE s.username
            END AS other_username,
            CASE
                WHEN pm.sender_id = %s THEN r.profile_image
                ELSE s.profile_image
            END AS other_profile_image,
            (
                SELECT COUNT(*)
                FROM private_messages
                WHERE recipient_id = %s
                AND sender_id = (CASE WHEN pm.sender_id = %s THEN pm.recipient_id ELSE pm.sender_id END)
                AND is_read = FALSE
            ) as unread_count,
            (
                SELECT pm2.content
                FROM private_messages pm2
                WHERE (
                    (pm2.sender_id = %s AND pm2.recipient_id = (CASE WHEN pm.sender_id = %s THEN pm.recipient_id ELSE pm.sender_id END))
                    OR
                    (pm2.sender_id = (CASE WHEN pm.sender_id = %s THEN pm.recipient_id ELSE pm.sender_id END) AND pm2.recipient_id = %s)
                )
                ORDER BY pm2.created_at DESC
                LIMIT 1
            ) as last_message,
            (
                SELECT pm2.created_at
                FROM private_messages pm2
                WHERE (
                    (pm2.sender_id = %s AND pm2.recipient_id = (CASE WHEN pm.sender_id = %s THEN pm.recipient_id ELSE pm.sender_id END))
                    OR
                    (pm2.sender_id = (CASE WHEN pm.sender_id = %s THEN pm.recipient_id ELSE pm.sender_id END) AND pm2.recipient_id = %s)
                )
                ORDER BY pm2.created_at DESC
                LIMIT 1
            ) as last_message_time
        FROM private_messages pm
        JOIN users s ON pm.sender_id = s.id
        JOIN users r ON pm.recipient_id = r.id
        WHERE pm.sender_id = %s OR pm.recipient_id = %s
        ORDER BY last_message_time DESC
        """

    params = (
        user_id, user_id, user_id, user_id, user_id,
        user_id, user_id, user_id, user_id, user_id,
        user_id, user_id, user_id, user_id, user_id
    )
    results = execute_query(query, params, fetch_all=True)

    logger.debug(f"Found {len(results) if results else 0} conversations for user ID: {user_id}")
    return results or []

def check_can_message(sender_id: int, recipient_id: int) -> bool:
    """Check if a user can send a message to another user.

    Rules:
    - Staff (editor, moderator, support_tech, admin) can message anyone
    - Premium subscribers can message anyone
    - Free users can read messages but can't reply/initiate

    Args:
        sender_id: ID of the sender to check.
        recipient_id: ID of the recipient to check.

    Returns:
        bool: True if the sender can message the recipient, False otherwise.
    """
    logger.debug(f"Checking if user ID {sender_id} can message user ID: {recipient_id}")

    # Check if sender is staff
    staff_query = """
    SELECT role FROM users WHERE id = %s
    """
    staff_result = execute_query(staff_query, (sender_id,), fetch_one=True)

    from utils.permissions import PermissionGroups
    if staff_result and staff_result['role'] in PermissionGroups.STAFF:
        logger.debug(f"User ID {sender_id} can message as staff role: {staff_result['role']}")
        return True

    # Check if sender has active subscription
    subscription_query = """
    SELECT COUNT(*) as count
    FROM subscriptions
    WHERE user_id = %s AND is_active = TRUE AND end_date >= CURDATE()
    """
    subscription_result = execute_query(subscription_query, (sender_id,), fetch_one=True)
    has_subscription = subscription_result['count'] > 0 if subscription_result else False

    if has_subscription:
        logger.debug(f"User ID {sender_id} can message as premium subscriber")
        return True

    logger.debug(f"User ID {sender_id} cannot message (free user)")
    return False