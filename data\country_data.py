"""
Country Data Module

This module handles database operations for country data.
"""

from typing import List, Dict, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def get_all_countries() -> List[Dict[str, Any]]:
    """Get all countries for dropdown lists.
    
    Returns:
        List[Dict[str, Any]]: List of country records with id, name, and code.
    """
    logger.debug("Getting all countries for dropdown")
    query = """
    SELECT id, name, code, code3, currency_code, currency_symbol, has_gst, gst_rate
    FROM countries
    ORDER BY name ASC
    """
    results = execute_query(query, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} countries")
    return results or []

def get_country_by_id(country_id: int) -> Dict[str, Any]:
    """Get country by ID.
    
    Args:
        country_id: The ID of the country to retrieve.
        
    Returns:
        Dict[str, Any]: Country data if found, empty dict otherwise.
    """
    logger.debug(f"Getting country with ID: {country_id}")
    query = """
    SELECT id, name, code, code3, currency_code, currency_symbol, has_gst, gst_rate
    FROM countries
    WHERE id = %s
    """
    result = execute_query(query, (country_id,), fetch_one=True)
    logger.debug(f"Country lookup result: {'Found' if result else 'Not found'}")
    return result or {}