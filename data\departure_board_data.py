from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def follow_journey(user_id: int, journey_id: int) -> int:
    """Follow a journey.
    
    Args:
        user_id: The ID of the user following the journey.
        journey_id: The ID of the journey to follow.
        
    Returns:
        int: ID of the newly created follow record, or 0 if already following.
    """
    logger.info(f"User ID {user_id} following journey ID: {journey_id}")
    
    # Check if already following
    check_query = """
    SELECT id FROM user_follows_journey
    WHERE user_id = %s AND journey_id = %s
    """
    existing = execute_query(check_query, (user_id, journey_id), fetch_one=True)
    
    if existing:
        logger.debug(f"User ID {user_id} already following journey ID: {journey_id}")
        return 0
    
    # Add follow
    insert_query = """
    INSERT INTO user_follows_journey (user_id, journey_id)
    VALUES (%s, %s)
    """
    follow_id = execute_query(insert_query, (user_id, journey_id))
    
    logger.info(f"Created follow with ID: {follow_id}")
    return follow_id

def unfollow_journey(user_id: int, journey_id: int) -> int:
    """Unfollow a journey.
    
    Args:
        user_id: The ID of the user unfollowing the journey.
        journey_id: The ID of the journey to unfollow.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"User ID {user_id} unfollowing journey ID: {journey_id}")
    query = """
    DELETE FROM user_follows_journey
    WHERE user_id = %s AND journey_id = %s
    """
    rows_affected = execute_query(query, (user_id, journey_id))
    logger.info(f"Unfollowed journey ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected

def check_following_journey(user_id: int, journey_id: int) -> bool:
    """Check if a user is following a journey.
    
    Args:
        user_id: The ID of the user to check.
        journey_id: The ID of the journey to check.
        
    Returns:
        bool: True if the user is following the journey, False otherwise.
    """
    logger.debug(f"Checking if user ID {user_id} is following journey ID: {journey_id}")
    query = """
    SELECT COUNT(*) as count
    FROM user_follows_journey
    WHERE user_id = %s AND journey_id = %s
    """
    result = execute_query(query, (user_id, journey_id), fetch_one=True)
    following = result['count'] > 0 if result else False
    logger.debug(f"User ID {user_id} following journey ID {journey_id}: {following}")
    return following

def get_followed_journeys(user_id: int) -> List[Dict[str, Any]]:
    """Get all journeys followed by a user.
    
    Args:
        user_id: The ID of the user to get followed journeys for.
        
    Returns:
        List[Dict[str, Any]]: List of journey records.
    """
    logger.debug(f"Getting journeys followed by user ID: {user_id}")
    query = """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    JOIN user_follows_journey ufj ON j.id = ufj.journey_id
    WHERE ufj.user_id = %s
    ORDER BY ufj.created_at DESC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} journeys followed by user ID: {user_id}")
    return results or []

def follow_user(follower_id: int, followed_id: int) -> int:
    """Follow a user.
    
    Args:
        follower_id: The ID of the user doing the following.
        followed_id: The ID of the user to follow.
        
    Returns:
        int: ID of the newly created follow record, or 0 if already following.
    """
    logger.info(f"User ID {follower_id} following user ID: {followed_id}")
    
    # Check if already following
    check_query = """
    SELECT id FROM user_follows_user
    WHERE follower_id = %s AND followed_id = %s
    """
    existing = execute_query(check_query, (follower_id, followed_id), fetch_one=True)
    
    if existing:
        logger.debug(f"User ID {follower_id} already following user ID: {followed_id}")
        return 0
    
    # Add follow
    insert_query = """
    INSERT INTO user_follows_user (follower_id, followed_id)
    VALUES (%s, %s)
    """
    follow_id = execute_query(insert_query, (follower_id, followed_id))
    
    logger.info(f"Created follow with ID: {follow_id}")
    return follow_id

def unfollow_user(follower_id: int, followed_id: int) -> int:
    """Unfollow a user.
    
    Args:
        follower_id: The ID of the user doing the unfollowing.
        followed_id: The ID of the user to unfollow.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"User ID {follower_id} unfollowing user ID: {followed_id}")
    query = """
    DELETE FROM user_follows_user
    WHERE follower_id = %s AND followed_id = %s
    """
    rows_affected = execute_query(query, (follower_id, followed_id))
    logger.info(f"Unfollowed user ID: {followed_id}, rows affected: {rows_affected}")
    return rows_affected

def check_following_user(follower_id: int, followed_id: int) -> bool:
    """Check if a user is following another user.
    
    Args:
        follower_id: The ID of the user to check.
        followed_id: The ID of the user being followed.
        
    Returns:
        bool: True if the user is following the other user, False otherwise.
    """
    logger.debug(f"Checking if user ID {follower_id} is following user ID: {followed_id}")
    query = """
    SELECT COUNT(*) as count
    FROM user_follows_user
    WHERE follower_id = %s AND followed_id = %s
    """
    result = execute_query(query, (follower_id, followed_id), fetch_one=True)
    following = result['count'] > 0 if result else False
    logger.debug(f"User ID {follower_id} following user ID {followed_id}: {following}")
    return following

def get_followed_users(follower_id: int) -> List[Dict[str, Any]]:
    """Get all users followed by a user.
    
    Args:
        follower_id: The ID of the user to get followed users for.
        
    Returns:
        List[Dict[str, Any]]: List of user records.
    """
    logger.debug(f"Getting users followed by user ID: {follower_id}")
    query = """
    SELECT u.id, u.username, u.first_name, u.last_name, u.profile_image, 
           u.location, u.description, ufu.created_at as followed_at
    FROM users u
    JOIN user_follows_user ufu ON u.id = ufu.followed_id
    WHERE ufu.follower_id = %s
    ORDER BY ufu.created_at DESC
    """
    results = execute_query(query, (follower_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} users followed by user ID: {follower_id}")
    return results or []

def get_followers(user_id: int) -> List[Dict[str, Any]]:
    """Get all followers of a user.
    
    Args:
        user_id: The ID of the user to get followers for.
        
    Returns:
        List[Dict[str, Any]]: List of user records.
    """
    logger.debug(f"Getting followers for user ID: {user_id}")
    query = """
    SELECT u.id, u.username, u.first_name, u.last_name, u.profile_image, 
           u.location, u.description, ufu.created_at as followed_at
    FROM users u
    JOIN user_follows_user ufu ON u.id = ufu.follower_id
    WHERE ufu.followed_id = %s
    ORDER BY ufu.created_at DESC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} followers for user ID: {user_id}")
    return results or []

def follow_location(user_id: int, location_id: int) -> int:
    """Follow a location.
    
    Args:
        user_id: The ID of the user following the location.
        location_id: The ID of the location to follow.
        
    Returns:
        int: ID of the newly created follow record, or 0 if already following.
    """
    logger.info(f"User ID {user_id} following location ID: {location_id}")
    
    # Check if already following
    check_query = """
    SELECT id FROM user_follows_location
    WHERE user_id = %s AND location_id = %s
    """
    existing = execute_query(check_query, (user_id, location_id), fetch_one=True)
    
    if existing:
        logger.debug(f"User ID {user_id} already following location ID: {location_id}")
        return 0
    
    # Add follow
    insert_query = """
    INSERT INTO user_follows_location (user_id, location_id)
    VALUES (%s, %s)
    """
    follow_id = execute_query(insert_query, (user_id, location_id))
    
    logger.info(f"Created follow with ID: {follow_id}")
    return follow_id

def unfollow_location(user_id: int, location_id: int) -> int:
    """Unfollow a location.
    
    Args:
        user_id: The ID of the user unfollowing the location.
        location_id: The ID of the location to unfollow.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"User ID {user_id} unfollowing location ID: {location_id}")
    query = """
    DELETE FROM user_follows_location
    WHERE user_id = %s AND location_id = %s
    """
    rows_affected = execute_query(query, (user_id, location_id))
    logger.info(f"Unfollowed location ID: {location_id}, rows affected: {rows_affected}")
    return rows_affected

def check_following_location(user_id: int, location_id: int) -> bool:
    """Check if a user is following a location.
    
    Args:
        user_id: The ID of the user to check.
        location_id: The ID of the location to check.
        
    Returns:
        bool: True if the user is following the location, False otherwise.
    """
    logger.debug(f"Checking if user ID {user_id} is following location ID: {location_id}")
    query = """
    SELECT COUNT(*) as count
    FROM user_follows_location
    WHERE user_id = %s AND location_id = %s
    """
    result = execute_query(query, (user_id, location_id), fetch_one=True)
    following = result['count'] > 0 if result else False
    logger.debug(f"User ID {user_id} following location ID {location_id}: {following}")
    return following

def get_followed_locations(user_id: int) -> List[Dict[str, Any]]:
    """Get all locations followed by a user.
    
    Args:
        user_id: The ID of the user to get followed locations for.
        
    Returns:
        List[Dict[str, Any]]: List of location records.
    """
    logger.debug(f"Getting locations followed by user ID: {user_id}")
    query = """
    SELECT l.*, ufl.created_at as followed_at
    FROM locations l
    JOIN user_follows_location ufl ON l.id = ufl.location_id
    WHERE ufl.user_id = %s
    ORDER BY ufl.created_at DESC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} locations followed by user ID: {user_id}")
    return results or []

def get_departure_board_events(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get events for a user's departure board.
    
    This includes events from followed journeys, users, and locations.
    
    Args:
        user_id: The ID of the user to get departure board for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of event records for the departure board.
    """
    logger.debug(f"Getting departure board events for user ID: {user_id}")
    query = """
    SELECT dbe.*, 
       e.title AS event_title,
       l.name AS location_name,
       l.latitude,
       l.longitude,
       (SELECT image_filename 
        FROM event_images 
        WHERE event_id = dbe.event_id AND is_primary = TRUE 
        LIMIT 1) AS event_image
    FROM departure_board_events dbe
    JOIN events e ON dbe.event_id = e.id
    JOIN locations l ON e.location_id = l.id
    WHERE dbe.follower_id = %s
    ORDER BY dbe.start_datetime DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} departure board events for user ID: {user_id}")
    return results or []

def search_departure_board_events(user_id: int, search_term: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for events in a user's departure board.

    This includes events from followed journeys, users, and locations,
    filtered by event title or location name.

    Args:
        user_id: The ID of the user to get departure board for.
        search_term: Term to search for in event titles or location names.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of matching departure board event records.
    """
    logger.debug(f"Searching departure board events for user ID: {user_id} with term: '{search_term}'")
    search_pattern = f"%{search_term}%"

    query = """
    SELECT dbe.*, 
           e.title AS event_title,
           l.name AS location_name,
           l.latitude,
           l.longitude,
           (SELECT image_filename 
            FROM event_images 
            WHERE event_id = dbe.event_id AND is_primary = TRUE 
            LIMIT 1) AS event_image
    FROM departure_board_events dbe
    JOIN events e ON dbe.event_id = e.id
    JOIN locations l ON e.location_id = l.id
    WHERE dbe.follower_id = %s
      AND (
        e.title LIKE %s OR 
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
        OR dbe.username LIKE %s
      )
    ORDER BY dbe.start_datetime DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, search_pattern, search_pattern, search_pattern, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} matching departure board events for user ID: {user_id}")
    return results or []


def get_follows(user_id: int, follow_type: str) -> List[Dict[str, Any]]:
    """Retrieve the list of journeys, users and locations a user is following.
    
    Args:
        user_id: The ID of the user whose follows should be retrieved.
        follow_type: The type of follow to retrieve - 'user', 'journey', or 'location'.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries containing followed entity data.
    """
     
    if follow_type == 'user':
        query = """
        SELECT f.followed_id, u.username, u.profile_image
        FROM user_follows_user f
        JOIN users u ON f.followed_id = u.id
        WHERE f.follower_id = %s
        """
    elif follow_type == 'journey':
        query = """
        SELECT f.journey_id, j.title AS journey_title
        FROM user_follows_journey f
        JOIN journeys j ON f.journey_id = j.id
        WHERE f.user_id = %s
        """
    elif follow_type == 'location':
        query = """
        SELECT f.location_id, l.name AS location_name
        FROM user_follows_location f
        JOIN locations l ON f.location_id = l.id
        WHERE f.user_id = %s
        """
    else:
        return []

    return execute_query(query, (user_id,), fetch_all=True) or []
