from typing import Dict, List, Optional, Any, Tuple
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def record_edit(editor_id: int, content_type: str, content_id: int,
               field_changes: Dict[str, Tuple[str, str]], reason: str) -> int:
    """Record an edit to a piece of content with multiple field changes.

    Args:
        editor_id: ID of the editor making the change.
        content_type: Type of content being edited ('journey', 'event', 'location').
        content_id: ID of the content being edited.
        field_changes: Dictionary of field names mapped to (old_value, new_value) tuples.
        reason: The reason for the edit.

    Returns:
        int: ID of the newly created edit history record.
    """
    logger.info(f"Recording edit by editor ID: {editor_id} to {content_type} ID: {content_id}")

    # Create main edit record
    query = """
    INSERT INTO edit_history
    (editor_id, content_type, content_id, reason)
    VALUES (%s, %s, %s, %s)
    """
    edit_id = execute_query(query, (editor_id, content_type, content_id, reason))

    # Record individual field changes
    for field_name, (old_value, new_value) in field_changes.items():
        field_query = """
        INSERT INTO edit_field_changes
        (edit_id, field_name, old_value, new_value)
        VALUES (%s, %s, %s, %s)
        """
        execute_query(field_query, (edit_id, field_name, old_value, new_value))

    logger.info(f"Recorded edit with ID: {edit_id} containing {len(field_changes)} field changes")

    return edit_id

def get_content_edit_history(content_type: str, content_id: int) -> List[Dict[str, Any]]:
    """Get edit history for a specific piece of content.

    Args:
        content_type: Type of content to get history for.
        content_id: ID of the content to get history for.

    Returns:
        List[Dict[str, Any]]: List of edit history records with field changes.
    """
    logger.debug(f"Getting edit history for {content_type} ID: {content_id}")

    query = """
    SELECT eh.*, u.username as editor_username
    FROM edit_history eh
    JOIN users u ON eh.editor_id = u.id
    WHERE eh.content_type = %s AND eh.content_id = %s
    ORDER BY eh.created_at DESC
    """
    results = execute_query(query, (content_type, content_id), fetch_all=True)

    if not results:
        return []

    # For each edit, fetch field changes
    for edit in results:
        field_query = """
        SELECT field_name, old_value, new_value
        FROM edit_field_changes
        WHERE edit_id = %s
        """
        field_changes = execute_query(field_query, (edit['id'],), fetch_all=True)
        edit['field_changes'] = field_changes

    logger.debug(f"Found {len(results)} edit history records")
    return results

def get_user_content_edit_history(user_id: int) -> List[Dict[str, Any]]:
    """Get edit history for all content owned by a user.

    Args:
        user_id: ID of the user to get edit history for.

    Returns:
        List[Dict[str, Any]]: List of edit history records.
    """
    logger.debug(f"Getting edit history for all content owned by user ID: {user_id}")

    query = """
    SELECT eh.*, u.username as editor_username,
           CASE
               WHEN eh.content_type = 'journey' THEN j.title
               WHEN eh.content_type = 'event' THEN e.title
               WHEN eh.content_type = 'location' THEN l.name
               WHEN eh.content_type = 'image' THEN 'Image'
           END as content_title
    FROM edit_history eh
    JOIN users u ON eh.editor_id = u.id
    LEFT JOIN journeys j ON eh.content_type = 'journey' AND eh.content_id = j.id
    LEFT JOIN events e ON eh.content_type = 'event' AND eh.content_id = e.id
    LEFT JOIN locations l ON eh.content_type = 'location' AND eh.content_id = l.id
    WHERE (eh.content_type = 'journey' AND j.user_id = %s)
       OR (eh.content_type = 'event' AND e.journey_id IN (SELECT id FROM journeys WHERE user_id = %s))
       OR (eh.content_type = 'image' AND eh.content_id IN (
           SELECT ei.id FROM event_images ei
           JOIN events e ON ei.event_id = e.id
           JOIN journeys j ON e.journey_id = j.id
           WHERE j.user_id = %s
       ))
    ORDER BY eh.created_at DESC
    """
    results = execute_query(query, (user_id, user_id, user_id), fetch_all=True)

    if results:
        # For each edit, fetch field changes
        for edit in results:
            field_query = """
            SELECT field_name, old_value, new_value
            FROM edit_field_changes
            WHERE edit_id = %s
            """
            field_changes = execute_query(field_query, (edit['id'],), fetch_all=True)
            edit['field_changes'] = field_changes

    logger.debug(f"Found {len(results) if results else 0} edit history records")
    return results or []

def get_editor_edit_history(editor_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get edit history for edits made by a specific editor.

    Args:
        editor_id: ID of the editor to get history for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of edit history records.
    """
    logger.debug(f"Getting edit history for editor ID: {editor_id}")

    query = """
    SELECT eh.*,
           CASE
               WHEN eh.content_type = 'journey' THEN j.title
               WHEN eh.content_type = 'event' THEN e.title
               WHEN eh.content_type = 'location' THEN l.name
               WHEN eh.content_type = 'photo' THEN 'Photo'
           END as content_title
    FROM edit_history eh
    LEFT JOIN journeys j ON eh.content_type = 'journey' AND eh.content_id = j.id
    LEFT JOIN events e ON eh.content_type = 'event' AND eh.content_id = e.id
    LEFT JOIN locations l ON eh.content_type = 'location' AND eh.content_id = l.id
    WHERE eh.editor_id = %s
    ORDER BY eh.created_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (editor_id, limit, offset), fetch_all=True)

    if results:
        # For each edit, fetch field changes
        for edit in results:
            field_query = """
            SELECT field_name, old_value, new_value
            FROM edit_field_changes
            WHERE edit_id = %s
            """
            field_changes = execute_query(field_query, (edit['id'],), fetch_all=True)
            edit['field_changes'] = field_changes

    logger.debug(f"Found {len(results) if results else 0} edit history records")
    return results or []
