"""
Helpdesk Data Module

This module handles database operations for the helpdesk system including:
- Support request management
- Request replies
- Request status updates
"""

from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def create_request(
    user_id: int,
    subject: str,
    description: str,
    request_type: str = 'help',
    appeal_type: str = None,
    related_id: int = None
) -> int:
    """Create a new helpdesk request.

    Args:
        user_id: The ID of the user creating the request
        subject: The subject of the request
        description: The detailed description of the request
        request_type: The type of request ('help', 'bug', 'appeal', 'other')
        appeal_type: The type of appeal if request_type is 'appeal'
        related_id: The ID of the related item for appeals

    Returns:
        int: The ID of the newly created request
    """
    logger.info(f"Creating new helpdesk request from user ID: {user_id}, type: {request_type}")

    if request_type not in ['help', 'bug', 'appeal', 'other']:
        logger.warning(f"Invalid request type: {request_type}, defaulting to 'help'")
        request_type = 'help'

    # Build query based on whether this is an appeal
    if request_type == 'appeal' and appeal_type and related_id:
        query = """
        INSERT INTO helpdesk_requests
        (user_id, subject, description, request_type, appeal_type, related_id, status)
        VALUES (%s, %s, %s, %s, %s, %s, 'new')
        """
        params = (user_id, subject, description, request_type, appeal_type, related_id)
    else:
        query = """
        INSERT INTO helpdesk_requests
        (user_id, subject, description, request_type, status)
        VALUES (%s, %s, %s, %s, 'new')
        """
        params = (user_id, subject, description, request_type)

    request_id = execute_query(query, params)

    logger.info(f"Created new helpdesk request with ID: {request_id}")
    return request_id

def get_request_by_id(request_id: int) -> Optional[Dict[str, Any]]:
    """Get a helpdesk request by ID.
    
    Args:
        request_id: The ID of the request to retrieve
        
    Returns:
        Dict[str, Any]: Request data if found, None otherwise
    """
    logger.debug(f"Getting helpdesk request by ID: {request_id}")
    
    query = """
    SELECT r.*, u.username, u.first_name, u.last_name, u.profile_image, u.email, u.role,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role,
           CASE WHEN EXISTS (
               SELECT 1 FROM subscriptions s 
               WHERE s.user_id = r.user_id 
               AND s.is_active = TRUE AND s.end_date >= CURDATE()
           ) THEN TRUE ELSE FALSE END as is_premium
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    WHERE r.id = %s
    """
    
    result = execute_query(query, (request_id,), fetch_one=True)
    logger.debug(f"Request lookup result: {'Found' if result else 'Not found'}")
    return result

def get_staff_by_request_id(request_id: int) -> Optional[Dict[str, Any]]:
    query = """
    SELECT u.* 
    FROM helpdesk_requests r
    JOIN users u ON r.assigned_to = u.id
    WHERE r.id = %s
    """
    
    result = execute_query(query, (request_id,), fetch_one=True)
    logger.debug(f"Request lookup result: {'Found' if result else 'Not found'}")
    return result

def get_user_requests(
    user_id: int,
    limit: int = 50,
    offset: int = 0,
    type_filter: Optional[List[str]] = None,
) -> List[Dict[str, Any]]:
    """
    Get all requests created by a specific user with pagination and optional type filter.

    Args:
        user_id: The ID of the user whose requests to retrieve
        limit: Maximum number of results to return
        offset: Number of results to skip
        type_filter: Optional list of request types to filter by

    Returns:
        List[Dict[str, Any]]: List of request records
    """
    logger.debug(
        f"Getting requests for user ID: {user_id}, limit: {limit}, offset: {offset}, type_filter: {type_filter}"
    )

    query_parts = [
        """
        SELECT r.*, u.username, u.email, u.role,
               (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
               assigned_user.username as assigned_username,
               assigned_user.role as assigned_user_role
        FROM helpdesk_requests r
        JOIN users u ON r.user_id = u.id
        LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
        WHERE r.user_id = %s
        """
    ]
    params = [user_id]

    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ", ".join(["%s"] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)

    query_parts.append(
        """
        ORDER BY 
            CASE 
                WHEN r.status IN ('new', 'open') THEN 0
                WHEN r.status = 'stalled' THEN 1
                WHEN r.status = 'resolved' THEN 2
            END,
            r.updated_at DESC
        LIMIT %s OFFSET %s
        """
    )
    params.extend([limit, offset])

    query = " ".join(query_parts)
    results = execute_query(query, tuple(params), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} requests for user")
    return results or []


def search_user_requests(
    user_id: int,
    search_term: str,
    limit: int = 50,
    offset: int = 0,
    type_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get all requests created by a specific user with pagination and optional filters.
    
    Args:
        user_id: The ID of the user whose requests to retrieve
        search_term: Search term to filter by subject/description
        limit: Maximum number of results to return
        offset: Number of results to skip
        type_filter: Optional list of request types to filter by
        
    Returns:
        List[Dict[str, Any]]: List of request records
    """
    logger.debug(f"Getting requests for user ID: {user_id} with term: '{search_term}', limit: {limit}, offset: {offset}, type_filter: {type_filter}")
    
    query_parts = ["""
    SELECT r.*, u.username, u.email, u.role,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    WHERE r.user_id = %s
    """]
    
    params = [user_id]
    
    # Add search term filter if provided
    if search_term:
        search_pattern = f"%{search_term}%"
        query_parts.append("AND (r.subject LIKE %s OR r.description LIKE %s)")
        params.extend([search_pattern, search_pattern])
    
    # Add type filter if provided
    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)
    
    # Add ordering
    query_parts.append("""
    ORDER BY
        CASE 
            WHEN r.status IN ('new', 'open') THEN 0
            WHEN r.status = 'stalled' THEN 1
            WHEN r.status = 'resolved' THEN 2
        END,
        r.updated_at DESC
    LIMIT %s OFFSET %s
    """)
    
    params.extend([limit, offset])
    
    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} requests for user")
    return results or []

def get_user_requests_count(
    user_id: int,
    type_filter: Optional[List[str]] = None
) -> int:
    """
    Get count of requests for a specific user with optional type filter.

    Args:
        user_id: The ID of the user whose requests to count
        type_filter: Optional list of request types to filter by

    Returns:
        int: Number of requests
    """
    logger.debug(f"Counting requests for user ID: {user_id}, type_filter: {type_filter}")

    query_parts = [
        """
        SELECT COUNT(*) as count
        FROM helpdesk_requests
        WHERE user_id = %s
        """
    ]
    params = [user_id]

    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND request_type = %s")
            params.append(type_filter)

    query = ' '.join(query_parts)
    result = execute_query(query, tuple(params), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} requests for user")
    return count


def search_user_requests_count(
    user_id: int,
    search_term: str,
    type_filter: Optional[List[str]] = None
) -> int:
    """
    Get count of requests for a specific user with optional search and type filter.

    Args:
        user_id: The ID of the user whose requests to search
        search_term: Term to search in subject and description
        type_filter: Optional list of request types to filter by

    Returns:
        int: Number of matching requests
    """
    logger.debug(f"Counting user requests for user ID: {user_id}, search_term: '{search_term}', type_filter: {type_filter}")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    WHERE r.user_id = %s
    """]
    params = [user_id]

    if search_term:
        if search_term.startswith('s'):
            search_term = search_term[1:]
        search_pattern = f"%{search_term}%"
        query_parts.append("AND (r.subject LIKE %s OR r.description LIKE %s)")
        params.extend([search_pattern, search_pattern])

    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)

    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} matching requests for user")
    return count


def get_open_requests(
    limit: int = 50, 
    offset: int = 0, 
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get all open helpdesk requests with pagination and optional filters.
    
    Args:
        limit: Maximum number of results to return
        offset: Number of results to skip
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by
        
    Returns:
        List[Dict[str, Any]]: List of open request records
    """
    logger.debug(f"Getting open requests with filters: status={status_filter}, type={type_filter}")
    
    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE 1=1
    """]
    
    params = []
    
    # Add status filter if provided
    if status_filter:
        if isinstance(status_filter, list):
            placeholders = ', '.join(['%s'] * len(status_filter))
            query_parts.append(f"AND r.status IN ({placeholders})")
            params.extend(status_filter)
        else:
            query_parts.append("AND r.status = %s")
            params.append(status_filter)

    # Add type filter if provided
    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)
    
    # Add ordering - Priority (tag) > Staff (tag) > Created datetime
    # Premium subscribers have highest priority, then staff users, then regular users
    query_parts.append("""
    ORDER BY
        CASE WHEN s.id IS NOT NULL THEN 0
             WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN 1
             ELSE 2
        END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)
    
    params.extend([limit, offset])

    
    
    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} open requests")
    return results or []



def get_open_requests_count(
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> int:
    """Get count of open helpdesk requests with optional filters.
    
    Args:
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by
        
    Returns:
        int: Number of open requests
    """
    logger.debug(f"Counting open requests with filters: status={status_filter}, type={type_filter}")
    
    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    WHERE 1=1
    """]
    
    params = []
    
    # Add status filter if provided
    if status_filter:
        if isinstance(status_filter, list):
            placeholders = ', '.join(['%s'] * len(status_filter))
            query_parts.append(f"AND r.status IN ({placeholders})")
            params.extend(status_filter)
        else:
            query_parts.append("AND r.status = %s")
            params.append(status_filter)

    # Add type filter if provided
    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)
    
    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} open requests")
    return count

def search_open_requests(
    search_term: str,
    limit: int = 50, 
    offset: int = 0, 
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get all open helpdesk requests with pagination and optional filters.
    
    Args:
        limit: Maximum number of results to return
        offset: Number of results to skip
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by
        
    Returns:
        List[Dict[str, Any]]: List of open request records
    """
    logger.debug(f"Getting open requests with term: '{search_term}' with filters: status={status_filter}, type={type_filter}")
    search_pattern = f"%{search_term}%"
    
    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE 1=1
    """]
    
    params = []

    if search_term:
        if search_term and search_term.startswith('s'):
            search_term=search_term[1:]
        search_pattern = f"%{search_term}%"
        query_parts.append("AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)")
        params.extend([search_pattern, search_pattern, search_pattern])

    # Add status filter if provided
    if status_filter:
        if isinstance(status_filter, list):
            placeholders = ', '.join(['%s'] * len(status_filter))
            query_parts.append(f"AND r.status IN ({placeholders})")
            params.extend(status_filter)
        else:
            query_parts.append("AND r.status = %s")
            params.append(status_filter)

    # Add type filter if provided
    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)
    
    # Add ordering - Staff (tag) > Priority (tag) > Created datetime
    # Staff users have highest priority, then premium subscribers, then regular users
    query_parts.append("""
    ORDER BY
        CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN 0
             WHEN s.id IS NOT NULL THEN 1
             ELSE 2
        END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)
    
    params.extend([limit, offset])

    
    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} open requests")
    return results or []

def search_open_requests_count(
    search_term: str,
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> int:
    """Get count of open helpdesk requests with optional filters.
    
    Args:
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by
        
    Returns:
        int: Number of open requests
    """
    logger.debug(f"Counting open requests with term: '{search_term}' with filters: status={status_filter}, type={type_filter}")
    search_pattern = f"%{search_term}%"
    
    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    WHERE 1=1
    """]
    
    params = []

    if search_term:
        if search_term and search_term.startswith('s'):
            search_term=search_term[1:]
        search_pattern = f"%{search_term}%"
        query_parts.append("AND (r.subject LIKE %s OR r.description LIKE %s)")
        params.extend([search_pattern, search_pattern])
    
    # Add status filter if provided
    if status_filter:
        if isinstance(status_filter, list):
            placeholders = ', '.join(['%s'] * len(status_filter))
            query_parts.append(f"AND r.status IN ({placeholders})")
            params.extend(status_filter)
        else:
            query_parts.append("AND r.status = %s")
            params.append(status_filter)

    # Add type filter if provided
    if type_filter:
        if isinstance(type_filter, list):
            placeholders = ', '.join(['%s'] * len(type_filter))
            query_parts.append(f"AND r.request_type IN ({placeholders})")
            params.extend(type_filter)
        else:
            query_parts.append("AND r.request_type = %s")
            params.append(type_filter)
    
    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} open requests")
    return count

def get_assigned_requests(
    staff_id: int,
    limit: int = 50, 
    offset: int = 0,
    status_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get all requests assigned to a specific staff member with pagination.
    
    Args:
        staff_id: The ID of the staff member whose assigned requests to retrieve
        limit: Maximum number of results to return
        offset: Number of results to skip
        status_filter: Optional list of statuses to filter by
        
    Returns:
        List[Dict[str, Any]]: List of assigned request records
    """
    logger.debug(f"Getting requests assigned to staff ID: {staff_id}")
    
    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE r.assigned_to = %s
    """]
    
    params = [staff_id]
    
    # Add status filter if provided
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)
    
    # Add ordering by status and date
    query_parts.append("""
    ORDER BY 
        CASE
            WHEN r.status = 'open' THEN 0
            WHEN r.status = 'stalled' THEN 1
            WHEN r.status = 'resolved' THEN 2
        END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)
    
    params.extend([limit, offset])
    
    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} assigned requests")
    return results or []

def get_assigned_requests_count(
    staff_id: int,
    status_filter: Optional[List[str]] = None
) -> int:
    """Get count of requests assigned to a specific staff member.
    
    Args:
        staff_id: The ID of the staff member whose assigned requests to count
        status_filter: Optional list of statuses to filter by
        
    Returns:
        int: Number of assigned requests
    """
    logger.debug(f"Counting requests assigned to staff ID: {staff_id}")
    
    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests
    WHERE assigned_to = %s
    """]
    
    params = [staff_id]
    
    # Add status filter if provided
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND status IN ({placeholders})")
        params.extend(status_filter)
    
    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} assigned requests")
    return count

def assign_request(request_id: int, staff_id: int) -> int:
    """Assign a request to a staff member.
    
    Args:
        request_id: The ID of the request to assign
        staff_id: The ID of the staff member to assign to
        
    Returns:
        int: Number of rows affected
    """
    logger.info(f"Assigning request ID: {request_id} to staff ID: {staff_id}")
    
    query = """
    UPDATE helpdesk_requests
    SET assigned_to = %s,status='open', updated_at = NOW()
    WHERE id = %s
    """
    
    rows_affected = execute_query(query, (staff_id, request_id))
    logger.info(f"Assigned request, rows affected: {rows_affected}")
    return rows_affected

def unassign_request(request_id: int) -> int:
    """Unassign a request (put it back in the queue).

    Args:
        request_id: The ID of the request to unassign

    Returns:
        int: Number of rows affected
    """
    logger.info(f"Unassigning request ID: {request_id}")

    query = """
    UPDATE helpdesk_requests
    SET assigned_to = NULL, updated_at = NOW()
    WHERE id = %s
    """

    rows_affected = execute_query(query, (request_id,))
    logger.info(f"Unassigned request, rows affected: {rows_affected}")
    return rows_affected

def search_assigned_requests(
    staff_id: int,
    search_term: str,
    limit: int = 50,
    offset: int = 0,
    status_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Search requests assigned to a specific staff member.

    Args:
        staff_id: The ID of the staff member whose assigned requests to search
        search_term: The search term to filter by
        limit: Maximum number of results to return
        offset: Number of results to skip
        status_filter: Optional list of statuses to filter by

    Returns:
        List[Dict[str, Any]]: List of assigned request records
    """
    logger.debug(f"Searching requests assigned to staff ID: {staff_id} with term: '{search_term}'")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE r.assigned_to = %s
    AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)
    """]

    params = [staff_id, search_pattern, search_pattern, search_pattern]

    # Add status filter if provided
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)

    # Add ordering by status and date
    query_parts.append("""
    ORDER BY
        CASE
            WHEN r.status = 'open' THEN 0
            WHEN r.status = 'stalled' THEN 1
            WHEN r.status = 'resolved' THEN 2
        END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)

    params.extend([limit, offset])

    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} assigned requests")
    return results or []

def search_assigned_requests_count(
    staff_id: int,
    search_term: str,
    status_filter: Optional[List[str]] = None
) -> int:
    """Get count of search results for requests assigned to a specific staff member.

    Args:
        staff_id: The ID of the staff member whose assigned requests to count
        search_term: The search term to filter by
        status_filter: Optional list of statuses to filter by

    Returns:
        int: Number of assigned requests matching search
    """
    logger.debug(f"Counting search results for requests assigned to staff ID: {staff_id}")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    WHERE r.assigned_to = %s
    AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)
    """]

    params = [staff_id, search_pattern, search_pattern, search_pattern]

    # Add status filter if provided
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)

    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} assigned requests matching search")
    return count

def get_unassigned_requests(
    limit: int = 50,
    offset: int = 0,
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get all unassigned helpdesk requests with pagination and optional filters.

    Args:
        limit: Maximum number of results to return
        offset: Number of results to skip
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by

    Returns:
        List[Dict[str, Any]]: List of unassigned request records
    """
    logger.debug(f"Getting unassigned requests with filters: status={status_filter}, type={type_filter}")

    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE r.assigned_to IS NULL
    """]

    params = []

    # Add filters
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)

    if type_filter:
        placeholders = ', '.join(['%s'] * len(type_filter))
        query_parts.append(f"AND r.request_type IN ({placeholders})")
        params.extend(type_filter)

    # Add ordering - Staff (tag) > Priority (tag) > Created datetime
    # Staff users have highest priority, then premium subscribers, then regular users
    query_parts.append("""
    ORDER BY
        CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN 0
             WHEN s.id IS NOT NULL THEN 1
             ELSE 2
        END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)

    params.extend([limit, offset])

    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} unassigned requests")
    return results or []

def get_unassigned_requests_count(
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> int:
    """Get count of unassigned helpdesk requests with optional filters.

    Args:
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by

    Returns:
        int: Number of unassigned requests
    """
    logger.debug(f"Counting unassigned requests with filters: status={status_filter}, type={type_filter}")

    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    WHERE r.assigned_to IS NULL
    """]

    params = []

    # Add filters
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)

    if type_filter:
        placeholders = ', '.join(['%s'] * len(type_filter))
        query_parts.append(f"AND r.request_type IN ({placeholders})")
        params.extend(type_filter)

    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} unassigned requests")
    return count

def search_unassigned_requests(
    search_term: str,
    limit: int = 50,
    offset: int = 0,
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Search unassigned helpdesk requests with pagination and optional filters.

    Args:
        search_term: The search term to filter by
        limit: Maximum number of results to return
        offset: Number of results to skip
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by

    Returns:
        List[Dict[str, Any]]: List of unassigned request records
    """
    logger.debug(f"Searching unassigned requests with term: '{search_term}' with filters: status={status_filter}, type={type_filter}")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE r.assigned_to IS NULL
    AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)
    """]

    params = [search_pattern, search_pattern, search_pattern]

    # Add filters
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)

    if type_filter:
        placeholders = ', '.join(['%s'] * len(type_filter))
        query_parts.append(f"AND r.request_type IN ({placeholders})")
        params.extend(type_filter)

    # Add ordering - Staff (tag) > Priority (tag) > Created datetime
    # Staff users have highest priority, then premium subscribers, then regular users
    query_parts.append("""
    ORDER BY
        CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN 0
             WHEN s.id IS NOT NULL THEN 1
             ELSE 2
        END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)

    params.extend([limit, offset])

    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} unassigned requests")
    return results or []

def search_unassigned_requests_count(
    search_term: str,
    status_filter: Optional[List[str]] = None,
    type_filter: Optional[List[str]] = None
) -> int:
    """Get count of search results for unassigned helpdesk requests.

    Args:
        search_term: The search term to filter by
        status_filter: Optional list of statuses to filter by
        type_filter: Optional list of request types to filter by

    Returns:
        int: Number of unassigned requests matching search
    """
    logger.debug(f"Counting unassigned requests with term: '{search_term}' with filters: status={status_filter}, type={type_filter}")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    WHERE r.assigned_to IS NULL
    AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)
    """]

    params = [search_pattern, search_pattern, search_pattern]

    # Add filters
    if status_filter:
        placeholders = ', '.join(['%s'] * len(status_filter))
        query_parts.append(f"AND r.status IN ({placeholders})")
        params.extend(status_filter)

    if type_filter:
        placeholders = ', '.join(['%s'] * len(type_filter))
        query_parts.append(f"AND r.request_type IN ({placeholders})")
        params.extend(type_filter)

    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} unassigned requests matching search")
    return count

def update_request_status(request_id: int, status: str) -> int:
    """Update a request's status.
    
    Args:
        request_id: The ID of the request to update
        status: The new status ('new', 'open', 'stalled', 'resolved')
        
    Returns:
        int: Number of rows affected
    """
    logger.info(f"Updating status of request ID: {request_id} to {status}")
    
    if status not in ['new', 'open', 'stalled', 'resolved','approved','rejected']:
        logger.warning(f"Invalid status: {status}, not updating")
        return 0
    
    # Handle resolution date if resolving
    if status == 'resolved':
        query = """
        UPDATE helpdesk_requests
        SET status = %s, updated_at = NOW(), resolved_at = NOW()
        WHERE id = %s
        """
    else:
        query = """
        UPDATE helpdesk_requests
        SET status = %s, updated_at = NOW()
        WHERE id = %s
        """
    
    rows_affected = execute_query(query, (status, request_id))
    logger.info(f"Updated request status, rows affected: {rows_affected}")
    return rows_affected

def add_reply(
    request_id: int, 
    user_id: int, 
    content: str, 
    is_internal: bool = False
) -> int:
    """Add a reply to a helpdesk request.
    
    Args:
        request_id: The ID of the request to reply to
        user_id: The ID of the user creating the reply
        content: The content of the reply
        is_internal: Whether this is an internal staff-only note
        
    Returns:
        int: The ID of the newly created reply
    """
    logger.info(f"Adding reply to request ID: {request_id} by user ID: {user_id}")
    
    query = """
    INSERT INTO helpdesk_replies
    (request_id, user_id, content, is_internal)
    VALUES (%s, %s, %s, %s)
    """
    
    reply_id = execute_query(query, (request_id, user_id, content, is_internal))
    
    # Update the request's updated_at timestamp
    update_query = """
    UPDATE helpdesk_requests
    SET updated_at = NOW()
    WHERE id = %s
    """
    
    execute_query(update_query, (request_id,))
    
    logger.info(f"Added reply with ID: {reply_id}")
    return reply_id

def get_request_replies(request_id: int, include_internal: bool = False) -> List[Dict[str, Any]]:
    """Get all replies for a specific request.
    
    Args:
        request_id: The ID of the request to get replies for
        include_internal: Whether to include internal staff-only notes
        
    Returns:
        List[Dict[str, Any]]: List of reply records
    """
    logger.debug(f"Getting replies for request ID: {request_id}")
    
    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.profile_image, u.role
    FROM helpdesk_replies r
    JOIN users u ON r.user_id = u.id
    WHERE r.request_id = %s
    """]
    
    params = [request_id]
    
    # Filter out internal notes if not requested
    if not include_internal:
        query_parts.append("AND r.is_internal = FALSE")
    
    query_parts.append("ORDER BY r.created_at")
    
    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    
    logger.debug(f"Found {len(results) if results else 0} replies")
    return results or []

def get_request_reply_count(request_id: int, include_internal: bool = False) -> int:
    """Get the number of replies for a specific request.
    
    Args:
        request_id: The ID of the request to count replies for
        include_internal: Whether to include internal staff-only notes
        
    Returns:
        int: Number of replies
    """
    logger.debug(f"Counting replies for request ID: {request_id}")
    
    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_replies
    WHERE request_id = %s
    """]
    
    params = [request_id]
    
    # Filter out internal notes if not requested
    if not include_internal:
        query_parts.append("AND is_internal = FALSE")
    
    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    
    count = result['count'] if result else 0
    logger.debug(f"Found {count} replies")
    return count

# ===== Appeal Management =====
"""
Appeal Data Module

This module handles database operations for appeals using the helpdesk system.
"""
def create_appeal_request(user_id: int, appeal_type: str, related_id: int, 
                         subject: str, description: str) -> int:
    """Create an appeal as a helpdesk request.
    
    Args:
        user_id: ID of the user making the appeal.
        appeal_type: Type of appeal ('hidden_journey', 'sharing_block', 'ban', 'hidden_comment').
        related_id: ID related to the appeal.
        subject: Subject of the appeal.
        description: Description of the appeal.
        
    Returns:
        int: ID of the newly created appeal.
    """
    logger.info(f"Creating {appeal_type} appeal for user ID: {user_id}")
    
    # Create appeal as a helpdesk request
    appeal_id = create_request(
        user_id=user_id,
        subject=subject,
        description=description,
        request_type='appeal',
        appeal_type=appeal_type,
        related_id=related_id
    )
    
    logger.info(f"Created appeal with ID: {appeal_id}")
    return appeal_id

def get_appeal_request(appeal_id: int) -> Optional[Dict[str, Any]]:
    """Get an appeal by ID.
    
    Args:
        appeal_id: ID of the appeal to retrieve.
        
    Returns:
        Dict[str, Any]: Appeal data if found, None otherwise.
    """
    logger.debug(f"Getting appeal with ID: {appeal_id}")
    appeal = get_request_by_id(appeal_id)
    
    # Verify this is an appeal request
    if appeal and appeal.get('request_type') != 'appeal':
        logger.warning(f"Requested ID {appeal_id} is not an appeal")
        return None
        
    return appeal

def get_user_appeals(user_id: int, limit: int = 20, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all appeals made by a user.
    
    Args:
        user_id: ID of the user to get appeals for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of appeal records.
    """
    logger.debug(f"Getting appeals for user ID: {user_id}")
    
    query = """
    SELECT r.*,
           CASE 
               WHEN r.appeal_type = 'hidden_journey' THEN 
                   (SELECT title FROM journeys WHERE id = r.related_id)
               WHEN r.appeal_type = 'hidden_comment' THEN 
                   'Comment'
               ELSE NULL
           END as related_item_name,
           CASE WHEN r.assigned_to IS NOT NULL THEN u.username ELSE NULL END as handler_name
    FROM helpdesk_requests r
    LEFT JOIN users u ON r.assigned_to = u.id
    WHERE r.user_id = %s AND r.request_type = 'appeal'
    ORDER BY r.created_at DESC
    LIMIT %s OFFSET %s
    """
    
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} appeals")
    return results or []

def get_pending_appeals(limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all pending appeals with pagination.
    
    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of pending appeal records.
    """
    logger.debug(f"Getting pending appeals with limit: {limit}, offset: {offset}")
    
    query = """
    SELECT r.*, 
           u.username as user_username,
           CASE 
               WHEN r.appeal_type = 'hidden_journey' THEN 
                   (SELECT title FROM journeys WHERE id = r.related_id)
               WHEN r.appeal_type = 'hidden_comment' THEN 
                   (SELECT content FROM event_comments WHERE id = r.related_id LIMIT 60)
               ELSE NULL
           END as related_item_name,
           CASE WHEN r.assigned_to IS NOT NULL THEN staff.username ELSE NULL END as handler_name
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users staff ON r.assigned_to = staff.id
    WHERE r.request_type = 'appeal' 
    AND r.status IN ('new', 'open')
    ORDER BY r.created_at ASC
    LIMIT %s OFFSET %s
    """
    
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} pending appeals")
    return results or []

def get_appeals_by_type(appeal_type: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get appeals filtered by type.
    
    Args:
        appeal_type: Type of appeal to filter by.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of appeal records.
    """
    logger.debug(f"Getting appeals of type '{appeal_type}'")
    
    query = """
    SELECT r.*, 
           u.username as user_username,
           CASE 
               WHEN r.appeal_type = 'hidden_journey' THEN 
                   (SELECT title FROM journeys WHERE id = r.related_id)
               WHEN r.appeal_type = 'hidden_comment' THEN 
                   (SELECT content FROM event_comments WHERE id = r.related_id LIMIT 60)
               ELSE NULL
           END as related_item_name
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    WHERE r.request_type = 'appeal' AND r.appeal_type = %s
    ORDER BY r.created_at DESC
    LIMIT %s OFFSET %s
    """
    
    results = execute_query(query, (appeal_type, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} appeals of type '{appeal_type}'")
    return results or []

def assign_appeal_request(appeal_id: int, staff_id: int) -> int:
    """Assign an appeal to a staff member.
    
    Args:
        appeal_id: ID of the appeal to assign.
        staff_id: ID of the staff member to assign to.
        
    Returns:
        int: Number of rows affected.
    """
    logger.info(f"Assigning appeal ID: {appeal_id} to staff ID: {staff_id}")
    
    query = """
    UPDATE helpdesk_requests
    SET assigned_to = %s, status = 'open', updated_at = NOW()
    WHERE id = %s AND request_type = 'appeal'
    """
    
    rows_affected = execute_query(query, (staff_id, appeal_id))
    logger.info(f"Appeal assignment result: {rows_affected} rows affected")
    return rows_affected

def update_appeal_request_status(appeal_id: int, staff_id: int, 
                              status: str, response: Optional[str] = None) -> int:
    """Update the status of an appeal.
    
    Args:
        appeal_id: ID of the appeal to update.
        staff_id: ID of the staff member handling the appeal.
        status: New status for the appeal ('approved', 'rejected').
        response: Optional response text from the staff member.
        
    Returns:
        int: Number of rows affected.
    """
    logger.info(f"Updating appeal ID: {appeal_id} to status: {status}")
    
    query = """
    UPDATE helpdesk_requests
    SET status = %s, 
        assigned_to = %s, 
        admin_response = %s,
        resolved_at = CURRENT_TIMESTAMP,
        updated_at = NOW()
    WHERE id = %s AND request_type = 'appeal'
    """
    
    rows_affected = execute_query(query, (status, staff_id, response, appeal_id))
    logger.info(f"Appeal status update result: {rows_affected} rows affected")
    return rows_affected

def unhide_journey(journey_id: int) -> int:
    """Unhide a journey.
    
    Args:
        journey_id: ID of the journey to unhide.
        
    Returns:
        int: Number of rows affected.
    """
    logger.info(f"Unhiding journey ID: {journey_id}")
    
    query = """
    UPDATE journeys
    SET is_hidden = FALSE
    WHERE id = %s
    """
    
    rows_affected = execute_query(query, (journey_id,))
    logger.info(f"Unhide journey result: {rows_affected} rows affected")
    return rows_affected

def unblock_user_sharing(user_id: int) -> int:
    """Unblock a user from sharing journeys.
    
    Args:
        user_id: ID of the user to unblock.
        
    Returns:
        int: Number of rows affected.
    """
    logger.info(f"Unblocking user ID: {user_id} from sharing")
    
    query = """
    UPDATE users
    SET is_blocked = FALSE
    WHERE id = %s
    """
    
    rows_affected = execute_query(query, (user_id,))
    logger.info(f"Unblock user sharing result: {rows_affected} rows affected")
    return rows_affected

def unban_user(user_id: int) -> int:
    """Unban a user.
    
    Args:
        user_id: ID of the user to unban.
        
    Returns:
        int: Number of rows affected.
    """
    logger.info(f"Unbanning user ID: {user_id}")
    
    query = """
    UPDATE users
    SET is_banned = FALSE
    WHERE id = %s
    """
    
    rows_affected = execute_query(query, (user_id,))
    logger.info(f"Unban user result: {rows_affected} rows affected")
    return rows_affected

def unhide_comment(comment_id: int) -> int:
    """Unhide a comment.
    
    Args:
        comment_id: ID of the comment to unhide.
        
    Returns:
        int: Number of rows affected.
    """
    logger.info(f"Unhiding comment ID: {comment_id}")
    
    query = """
    UPDATE event_comments
    SET is_hidden = FALSE
    WHERE id = %s
    """
    
    rows_affected = execute_query(query, (comment_id,))
    logger.info(f"Unhide comment result: {rows_affected} rows affected")
    return rows_affected

def get_journey_details(journey_id: int) -> Optional[Dict[str, Any]]:
    """Get details about a journey.
    
    Args:
        journey_id: ID of the journey.
        
    Returns:
        Dict[str, Any]: Journey details if found, None otherwise.
    """
    logger.debug(f"Getting journey details for ID: {journey_id}")
    
    query = """
    SELECT j.*, u.username 
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.id = %s
    """
    
    result = execute_query(query, (journey_id,), fetch_one=True)
    logger.debug(f"Journey details lookup result: {'Found' if result else 'Not found'}")
    return result

def get_comment_details(comment_id: int) -> Optional[Dict[str, Any]]:
    """Get details about a comment.
    
    Args:
        comment_id: ID of the comment.
        
    Returns:
        Dict[str, Any]: Comment details if found, None otherwise.
    """
    logger.debug(f"Getting comment details for ID: {comment_id}")
    
    query = """
    SELECT ec.*, u.username, e.title as event_title, j.title as journey_title
    FROM event_comments ec
    JOIN users u ON ec.user_id = u.id
    JOIN events e ON ec.event_id = e.id
    JOIN journeys j ON e.journey_id = j.id
    WHERE ec.id = %s
    """
    
    result = execute_query(query, (comment_id,), fetch_one=True)
    logger.debug(f"Comment details lookup result: {'Found' if result else 'Not found'}")
    return result

def get_appeal_statistics() -> Dict[str, Any]:
    """Get statistics about appeals.
    
    Returns:
        Dict[str, Any]: Appeal statistics.
    """
    logger.debug("Getting appeal statistics")
    
    # Get counts by status
    status_query = """
    SELECT status, COUNT(*) as count
    FROM helpdesk_requests
    WHERE request_type = 'appeal'
    GROUP BY status
    """
    status_results = execute_query(status_query, fetch_all=True)
    
    # Get counts by type
    type_query = """
    SELECT appeal_type, COUNT(*) as count
    FROM helpdesk_requests
    WHERE request_type = 'appeal'
    GROUP BY appeal_type
    """
    type_results = execute_query(type_query, fetch_all=True)
    
    # Get approval rate
    approval_query = """
    SELECT 
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        COUNT(*) as total_decided
    FROM helpdesk_requests
    WHERE request_type = 'appeal' AND status IN ('approved', 'rejected')
    """
    approval_result = execute_query(approval_query, fetch_one=True)
    
    # Build statistics object
    statistics = {
        'by_status': {r['status']: r['count'] for r in status_results} if status_results else {},
        'by_type': {r['appeal_type']: r['count'] for r in type_results} if type_results else {},
        'approval_rate': 0
    }
    
    # Calculate approval rate
    if approval_result and approval_result['total_decided'] > 0:
        statistics['approval_rate'] = round(
            approval_result['approved_count'] / approval_result['total_decided'] * 100, 2
        )
    
    logger.debug(f"Appeal statistics: {statistics}")
    return statistics

def has_pending_journey_appeal(user_id: int, journey_id: int) -> bool:
    """Check if a user has a pending appeal for a specific journey.

    Args:
        user_id: ID of the user to check.
        journey_id: ID of the journey to check.

    Returns:
        bool: True if there's a pending appeal, False otherwise.
    """
    logger.debug(f"Checking if user {user_id} has pending appeal for journey {journey_id}")

    query = """
    SELECT COUNT(*) as count
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'hidden_journey'
    AND related_id = %s
    AND status IN ('new', 'open')
    """

    result = execute_query(query, (user_id, journey_id), fetch_one=True)
    has_pending = result['count'] > 0 if result else False
    logger.debug(f"User {user_id} has pending appeal for journey {journey_id}: {has_pending}")
    return has_pending


def has_pending_blocked_user_appeal(user_id: int) -> bool:
    """Check if a user has a pending appeal for their blocked status.

    Args:
        user_id: ID of the user to check.

    Returns:
        bool: True if there's a pending appeal, False otherwise.
    """
    logger.debug(f"Checking if user {user_id} has pending blocked user appeal")

    query = """
    SELECT COUNT(*) as count
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'sharing_block'
    AND status IN ('new', 'open')
    """

    result = execute_query(query, (user_id,), fetch_one=True)
    has_pending = result['count'] > 0 if result else False
    logger.debug(f"User {user_id} has pending blocked user appeal: {has_pending}")
    return has_pending

def get_journey_appeal_status(user_id: int, journey_id: int) -> Optional[Dict[str, Any]]:
    """Get the status of a user's appeal for a specific journey.

    For currently hidden journeys, this returns:
    - The pending appeal if one exists (new/open status)
    - The most recent rejected appeal only if it's more recent than any approved appeal
    - None if no relevant appeal exists (allowing new appeal submission)

    Args:
        user_id: ID of the user.
        journey_id: ID of the journey.

    Returns:
        Dict with appeal status info, or None if no relevant appeal exists.
    """
    logger.debug(f"Getting appeal status for user {user_id} and journey {journey_id}")

    # First check if there's a pending appeal
    pending_query = """
    SELECT id, status, created_at, admin_response, resolved_at
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'hidden_journey'
    AND related_id = %s
    AND status IN ('new', 'open')
    ORDER BY created_at DESC
    LIMIT 1
    """

    pending_result = execute_query(pending_query, (user_id, journey_id), fetch_one=True)
    if pending_result:
        logger.debug(f"Found pending appeal: {pending_result['id']}")
        return pending_result

    # Check for the most recent rejected appeal, but only if it's more recent than any approved appeal
    relevant_rejected_query = """
    SELECT id, status, created_at, admin_response, resolved_at
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'hidden_journey'
    AND related_id = %s
    AND status = 'rejected'
    AND created_at > COALESCE(
        (SELECT MAX(created_at)
         FROM helpdesk_requests
         WHERE user_id = %s
         AND request_type = 'appeal'
         AND appeal_type = 'hidden_journey'
         AND related_id = %s
         AND status = 'approved'),
        '1900-01-01'
    )
    ORDER BY created_at DESC
    LIMIT 1
    """

    rejected_result = execute_query(relevant_rejected_query, (user_id, journey_id, user_id, journey_id), fetch_one=True)
    if rejected_result:
        logger.debug(f"Found relevant rejected appeal: {rejected_result['id']}")
        return rejected_result

    # No relevant appeal found - user can submit new appeal
    logger.debug(f"No relevant appeal found for user {user_id} and journey {journey_id}")
    return None


def get_blocked_user_appeal_status(user_id: int) -> Optional[Dict[str, Any]]:
    """Get the status of a user's appeal for their blocked status.

    Args:
        user_id: ID of the user.

    Returns:
        Dict with appeal status info, or None if no relevant appeal exists.
    """
    logger.debug(f"Getting blocked user appeal status for user {user_id}")

    # First check if there's a pending appeal
    pending_query = """
    SELECT id, status, created_at, admin_response, resolved_at
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'sharing_block'
    AND status IN ('new', 'open')
    ORDER BY created_at DESC
    LIMIT 1
    """

    pending_result = execute_query(pending_query, (user_id,), fetch_one=True)
    if pending_result:
        logger.debug(f"Found pending blocked user appeal: {pending_result['id']}")
        return pending_result

    # Check for the most recent rejected appeal, but only if it's more recent than any approved appeal
    relevant_rejected_query = """
    SELECT id, status, created_at, admin_response, resolved_at
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'sharing_block'
    AND status = 'rejected'
    AND created_at > COALESCE(
        (SELECT MAX(created_at)
         FROM helpdesk_requests
         WHERE user_id = %s
         AND request_type = 'appeal'
         AND appeal_type = 'sharing_block'
         AND status = 'approved'),
        '1900-01-01'
    )
    ORDER BY created_at DESC
    LIMIT 1
    """

    rejected_result = execute_query(relevant_rejected_query, (user_id, user_id), fetch_one=True)
    if rejected_result:
        logger.debug(f"Found relevant rejected blocked user appeal: {rejected_result['id']}")
        return rejected_result

    # No relevant appeal found - user can submit new appeal
    logger.debug(f"No relevant blocked user appeal found for user {user_id}")
    return None

def get_banned_user_appeal_status(user_id: int):
    """Get the appeal status for a banned user.

    Args:
        user_id: The ID of the user to check

    Returns:
        Dict with appeal status info, or None if no relevant appeal exists.
    """
    logger.debug(f"Getting banned user appeal status for user {user_id}")

    # First check if there's a pending appeal
    pending_query = """
    SELECT id, status, created_at, admin_response, resolved_at
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'ban'
    AND status IN ('new', 'open')
    ORDER BY created_at DESC
    LIMIT 1
    """

    pending_result = execute_query(pending_query, (user_id,), fetch_one=True)
    if pending_result:
        logger.debug(f"Found pending banned user appeal: {pending_result['id']}")
        return pending_result

    # If no pending appeal, check for the most recent resolved appeal
    resolved_query = """
    SELECT id, status, created_at, admin_response, resolved_at
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'ban'
    AND status IN ('approved', 'rejected')
    ORDER BY resolved_at DESC
    LIMIT 1
    """

    resolved_result = execute_query(resolved_query, (user_id,), fetch_one=True)
    if resolved_result:
        logger.debug(f"Found resolved banned user appeal: {resolved_result['id']} with status {resolved_result['status']}")
        return resolved_result

    logger.debug(f"No banned user appeal found for user {user_id}")
    return None

def has_pending_ban_appeal(user_id: int) -> bool:
    """Check if a user has a pending ban appeal.

    Args:
        user_id: The ID of the user to check

    Returns:
        bool: True if user has a pending ban appeal, False otherwise
    """
    logger.debug(f"Checking for pending ban appeal for user {user_id}")

    query = """
    SELECT id
    FROM helpdesk_requests
    WHERE user_id = %s
    AND request_type = 'appeal'
    AND appeal_type = 'ban'
    AND status IN ('new', 'open')
    LIMIT 1
    """

    result = execute_query(query, (user_id,), fetch_one=True)
    has_pending = result is not None

    logger.debug(f"User {user_id} has pending ban appeal: {has_pending}")
    return has_pending

def get_enum_values(table: str, column: str) -> list:
    query = f"SHOW COLUMNS FROM {table} LIKE %s"
    result = execute_query(query, (column,), fetch_one=True)
    if not result or 'Type' not in result:
        return []
    import re
    enum_str = result['Type']
    values = re.findall(r"'(.*?)'", enum_str)
    return values


def search_staff_requests(search_term, limit=10, offset=0, status_filter=None, type_filter=None):
    """Search tickets submitted by staff members"""
    logger.debug(f"Searching staff requests with term: '{search_term}'")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT r.*, u.username, u.first_name, u.last_name, u.email, u.role,
           CASE WHEN s.id IS NOT NULL THEN TRUE ELSE FALSE END as is_premium,
           CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN TRUE ELSE FALSE END as is_staff,
           (SELECT COUNT(*) FROM helpdesk_replies WHERE request_id = r.id) as reply_count,
           assigned_user.username as assigned_username,
           assigned_user.role as assigned_user_role
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    LEFT JOIN users assigned_user ON r.assigned_to = assigned_user.id
    LEFT JOIN subscriptions s ON r.user_id = s.user_id
        AND s.is_active = TRUE AND s.end_date >= CURDATE()
    WHERE u.role IN ('admin', 'editor', 'moderator', 'support_tech')
    AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)
    """]

    params = [search_pattern, search_pattern, search_pattern]

    # Add status filter
    if status_filter:
        if isinstance(status_filter, list):
            placeholders = ','.join(['%s'] * len(status_filter))
            query_parts.append(f"AND r.status IN ({placeholders})")
            params.extend(status_filter)
        else:
            query_parts.append("AND r.status = %s")
            params.append(status_filter)

    # Add type filter
    if type_filter:
        query_parts.append("AND r.request_type = %s")
        params.append(type_filter)

    # Add priority sorting and pagination
    query_parts.append("""
    ORDER BY
        CASE WHEN s.id IS NOT NULL THEN 1 ELSE 2 END,
        CASE WHEN u.role IN ('admin', 'editor', 'moderator', 'support_tech') THEN 1 ELSE 2 END,
        CASE WHEN r.status = 'new' THEN 1 ELSE 2 END,
        r.created_at DESC
    LIMIT %s OFFSET %s
    """)
    params.extend([limit, offset])

    query = ' '.join(query_parts)
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} staff requests matching search")
    return results or []


def search_staff_requests_count(search_term, status_filter=None, type_filter=None):
    """Get count of search results for staff tickets"""
    logger.debug(f"Counting staff requests matching search term: '{search_term}'")
    search_pattern = f"%{search_term}%"

    query_parts = ["""
    SELECT COUNT(*) as count
    FROM helpdesk_requests r
    JOIN users u ON r.user_id = u.id
    WHERE u.role IN ('admin', 'editor', 'moderator', 'support_tech')
    AND (r.subject LIKE %s OR r.description LIKE %s OR u.username LIKE %s)
    """]

    params = [search_pattern, search_pattern, search_pattern]

    # Add status filter
    if status_filter:
        if isinstance(status_filter, list):
            placeholders = ','.join(['%s'] * len(status_filter))
            query_parts.append(f"AND r.status IN ({placeholders})")
            params.extend(status_filter)
        else:
            query_parts.append("AND r.status = %s")
            params.append(status_filter)

    # Add type filter
    if type_filter:
        query_parts.append("AND r.request_type = %s")
        params.append(type_filter)

    query = ' '.join(query_parts)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} staff requests matching search")
    return count
