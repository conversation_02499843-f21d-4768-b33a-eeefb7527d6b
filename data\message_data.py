from typing import List, Dict, Any, Optional
from utils.db_utils import execute_query
from utils.logger import get_logger

logger = get_logger(__name__)

def get_conversations(user_id: int) -> List[Dict[str, Any]]:
    """
    Get a list of conversation partners and the last message time for a user.
    
    Args:
        user_id (int): The ID of the user.
    
    Returns:
        List[Dict[str, Any]]: List of conversations with other_user_id and last_message_time.
    """
    logger.debug(f"Getting conversations for user_id={user_id}")
    query = """
    SELECT
        CASE
            WHEN sender_id = %s THEN recipient_id
            ELSE sender_id
        END AS other_user_id,
        MAX(created_at) as last_message_time,
        (SELECT content FROM private_messages 
         WHERE (sender_id = %s AND recipient_id = other_user_id) 
            OR (sender_id = other_user_id AND recipient_id = %s)
         ORDER BY created_at DESC LIMIT 1) as last_message,
        (SELECT COUNT(*) FROM private_messages 
         WHERE recipient_id = %s AND sender_id = other_user_id AND is_read = 0) as unread_count
    FROM private_messages
    WHERE sender_id = %s OR recipient_id = %s
    GROUP BY other_user_id
    ORDER BY last_message_time DESC
    """
    return execute_query(query, (user_id, user_id, user_id, user_id, user_id, user_id), fetch_all=True) or []

def get_conversation_between(user_id: int, other_user_id: int) -> Optional[Dict[str, Any]]:
    """
    Check if a conversation exists between two users.
    
    Args:
        user_id (int): The ID of the current user.
        other_user_id (int): The ID of the other user.
    
    Returns:
        Optional[Dict[str, Any]]: Conversation info if exists, None otherwise.
    """
    logger.debug(f"Checking for conversation between user_id={user_id} and other_user_id={other_user_id}")
    query = """
    SELECT 
        COUNT(*) as message_count,
        MAX(created_at) as last_message_time
    FROM private_messages
    WHERE (sender_id = %s AND recipient_id = %s)
       OR (sender_id = %s AND recipient_id = %s)
    """
    result = execute_query(query, (user_id, other_user_id, other_user_id, user_id), fetch_one=True)
    
    if result and result['message_count'] > 0:
        return result
    return None


def get_messages_between(user_id: int, other_user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
    """
    Get messages exchanged between two users.
    
    Args:
        user_id (int): The ID of the current user.
        other_user_id (int): The ID of the other user.
        limit (int): Maximum number of messages to return.
    
    Returns:
        List[Dict[str, Any]]: List of message records.
    """
    logger.debug(f"Getting messages between user_id={user_id} and other_user_id={other_user_id}")
    query = """
    SELECT *
    FROM private_messages
    WHERE (sender_id = %s AND recipient_id = %s)
       OR (sender_id = %s AND recipient_id = %s)
    ORDER BY created_at ASC
    LIMIT %s
    """
    return execute_query(query, (user_id, other_user_id, other_user_id, user_id, limit), fetch_all=True) or []

def send_message(sender_id: int, recipient_id: int, content: str) -> Optional[Dict[str, Any]]:
    """
    Send a message from one user to another.
    
    Args:
        sender_id (int): The ID of the sender.
        recipient_id (int): The ID of the recipient.
        content (str): The message content.
    
    Returns:
        Optional[Dict[str, Any]]: The created message record if successful, None otherwise.
    """
    logger.info(f"Sending message from sender_id={sender_id} to recipient_id={recipient_id}")
    
    # First, insert the message
    insert_query = """
    INSERT INTO private_messages (sender_id, recipient_id, content)
    VALUES (%s, %s, %s)
    """
    execute_query(insert_query, (sender_id, recipient_id, content))
    
    # Then get the last inserted ID
    id_query = "SELECT LAST_INSERT_ID() as id"
    result = execute_query(id_query, fetch_one=True)
    
    message_id = result['id'] if result else None
    logger.info(f"Message sent with id={message_id}")
    return message_id

def delete_message(message_id: int, user_id: int) -> int:
    """
    Delete a message if the user is the sender or recipient.
    
    Args:
        message_id (int): The ID of the message to delete.
        user_id (int): The ID of the user attempting to delete the message.
    
    Returns:
        int: Number of rows affected (1 if successful, 0 if not).
    """
    logger.debug(f"Deleting message id={message_id} by user_id={user_id}")
    query = """
    DELETE FROM private_messages
    WHERE id = %s AND (sender_id = %s OR recipient_id = %s)
    """
    rows_affected = execute_query(query, (message_id, user_id, user_id))
    logger.debug(f"Deleted message id={message_id}, rows_affected={rows_affected}")
    return rows_affected

def mark_message_as_read(message_id: int, user_id: int) -> bool:
    """
    Mark a message as read by the recipient.
    
    Args:
        message_id (int): The ID of the message to mark as read.
        user_id (int): The ID of the user (must be the recipient).
    
    Returns:
        bool: True if the message was marked as read, False otherwise.
    """
    logger.info(f"Marking message_id={message_id} as read for user_id={user_id}")
    query = "UPDATE private_messages SET is_read = 1 WHERE id = %s AND recipient_id = %s"
    rows_affected = execute_query(query, (message_id, user_id))
    success = rows_affected > 0
    logger.info(f"Marked message_id={message_id} as read, success: {success}")
    return success

def get_unread_messages_count(user_id: int) -> int:
    """
    Get the count of unread messages for a user.
    
    Args:
        user_id (int): The ID of the user.
    
    Returns:
        int: The number of unread messages.
    """
    logger.debug(f"Getting unread messages count for user_id={user_id}")
    query = "SELECT COUNT(*) as count FROM private_messages WHERE recipient_id = %s AND is_read = 0"
    result = execute_query(query, (user_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Unread messages count for user_id={user_id}: {count}")
    return count