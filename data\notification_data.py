from typing import Dict, List, Optional, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def create_notification(user_id: int, notification_type: str, content: str, 
                       related_id: Optional[int] = None) -> int:
    """Create a new notification.
    
    Args:
        user_id: The ID of the user to notify.
        notification_type: Type of notification ('edit', 'comment', 'like', 'dislike', 'message', 'subscription', 'achievement', 'report').
        content: The notification content.
        related_id: Optional ID of the related object.
        
    Returns:
        int: The ID of the newly created notification.
    """
    logger.info(f"Creating notification for user ID: {user_id}, type: {notification_type}")
    
    # Validate notification type
    valid_types = ['edit', 'comment', 'like', 'dislike', 'message', 'subscription', 'achievement', 'report', 'helpdesk', 'appeal']
    if notification_type not in valid_types:
        logger.error(f"Invalid notification type: {notification_type}")
        raise ValueError(f"Invalid notification type. Must be one of: {valid_types}")
    
    query = """
    INSERT INTO notifications
    (user_id, notification_type, content, related_id, is_read)
    VALUES (%s, %s, %s, %s, FALSE)
    """
    notification_id = execute_query(query, (user_id, notification_type, content, related_id))
    
    logger.info(f"Created notification with ID: {notification_id}")
    return notification_id

def get_user_notifications(user_id: int, limit: int = 50, offset: int = 0, 
                          include_read: bool = False) -> List[Dict[str, Any]]:
    """Get notifications for a user.
    
    Args:
        user_id: The ID of the user to get notifications for.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        include_read: Whether to include read notifications.
        
    Returns:
        List[Dict[str, Any]]: List of notification records.
    """
    logger.debug(f"Getting notifications for user ID: {user_id}, include_read: {include_read}")
    
    query = """
    SELECT *
    FROM notifications
    WHERE user_id = %s
    """
    
    if not include_read:
        query += " AND is_read = FALSE"
    
    query += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
    
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} notifications for user ID: {user_id}")
    return results or []

def mark_notification_as_read(notification_id: int) -> int:
    """Mark a notification as read.
    
    Args:
        notification_id: The ID of the notification to mark as read.
        
    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Marking notification ID: {notification_id} as read")
    query = """
    UPDATE notifications
    SET is_read = TRUE
    WHERE id = %s
    """
    rows_affected = execute_query(query, (notification_id,))
    logger.info(f"Marked notification ID: {notification_id} as read, rows affected: {rows_affected}")
    return rows_affected

def mark_all_notifications_as_read(user_id: int) -> int:
    """Mark all notifications for a user as read.
    
    Args:
        user_id: The ID of the user to mark notifications for.
        
    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Marking all notifications for user ID: {user_id} as read")
    query = """
    UPDATE notifications
    SET is_read = TRUE
    WHERE user_id = %s AND is_read = FALSE
    """
    rows_affected = execute_query(query, (user_id,))
    logger.info(f"Marked {rows_affected} notifications as read for user ID: {user_id}")
    return rows_affected

def count_unread_notifications(user_id: int) -> int:
    """Count unread notifications for a user.
    
    Args:
        user_id: The ID of the user to count notifications for.
        
    Returns:
        int: Count of unread notifications.
    """
    logger.debug(f"Counting unread notifications for user ID: {user_id}")
    query = """
    SELECT COUNT(*) as count
    FROM notifications
    WHERE user_id = %s AND is_read = FALSE
    """
    result = execute_query(query, (user_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} unread notifications for user ID: {user_id}")
    return count

def count_user_notifications(user_id: int, include_read: bool = False) -> int:
    """Count all notifications for a user.
    
    Args:
        user_id: The ID of the user to count notifications for.
        include_read: Whether to include read notifications.
        
    Returns:
        int: Total count of notifications.
    """
    logger.debug(f"Counting total notifications for user ID: {user_id}, include_read: {include_read}")
    query = """
    SELECT COUNT(*) as count
    FROM notifications
    WHERE user_id = %s
    """
    
    if not include_read:
        query += " AND is_read = FALSE"
    
    result = execute_query(query, (user_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} total notifications for user ID: {user_id}")
    return count

def delete_notification(notification_id: int) -> int:
    """Delete a notification.
    
    Args:
        notification_id: The ID of the notification to delete.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting notification with ID: {notification_id}")
    query = """
    DELETE FROM notifications
    WHERE id = %s
    """
    rows_affected = execute_query(query, (notification_id,))
    logger.info(f"Deleted notification with ID: {notification_id}, rows affected: {rows_affected}")
    return rows_affected
