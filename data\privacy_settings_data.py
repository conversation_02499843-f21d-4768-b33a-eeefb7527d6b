from typing import Dict
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def get_user_privacy_settings(user_id: int) -> Dict[str, str]:
    """Get all privacy settings for a user.
    
    Args:
        user_id: The ID of the user to get privacy settings for.
        
    Returns:
        Dict[str, str]: Dictionary mapping attribute names to visibility settings.
    """
    logger.debug(f"Getting privacy settings for user ID: {user_id}")
    query = """
    SELECT attribute_name, visibility
    FROM user_privacy_settings
    WHERE user_id = %s
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    
    # Convert to dictionary
    settings = {}
    if results:
        for row in results:
            settings[row['attribute_name']] = row['visibility']
    
    logger.debug(f"Found {len(settings)} privacy settings for user ID: {user_id}")
    return settings

def get_attribute_visibility(user_id: int, attribute_name: str) -> str:
    """Get the visibility setting for a specific attribute.
    
    Args:
        user_id: The ID of the user to check settings for.
        attribute_name: The name of the attribute to check.
        
    Returns:
        str: Visibility setting ('private', 'friends', 'registered', 'public') or 'private' if not set.
    """
    logger.debug(f"Getting visibility for attribute '{attribute_name}' for user ID: {user_id}")
    query = """
    SELECT visibility
    FROM user_privacy_settings
    WHERE user_id = %s AND attribute_name = %s
    """
    result = execute_query(query, (user_id, attribute_name), fetch_one=True)
    
    if result:
        visibility = result['visibility']
        logger.debug(f"Visibility for attribute '{attribute_name}': {visibility}")
        return visibility
    else:
        logger.debug(f"No visibility setting found for attribute '{attribute_name}', defaulting to 'private'")
        return 'private'

def set_attribute_visibility(user_id: int, attribute_name: str, visibility: str) -> int:
    """Set the visibility for a specific attribute.
    
    Args:
        user_id: The ID of the user to update settings for.
        attribute_name: The name of the attribute to update.
        visibility: The visibility setting ('private', 'public').
        
    Returns:
        int: 1 if setting was created or updated, 0 otherwise.
    """
    logger.info(f"Setting visibility for attribute '{attribute_name}' to '{visibility}' for user ID: {user_id}")
    
    # Validate visibility
    valid_visibilities = ['private', 'public']
    if visibility not in valid_visibilities:
        logger.error(f"Invalid visibility: {visibility}")
        raise ValueError(f"Invalid visibility. Must be one of: {valid_visibilities}")
    
    # For debug purposes, log everything going in
    logger.debug(f"Setting visibility - user_id: {user_id}, attribute: {attribute_name}, value: {visibility}")
    
    try:
        # First check if setting already exists
        check_query = """
        SELECT id, visibility FROM user_privacy_settings 
        WHERE user_id = %s AND attribute_name = %s
        """
        existing = execute_query(check_query, (user_id, attribute_name), fetch_one=True)
        
        if existing:
            # Log current value
            logger.debug(f"Existing setting found: id={existing['id']}, current visibility={existing['visibility']}")
            
            if existing['visibility'] == visibility:
                # No change needed
                logger.debug(f"No change needed for {attribute_name}, already set to {visibility}")
                return 1
                
            # Update existing setting
            logger.debug(f"Updating existing visibility setting for {attribute_name} from {existing['visibility']} to {visibility}")
            update_query = """
            UPDATE user_privacy_settings
            SET visibility = %s
            WHERE user_id = %s AND attribute_name = %s
            """
            rows_affected = execute_query(update_query, (visibility, user_id, attribute_name))
            logger.debug(f"Updated visibility setting, rows affected: {rows_affected}")
            return rows_affected
        else:
            # Create new setting
            logger.debug(f"No existing setting found. Creating new visibility setting for {attribute_name}")
            insert_query = """
            INSERT INTO user_privacy_settings
            (user_id, attribute_name, visibility)
            VALUES (%s, %s, %s)
            """
            setting_id = execute_query(insert_query, (user_id, attribute_name, visibility))
            logger.debug(f"Created new visibility setting with ID: {setting_id}")
            
            # Verify the setting was created
            verify_query = """
            SELECT id, visibility FROM user_privacy_settings 
            WHERE user_id = %s AND attribute_name = %s
            """
            verify = execute_query(verify_query, (user_id, attribute_name), fetch_one=True)
            if verify:
                logger.debug(f"Verified setting was created: id={verify['id']}, visibility={verify['visibility']}")
            else:
                logger.warning(f"Failed to verify setting was created for {attribute_name}")
                
            return 1 if setting_id > 0 else 0
    except Exception as e:
        logger.error(f"Error setting visibility for {attribute_name}: {str(e)}", exc_info=True)
        raise

def reset_privacy_settings(user_id: int) -> int:
    """Reset all privacy settings for a user.
    
    Args:
        user_id: The ID of the user to reset settings for.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Resetting privacy settings for user ID: {user_id}")
    query = """
    DELETE FROM user_privacy_settings
    WHERE user_id = %s
    """
    rows_affected = execute_query(query, (user_id,))
    logger.info(f"Reset privacy settings for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def reset_attribute_visibility(user_id: int, attribute_name: str) -> int:
    """Reset visibility setting for a specific attribute.
    
    Args:
        user_id: The ID of the user to reset settings for.
        attribute_name: The name of the attribute to reset.
        
    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Resetting visibility for attribute '{attribute_name}' for user ID: {user_id}")
    query = """
    DELETE FROM user_privacy_settings
    WHERE user_id = %s AND attribute_name = %s
    """
    rows_affected = execute_query(query, (user_id, attribute_name))
    logger.debug(f"Reset visibility for attribute '{attribute_name}', rows affected: {rows_affected}")
    return rows_affected

def set_default_privacy_settings(user_id: int) -> Dict[str, str]:
    """Set default privacy settings for a new user.
    
    Args:
        user_id: The ID of the user to set defaults for.
        
    Returns:
        Dict[str, str]: Dictionary of the default settings that were created.
    """
    logger.info(f"Setting default privacy settings for user ID: {user_id}")
    
    # Default settings - all public
    default_settings = {
        'first_name': 'public',
        'last_name': 'public',
        'email': 'public',
        'username': 'public',
        'location': 'public',
        'description': 'public',
        'interests': 'public',
        'recent_likes': 'public',
        'recent_comments': 'public',
        'public_journeys': 'public',
        'visited_places': 'public'
    }
    
    # Apply default settings
    for field, visibility in default_settings.items():
        set_attribute_visibility(user_id, field, visibility)
    
    logger.info(f"Default privacy settings applied for user ID: {user_id}")
    return default_settings