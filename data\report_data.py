from typing import Dict, List, Optional, Any
from data import community_data
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def report_content(reporter_id: int, content_type: str, content_id: int, reason: str) -> int:
    """Report a content item for moderation.
    
    Args:
        reporter_id: The ID of the user reporting the content.
        content_type: The type of content being reported ('comment', 'event', 'journey', 'image', 'user', 'location').
        content_id: The ID of the content being reported.
        reason: The reason for the report.
        
    Returns:
        int: ID of the newly created report, or 0 if already reported.
    """
    logger.info(f"User ID {reporter_id} reporting {content_type} ID: {content_id}")
    
    # Validate content type
    valid_types = ['comment', 'event', 'journey', 'image', 'user', 'location']
    if content_type not in valid_types:
        logger.error(f"Invalid content type: {content_type}")
        raise ValueError(f"Invalid content type. Must be one of: {valid_types}")
    
    # Check if already reported by this user
    check_query = """
    SELECT id FROM reports
    WHERE reporter_id = %s AND content_type = %s AND content_id = %s
    """
    existing = execute_query(check_query, (reporter_id, content_type, content_id), fetch_one=True)
    
    if existing:
        logger.debug(f"User ID {reporter_id} already reported {content_type} ID: {content_id}")
        return 0
    
    # Add report
    insert_query = """
    INSERT INTO reports
    (reporter_id, content_type, content_id, reason, status)
    VALUES (%s, %s, %s, %s, 'new')
    """
    report_id = execute_query(insert_query, (reporter_id, content_type, content_id, reason))
    
    logger.info(f"Created report with ID: {report_id}")
    
    # Notify staff of the report, passing the report_id
    notify_moderator_of_report(reporter_id, content_type, content_id, reason, report_id)
    community_data.notify_commenter_of_report(content_id, reporter_id, reason, report_id )
    return report_id

def get_report(report_id: int) -> Optional[Dict[str, Any]]:
    """Get a report by ID.
    
    Args:
        report_id: The ID of the report to retrieve.
        
    Returns:
        Dict[str, Any]: Report data if found, None otherwise.
    """
    logger.debug(f"Getting report with ID: {report_id}")
    query = """
    SELECT r.*, u.username as reporter_username, 
           COALESCE(a.username, '') as reviewer_username,
           cu.is_banned AS is_banned,
           ec.is_hidden as is_hidden
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    LEFT JOIN users a ON r.reviewed_by = a.id
    JOIN event_comments ec ON r.content_id = ec.id
    JOIN users cu ON ec.user_id = cu.id
    WHERE r.id = %s
    """
    result = execute_query(query, (report_id,), fetch_one=True)
    logger.debug(f"Report lookup result: {'Found' if result else 'Not found'}")
    return result

def get_reports_by_content(content_type: str, content_id: int) -> List[Dict[str, Any]]:
    """Get all reports for a specific content item.
    
    Args:
        content_type: The type of content ('comment', 'event', 'journey', 'image', 'user', 'location').
        content_id: The ID of the content.
        
    Returns:
        List[Dict[str, Any]]: List of report records.
    """
    logger.debug(f"Getting reports for {content_type} ID: {content_id}")
    query = """
    SELECT r.*, u.username as reporter_username, 
           COALESCE(a.username, '') as reviewer_username
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    LEFT JOIN users a ON r.reviewed_by = a.id
    WHERE r.content_type = %s AND r.content_id = %s
    ORDER BY r.created_at DESC
    """
    results = execute_query(query, (content_type, content_id), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} reports for {content_type} ID: {content_id}")
    return results or []

def review_report(report_id: int, reviewer_id: int, status: str) -> int:
    """Review a report and update its status.
    
    Args:
        report_id: The ID of the report to review.
        reviewer_id: The ID of the staff member reviewing the report.
        status: The new status for the report ('reviewed', 'dismissed', 'resolved', 'open).
        
    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Reviewing report ID: {report_id} by staff ID: {reviewer_id}, status: {status}")
    
    # Validate status
    valid_statuses = ['reviewed', 'dismissed', 'resolved', 'open']
    if status not in valid_statuses:
        logger.error(f"Invalid status: {status}")
        raise ValueError(f"Invalid status. Must be one of: {valid_statuses}")
    
    query = """
    UPDATE reports
    SET status = %s, reviewed_by = %s, reviewed_at = CURRENT_TIMESTAMP
    WHERE id = %s
    """
    rows_affected = execute_query(query, (status, reviewer_id, report_id))
    
    logger.info(f"Reviewed report ID: {report_id}, rows affected: {rows_affected}")
    return rows_affected

def notify_moderator_of_report(reporter_id: int, content_type: str, content_id: int, reason: str, report_id: int) -> None:
    """Notify moderators of a reported content item.
    
    Args:
        reporter_id: ID of the user who reported the content.
        content_type: Type of content that was reported.
        content_id: ID of the content that was reported.
        reason: Reason for the report.
        report_id: ID of the newly created report.
    """
    logger.debug(f"Notifying moderators of report for {content_type} ID: {content_id}")
    
    # Get reporter info
    reporter_query = """
    SELECT username FROM users WHERE id = %s
    """
    reporter_info = execute_query(reporter_query, (reporter_id,), fetch_one=True)
    
    if not reporter_info:
        logger.error(f"Could not find reporter info for user ID: {reporter_id}")
        return
    
    # Create notification content
    notification_content = (
        f"{reporter_info['username']} reported {content_type} #{content_id}. "
        f"Reason: {reason}"
    )
    
    # Get all moderator users
    staff_query = """
    SELECT id FROM users
    WHERE role = 'moderator'
    """
    staff_results = execute_query(staff_query, fetch_all=True)
    
    if not staff_results:
        logger.warning("No moderators found to notify of report")
        return
    
    # Send notification to all moderators - using report_id instead of content_id
    for staff in staff_results:
        from data import notification_data
        notification_data.create_notification(
            user_id=staff['id'],
            notification_type='report',
            content=notification_content,
            related_id=report_id  # Use report_id instead of content_id
        )
    
    logger.debug(f"Notified {len(staff_results)} staff users about reported {content_type} ID: {content_id}")


def notify_admin_of_escalated_comment(comment_id: int, escalated_by_id: int) -> None:
    """Notify admins when a comment has been escalated.

    Args:
        comment_id: ID of the comment that was escalated.
        escalated_by_id: ID of the user who escalated the comment.
    """
    logger.debug(f"Notifying admins of escalated comment ID: {comment_id}")

    # Get info about the escalator
    escalator_query = """
    SELECT username FROM users WHERE id = %s
    """
    escalator_info = execute_query(escalator_query, (escalated_by_id,), fetch_one=True)

    if not escalator_info:
        logger.error(f"Could not find user info for escalator ID: {escalated_by_id}")
        return

    # Prepare the notification content
    notification_content = f"{escalator_info['username']} escalated comment #{comment_id} to admin."

    # Get all admins
    admin_query = """
    SELECT id FROM users
    WHERE role = 'admin'
    """
    admin_users = execute_query(admin_query, fetch_all=True)

    if not admin_users:
        logger.warning("No admin users found to notify of escalated comment.")
        return

    # Send a notification to each admin
    for admin in admin_users:
        from data import notification_data
        notification_data.create_notification(
            user_id=admin['id'],
            notification_type='report',
            content=notification_content,
            related_id=comment_id
        )

    logger.debug(f"Notified {len(admin_users)} admins about escalated comment ID: {comment_id}")



def has_user_flagged_comment(user_id: int, comment_id: int) -> bool:
    """Check if a user has already flagged a specific comment."""
    logger.debug(f"Checking if user {user_id} has flagged comment {comment_id}")
    query = """
    SELECT 1 FROM reports
    WHERE reporter_id = %s AND content_type = 'comment' AND content_id = %s
    """
    result = execute_query(query, (user_id, comment_id), fetch_one=True)
    return bool(result)

from typing import List, Dict, Any

def get_comment_reports(limit: int = 50, offset: int = 0, active: Optional[bool] = None, new: Optional[bool] = None, escalated: Optional[bool] = None, resolved: Optional[bool] = None) -> List[Dict[str, Any]]:
    """Retrieve comment reports with optional hidden filter."""
    logger.debug(f"Getting comment reports")

    base_query = """
      SELECT r.*, 
           u.username AS reporter_username,
           COALESCE(a.username, '') AS reviewer_username,
           ec.content AS comment_content,
           ec.created_at AS comment_created_at,
           ec.is_hidden,
           cu.is_banned AS is_banned,
           cu.username AS commenter_username,
           cu.profile_image AS commenter_profile_image
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    LEFT JOIN users a ON r.reviewed_by = a.id
    JOIN event_comments ec ON r.content_id = ec.id
    JOIN users cu ON ec.user_id = cu.id
    WHERE r.content_type = 'comment'
    """
    params = []
    
    if escalated is not None:
        base_query += " AND r.escalated_to_admin = %s"
        params.append(escalated)

    if active is True:
        base_query += " AND (r.status = 'open' OR r.status = 'new')"

    if resolved is True:
        base_query += " AND (r.status = 'dismissed' OR r.status = 'resolved')"

    if new is True:
        base_query += " AND r.status = 'new'"


    base_query += " ORDER BY r.created_at DESC LIMIT %s OFFSET %s"
    params.extend([limit, offset])

    results = execute_query(base_query, tuple(params), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} comment report(s)")
    return results or []


def get_comment_reports_count(active: Optional[bool] = None, escalated: Optional[bool] = None, resolved: Optional[bool] = None) -> int:
    """Count comment reports with filter."""
    logger.debug(f"Counting comment reports")

    query = """
    SELECT COUNT(*) as count
    FROM reports r
    JOIN event_comments ec ON r.content_id = ec.id
    WHERE r.content_type = 'comment'
    """
    params = []

    if escalated is not None:
        query += " AND r.escalated_to_admin = %s"
        params.append(escalated)

    if active is True:
        query += " AND (r.status = 'open' OR r.status = 'new')"

    if resolved is True:
        query += " AND (r.status = 'dismissed' OR r.status = 'resolved')"


    result = execute_query(query, tuple(params), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} comment reports")
    return count



def search_comment_reports(search_term: str, limit: int = 50, offset: int = 0, active: Optional[bool] = None, escalated: Optional[bool] = None, resolved: Optional[bool] = None) -> List[Dict[str, Any]]:
    """Search comment reports with optional hidden filter."""
    logger.debug(f"Searching comment reports: term={search_term}")
    search_pattern = f"%{search_term}%"

    query = """
  SELECT r.*, 
           u.username AS reporter_username,
           COALESCE(a.username, '') AS reviewer_username,
           ec.content AS comment_content,
           ec.created_at AS comment_created_at,
           ec.is_hidden,
           cu.is_banned AS is_banned,
           cu.username AS commenter_username,
           cu.profile_image AS commenter_profile_image
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    LEFT JOIN users a ON r.reviewed_by = a.id
    JOIN event_comments ec ON r.content_id = ec.id
    JOIN users cu ON ec.user_id = cu.id
    WHERE r.content_type = 'comment'
    AND (
        r.reason LIKE %s OR 
        u.username LIKE %s 
    )
    """
    params = [search_pattern, search_pattern]

    if escalated is not None:
        query += " AND r.escalated_to_admin = %s"
        params.append(escalated)

    if active is True:
        query += " AND (r.status = 'open' OR r.status = 'new')"

    if resolved is True:
        query += " AND (r.status = 'dismissed' OR r.status = 'resolved')"


    query += " ORDER BY r.created_at DESC LIMIT %s OFFSET %s"
    params.extend([limit, offset])

    results = execute_query(query, tuple(params), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} search result(s)")
    return results or []



def get_search_comment_reports_count(search_term: str,  active: Optional[bool] = None, escalated: Optional[bool] = None, resolved: Optional[bool] = None) -> int:
    """Count search results for comment reports."""
    logger.debug(f"Counting search comment reports, search_term={search_term}")

    search_pattern = f"%{search_term}%"

    query = """
    SELECT COUNT(*) as count
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    JOIN event_comments ec ON r.content_id = ec.id
    WHERE r.content_type = 'comment'
    AND (
        r.reason LIKE %s OR 
        u.username LIKE %s 
    )
    """
    params = [search_pattern, search_pattern]

    if escalated is not None:
        query += " AND r.escalated_to_admin = %s"
        params.append(escalated)

    if active is True:
        query += " AND (r.status = 'open' OR r.status = 'new')"

    if resolved is True:
        query += " AND (r.status = 'dismissed' OR r.status = 'resolved')"


    result = execute_query(query, tuple(params), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} matching search results")
    return count


def get_comment_report(report_id: int) -> Optional[Dict[str, Any]]:
    """Get a report by ID.
    
    Args:
        report_id: The ID of the report to retrieve.
        
    Returns:
        Dict[str, Any]: Report data if found, None otherwise.
    """
    logger.debug(f"Getting report with ID: {report_id}")
    query = """
    SELECT r.*, 
           u.username AS reporter_username, 
           COALESCE(a.username, '') AS reviewer_username,
           ec.is_hidden
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    LEFT JOIN users a ON r.reviewed_by = a.id
    JOIN event_comments ec ON r.content_id = ec.id
    WHERE r.id = %s
    """
    result = execute_query(query, (report_id,), fetch_one=True)
    logger.debug(f"Report lookup result: {'Found' if result else 'Not found'}")
    return result

def escalate_comment_to_admin(report_id: int, escalate: int, escalated_by_id: int) -> None:
    """Set escalated_to_admin = 1 or 0 for the comment associated with a report.

    Args:
        report_id (int): The ID of the report referencing the comment.
    """
    logger.debug(f"Escalating comment via report ID: {report_id}")

    query = """
    UPDATE reports
    SET escalated_to_admin = %s
    WHERE id = %s
    AND content_type = 'comment'
    """

    try:
        execute_query(query, (escalate, report_id,))
        logger.info(f"Comment escalated to admin via report ID {report_id}")
        # Notify staff of the report, passing the report_id
        if escalate == 1:
            notify_admin_of_escalated_comment(report_id, escalated_by_id)
        return True
    except Exception as e:
        logger.error(f"Failed to escalate comment via report ID {report_id}: {e}")
        return False

def get_my_comment_reports(user_id: int, limit: int = 50, offset: int = 0, active: Optional[bool] = None, resolved: Optional[bool] = None) -> List[Dict[str, Any]]:
    query = """
    SELECT r.*, 
           u.username AS reporter_username,
           COALESCE(a.username, '') AS reviewer_username,
           ec.is_hidden,
           cu.is_banned AS is_banned
    FROM reports r
    JOIN users u ON r.reporter_id = u.id
    LEFT JOIN users a ON r.reviewed_by = a.id
    JOIN event_comments ec ON r.content_id = ec.id
    JOIN users cu ON ec.user_id = cu.id
    WHERE r.content_type = 'comment' AND r.reporter_id = %s
    """
    params = [user_id]
    if active:
        query += " AND ((r.status != 'dismissed' AND r.status != 'resolved') OR r.escalated_to_admin = 1)"
    if resolved:
        query += " AND (r.status = 'dismissed' OR r.status = 'resolved') AND r.escalated_to_admin = 0"
    query += " ORDER BY r.created_at DESC LIMIT %s OFFSET %s"
    params.extend([limit, offset])
    results = execute_query(query, tuple(params), fetch_all=True)
    return results or []

def get_my_comment_reports_count(user_id: int, active: Optional[bool] = None, resolved: Optional[bool] = None) -> int:
    query = """
    SELECT COUNT(*) as count
    FROM reports r
    WHERE r.content_type = 'comment' AND r.reporter_id = %s
    """
    params = [user_id]
    if active:
        query += " AND ((r.status != 'dismissed' AND r.status != 'resolved') OR r.escalated_to_admin = 1)"
    if resolved:
        query += " AND ((r.status = 'dismissed' OR r.status = 'resolved') AND r.escalated_to_admin = 0)"
    result = execute_query(query, tuple(params), fetch_one=True)
    return result['count'] if result else 0