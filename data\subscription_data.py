"""
Subscription Data Module

This module handles database operations for user subscriptions including:
- Subscription creation
- Subscription renewal
- Subscription status checks
"""

from typing import Dict, List, Optional, Any
from datetime import date, timedelta
from utils.db_utils import execute_query
from utils.logger import get_logger
from decimal import Decimal

# Initialize logger
logger = get_logger(__name__)

def get_subscription_plan(plan_code: str) -> Optional[Dict[str, Any]]:
    """Get subscription plan details by plan code.

    Args:
        plan_code: The plan code to retrieve.

    Returns:
        Optional[Dict[str, Any]]: Plan details if found, None otherwise.
    """
    logger.debug(f"Getting subscription plan with code: {plan_code}")
    query = """
    SELECT *
    FROM subscription_plans
    WHERE plan_code = %s AND is_active = TRUE
    """
    result = execute_query(query, (plan_code,), fetch_one=True)
    logger.debug(f"Plan lookup result: {'Found' if result else 'Not found'}")
    return result

def create_subscription(user_id: int, plan_code: str,
                       start_date: date, end_date: date,
                       reason: Optional[str] = None,
                       subscription_type: Optional[str] = None, months: Optional[int] = None) -> int:
    """Create a new subscription.

    Args:
        user_id: The ID of the user subscribing.
        plan_code: Code of the subscription plan.
        start_date: Start date of the subscription.
        end_date: End date of the subscription.
        reason: Optional reason for the subscription.
        subscription_type: Optional subscription type (e.g., 'Free Trial', 'Premium').

    Returns:
        int: The ID of the newly created subscription.

    Raises:
        ValueError: If the plan code is invalid or dates are invalid.
    """
    logger.info(f"Creating {plan_code} subscription for user ID: {user_id}")

    # Validate dates
    if not isinstance(start_date, date) or not isinstance(end_date, date):
        raise ValueError("Start date and end date must be date objects")
    if start_date >= end_date:
        raise ValueError("Start date must be before end date")

    # Validate plan exists
    plan = get_subscription_plan(plan_code)
    if not plan:
        logger.error(f"Invalid subscription plan: {plan_code}")
        raise ValueError(f"Invalid subscription plan: {plan_code}")

    try:
        query = """
        INSERT INTO subscriptions
        (user_id, plan_code, start_date, end_date, is_active, reason, months)
        VALUES (%s, %s, %s, %s, TRUE, %s, %s)
        """
        if reason is None:
            reason = ''
        if months is None:
            months = 1
        subscription_id = execute_query(query, (user_id, plan_code, start_date, end_date, reason, months))

        logger.info(f"Created subscription with ID: {subscription_id}")

        # If this is a free trial, update the user's had_free_trial status
        if plan_code == 'free_trial':
            update_query = """
            UPDATE users
            SET had_free_trial = TRUE
            WHERE id = %s
            """
            execute_query(update_query, (user_id,))
            logger.debug(f"Updated had_free_trial status for user ID: {user_id}")

        return subscription_id
    except Exception as e:
        logger.error(f"Error creating subscription for user {user_id}: {str(e)}")
        raise

def record_payment(user_id: int, subscription_id: int, amount: float,
                  gst_amount: Optional[float] = None,
                  payment_method: Optional[str] = 'credit_card',
                  billing_address: Optional[str] = None,
                  country_id: int = 1,
                  card_last_four: Optional[str] = None,
                  transaction_reference: Optional[str] = None) -> int:
    """Record a payment for a subscription."""
    logger.info(f"Recording payment of {amount} for user ID: {user_id}, subscription ID: {subscription_id}")
    query = """
    INSERT INTO payments
    (user_id, subscription_id, amount, gst_amount, payment_method, billing_address, country_id, card_last_four, transaction_reference)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    if country_id is None:
        country_id = 1  # Default to NZ if not provided
    payment_id = execute_query(
        query,
        (user_id, subscription_id, amount, gst_amount, payment_method, billing_address, country_id, card_last_four, transaction_reference)
    )
    logger.info(f"Recorded payment with ID: {payment_id}")
    return payment_id

def get_subscription(subscription_id: int) -> Optional[Dict[str, Any]]:
    """Get a subscription by ID.

    Args:
        subscription_id: The ID of the subscription to retrieve.

    Returns:
        Dict[str, Any]: Subscription data if found, None otherwise.
    """
    logger.debug(f"Getting subscription with ID: {subscription_id}")
    query = """
    SELECT *
    FROM subscriptions
    WHERE id = %s
    """
    result = execute_query(query, (subscription_id,), fetch_one=True)
    logger.debug(f"Subscription lookup result: {'Found' if result else 'Not found'}")
    return result

def get_user_subscriptions(user_id: int, limit: int = None, offset: int = None) -> List[Dict[str, Any]]:
    """Get all (or paginated) subscriptions for a user, including plan info and country name."""
    logger.debug(f"Getting subscriptions for user ID: {user_id} (with plan info and country name)")
    query = """
    SELECT s.*, sp.name AS plan_name, sp.base_price, sp.period_months, sp.discount_percentage, sp.plan_code,
           c.code AS country_code, c.name AS country_name, c.currency_code AS currency_code
    FROM subscriptions s
    LEFT JOIN subscription_plans sp ON s.plan_code = sp.plan_code
    LEFT JOIN payments p ON p.subscription_id = s.id
    LEFT JOIN countries c ON p.country_id = c.id
    WHERE s.user_id = %s
    ORDER BY s.start_date DESC
    """
    params = [user_id]
    if limit is not None:
        query += " LIMIT %s"
        params.append(limit)
        if offset is not None:
            query += " OFFSET %s"
            params.append(offset)
    results = execute_query(query, tuple(params), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} subscriptions for user ID: {user_id}")
    return results or []

def get_user_subscription(user_id: int) -> Optional[Dict[str, Any]]:
    """Get a user's active subscription.

    Args:
        user_id: The ID of the user to check

    Returns:
        Dict[str, Any]: Subscription data if found, None otherwise
    """
    logger.debug(f"Getting active subscription for user ID: {user_id}")

    query = """
    SELECT s.*, sp.name as plan_name,
           CASE
               WHEN s.plan_code = 'free_trial' THEN 'Free Trial'
               WHEN s.plan_code = 'admin_gift' THEN 'Gifted Premium'
               ELSE 'Premium'
           END as subscription_type
    FROM subscriptions s
    JOIN subscription_plans sp ON s.plan_code = sp.plan_code
    WHERE s.user_id = %s
      AND s.is_active = TRUE
      AND s.end_date >= CURDATE()
    ORDER BY s.end_date DESC
    LIMIT 1
    """

    result = execute_query(query, (user_id,), fetch_one=True)
    logger.debug(f"Subscription lookup result: {'Found' if result else 'Not found'}")
    return result

def get_active_subscription_plans() -> List[Dict[str, Any]]:
    """Get all active subscription plans.

    Returns:
        List[Dict[str, Any]]: List of active subscription plans.
    """
    logger.debug("Getting all active subscription plans")
    query = """
    SELECT *
    FROM subscription_plans
    WHERE is_active = TRUE
    ORDER BY display_order ASC
    """
    results = execute_query(query, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} active plans")
    return results or []

def has_active_subscription(user_id: int) -> bool:
    """Check if a user has an active subscription.

    Args:
        user_id: The ID of the user to check

    Returns:
        bool: True if the user has an active subscription, False otherwise
    """
    logger.debug(f"Checking if user ID: {user_id} has active subscription")

    query = """
    SELECT COUNT(*) as count
    FROM subscriptions
    WHERE user_id = %s
      AND is_active = TRUE
      AND end_date >= CURDATE()
    """

    result = execute_query(query, (user_id,), fetch_one=True)
    has_subscription = result and result['count'] > 0

    logger.debug(f"User ID: {user_id} has active subscription: {has_subscription}")
    return has_subscription

def get_subscription_end_date(user_id: int) -> Optional[date]:
    """Get the end date of a user's active subscription.

    Args:
        user_id: The ID of the user to check.

    Returns:
        date: End date of the active subscription if found, None otherwise.

    Note:
        This is a helper function that extracts info from the full subscription object.
        For more complete information, use get_user_subscription.
    """
    logger.debug(f"Getting subscription end date for user ID: {user_id}")

    subscription = get_user_subscription(user_id)
    if subscription and subscription.get('end_date'):
        logger.debug(f"Subscription end date for user ID: {user_id}: {subscription['end_date']}")
        return subscription['end_date']
    else:
        logger.debug(f"No active subscription found for user ID: {user_id}")
        return None

def calculate_subscription_end_date(start_date: date, plan_code: str) -> date:
    """Calculate the end date based on subscription plan.

    Args:
        start_date: Start date of the subscription.
        plan_code: Code of the subscription plan.

    Returns:
        date: Calculated end date.
    """
    logger.debug(f"Calculating end date for plan {plan_code} starting {start_date}")

    # Get plan details
    plan = get_subscription_plan(plan_code)
    if not plan:
        logger.error(f"Invalid subscription plan: {plan_code}")
        raise ValueError(f"Invalid subscription plan: {plan_code}")

    # Get period in months
    period_months = plan['period_months']

    # Special case for admin_gift which might have 0 months
    if period_months <= 0:
        logger.warning(f"Plan {plan_code} has invalid period of {period_months} months")
        return start_date

    # Add months to the start date
    # This correctly handles different month lengths and year transitions
    year = start_date.year + (start_date.month + period_months - 1) // 12
    month = (start_date.month + period_months - 1) % 12 + 1

    # Try to use the same day, but handle month length differences
    try:
        end_date = date(year, month, start_date.day)
    except ValueError:
        # If the day doesn't exist in the target month (e.g., Feb 30), use the last day
        if month == 2:
            # Handle leap years
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                end_date = date(year, month, 29)
            else:
                end_date = date(year, month, 28)
        elif month in [4, 6, 9, 11]:
            end_date = date(year, month, 30)
        else:
            end_date = date(year, month, 31)

    logger.debug(f"Calculated end date: {end_date}")
    return end_date

def extend_subscription(user_id: int, plan_code: str, reason: Optional[str] = None) -> int:
    """Extend a user's subscription using a subscription plan.

    Args:
        user_id: The ID of the user whose subscription to extend.
        plan_code: Code of the subscription plan.
        reason: Optional reason for the extension.

    Returns:
        int: The ID of the newly created subscription.
    """
    logger.info(f"Extending subscription for user ID: {user_id} with plan {plan_code}")

    # Get plan details
    plan = get_subscription_plan(plan_code)
    if not plan:
        logger.error(f"Invalid subscription plan: {plan_code}")
        raise ValueError(f"Invalid subscription plan: {plan_code}")

    # Get current end date if there's an active subscription
    current_end_date = get_subscription_end_date(user_id)

    if current_end_date and current_end_date >= date.today():
        # Extend from current end date
        start_date = current_end_date + timedelta(days=1)
    else:
        # Start new subscription from today
        start_date = date.today()

    # Calculate end date using months
    end_date = calculate_subscription_end_date(start_date, plan_code)

    # Create new subscription
    return create_subscription(user_id, plan_code, start_date, end_date, reason)

def create_gift_subscription(user_id: int, months: int, reason: Optional[str] = None) -> int:
    """Gift a subscription to a user as an admin.

    Args:
        user_id: The ID of the user to gift a subscription to.
        months: The number of months to gift (1-12).
        reason: Optional reason for gifting.

    Returns:
        int: The ID of the newly created subscription.
    """
    logger.info(f"Admin gifting {months} month subscription to user ID: {user_id}")

    if months < 1 or months > 12:
        logger.error(f"Invalid gift subscription duration: {months}")
        raise ValueError("Gift subscription must be between 1 and 12 months")

    # Get current end date if there's an active subscription
    current_end_date = get_subscription_end_date(user_id)

    if current_end_date and current_end_date >= date.today():
        # Extend from current end date
        start_date = current_end_date + timedelta(days=1)
    else:
        # Start new subscription from today
        start_date = date.today()

    # Calculate end date by adding months
    year = start_date.year + (start_date.month + months - 1) // 12
    month = (start_date.month + months - 1) % 12 + 1

    # Try to use same day, handle month length differences
    try:
        end_date = date(year, month, start_date.day)
    except ValueError:
        # If the day doesn't exist in the target month (e.g., Feb 30), use the last day
        if month == 2:
            # Handle leap years
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                end_date = date(year, month, 29)
            else:
                end_date = date(year, month, 28)
        elif month in [4, 6, 9, 11]:
            end_date = date(year, month, 30)
        else:
            end_date = date(year, month, 31)

    # Create new subscription
    return create_subscription(user_id, 'admin_gift', start_date, end_date, reason=reason, months=months)

def cancel_subscription(subscription_id: int) -> int:
    """Cancel a subscription.

    Args:
        subscription_id: The ID of the subscription to cancel.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Cancelling subscription with ID: {subscription_id}")
    query = """
    UPDATE subscriptions
    SET is_active = FALSE
    WHERE id = %s
    """
    rows_affected = execute_query(query, (subscription_id,))
    logger.info(f"Cancelled subscription with ID: {subscription_id}, rows affected: {rows_affected}")
    return rows_affected

def get_user_payments(user_id: int) -> List[Dict[str, Any]]:
    """Get all payments made by a user.

    Args:
        user_id: The ID of the user to get payments for.

    Returns:
        List[Dict[str, Any]]: List of payment records.
    """
    logger.debug(f"Getting payments for user ID: {user_id}")
    query = """
    SELECT p.*, s.plan_code
    FROM payments p
    JOIN subscriptions s ON p.subscription_id = s.id
    WHERE p.user_id = %s
    ORDER BY p.id DESC, p.payment_date DESC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} payments for user ID: {user_id}")
    return results or []

def get_payment(payment_id: int) -> Optional[Dict[str, Any]]:
    """Get a payment by ID, including subscription, plan, and country info from the payments_with_subscription_and_country view."""
    logger.debug(f"Getting payment with ID: {payment_id} (with subscription and country info)")
    query = """
    SELECT * FROM payments_with_subscription_and_country WHERE id = %s
    """
    result = execute_query(query, (payment_id,), fetch_one=True)
    logger.debug(f"Payment lookup result: {'Found' if result else 'Not found'}")
    return result

def get_users_expiring_soon(days: int = 7) -> List[Dict[str, Any]]:
    """Get users whose subscriptions are expiring within the specified number of days.

    Args:
        days: Number of days from today to consider as 'expiring soon'.

    Returns:
        List[Dict[str, Any]]: List of user records with expiration dates.
    """
    logger.debug(f"Getting users with subscriptions expiring within {days} days")
    query = """
    SELECT u.id, u.username, u.email, s.end_date
    FROM users u
    JOIN subscriptions s ON u.id = s.user_id
    WHERE s.is_active = TRUE
    AND s.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL %s DAY)
    """
    results = execute_query(query, (days,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} users with expiring subscriptions")
    return results or []

def can_use_premium_features(user_id: int) -> bool:
    """Check if a user can use premium features (active subscription or staff role).

    Args:
        user_id: The ID of the user to check.

    Returns:
        bool: True if the user can use premium features, False otherwise.
    """
    logger.debug(f"Checking if user ID: {user_id} can use premium features")

    # First check if user is staff (editor, moderator, admin, support_tech)
    from utils.permissions import PermissionGroups
    staff_roles = "', '".join(PermissionGroups.STAFF)
    query_staff = f"""
    SELECT COUNT(*) as count
    FROM users
    WHERE id = %s AND role IN ('{staff_roles}')
    """
    result_staff = execute_query(query_staff, (user_id,), fetch_one=True)
    is_staff = result_staff['count'] > 0 if result_staff else False

    if is_staff:
        logger.debug(f"User ID: {user_id} can use premium features (staff role)")
        return True

    # Then check if user has active subscription
    has_subscription = has_active_subscription(user_id)

    logger.debug(f"User ID: {user_id} can use premium features: {has_subscription}")
    return has_subscription

def get_subscription_status(user_id: int) -> str:
    """Get a user's subscription status.

    Args:
        user_id: The ID of the user to check.

    Returns:
        str: 'free', 'trial', or 'premium' based on subscription status.
    """
    logger.debug(f"Getting subscription status for user ID: {user_id}")

    subscription = get_user_subscription(user_id)

    if subscription:
        if subscription['subscription_type'] == 'free_trial':
            status = 'trial'
        else:
            status = 'premium'
        logger.debug(f"Subscription status for user ID: {user_id}: {status}")
        return status
    else:
        logger.debug(f"Subscription status for user ID: {user_id}: free")
        return 'free'

def calculate_subscription_price(plan_code: str, country_id: int) -> Dict[str, float]:
    """Calculate the price of a subscription based on plan and country.

    Args:
        plan_code: Code of the subscription plan.
        country_id: ID of the billing country.

    Returns:
        Dict[str, float]: Dictionary with 'amount', 'gst_amount', and 'total_amount'.
    """
    logger.debug(f"Calculating price for {plan_code} subscription in country {country_id}")

    # Get plan details
    plan = get_subscription_plan(plan_code)
    if not plan:
        logger.error(f"Invalid subscription plan: {plan_code}")
        raise ValueError(f"Invalid subscription plan: {plan_code}")

    # Get country details
    query_country = """
    SELECT * FROM countries WHERE id = %s
    """
    country = execute_query(query_country, (country_id,), fetch_one=True)
    if not country:
        logger.error(f"Invalid country ID: {country_id}")
        raise ValueError(f"Invalid country ID: {country_id}")

    # Calculate base price with discount
    base_price = plan['base_price']
    discount_percentage = Decimal(str(plan['discount_percentage'] or 0))
    amount = base_price * (Decimal('1') - discount_percentage / Decimal('100'))

    # Apply GST if applicable
    if country['has_gst'] and country['gst_rate']:
        gst_rate = country['gst_rate'] / 100
        gst_amount = round(amount * gst_rate, 2)
        total_amount = amount + gst_amount
    else:
        gst_amount = 0
        total_amount = amount

    result = {
        'amount': amount,
        'gst_amount': gst_amount,
        'total_amount': total_amount,
        'currency_code': country['currency_code'] or 'NZD',
    }

    logger.debug(f"Price calculation result: {result}")
    return result

def count_active_subscribers() -> int:
    """Count the number of active subscribers.

    Returns:
        int: Count of active subscribers.
    """
    logger.debug("Counting active subscribers")
    query = """
    SELECT COUNT(DISTINCT user_id) as count
    FROM subscriptions
    WHERE is_active = TRUE AND end_date >= CURDATE()
    """
    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} active subscribers")
    return count

def get_subscription_metrics() -> Dict[str, Any]:
    """Get metrics about subscriptions.

    Returns:
        Dict[str, Any]: Dictionary with subscription metrics.
    """
    logger.debug("Getting subscription metrics")

    # Total active subscribers
    active_query = """
    SELECT COUNT(DISTINCT user_id) as active_count
    FROM subscriptions
    WHERE is_active = TRUE AND end_date >= CURDATE()
    """
    active_result = execute_query(active_query, fetch_one=True)
    active_count = active_result['active_count'] if active_result else 0

    # Count by subscription type
    type_query = """
    SELECT plan_code AS subscription_type, COUNT(*) as type_count
    FROM subscriptions
    WHERE is_active = TRUE AND end_date >= CURDATE()
    GROUP BY plan_code
    """
    type_results = execute_query(type_query, fetch_all=True)

    # Count of subscribers ending within 7 days
    expiring_query = """
    SELECT COUNT(DISTINCT user_id) as expiring_count
    FROM subscriptions
    WHERE is_active = TRUE
    AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    """
    expiring_result = execute_query(expiring_query, fetch_one=True)
    expiring_count = expiring_result['expiring_count'] if expiring_result else 0

    # Subscriptions started in the last 30 days
    new_query = """
    SELECT COUNT(*) as new_count
    FROM subscriptions
    WHERE start_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    """
    new_result = execute_query(new_query, fetch_one=True)
    new_count = new_result['new_count'] if new_result else 0

    # Build metrics object
    metrics = {
        'active_subscribers': active_count,
        'by_type': {r['subscription_type']: r['type_count'] for r in type_results} if type_results else {},
        'expiring_soon': expiring_count,
        'new_last_30_days': new_count
    }

    logger.debug(f"Subscription metrics: {metrics}")
    return metrics

def get_user_subscription_history(user_id: int) -> List[Dict[str, Any]]:
    """Get a user's subscription history.

    Args:
        user_id: The ID of the user to check

    Returns:
        List[Dict[str, Any]]: List of subscription records
    """
    logger.debug(f"Getting subscription history for user ID: {user_id}")

    query = """
    SELECT s.*, p.*
    FROM subscriptions s
    LEFT JOIN payments p ON s.id = p.subscription_id
    WHERE s.user_id = %s
    ORDER BY s.created_at DESC
    """

    results = execute_query(query, (user_id,), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} subscription records")
    return results or []

def renew_subscription(subscription_id: int, months: int) -> int:
    """Renew a subscription by extending its end date.

    Args:
        subscription_id: The ID of the subscription to renew
        months: The number of additional months

    Returns:
        int: Number of rows affected
    """
    logger.info(f"Renewing subscription ID: {subscription_id} for {months} months")

    # First, get the current subscription
    subscription = get_subscription(subscription_id)
    if not subscription:
        logger.error(f"Subscription not found: {subscription_id}")
        raise ValueError(f"Subscription not found: {subscription_id}")

    # Get end date
    end_date = subscription['end_date']

    # Calculate new end date by adding months
    year = end_date.year + (end_date.month + months - 1) // 12
    month = (end_date.month + months - 1) % 12 + 1

    # Try to use same day, handle month length differences
    try:
        new_end_date = date(year, month, end_date.day)
    except ValueError:
        # If the day doesn't exist in the target month (e.g., Feb 30), use the last day
        if month == 2:
            # Handle leap years
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                new_end_date = date(year, month, 29)
            else:
                new_end_date = date(year, month, 28)
        elif month in [4, 6, 9, 11]:
            new_end_date = date(year, month, 30)
        else:
            new_end_date = date(year, month, 31)

    query = """
    UPDATE subscriptions
    SET end_date = %s
    WHERE id = %s
    """

    rows_affected = execute_query(query, (new_end_date, subscription_id))
    logger.info(f"Renewed subscription, rows affected: {rows_affected}")
    return rows_affected

def get_free_trial_plans() -> List[Dict[str, Any]]:
    """
    Get list of available free trial plans.

    Returns:
        List[Dict[str, Any]]: List of free trial plan records.
    """
    logger.debug("Getting all free trial subscription plans")
    query = """
        SELECT *
        FROM subscription_plans
        WHERE plan_code = %s AND is_active = TRUE
    """
    results = execute_query(query, ('free_trial',), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} free trial plans")
    return results or []

def get_users_with_subscription_status(
    search_term: str = '',
    limit: int = 100,
    offset: int = 0,
    public_only: bool = False
) -> List[Dict[str, Any]]:
    """
    Get all users with their subscription status in a single SQL query, with search and pagination.
    """
    logger.debug(f"Getting all users with subscription status (limit: {limit}, offset: {offset}, search: '{search_term}', public_only: {public_only})")
    query = """
    SELECT
        u.*,
        COALESCE(
            CASE
                WHEN s.plan_code = 'free_trial' THEN 'trial'
                WHEN s.plan_code IS NOT NULL THEN 'premium'
                ELSE 'free'
            END, 'free'
        ) AS subscription_status
    FROM users u
    LEFT JOIN (
        SELECT s1.user_id, s1.plan_code
        FROM subscriptions s1
        WHERE s1.is_active = TRUE
        AND s1.id = (
            SELECT s2.id
            FROM subscriptions s2
            WHERE s2.user_id = s1.user_id AND s2.is_active = TRUE
            ORDER BY s2.end_date DESC, s2.id DESC
            LIMIT 1
        )
    ) s ON u.id = s.user_id
    WHERE 1=1
    """
    params = []
    if search_term:
        query += " AND (u.username LIKE %s OR u.email LIKE %s OR u.first_name LIKE %s OR u.last_name LIKE %s)"
        like = f"%{search_term}%"
        params.extend([like, like, like, like])
    if public_only:
        query += " AND u.is_public = TRUE"
    query += " ORDER BY u.username LIMIT %s OFFSET %s"
    params.extend([limit, offset])
    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} users with subscription status")
    return results or []

def count_users_with_subscription_status(
    search_term: str = '',
    public_only: bool = False
) -> int:
    """
    Count all users with optional search and public_only filter.
    """
    logger.debug(f"Counting users with subscription status (search: '{search_term}', public_only: {public_only})")
    query = "SELECT COUNT(*) as count FROM users u WHERE 1=1"
    params = []
    if search_term:
        query += " AND (u.username LIKE %s OR u.email LIKE %s OR u.first_name LIKE %s OR u.last_name LIKE %s)"
        like = f"%{search_term}%"
        params.extend([like, like, like, like])
    if public_only:
        query += " AND u.is_public = TRUE"
    result = execute_query(query, params, fetch_one=True)
    return result['count'] if result else 0

def get_subscription_by_id(subscription_id: int) -> Optional[Dict[str, Any]]:
    """Get subscription details by subscription ID."""
    query = """
    SELECT * FROM subscriptions
    WHERE id = %s
    """
    result = execute_query(query, (subscription_id,), fetch_one=True)
    return result

def get_payment_by_subscription_id(subscription_id: int) -> Optional[Dict[str, Any]]:
    """Get payment details by subscription ID, including subscription, plan, and country info from the payments_with_subscription_and_country view."""
    query = """
    SELECT * FROM payments_with_subscription_and_country WHERE subscription_id = %s
    """
    result = execute_query(query, (subscription_id,), fetch_one=True)
    return result

def deactivate_user_subscriptions(user_id: int) -> bool:
    """Deactivate all active subscriptions for a user.

    Args:
        user_id: The ID of the user

    Returns:
        bool: True if successful, False otherwise
    """
    logger.debug(f"Deactivating all active subscriptions for user ID: {user_id}")

    try:
        query = """
        UPDATE subscriptions
        SET is_active = FALSE
        WHERE user_id = %s AND is_active = TRUE
        """
        execute_query(query, (user_id,))
        logger.info(f"Successfully deactivated subscriptions for user ID: {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error deactivating subscriptions for user {user_id}: {str(e)}")
        return False

def update_payment(user_id: int, subscription_id: int, billing_address: str,
                  card_last_four: str, country_id: int) -> bool:
    """Update payment information for a subscription.

    Args:
        user_id: The ID of the user
        subscription_id: The ID of the subscription
        billing_address: New billing address
        card_last_four: New card last four digits
        country_id: New country ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # First check if payment record exists
        query = """
        SELECT id FROM payments
        WHERE user_id = %s AND subscription_id = %s
        ORDER BY id DESC LIMIT 1
        """
        existing_payment = execute_query(query, (user_id, subscription_id), fetch_one=True)

        if existing_payment:
            # Update existing payment
            update_query = """
            UPDATE payments
            SET billing_address = %s, card_last_four = %s, country_id = %s
            WHERE id = %s
            """
            execute_query(update_query, (billing_address, card_last_four, country_id, existing_payment['id']))
        else:
            # Create new payment record
            insert_query = """
            INSERT INTO payments
            (user_id, subscription_id, billing_address, card_last_four, country_id, amount, currency)
            VALUES (%s, %s, %s, %s, %s, 0, 'NZD')
            """
            execute_query(insert_query, (user_id, subscription_id, billing_address, card_last_four, country_id))

        return True
    except Exception as e:
        logger.error(f"Error updating payment for subscription {subscription_id}: {str(e)}")
        return False

def get_selectable_plans() -> List[Dict[str, Any]]:
    """Get all selectable premium subscription plans (is_active, is_premium, is_selectable)."""
    logger.debug("Getting all selectable premium subscription plans")
    query = """
    SELECT *
    FROM subscription_plans
    WHERE is_active = TRUE AND is_premium = TRUE AND is_selectable = TRUE
    ORDER BY display_order ASC
    """

    results = execute_query(query, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} selectable plans")
    return results or []

def check_user_had_free_trial(user_id: int) -> bool:
    """Check if a user has had a free trial."""
    query = """
    SELECT 1 FROM subscriptions
    WHERE user_id = %s AND plan_code = 'free_trial'
    LIMIT 1
    """
    result = execute_query(query, (user_id,), fetch_one=True)
    return bool(result)

def get_countries() -> list:
    """Return a list of all countries (id, name, currency_code) for payment modal country dropdown."""
    query = """
    SELECT id, name, currency_code FROM countries ORDER BY name ASC
    """
    return execute_query(query, fetch_all=True)

def get_most_recent_premium_or_trial_subscription(user_id: int) -> Optional[Dict[str, Any]]:
    """
    Get the most recent premium or trial subscription for a user (including expired).
    """
    query = """
    SELECT *
    FROM subscriptions
    WHERE user_id = %s
      AND (plan_code LIKE '%%premium%%' OR plan_code LIKE '%%trial%%')
    ORDER BY end_date DESC
    LIMIT 1
    """
    return execute_query(query, (user_id,), fetch_one=True)

def check_user_had_premium(user_id: int) -> bool:
    """Check if a user has ever had a premium subscription."""
    query = """
    SELECT 1
    FROM subscriptions s
    JOIN subscription_plans sp ON s.plan_code = sp.plan_code
    WHERE s.user_id = %s
      AND sp.is_premium = TRUE
    LIMIT 1
    """
    return bool(execute_query(query, (user_id,), fetch_one=True))
        