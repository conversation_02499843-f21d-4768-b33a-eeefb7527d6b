from typing import Dict, List, Optional, Union, Any
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def get_user_by_username(username: str) -> Optional[Dict[str, Any]]:
    """Get a user by username.

    Args:
        username: The username to search for.

    Returns:
        Dict[str, Any]: User data dictionary if found, None otherwise.
    """
    logger.debug(f"Getting user by username: {username}")
    query = """
    SELECT id, username, email, password_hash, first_name, last_name,
           location, description, profile_image, role, is_blocked, is_banned,
           is_public, biography, interests, had_free_trial
    FROM users
    WHERE username = %s
    """
    result = execute_query(query, (username,), fetch_one=True)
    logger.debug(f"User lookup result: {'Found' if result else 'Not found'}")
    return result

def get_user_by_email(email: str) -> Optional[Dict[str, Any]]:
    """Get a user by email.

    Args:
        email: The email address to search for.

    Returns:
        Dict[str, Any]: User data dictionary if found, None otherwise.
    """
    logger.debug(f"Getting user by email: {email}")
    query = """
    SELECT id, username, email, password_hash, first_name, last_name,
           location, description, profile_image, role, is_blocked, is_banned,
           is_public, biography, interests, had_free_trial
    FROM users
    WHERE email = %s
    """
    result = execute_query(query, (email,), fetch_one=True)
    logger.debug(f"User lookup result: {'Found' if result else 'Not found'}")
    return result

def get_user_by_id(user_id: int) -> Optional[Dict[str, Any]]:
    """Get a user by ID.

    Args:
        user_id: The user ID to search for.

    Returns:
        Dict[str, Any]: User data dictionary if found, None otherwise.
    """
    logger.debug(f"Getting user by ID: {user_id}")
    query = """
    SELECT id, username, password_hash, email, first_name, last_name,
           location, description, profile_image, role, is_blocked, is_banned,
           is_public, biography, interests, had_free_trial
    FROM users
    WHERE id = %s
    """
    result = execute_query(query, (user_id,), fetch_one=True)
    logger.debug(f"User lookup result: {'Found' if result else 'Not found'}")
    return result

def create_user(username: str, email: str, password_hash: str,
                first_name: Optional[str] = None,
                last_name: Optional[str] = None,
                location: Optional[str] = None) -> int:
    """Create a new user.

    Args:
        username: The username for the new user.
        email: The email address for the new user.
        password_hash: The hashed password for the new user.
        first_name: Optional first name for the new user.
        last_name: Optional last name for the new user.
        location: Optional location for the new user.

    Returns:
        int: The ID of the newly created user.
    """
    logger.info(f"Creating new user with username: {username}, email: {email}")
    query = """
    INSERT INTO users
    (username, email, password_hash, first_name, last_name, location, role)
    VALUES (%s, %s, %s, %s, %s, %s, 'traveller')
    """
    user_id = execute_query(
        query,
        (username, email, password_hash, first_name, last_name, location)
    )
    logger.info(f"Created new user with ID: {user_id}")
    return user_id

def update_user_profile(user_id: int,
                        password: Optional[str] = None,
                        email: Optional[str] = None,
                        first_name: Optional[str] = None,
                        last_name: Optional[str] = None,
                        location: Optional[str] = None,
                        description: Optional[str] = None,
                        biography: Optional[str] = None,
                        interests: Optional[str] = None,
                        is_public: Optional[bool] = None) -> int:
    """Update a user's account.

    Args:
        user_id: The ID of the user to update.
        password: Optional new password hash for the user.
        email: Optional new email address for the user.
        first_name: Optional new first name for the user.
        last_name: Optional new last name for the user.
        location: Optional new location for the user.
        description: Optional new description for the user.
        biography: Optional new biography for the user.
        interests: Optional new interests for the user.
        is_public: Optional new public visibility setting.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating profile for user ID: {user_id}")
    # Build the query dynamically based on provided parameters
    updates = []
    params = []

    if password is not None:
        updates.append("password_hash = %s")
        params.append(password)
        logger.debug("Updating password")

    if email is not None:
        updates.append("email = %s")
        params.append(email)
        logger.debug(f"Updating email to: {email}")

    if first_name is not None:
        updates.append("first_name = %s")
        params.append(first_name)
        logger.debug(f"Updating first name to: {first_name}")

    if last_name is not None:
        updates.append("last_name = %s")
        params.append(last_name)
        logger.debug(f"Updating last name to: {last_name}")

    if location is not None:
        updates.append("location = %s")
        params.append(location)
        logger.debug(f"Updating location to: {location}")

    if description is not None:
        updates.append("description = %s")
        params.append(description)
        logger.debug(f"Updating description")

    if biography is not None:
        updates.append("biography = %s")
        params.append(biography)
        logger.debug(f"Updating biography")

    if interests is not None:
        updates.append("interests = %s")
        params.append(interests)
        logger.debug(f"Updating interests")

    if is_public is not None:
        updates.append("is_public = %s")
        params.append(is_public)
        logger.debug(f"Updating is_public to: {is_public}")

    if not updates:
        logger.warning(f"No updates provided for user ID: {user_id}")
        return 0

    query = f"""
    UPDATE users
    SET {', '.join(updates)}
    WHERE id = %s
    """

    params.append(user_id)
    rows_affected = execute_query(query, params)
    logger.info(f"Updated profile for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def update_user_profile_image(user_id: int, image_path: str) -> int:
    """Update a user's profile image.

    Args:
        user_id: The ID of the user to update.
        image_path: The path to the new profile image.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating profile image for user ID: {user_id}")
    query = """
    UPDATE users
    SET profile_image = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (image_path, user_id))
    logger.info(f"Updated profile image for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def remove_user_profile_image(user_id: int) -> int:
    """Remove a user's profile image.

    Args:
        user_id: The ID of the user to update.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Removing profile image for user ID: {user_id}")
    query = """
    UPDATE users
    SET profile_image = NULL
    WHERE id = %s
    """
    rows_affected = execute_query(query, (user_id,))
    logger.info(f"Removed profile image for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def update_user_password(user_id: int, password_hash: str) -> int:
    """Update a user's password.

    Args:
        user_id: The ID of the user to update.
        password_hash: The new hashed password.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating password for user ID: {user_id}")
    query = """
    UPDATE users
    SET password_hash = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (password_hash, user_id))
    logger.info(f"Updated password for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def update_user_role(user_id: int, role: str) -> int:
    """Update a user's role.

    Args:
        user_id: The ID of the user to update.
        role: The new role for the user ('traveller', 'moderator', 'editor', 'admin', 'support_tech').

    Returns:
        int: Number of rows affected by the update operation.

    Raises:
        ValueError: If an invalid role is provided.
    """
    logger.info(f"Updating role for user ID: {user_id} to {role}")
    from utils.permissions import Roles
    valid_roles = [Roles.TRAVELLER, Roles.MODERATOR, Roles.EDITOR, Roles.ADMIN, Roles.SUPPORT_TECH]
    if role not in valid_roles:
        logger.error(f"Invalid role attempted: {role}")
        raise ValueError(f"Invalid role: {role}")

    query = """
    UPDATE users
    SET role = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (role, user_id))
    logger.info(f"Updated role for user ID: {user_id} to {role}, rows affected: {rows_affected}")
    return rows_affected

def update_user_block_status(user_id: int, is_blocked: bool) -> int:
    """Update a user's blocked status.

    Args:
        user_id: The ID of the user to update.
        is_blocked: Whether the user should be blocked or not.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating block status for user ID: {user_id} to {is_blocked}")
    query = """
    UPDATE users
    SET is_blocked = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (is_blocked, user_id))
    logger.info(f"Updated block status for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def update_user_ban_status(user_id: int, is_banned: bool) -> int:
    """Update a user's banned status.

    Args:
        user_id: The ID of the user to update.
        is_banned: Whether the user should be banned or not.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating ban status for user ID: {user_id} to {is_banned}")
    query = """
    UPDATE users
    SET is_banned = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (is_banned, user_id))
    logger.info(f"Updated ban status for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def search_users(search_term: str,
                limit: int = 50,
                offset: int = 0,
                filter_role: Optional[Union[str, List[str]]] = None,
                filter_blocked: Optional[bool] = None,
                filter_banned: Optional[bool] = None,
                public_only: bool = False) -> List[Dict[str, Any]]:
    """Search for users by username, email, or name with optional filters.

    Args:
        search_term: Search term to match.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        filter_role: Optional role or list of roles to filter by.
        filter_blocked: Optional filter for blocked status.
        filter_banned: Optional filter for banned status.
        public_only: If True, only return users who have set their profile as public.

    Returns:
        List[Dict[str, Any]]: List of matching user records.
    """
    logger.debug(f"Searching users with term: '{search_term}', limit: {limit}, offset: {offset}")
    search_pattern = f"%{search_term}%"
    base_query = """
    SELECT id, username, email, first_name, last_name,
           location, profile_image, role, is_blocked, is_banned,
           is_public, biography, interests
    FROM users
    WHERE (username LIKE %s OR email LIKE %s
       OR first_name LIKE %s OR last_name LIKE %s
       OR CONCAT(first_name, ' ', last_name) LIKE %s
       OR CONCAT(last_name, ' ', first_name) LIKE %s)
    """

    params = [search_pattern, search_pattern, search_pattern, search_pattern,
              search_pattern, search_pattern]

    # Add role filter if specified
    if filter_role:
        if isinstance(filter_role, list):
            placeholders = ', '.join(['%s'] * len(filter_role))
            base_query += f" AND role IN ({placeholders})"
            params.extend(filter_role)
            logger.debug(f"Filtering by roles: {filter_role}")
        else:
            base_query += " AND role = %s"
            params.append(filter_role)
            logger.debug(f"Filtering by role: {filter_role}")

    # Add blocked filter if specified
    if filter_blocked is not None:
        base_query += " AND is_blocked = %s"
        params.append(filter_blocked)
        logger.debug(f"Filtering by blocked status: {filter_blocked}")

    # Add banned filter if specified
    if filter_banned is not None:
        base_query += " AND is_banned = %s"
        params.append(filter_banned)
        logger.debug(f"Filtering by banned status: {filter_banned}")

    # Add public only filter if specified
    if public_only:
        base_query += " AND is_public = TRUE"
        logger.debug("Filtering by public status: TRUE")

    # Add ordering and limit
    query = base_query + " ORDER BY username LIMIT %s OFFSET %s"
    params.extend([limit, offset])

    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} users matching search criteria")
    return results or []

def count_search_users(search_term: str,
                     filter_role: Optional[Union[str, List[str]]] = None,
                     filter_blocked: Optional[bool] = None,
                     filter_banned: Optional[bool] = None,
                     public_only: bool = False) -> int:
    """Count users matching a search term with optional filters.

    Args:
        search_term: Search term to match.
        filter_role: Optional role or list of roles to filter by.
        filter_blocked: Optional filter for blocked status.
        filter_banned: Optional filter for banned status.
        public_only: If True, only count users who have set their profile as public.

    Returns:
        int: Count of matching users.
    """
    logger.debug(f"Counting users matching search term: '{search_term}'")
    search_pattern = f"%{search_term}%"
    base_query = """
    SELECT COUNT(*) as count
    FROM users
    WHERE (username LIKE %s OR email LIKE %s
       OR first_name LIKE %s OR last_name LIKE %s
       OR CONCAT(first_name, ' ', last_name) LIKE %s
       OR CONCAT(last_name, ' ', first_name) LIKE %s)
    """

    params = [search_pattern, search_pattern, search_pattern, search_pattern,
              search_pattern, search_pattern]

    # Add role filter if specified
    if filter_role:
        if isinstance(filter_role, list):
            placeholders = ', '.join(['%s'] * len(filter_role))
            base_query += f" AND role IN ({placeholders})"
            params.extend(filter_role)
            logger.debug(f"Filtering by roles: {filter_role}")
        else:
            base_query += " AND role = %s"
            params.append(filter_role)
            logger.debug(f"Filtering by role: {filter_role}")

    # Add blocked filter if specified
    if filter_blocked is not None:
        base_query += " AND is_blocked = %s"
        params.append(filter_blocked)
        logger.debug(f"Filtering by blocked status: {filter_blocked}")

    # Add banned filter if specified
    if filter_banned is not None:
        base_query += " AND is_banned = %s"
        params.append(filter_banned)
        logger.debug(f"Filtering by banned status: {filter_banned}")

    # Add public only filter if specified
    if public_only:
        base_query += " AND is_public = TRUE"
        logger.debug("Filtering by public status: TRUE")

    result = execute_query(base_query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} users matching search criteria")
    return count

def get_staff_accounts(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all staff accounts (editors, moderators, support techs, and admins) with pagination.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of staff account records.
    """
    logger.debug(f"Getting staff accounts with limit: {limit}, offset: {offset}")
    query = """
    SELECT id, username, email, first_name, last_name,
           location, profile_image, role, is_blocked, is_banned, created_at
    FROM users
    WHERE role IN ('editor', 'moderator', 'admin', 'support_tech')
    ORDER BY role, username
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} staff accounts")
    return results or []

def get_staff_accounts_count() -> int:
    """Get count of staff accounts.

    Returns:
        int: Count of staff accounts.
    """
    logger.debug("Counting staff accounts")
    query = """
    SELECT COUNT(*) as count
    FROM users
    WHERE role IN ('editor', 'moderator', 'admin', 'support_tech')
    """
    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} staff accounts")
    return count

def get_blocked_users(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all users who are blocked from sharing journeys with pagination.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of blocked user records.
    """
    logger.debug(f"Getting blocked users with limit: {limit}, offset: {offset}")
    query = """
    SELECT id, username, email, first_name, last_name,
           location, profile_image, role, is_blocked, is_banned, created_at
    FROM users
    WHERE is_blocked = TRUE
    ORDER BY username
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} blocked users")
    return results or []

def get_blocked_users_count() -> int:
    """Get count of blocked users.

    Returns:
        int: Count of blocked users.
    """
    logger.debug("Counting blocked users")
    query = """
    SELECT COUNT(*) as count
    FROM users
    WHERE is_blocked = TRUE
    """
    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} blocked users")
    return count

def get_banned_users(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all users who are banned from logging in with pagination.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of banned user records.
    """
    logger.debug(f"Getting banned users with limit: {limit}, offset: {offset}")
    query = """
    SELECT id, username, email, first_name, last_name,
           location, profile_image, role, is_blocked, is_banned, created_at
    FROM users
    WHERE is_banned = TRUE
    ORDER BY username
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} banned users")
    return results or []

def get_banned_users_count() -> int:
    """Get count of banned users.

    Returns:
        int: Count of banned users.
    """
    logger.debug("Counting banned users")
    query = """
    SELECT COUNT(*) as count
    FROM users
    WHERE is_banned = TRUE
    """
    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} banned users")
    return count

def check_user_exists(username: Optional[str] = None, email: Optional[str] = None) -> bool:
    """Check if a user exists with the given username or email.

    Args:
        username: Optional username to check.
        email: Optional email to check.

    Returns:
        bool: True if a user with the given username or email exists, False otherwise.
    """
    logger.debug(f"Checking if user exists with username: {username} or email: {email}")
    if username is None and email is None:
        logger.warning("check_user_exists called without username or email")
        return False

    conditions = []
    params = []

    if username:
        conditions.append("username = %s")
        params.append(username)

    if email:
        conditions.append("email = %s")
        params.append(email)

    query = f"""
    SELECT COUNT(*) as count
    FROM users
    WHERE {' OR '.join(conditions)}
    """

    result = execute_query(query, params, fetch_one=True)
    exists = result['count'] > 0 if result else False
    logger.debug(f"User exists: {exists}")
    return exists

def get_users(limit: int = 100, offset: int = 0, public_only: bool = False) -> List[Dict[str, Any]]:
    """Get all users with pagination.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        public_only: If True, only return users who have set their profile as public.

    Returns:
        List[Dict[str, Any]]: List of user records.
    """
    logger.debug(f"Getting all users with limit: {limit}, offset: {offset}, public_only: {public_only}")

    query = """
    SELECT id, username, email, first_name, last_name,
           location, profile_image, role, is_blocked, is_banned, is_public,
           biography, interests
    FROM users
    """

    params = []

    if public_only:
        query += " WHERE is_public = TRUE"
        logger.debug("Filtering by public status: TRUE")

    query += " ORDER BY username LIMIT %s OFFSET %s"
    params.extend([limit, offset])

    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} users")
    return results or []

def get_travellers(limit: int = 100, offset: int = 0, public_only: bool = False, exclude_user_id: int = None) -> List[Dict[str, Any]]:
    """Get all travellers with pagination.
    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        public_only: If True, only return travellers who have set their profile as public.
        exclude_user_id: If set, exclude this user from the results.
    Returns:
        List[Dict[str, Any]]: List of traveller records.
    """
    logger.debug(f"Getting all travellers with limit: {limit}, offset: {offset}")

    query = """
    SELECT id, username, email, first_name, last_name,
           location, profile_image, role, is_blocked, is_banned, is_public,
           biography, interests
    FROM users
    WHERE role = 'traveller'
    """
    params = []
    if public_only:
        query += " AND is_public = TRUE"
        logger.debug("Filtering by public status: TRUE")
    if exclude_user_id:
        query += " AND id != %s"
        params.append(exclude_user_id)
    query += " ORDER BY username LIMIT %s OFFSET %s"
    params.extend([limit, offset])

    results = execute_query(query, params, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} travellers")
    return results or []

def get_travellers_count(public_only: bool = False, exclude_user_id: int = None) -> int:
    """Get count of travellers.
    Returns:
        int: Count of travellers.
    """
    logger.debug("Counting travellers")
    query = """
    SELECT COUNT(*) as count
    FROM users
    WHERE role = 'traveller'
    """
    params = []
    if public_only:
        query += " AND is_public = TRUE"
        logger.debug("Filtering by public status: TRUE")
    if exclude_user_id:
        query += " AND id != %s"
        params.append(exclude_user_id)
    result = execute_query(query, params, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} travellers")
    return count

def get_total_users_count(public_only: bool = False) -> int:
    """Get total count of users.

    Args:
        public_only: If True, only count users who have set their profile as public.

    Returns:
        int: Total count of users.
    """
    logger.debug(f"Getting total user count, public_only: {public_only}")

    query = """
    SELECT COUNT(*) as count
    FROM users
    """

    if public_only:
        query += " WHERE is_public = TRUE"
        logger.debug("Filtering by public status: TRUE")

    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Total user count: {count}")
    return count

def get_user_blocked_status(user_id: int) -> Optional[bool]:
    """Get a user's blocked status by their ID.

    Args:
        user_id: The ID of the user to check.

    Returns:
        bool: User's blocked status if the user exists, None otherwise.
    """
    logger.debug(f"Getting blocked status for user ID: {user_id}")
    query = """
    SELECT is_blocked
    FROM users
    WHERE id = %s
    """
    result = execute_query(query, (user_id,), fetch_one=True)

    if result:
        logger.debug(f"Blocked status for user ID {user_id}: {result['is_blocked']}")
        return result['is_blocked']
    else:
        logger.warning(f"User with ID {user_id} not found when checking blocked status")
        return None

def update_user_free_trial_status(user_id: int, had_free_trial: bool) -> int:
    """Update a user's free trial status.

    Args:
        user_id: The ID of the user to update.
        had_free_trial: Whether the user has used their free trial.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating free trial status for user ID: {user_id} to {had_free_trial}")
    query = """
    UPDATE users
    SET had_free_trial = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (had_free_trial, user_id))
    logger.info(f"Updated free trial status for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def check_user_had_free_trial(user_id: int) -> bool:
    """Check if a user has already used their free trial.

    Args:
        user_id: The ID of the user to check.

    Returns:
        bool: True if the user has used their free trial, False otherwise.
    """
    logger.debug(f"Checking if user ID: {user_id} has used their free trial")
    query = """
    SELECT had_free_trial
    FROM users
    WHERE id = %s
    """
    result = execute_query(query, (user_id,), fetch_one=True)

    if result:
        had_trial = result['had_free_trial']
        logger.debug(f"User ID: {user_id} had_free_trial status: {had_trial}")
        return had_trial
    else:
        logger.warning(f"User with ID {user_id} not found when checking free trial status")
        return False

def toggle_user_public_status(user_id: int, is_public: bool) -> int:
    """Toggle a user's public visibility status.

    Args:
        user_id: The ID of the user to update.
        is_public: Whether the user's profile should be publicly visible.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Toggling public status for user ID: {user_id} to {is_public}")
    query = """
    UPDATE users
    SET is_public = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (is_public, user_id))
    logger.info(f"Toggled public status for user ID: {user_id}, rows affected: {rows_affected}")
    return rows_affected

def get_user_with_subscription_status(user_id: int) -> Optional[Dict[str, Any]]:
    """Get a user's basic info and subscription status together.
    Args:
        user_id: The user ID to search for.
    Returns:
        Dict[str, Any]: User data dictionary with 'subscription_status' key if found, None otherwise.
    """
    user = get_user_by_id(user_id)
    if not user:
        return None
    from data.subscription_data import get_subscription_status
    user['subscription_status'] = get_subscription_status(user_id)
    return user