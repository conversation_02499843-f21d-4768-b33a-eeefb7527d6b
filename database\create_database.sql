-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Drop tables if they exist to ensure a clean database
DROP TABLE IF EXISTS reports;
DROP TABLE IF EXISTS user_privacy_settings;
DROP TABLE IF EXISTS user_follows_journey;
DROP TABLE IF EXISTS user_follows_user;
DROP TABLE IF EXISTS user_follows_location;
DROP TABLE IF EXISTS helpdesk_requests;
DROP TABLE IF EXISTS helpdesk_replies;
DROP TABLE IF EXISTS event_destinations;
DROP TABLE IF EXISTS event_images;
DROP TABLE IF EXISTS event_likes;
DROP TABLE IF EXISTS comment_interactions;
DROP TABLE IF EXISTS event_comments;
DROP TABLE IF EXISTS private_messages;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS payments;
DROP TABLE IF EXISTS countries;
DROP TABLE IF EXISTS subscription_plans;
DROP TABLE IF EXISTS subscriptions;
DROP TABLE IF EXISTS edit_history;
DROP TABLE IF EXISTS edit_field_changes;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS journeys;
DROP TABLE IF EXISTS locations;
DROP TABLE IF EXISTS user_announcements;
DROP TABLE IF EXISTS announcements;
DROP TABLE IF EXISTS users;

-- Create database
CREATE DATABASE IF NOT EXISTS travel_journal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE travel_journal;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(20) NOT NULL UNIQUE,
    email VARCHAR(320) NOT NULL UNIQUE COMMENT 'Maximum email address length according to RFC5321',
    password_hash char(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Bcrypt Password Hash and Salt (60 bytes)',
    first_name VARCHAR(50) DEFAULT NULL,
    last_name VARCHAR(50) DEFAULT NULL,
    location VARCHAR(128) DEFAULT NULL,
    description TEXT DEFAULT NULL COMMENT 'Short profile tagline or motto (1-2 sentences)',
    biography TEXT DEFAULT NULL COMMENT 'Extended user biography (detailed personal information)',
    profile_image VARCHAR(255) DEFAULT NULL COMMENT 'Profile image filename',
    role ENUM('traveller', 'moderator', 'editor', 'admin', 'support_tech') NOT NULL DEFAULT 'traveller',
    is_blocked BOOLEAN NOT NULL DEFAULT FALSE,
    is_banned BOOLEAN NOT NULL DEFAULT FALSE,
    is_public BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether user appears in public listings',
    interests VARCHAR(255) DEFAULT NULL COMMENT 'User interests',
    had_free_trial BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether user has used their free trial',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_users_search (username, email, first_name, last_name),
    INDEX idx_users_role (role)
) ENGINE=InnoDB;

-- User privacy settings table (without timestamps)
CREATE TABLE user_privacy_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    attribute_name VARCHAR(50) NOT NULL,
    visibility ENUM('private', 'public') NOT NULL DEFAULT 'public',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_attribute (user_id, attribute_name),
    INDEX idx_privacy_settings_user (user_id),
    INDEX idx_privacy_settings_visibility (visibility)
) ENGINE=InnoDB;

CREATE TABLE subscription_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    plan_code VARCHAR(20) NOT NULL UNIQUE COMMENT 'Code for the plan (e.g., one_month, one_quarter)',
    name VARCHAR(50) NOT NULL COMMENT 'Display name for the plan',
    description TEXT DEFAULT NULL COMMENT 'Description of the plan benefits',
    period_months INT NOT NULL COMMENT 'Duration in months',
    base_price DECIMAL(10, 2) NOT NULL COMMENT 'Base price without GST in default currency',
    discount_percentage DECIMAL(5, 2) DEFAULT 0 COMMENT 'Discount percentage (0-100)',
    is_premium BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this plan grants premium features',
    is_selectable BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether this plan is shown as a selectable option to customers',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the plan is available for purchase',
    display_order INT DEFAULT 0 COMMENT 'Order to display plans in UI',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_subscription_plans_active (is_active),
    INDEX idx_subscription_plans_order (display_order)
) ENGINE=InnoDB;

-- Subscriptions table
CREATE TABLE subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan_code VARCHAR(20) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    months INT DEFAULT NULL COMMENT 'Duration in months',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    reason TEXT DEFAULT NULL COMMENT 'Reason for the subscription (e.g., gift, purchase)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (plan_code) REFERENCES subscription_plans(plan_code),
    INDEX idx_subscriptions_user (user_id),
    INDEX idx_subscriptions_date (end_date)
) ENGINE=InnoDB;

-- Payments table and countries table
CREATE TABLE countries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    code VARCHAR(3) NOT NULL DEFAULT 'NZD',
    has_gst BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether the country applies GST/VAT',
    gst_rate DECIMAL(5,2) DEFAULT NULL COMMENT 'VAT/GST rate if applicable',
    currency_code VARCHAR(3) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_countries_code (code)
) ENGINE=InnoDB;

CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    gst_amount DECIMAL(10, 2) DEFAULT NULL,
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_method VARCHAR(50) NOT NULL DEFAULT 'credit_card',
    billing_address TEXT,
    country_id INT NOT NULL COMMENT 'Country ID for billing address',
    card_last_four VARCHAR(4),
    transaction_reference VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE,
    FOREIGN KEY (country_id) REFERENCES countries(id),
    INDEX idx_payments_user (user_id),
    INDEX idx_payments_date (payment_date)
) ENGINE=InnoDB;

-- Locations table
CREATE TABLE locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(128) NOT NULL UNIQUE,
    latitude DECIMAL(10, 8) DEFAULT NULL,
    longitude DECIMAL(11, 8) DEFAULT NULL,
    map_zoom_level INT DEFAULT 13,
    location_type ENUM('city', 'point_of_interest', 'airport', 'accommodation', 'transportation', 'custom') DEFAULT 'custom',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_locations_name (name)
    -- SPATIAL INDEX idx_location_coordinates (latitude, longitude)
) ENGINE=InnoDB;

-- Journeys table
CREATE TABLE journeys (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(128) NOT NULL,
    description TEXT,
    start_date DATE NOT NULL,
    visibility ENUM('private', 'public', 'published') NOT NULL DEFAULT 'private',
    is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
    cover_image VARCHAR(255) DEFAULT NULL COMMENT 'Cover image filename',
    no_edits BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether editors can edit this journey',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_journeys_user (user_id),
    INDEX idx_journeys_visibility (visibility, is_hidden),
    INDEX idx_journeys_updated (updated_at),
    FULLTEXT INDEX idx_journey_search (title, description)
) ENGINE=InnoDB;

-- Events table
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    journey_id INT NOT NULL,
    location_id INT,
    title VARCHAR(128) NOT NULL,
    description TEXT,
    start_datetime DATETIME NOT NULL,
    end_datetime DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (journey_id) REFERENCES journeys(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE SET NULL,
    INDEX idx_events_journey (journey_id),
    INDEX idx_events_location (location_id),
    INDEX idx_events_datetime (start_datetime)
) ENGINE=InnoDB;

-- Event destinations table
CREATE TABLE event_destinations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    destination_location_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (destination_location_id) REFERENCES locations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_destination (event_id),
    INDEX idx_event_destination_location (destination_location_id)
) ENGINE=InnoDB;

-- Event images table (renamed to use 'filename' for clarity)
CREATE TABLE event_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    image_filename VARCHAR(255) NOT NULL COMMENT 'Image filename without path',
    caption TEXT,
    is_primary BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_event_images_event (event_id),
    INDEX idx_event_images_primary (is_primary)
) ENGINE=InnoDB;

-- Event likes table
CREATE TABLE event_likes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_user_like (event_id, user_id),
    INDEX idx_event_likes_event (event_id),
    INDEX idx_event_likes_user (user_id)
) ENGINE=InnoDB;

-- Event comments table
CREATE TABLE event_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_event_comments_event (event_id),
    INDEX idx_event_comments_user (user_id),
    INDEX idx_event_comments_hidden (is_hidden)
) ENGINE=InnoDB;

-- Comment interactions table (for likes and dislikes only, NOT reports)
CREATE TABLE comment_interactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comment_id INT NOT NULL,
    user_id INT NOT NULL,
    interaction_type ENUM('like', 'dislike') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (comment_id) REFERENCES event_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_comment_user_interaction (comment_id, user_id, interaction_type),
    INDEX idx_comment_interactions_comment (comment_id),
    INDEX idx_comment_interactions_user (user_id),
    INDEX idx_comment_interactions_type (interaction_type)
) ENGINE=InnoDB;

-- Generic reports table for all content types
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reporter_id INT NOT NULL,
    content_type ENUM('comment', 'event', 'journey', 'event_image', 'journey_image', 'user', 'location') NOT NULL,
    content_id INT NOT NULL,
    reason TEXT NOT NULL,
    status ENUM('new', 'dismissed', 'resolved', 'open') NOT NULL DEFAULT 'new',
    escalated_to_admin BOOLEAN NOT NULL DEFAULT FALSE,
    reviewed_by INT DEFAULT NULL,
    reviewed_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_content_report (reporter_id, content_type, content_id),
    INDEX idx_reports_status (status),
    INDEX idx_reports_content (content_type, content_id),
    INDEX idx_reports_created (created_at)
) ENGINE=InnoDB;

-- Private messages table
CREATE TABLE private_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    content TEXT NOT NULL,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_messages_sender (sender_id),
    INDEX idx_messages_recipient (recipient_id),
    INDEX idx_messages_read (is_read)
) ENGINE=InnoDB;

-- Main edit history table for the primary edit record
CREATE TABLE edit_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    editor_id INT NOT NULL,
    content_type ENUM('journey', 'event', 'location') NOT NULL,
    content_id INT NOT NULL,
    reason TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (editor_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_edit_history_content (content_type, content_id),
    INDEX idx_edit_history_editor (editor_id)
) ENGINE=InnoDB;

-- Table for individual field changes in each edit
CREATE TABLE edit_field_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    edit_id INT NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    old_value TEXT,
    new_value TEXT,
    FOREIGN KEY (edit_id) REFERENCES edit_history(id) ON DELETE CASCADE,
    INDEX idx_field_changes_edit (edit_id)
) ENGINE=InnoDB;

-- Notifications table
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notification_type ENUM('edit', 'comment', 'like', 'dislike', 'message', 'subscription', 'report', 'appeal','helpdesk') NOT NULL,
    content TEXT NOT NULL,
    related_id INT,
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_notifications_user (user_id),
    INDEX idx_notifications_read (is_read),
    INDEX idx_notifications_type (notification_type)
) ENGINE=InnoDB;

-- Announcements table
CREATE TABLE announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    author_id INT NOT NULL,
    title VARCHAR(128) NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_announcements_author (author_id)
) ENGINE=InnoDB;

-- User_Announcements junction table
CREATE TABLE user_announcements (
    user_id INT NOT NULL,
    announcement_id INT NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, announcement_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (announcement_id) REFERENCES announcements(id) ON DELETE CASCADE
) ENGINE=InnoDB;

-- User follows Journey table (Epic 4: Departure Board)
CREATE TABLE user_follows_journey (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    journey_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (journey_id) REFERENCES journeys(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_journey_follow (user_id, journey_id),
    INDEX idx_follows_journey_user (user_id),
    INDEX idx_follows_journey_journey (journey_id)
) ENGINE=InnoDB;

-- User follows User table (Epic 4: Departure Board)
CREATE TABLE user_follows_user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    follower_id INT NOT NULL,
    followed_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (followed_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_user_follow (follower_id, followed_id),
    INDEX idx_follows_user_follower (follower_id),
    INDEX idx_follows_user_followed (followed_id)
) ENGINE=InnoDB;

-- User follows Location table (Epic 4: Departure Board)
CREATE TABLE user_follows_location (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    location_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_location_follow (user_id, location_id),
    INDEX idx_follows_location_user (user_id),
    INDEX idx_follows_location_location (location_id)
) ENGINE=InnoDB;

-- Helpdesk Requests table (Epic 6: Helpdesk)
CREATE TABLE helpdesk_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    request_type ENUM('help', 'bug', 'appeal', 'other') NOT NULL DEFAULT 'help',
    status ENUM('new', 'open', 'stalled', 'resolved', 'approved', 'rejected') NOT NULL DEFAULT 'new',
    assigned_to INT DEFAULT NULL COMMENT 'Staff user ID of the assigned support person',
    appeal_type ENUM('hidden_journey', 'sharing_block', 'ban') DEFAULT NULL COMMENT 'Type of appeal if request_type is appeal',
    related_id INT DEFAULT NULL COMMENT 'ID of related item (e.g., journey ID for hidden_journey appeal)',
    admin_response TEXT DEFAULT NULL COMMENT 'Response from admin for appeals or complex issues',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_helpdesk_user (user_id),
    INDEX idx_helpdesk_status (status),
    INDEX idx_helpdesk_assigned (assigned_to),
    INDEX idx_helpdesk_type (request_type),
    INDEX idx_helpdesk_appeal_type (appeal_type)
) ENGINE=InnoDB;

-- Helpdesk Replies table (Epic 6: Helpdesk)
CREATE TABLE helpdesk_replies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    is_internal BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Whether this reply is only visible to staff',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES helpdesk_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_helpdesk_replies_request (request_id),
    INDEX idx_helpdesk_replies_user (user_id)
) ENGINE=InnoDB;

-- =============================================
-- VIEWS
-- =============================================

-- View for public journeys
CREATE OR REPLACE VIEW public_journeys AS
SELECT j.*, u.username, u.first_name, u.last_name
FROM journeys j
JOIN users u ON j.user_id = u.id
WHERE j.visibility = 'public' AND j.is_hidden = FALSE
ORDER BY j.updated_at DESC;

-- View for published journeys (for homepage)
CREATE OR REPLACE VIEW published_journeys AS
SELECT j.*, u.username, u.first_name, u.last_name
FROM journeys j
JOIN users u ON j.user_id = u.id
WHERE j.visibility = 'published' AND j.is_hidden = FALSE
ORDER BY j.updated_at DESC;

-- View for hidden journeys (for admins/editors)
CREATE OR REPLACE VIEW hidden_journeys AS
SELECT j.*, u.username, u.first_name, u.last_name
FROM journeys j
JOIN users u ON j.user_id = u.id
WHERE j.is_hidden = TRUE
ORDER BY j.user_id;

-- View for active premium users
CREATE OR REPLACE VIEW active_premium_users AS
SELECT u.id, u.username, s.plan_code, s.end_date
FROM users u
JOIN subscriptions s ON u.id = s.user_id
WHERE s.is_active = TRUE AND s.end_date >= CURDATE()
ORDER BY s.end_date;

-- View for staff accounts
CREATE OR REPLACE VIEW staff_accounts AS
SELECT id, username, email, first_name, last_name, role, created_at
FROM users
WHERE role IN ('moderator', 'editor', 'admin', 'support_tech')
ORDER BY role, username;

-- View for blocked users
CREATE OR REPLACE VIEW blocked_users AS
SELECT id, username, email, first_name, last_name, is_blocked, created_at
FROM users
WHERE is_blocked = TRUE 
ORDER BY username;

-- View for banned users
CREATE OR REPLACE VIEW banned_users AS
SELECT id, username, email, first_name, last_name, is_banned, created_at
FROM users
WHERE is_banned = TRUE
ORDER BY username;

-- View for appeals
CREATE OR REPLACE VIEW user_appeals AS
SELECT 
    hr.id,
    hr.user_id,
    u.username,
    hr.subject,
    hr.description,
    hr.appeal_type,
    hr.related_id,
    hr.status,
    hr.admin_response,
    hr.assigned_to,
    staff.username AS assigned_to_username,
    hr.created_at,
    hr.updated_at,
    hr.resolved_at
FROM 
    helpdesk_requests hr
JOIN 
    users u ON hr.user_id = u.id
LEFT JOIN
    users staff ON hr.assigned_to = staff.id
WHERE 
    hr.request_type = 'appeal'
ORDER BY
    CASE 
        WHEN hr.status = 'new' THEN 0
        WHEN hr.status = 'open' THEN 1
        WHEN hr.status = 'stalled' THEN 2
        WHEN hr.status = 'approved' THEN 3
        WHEN hr.status = 'rejected' THEN 4
        WHEN hr.status = 'resolved' THEN 5
    END,
    hr.created_at DESC;

-- View for reported content
CREATE OR REPLACE VIEW reported_content AS
SELECT 
    r.id, 
    r.content_type, 
    r.content_id,
    r.reason,
    r.status,
    r.created_at,
    u.username AS reporter_username,
    r.reporter_id,
    CASE 
        WHEN r.content_type = 'comment' THEN (
            SELECT CONCAT(e.title, ' (', j.title, ')') 
            FROM event_comments ec
            JOIN events e ON ec.event_id = e.id
            JOIN journeys j ON e.journey_id = j.id
            WHERE ec.id = r.content_id
        )
        WHEN r.content_type = 'event' THEN (
            SELECT CONCAT(e.title, ' (', j.title, ')') 
            FROM events e
            JOIN journeys j ON e.journey_id = j.id
            WHERE e.id = r.content_id
        )
        WHEN r.content_type = 'journey' THEN (
            SELECT title FROM journeys WHERE id = r.content_id
        )
        WHEN r.content_type = 'image' THEN (
            SELECT CONCAT('Image in ', e.title)
            FROM event_images ei
            JOIN events e ON ei.event_id = e.id
            WHERE ei.id = r.content_id
        )
        WHEN r.content_type = 'user' THEN (
            SELECT username FROM users WHERE id = r.content_id
        )
        WHEN r.content_type = 'location' THEN (
            SELECT name FROM locations WHERE id = r.content_id
        )
        ELSE 'Unknown content'
    END AS content_description,
    CASE 
        WHEN r.content_type = 'comment' THEN (
            SELECT user_id FROM event_comments WHERE id = r.content_id
        )
        WHEN r.content_type = 'event' THEN (
            SELECT j.user_id FROM events e JOIN journeys j ON e.journey_id = j.id WHERE e.id = r.content_id
        )
        WHEN r.content_type = 'journey' THEN (
            SELECT user_id FROM journeys WHERE id = r.content_id
        )
        WHEN r.content_type = 'image' THEN (
            SELECT j.user_id 
            FROM event_images ei
            JOIN events e ON ei.event_id = e.id
            JOIN journeys j ON e.journey_id = j.id
            WHERE ei.id = r.content_id
        )
        WHEN r.content_type = 'user' THEN r.content_id
        ELSE NULL
    END AS content_owner_id
FROM 
    reports r
JOIN 
    users u ON r.reporter_id = u.id
ORDER BY 
    CASE 
        WHEN r.status = 'new' THEN 0
        WHEN r.status = 'reviewed' THEN 1
        WHEN r.status = 'dismissed' THEN 2
        WHEN r.status = 'actioned' THEN 3
    END, 
    r.created_at DESC;

-- View for departure board (Epic 4: Departure Board)
CREATE OR REPLACE VIEW departure_board_events AS
SELECT 
    e.id AS event_id,
    e.title AS event_title,
    e.description AS event_description,
    e.start_datetime,
    e.end_datetime,
    e.updated_at,
    j.id AS journey_id,
    j.title AS journey_title,
    j.visibility,
    u.id AS user_id,
    u.username,
    u.profile_image,
    l.id AS location_id,
    l.name AS location_name,
    'journey' AS follow_type,
    ufj.user_id AS follower_id
FROM 
    events e
JOIN 
    journeys j ON e.journey_id = j.id
JOIN 
    users u ON j.user_id = u.id
LEFT JOIN 
    locations l ON e.location_id = l.id
JOIN 
    user_follows_journey ufj ON j.id = ufj.journey_id
WHERE 
    j.visibility IN ('public', 'published') AND 
    j.is_hidden = FALSE

UNION

SELECT 
    e.id AS event_id,
    e.title AS event_title,
    e.description AS event_description,
    e.start_datetime,
    e.end_datetime,
    e.updated_at,
    j.id AS journey_id,
    j.title AS journey_title,
    j.visibility,
    u.id AS user_id,
    u.username,
    u.profile_image,
    l.id AS location_id,
    l.name AS location_name,
    'user' AS follow_type,
    ufu.follower_id
FROM 
    events e
JOIN 
    journeys j ON e.journey_id = j.id
JOIN 
    users u ON j.user_id = u.id
LEFT JOIN 
    locations l ON e.location_id = l.id
JOIN 
    user_follows_user ufu ON j.user_id = ufu.followed_id
WHERE 
    j.visibility IN ('public', 'published') AND 
    j.is_hidden = FALSE

UNION

SELECT 
    e.id AS event_id,
    e.title AS event_title,
    e.description AS event_description,
    e.start_datetime,
    e.end_datetime,
    e.updated_at,
    j.id AS journey_id,
    j.title AS journey_title,
    j.visibility,
    u.id AS user_id,
    u.username,
    u.profile_image,
    l.id AS location_id,
    l.name AS location_name,
    'location' AS follow_type,
    ufl.user_id AS follower_id
FROM 
    events e
JOIN 
    journeys j ON e.journey_id = j.id
JOIN 
    users u ON j.user_id = u.id
JOIN 
    locations l ON e.location_id = l.id
JOIN 
    user_follows_location ufl ON l.id = ufl.location_id
WHERE 
    j.visibility IN ('public', 'published') AND 
    j.is_hidden = FALSE;

-- -- View for payments with country details
-- CREATE OR REPLACE VIEW payments_with_country AS
-- SELECT
--     p.*,
--     c.name AS country_name,
--     c.code AS country_code,
--     c.has_gst,
--     c.gst_rate,
--     c.currency_code
-- FROM
--     payments p
-- JOIN
--     countries c ON p.country_id = c.id;
CREATE OR REPLACE VIEW payments_with_subscription_and_country AS
SELECT
    p.*,
    s.plan_code,
    s.start_date AS subscription_start,
    s.end_date AS subscription_end,
    s.months AS subscription_months,
    s.reason AS subscription_reason,
    sp.name AS plan_name,
    sp.base_price,
    sp.discount_percentage,
    c.name AS country_name,
    c.code AS country_code,
    c.has_gst,
    c.gst_rate,
    c.currency_code
FROM
    payments p
JOIN
    subscriptions s ON p.subscription_id = s.id
JOIN
    subscription_plans sp ON s.plan_code = sp.plan_code
JOIN
    countries c ON p.country_id = c.id;
    
ALTER TABLE helpdesk_requests
MODIFY COLUMN user_id INT NULL;


