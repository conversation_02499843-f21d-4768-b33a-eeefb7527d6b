"""
Database Initialization Script

This script initializes the database by creating the schema and populating it with sample data.
It should be run once when setting up the application for the first time.
"""

import os
import mysql.connector
from mysql.connector import Error
import sys
import logging
import time
from datetime import datetime

# Set up logging
LOG_DIR = "logs"
os.makedirs(LOG_DIR, exist_ok=True)
log_filename = f"db_init_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
log_path = os.path.join(LOG_DIR, log_filename)

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import database connection information
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from connect import DB_USER, DB_PASSWORD, DB_HOST, DB_PORT
    logger.info(f"Successfully imported database connection settings from connect.py")
except ImportError:
    logger.error("Could not import database connection settings.")
    logger.error("Make sure connect.py exists with DB_USER, DB_PASSWORD, DB_HOST, and DB_PORT variables.")
    sys.exit(1)

# Database name
DB_NAME = "travel_journal"

# Check if we're running on PythonAnywhere
def is_pythonanywhere():
    """
    Check if we're running on PythonAnywhere
    
    Returns:
        boolean indicating if running on PythonAnywhere
    """
    return 'PYTHONANYWHERE_DOMAIN' in os.environ

def get_connection(database=None):
    """
    Establish a connection to the MySQL server
    
    Args:
        database: Optional database name to connect to
        
    Returns:
        MySQL connection object or None if connection failed
    """
    connection_params = {
        'host': DB_HOST,
        'user': DB_USER,
        'password': DB_PASSWORD
    }
    
    if is_pythonanywhere():
        # For PythonAnywhere, database names follow the format username$dbname
        if database:
            # Extract username from DB_USER or use it directly
            username = DB_USER
            connection_params['database'] = f"{username}${database}"
        # No need for port on PythonAnywhere
    else:
        # Standard connection for local development
        connection_params['port'] = DB_PORT
        if database:
            connection_params['database'] = database
    
    try:
        connection = mysql.connector.connect(**connection_params)
        if connection.is_connected():
            logger.debug(f"Successfully connected to MySQL {'server' if not database else f'database {database}'}")
            # Set a shorter timeout to avoid PythonAnywhere's 5-minute connection timeout
            cursor = connection.cursor()
            cursor.execute("SET SESSION wait_timeout=280")  # 4:40 minutes
            cursor.close()
            return connection
    except Error as e:
        logger.error(f"Error connecting to MySQL: {e}")
        return None

def execute_sql_file(connection, file_path):
    """
    Execute SQL commands from a file
    
    Args:
        connection: MySQL connection object
        file_path: Path to the SQL file
        
    Returns:
        boolean indicating success
    """
    cursor = None
    try:
        # Read the SQL file
        logger.info(f"Reading SQL file: {file_path}")
        with open(file_path, 'r') as file:
            sql_script = file.read()
            logger.debug(f"SQL script loaded, size: {len(sql_script)} bytes")
        
        # Split by semicolon to get individual commands
        commands = [cmd.strip() for cmd in sql_script.split(';') if cmd.strip()]
        logger.info(f"Found {len(commands)} SQL commands to execute")
        
        cursor = connection.cursor()
        
        successful_commands = 0
        failed_commands = 0
        
        for i, command in enumerate(commands):
            try:
                logger.debug(f"Executing command {i+1}/{len(commands)}: {command[:60]}...")
                cursor.execute(command)
                successful_commands += 1
            except Error as e:
                failed_commands += 1
                logger.warning(f"Error executing command {i+1}: {e}")
                logger.warning(f"Command: {command[:200]}...")
                # Continue with other commands despite error
        
        connection.commit()
        logger.info(f"SQL execution completed: {successful_commands} successful, {failed_commands} failed")
        
        return failed_commands == 0
    
    except Error as e:
        if connection.is_connected():
            connection.rollback()
        logger.error(f"Database error during SQL execution: {e}")
        return False
    
    except IOError as e:
        logger.error(f"Error reading SQL file {file_path}: {e}")
        return False
    
    except Exception as e:
        logger.error(f"Unexpected error during SQL execution: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()

def create_database():
    """
    Create the database if it doesn't exist
    
    Returns:
        boolean indicating success
    """
    conn = get_connection()
    if not conn:
        logger.error("Failed to connect to MySQL server. Cannot create database.")
        return False
    
    cursor = None
    try:
        cursor = conn.cursor()
        
        # Check if we're on PythonAnywhere
        if is_pythonanywhere():
            # We'll just check if the database exists
            username = DB_USER
            db_name = f"{username}${DB_NAME}"
            cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
            result = cursor.fetchone()
            
            if result:
                logger.info(f"Database '{db_name}' exists on PythonAnywhere")
                return True
            else:
                logger.error(f"Database '{db_name}' does not exist on PythonAnywhere.")
                logger.error("Please create it through the PythonAnywhere web interface first.")
                return False
        else:
            # Check if database exists
            cursor.execute(f"SHOW DATABASES LIKE '{DB_NAME}'")
            result = cursor.fetchone()
            
            if result:
                logger.info(f"Database '{DB_NAME}' already exists")
            else:
                logger.info(f"Creating database '{DB_NAME}'...")
                cursor.execute(f"CREATE DATABASE {DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                logger.info(f"Database '{DB_NAME}' created successfully")
            
            return True
    
    except Error as e:
        logger.error(f"Error creating database: {e}")
        return False
    
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()
            logger.debug("Database connection closed")

def main():
    """Main function to initialize database"""
    start_time = time.time()
    logger.info("=== Database Initialization Started ===")
    
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to SQL files
    create_db_file = os.path.join(script_dir, "create_database.sql")
    populate_db_file = os.path.join(script_dir, "populate_database.sql")
    
    # Check if files exist
    if not os.path.exists(create_db_file):
        logger.error(f"Error: {create_db_file} does not exist.")
        return
    
    if not os.path.exists(populate_db_file):
        logger.warning(f"Warning: {populate_db_file} does not exist. Database will not be populated with sample data.")
    
    # Step 1: Create the database
    if not create_database():
        logger.error("Failed to create database. Aborting initialization.")
        return
    
    # Step 2: Execute schema creation script
    logger.info("Creating database schema...")
    conn = get_connection(DB_NAME)
    if not conn:
        logger.error(f"Failed to connect to database '{DB_NAME}'. Aborting initialization.")
        return
    
    schema_success = execute_sql_file(conn, create_db_file)
    conn.close()
    
    if not schema_success:
        logger.warning("Schema creation completed with errors.")
    else:
        logger.info("Schema creation completed successfully.")
    
    # Step 3: Populate database with sample data
    if os.path.exists(populate_db_file):
        logger.info("Populating database with sample data...")
        conn = get_connection(DB_NAME)
        if not conn:
            logger.error(f"Failed to connect to database '{DB_NAME}'. Skipping data population.")
        else:
            data_success = execute_sql_file(conn, populate_db_file)
            conn.close()
            
            if not data_success:
                logger.warning("Database population completed with errors.")
            else:
                logger.info("Database population completed successfully.")
    
    end_time = time.time()
    duration = end_time - start_time
    logger.info(f"=== Database Initialization Completed in {duration:.2f} seconds ===")
    logger.info(f"Log file created at: {log_path}")

if __name__ == "__main__":
    main()