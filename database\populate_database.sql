-- This populate database with sample data for the Premium Footprints Travel Journal application
-- This script adds test data including users, journeys, events, locations, and additional premium features

-- Sample users (passwords are 'Password123!')
-- Actual hash for 'Password123!' is '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC'

-- Travellers (20)
INSERT INTO users (username, email, password_hash, first_name, last_name, location, role, description, profile_image, is_public, biography, interests, had_free_trial, created_at, updated_at)
VALUES
('traveller1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', '<PERSON>', '<PERSON>', 'New York, USA', 'traveller', 'I love exploring new cities and trying local foods.', 'traveller1.png', TRUE, 'Travel blogger and food enthusiast with a passion for urban exploration. Born in Chicago but now based in New York.', 'Food tourism, architecture, city life', TRUE, '2023-01-15 10:30:45', '2023-01-15 10:30:45'),
('traveller2', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Emily', 'Johnson', 'London, UK', 'traveller', 'Passionate about hiking and nature photography.', 'traveller2.png', TRUE, 'Professional photographer specializing in landscapes and wildlife. I\'ve visited over 30 countries and hiked some of the world\'s most challenging trails.', 'Photography, hiking, wildlife, conservation', TRUE, '2023-01-20 14:22:15', '2023-01-20 14:22:15'),
('traveller3', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Michael', 'Brown', 'Sydney, Australia', 'traveller', 'Beach lover and surfing enthusiast.', 'traveller3.png', TRUE, 'Professional surfer and ocean conservationist. I travel the world in search of the perfect wave while raising awareness about ocean pollution.', 'Surfing, ocean conservation, beach life', FALSE, '2023-01-25 09:15:30', '2023-01-25 09:15:30'),
('traveller4', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Jessica', 'Davis', 'Paris, France', 'traveller', 'Art and history buff, always visiting museums.', 'traveller4.png', TRUE, 'Art historian with a focus on Renaissance paintings. I work as a tour guide in Paris and spend my free time exploring Europe\'s cultural capitals.', 'Art history, museums, architecture, European culture', FALSE, '2023-02-01 16:45:22', '2023-02-01 16:45:22'),
('traveller5', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'David', 'Miller', 'Tokyo, Japan', 'traveller', 'Foodie exploring cuisines around the world.', 'traveller5.png', TRUE, 'Former chef turned culinary travel writer. I\'ve written three cookbooks based on my international food discoveries.', 'Global cuisines, cooking, food markets, culinary history', TRUE, '2023-02-05 11:20:35', '2023-02-05 11:20:35'),
('traveller6', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Sarah', 'Wilson', 'Vancouver, Canada', 'traveller', 'Mountain climbing and winter sports enthusiast.', 'traveller6.png', TRUE, 'Professional mountain guide with expeditions to six of the Seven Summits. Planning to complete the set with Everest next year.', 'Mountaineering, skiing, rock climbing, wilderness survival', FALSE, '2023-02-10 08:30:25', '2023-02-10 08:30:25'),
('traveller7', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'James', 'Taylor', 'Berlin, Germany', 'traveller', 'Love exploring historical sites and architecture.', 'traveller7.png', TRUE, 'Architectural historian focusing on post-war European reconstruction. Currently researching Berlin\'s architectural evolution since the fall of the Wall.', 'Architecture, urban planning, European history, photography', TRUE, '2023-02-15 13:45:30', '2023-02-15 13:45:30'),
('traveller8', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Jennifer', 'Anderson', 'Rome, Italy', 'traveller', 'Passionate about ancient history and archaeology.', 'traveller8.png', FALSE, 'Archaeologist specializing in Roman civilization. I\'ve participated in digs throughout Italy and the Mediterranean.', 'Archaeology, ancient history, Mediterranean cultures', FALSE, '2023-02-20 10:10:22', '2023-02-20 10:10:22'),
('traveller9', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Robert', 'Thomas', 'Barcelona, Spain', 'traveller', 'Beach and nightlife enthusiast.', 'traveller9.png', TRUE, 'DJ and event organizer who travels the world\'s best party destinations. I rate cities by their beaches and club scenes.', 'Beach clubs, electronic music, nightlife, festival culture', FALSE, '2023-02-25 15:25:18', '2023-02-25 15:25:18'),
('traveller10', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Elizabeth', 'Jackson', 'Amsterdam, Netherlands', 'traveller', 'Cycling around the world one city at a time.', 'traveller10.png', TRUE, 'Cycling advocate who has pedaled through 45 countries on six continents. I promote sustainable travel and bike-friendly urban planning.', 'Cycling, sustainable transportation, urban planning, minimalist travel', TRUE, '2023-03-01 12:40:35', '2023-03-01 12:40:35'),
('traveller11', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'William', 'White', 'Stockholm, Sweden', 'traveller', 'Winter travel specialist.', 'traveller11.png', TRUE, 'Polar expedition guide who has led tours to both the Arctic and Antarctic. Expert in winter survival and northern lights photography.', 'Arctic exploration, northern lights, winter sports, photography', FALSE, '2023-03-05 09:55:40', '2023-03-05 09:55:40'),
('traveller12', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Olivia', 'Harris', 'Prague, Czech Republic', 'traveller', 'Photography enthusiast capturing Gothic architecture.', 'traveller12.png', TRUE, 'Architectural photographer specializing in Gothic and Baroque structures. My work has been featured in several architecture magazines.', 'Architecture photography, Gothic art, historical buildings', FALSE, '2023-03-10 14:15:20', '2023-03-10 14:15:20'),
('traveller13', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Daniel', 'Martin', 'Istanbul, Turkey', 'traveller', 'Cultural explorer and food enthusiast.', 'traveller13.png', TRUE, 'Culinary anthropologist studying the relationship between food traditions and cultural identity. I explore marketplaces and home kitchens around the world.', 'Food anthropology, culinary traditions, spice markets, cooking classes', TRUE, '2023-03-15 10:30:15', '2023-03-15 10:30:15'),
('traveller14', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Sophia', 'Thompson', 'Seoul, South Korea', 'traveller', 'K-pop fan traveling Asia.', 'traveller14.png', TRUE, 'Music journalist covering the K-pop scene and Asian music trends. I attend concerts and music festivals throughout Asia.', 'K-pop, music journalism, Asian pop culture, concert photography', FALSE, '2023-03-20 15:45:10', '2023-03-20 15:45:10'),
('traveller15', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Matthew', 'Garcia', 'Bangkok, Thailand', 'traveller', 'Backpacker on a budget seeing the world.', 'traveller15.png', FALSE, 'Digital nomad who has been traveling continuously for five years on less than $25 per day. I write a popular budget travel blog.', 'Budget travel, hostels, street food, digital nomad lifestyle', TRUE, '2023-03-25 11:20:35', '2023-03-25 11:20:35'),
('traveller16', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Ava', 'Martinez', 'Buenos Aires, Argentina', 'traveller', 'Tango dancer exploring South America.', 'traveller16.png', TRUE, 'Professional dancer touring South America\'s dance scenes. I learn local dance styles in each country I visit and teach tango workshops.', 'Tango, Latin dance, cultural festivals, dance history', FALSE, '2023-04-01 08:30:22', '2023-04-01 08:30:22'),
('traveller17', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Christopher', 'Robinson', 'Cairo, Egypt', 'traveller', 'Ancient history buff and desert explorer.', 'traveller17.png', TRUE, 'Egyptologist who has worked on archaeological digs throughout Egypt and the Middle East. I specialize in ancient Egyptian religious practices.', 'Egyptology, desert exploration, ancient religions, hieroglyphics', TRUE, '2023-04-05 13:45:30', '2023-04-05 13:45:30'),
('traveller18', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Emma', 'Clark', 'Dublin, Ireland', 'traveller', 'Literary tours and pub crawls.', 'traveller18.png', TRUE, 'Literary scholar specializing in Irish writers. I organize literary walking tours in Dublin and other European literary capitals.', 'Literary history, Irish literature, pub culture, walking tours', FALSE, '2023-04-10 10:15:40', '2023-04-10 10:15:40'),
('traveller19', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Andrew', 'Rodriguez', 'Mexico City, Mexico', 'traveller', 'Street food connoisseur and cultural explorer.', 'traveller19.png', FALSE, 'Food writer specializing in Latin American street food. I\'ve authored a guidebook on Mexico City\'s best street food vendors.', 'Street food, Mexican cuisine, food markets, culinary traditions', TRUE, '2023-04-15 15:30:20', '2023-04-15 15:30:20'),
('traveller20', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Mia', 'Lewis', 'Cape Town, South Africa', 'traveller', 'Wildlife photographer and safari enthusiast.', 'traveller20.png', TRUE, 'Wildlife conservationist and photographer. I lead photography safaris and donate proceeds to conservation efforts.', 'Wildlife photography, conservation, safari adventures, animal behavior', FALSE, '2023-04-20 12:10:35', '2023-04-20 12:10:35');

-- Editors (5) + 1 Moderator
INSERT INTO users (username, email, password_hash, first_name, last_name, location, role, description, profile_image, is_public, biography, interests, had_free_trial, created_at, updated_at)
VALUES
('editor1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Richard', 'Wilson', 'San Francisco, USA', 'editor', 'Senior travel editor with 10 years of experience.', 'editor1.png', TRUE, 'Former travel magazine editor with expertise in travel writing and photography. I help travelers tell their stories effectively.', 'Travel writing, photography, content curation', FALSE, '2023-01-10 09:20:15', '2023-01-10 09:20:15'),
('editor2', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Patricia', 'Moore', 'Toronto, Canada', 'editor', 'Former travel writer, now helping others share their stories.', 'editor2.png', TRUE, 'Award-winning travel writer who has authored five travel guides. I specialize in making travel narratives engaging and authentic.', 'Travel literature, narrative structure, destination guides', FALSE, '2023-01-11 14:35:25', '2023-01-11 14:35:25'),
('editor3', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Thomas', 'Taylor', 'Melbourne, Australia', 'editor', 'Passionate about quality travel content and photography.', 'editor3.png', TRUE, 'Photojournalist who has covered travel stories on all seven continents. I focus on visual storytelling and high-quality imagery.', 'Travel photography, photojournalism, visual narratives', FALSE, '2023-01-12 10:45:30', '2023-01-12 10:45:30'),
('editor4', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Linda', 'Anderson', 'Edinburgh, UK', 'editor', 'Editor specializing in adventure travel and extreme sports.', 'editor4.png', TRUE, 'Adventure sports journalist and former guide. I help travelers accurately depict adventure experiences and emphasize safety information.', 'Adventure travel, extreme sports, risk management', FALSE, '2023-01-13 16:20:40', '2023-01-13 16:20:40'),
('supportTech1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Charles', 'White', 'Wellington, New Zealand', 'support_tech', 'Former guidebook editor helping travelers share authentic experiences.', 'supportTech1.png', TRUE, 'Longtime Lonely Planet editor with expertise in South Pacific and Oceania destinations. I help travelers avoid clichés and find authentic experiences.', 'Guidebook publishing, destination research, cultural authenticity', FALSE, '2023-01-14 11:30:20', '2023-01-14 11:30:20'),
('moderator1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Karen', 'Johnson', 'Austin, USA', 'moderator', 'Community moderator focused on keeping discussions respectful and informative.', 'moderator1.png', TRUE, 'Former online community manager with experience in content moderation. I ensure our travel community remains supportive and respectful.', 'Community building, content moderation, conflict resolution', FALSE, '2023-01-15 10:15:30', '2023-01-15 10:15:30');

-- Admin (3)
INSERT INTO users (username, email, password_hash, first_name, last_name, location, role, description, profile_image, is_public, biography, interests, had_free_trial, created_at, updated_at)
VALUES
('admin1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'System', 'Administrator', 'Server Room', 'admin', 'Main system administrator.', 'admin1.png', FALSE, 'Technical administrator responsible for system maintenance and updates.', 'System administration, web development, user experience', FALSE, '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('admin2', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Margaret', 'Harris', 'Singapore', 'admin', 'System administrator and travel enthusiast.', 'admin2.png', TRUE, 'Administrator with a background in travel technology. I combine technical expertise with a passion for travel innovation.', 'Travel tech, system administration, user experience, Asian travel', FALSE, '2023-01-02 09:15:35', '2023-01-02 09:15:35'),
('admin3', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Steven', 'Clark', 'Oslo, Norway', 'admin', 'Technical administrator and backpacker.', 'admin3.png', TRUE, 'Backend developer and system administrator. I ensure our platform remains secure and reliable for all users.', 'System security, database management, backpacking, Nordic travel', FALSE, '2023-01-03 14:30:45', '2023-01-03 14:30:45');

-- Add default privacy settings for all users
-- For traveller1
INSERT INTO user_privacy_settings (user_id, attribute_name, visibility)
VALUES
(1, 'username', 'public'),
(1, 'email', 'private'),
(1, 'first_name', 'public'),
(1, 'last_name', 'public'),
(1, 'location', 'public'),
(1, 'description', 'public'),
(1, 'biography', 'public'),
(1, 'interests', 'public'),
(1, 'profile_image', 'public');

-- For traveller2
INSERT INTO user_privacy_settings (user_id, attribute_name, visibility)
VALUES
(2, 'username', 'public'),
(2, 'email', 'private'),
(2, 'first_name', 'public'),
(2, 'last_name', 'public'),
(2, 'location', 'public'),
(2, 'description', 'public'),
(2, 'biography', 'public'),
(2, 'interests', 'public'),
(2, 'profile_image', 'public');

-- For traveller3
INSERT INTO user_privacy_settings (user_id, attribute_name, visibility)
VALUES
(3, 'username', 'public'),
(3, 'email', 'private'),
(3, 'first_name', 'public'),
(3, 'last_name', 'public'),
(3, 'location', 'public'),
(3, 'description', 'public'),
(3, 'biography', 'public'),
(3, 'interests', 'public'),
(3, 'profile_image', 'public');

-- For admin users (more private settings)
INSERT INTO user_privacy_settings (user_id, attribute_name, visibility)
VALUES
(27, 'username', 'public'),
(27, 'email', 'private'),
(27, 'first_name', 'private'),
(27, 'last_name', 'private'),
(27, 'location', 'private'),
(27, 'description', 'public'),
(27, 'biography', 'private'),
(27, 'interests', 'private'),
(27, 'profile_image', 'public');

-- Sample Locations
INSERT INTO locations (name, latitude, longitude, location_type, created_at, updated_at)
VALUES
('New York, USA', 40.7128, -74.0060, 'city', '2023-01-15 10:35:20', '2023-01-15 10:35:20'),
('Paris, France', 48.8566, 2.3522, 'city', '2023-01-15 10:36:15', '2023-01-15 10:36:15'),
('Tokyo, Japan', 35.6762, 139.6503, 'city', '2023-01-15 10:37:10', '2023-01-15 10:37:10'),
('Sydney, Australia', -33.8688, 151.2093, 'city', '2023-01-15 10:38:05', '2023-01-15 10:38:05'),
('Rome, Italy', 41.9028, 12.4964, 'city', '2023-01-15 10:39:00', '2023-01-15 10:39:00'),
('London, UK', 51.5074, -0.1278, 'city', '2023-01-15 10:39:55', '2023-01-15 10:39:55'),
('Bangkok, Thailand', 13.7563, 100.5018, 'city', '2023-01-15 10:40:50', '2023-01-15 10:40:50'),
('Cairo, Egypt', 30.0444, 31.2357, 'city', '2023-01-15 10:41:45', '2023-01-15 10:41:45'),
('Rio de Janeiro, Brazil', -22.9068, -43.1729, 'city', '2023-01-15 10:42:40', '2023-01-15 10:42:40'),
('Barcelona, Spain', 41.3851, 2.1734, 'city', '2023-01-15 10:43:35', '2023-01-15 10:43:35'),
('Amsterdam, Netherlands', 52.3676, 4.9041, 'city', '2023-01-15 10:44:30', '2023-01-15 10:44:30'),
('Berlin, Germany', 52.5200, 13.4050, 'city', '2023-01-15 10:45:25', '2023-01-15 10:45:25'),
('Istanbul, Turkey', 41.0082, 28.9784, 'city', '2023-01-15 10:46:20', '2023-01-15 10:46:20'),
('Beijing, China', 39.9042, 116.4074, 'city', '2023-01-15 10:47:15', '2023-01-15 10:47:15'),
('Dubai, UAE', 25.2048, 55.2708, 'city', '2023-01-15 10:48:10', '2023-01-15 10:48:10'),
('Marrakech, Morocco', 31.6295, -7.9811, 'city', '2023-01-15 10:49:05', '2023-01-15 10:49:05'),
('Cape Town, South Africa', -33.9249, 18.4241, 'city', '2023-01-15 10:50:00', '2023-01-15 10:50:00'),
('Mexico City, Mexico', 19.4326, -99.1332, 'city', '2023-01-15 10:50:55', '2023-01-15 10:50:55'),
('Mumbai, India', 19.0760, 72.8777, 'city', '2023-01-15 10:51:50', '2023-01-15 10:51:50'),
('Athens, Greece', 37.9838, 23.7275, 'city', '2023-01-15 10:52:45', '2023-01-15 10:52:45'),
('Stockholm, Sweden', 59.3293, 18.0686, 'city', '2023-01-15 10:53:40', '2023-01-15 10:53:40'),
('Prague, Czech Republic', 50.0755, 14.4378, 'city', '2023-01-15 10:54:35', '2023-01-15 10:54:35'),
('Vienna, Austria', 48.2082, 16.3738, 'city', '2023-01-15 10:55:30', '2023-01-15 10:55:30'),
('Budapest, Hungary', 47.4979, 19.0402, 'city', '2023-01-15 10:56:25', '2023-01-15 10:56:25'),
('Edinburgh, UK', 55.9533, -3.1883, 'city', '2023-01-15 10:57:20', '2023-01-15 10:57:20'),
('Kyoto, Japan', 35.0116, 135.7680, 'city', '2023-01-15 10:58:15', '2023-01-15 10:58:15'),
('Queenstown, New Zealand', -45.0312, 168.6626, 'city', '2023-01-15 10:59:10', '2023-01-15 10:59:10'),
('Vancouver, Canada', 49.2827, -123.1207, 'city', '2023-01-15 11:00:05', '2023-01-15 11:00:05'),
('Santorini, Greece', 36.3932, 25.4615, 'point_of_interest', '2023-01-15 11:01:00', '2023-01-15 11:01:00'),
('Machu Picchu, Peru', -13.1631, -72.5450, 'point_of_interest', '2023-01-15 11:01:55', '2023-01-15 11:01:55'),
('Seoul, South Korea', 37.5665, 126.9780, 'city', '2023-01-15 11:02:50', '2023-01-15 11:02:50'),
('Bali, Indonesia', -8.3405, 115.0920, 'point_of_interest', '2023-01-15 11:03:45', '2023-01-15 11:03:45'),
('Toronto, Canada', 43.6532, -79.3832, 'city', '2023-01-15 11:04:40', '2023-01-15 11:04:40'),
('San Francisco, USA', 37.7749, -122.4194, 'city', '2023-01-15 11:05:35', '2023-01-15 11:05:35'),
('Buenos Aires, Argentina', -34.6037, -58.3816, 'city', '2023-01-15 11:06:30', '2023-01-15 11:06:30'),
('Reykjavik, Iceland', 64.1466, -21.9426, 'city', '2023-01-15 11:07:25', '2023-01-15 11:07:25'),
('Singapore', 1.3521, 103.8198, 'city', '2023-01-15 11:08:20', '2023-01-15 11:08:20'),
('Las Vegas, USA', 36.1699, -115.1398, 'city', '2023-01-15 11:09:15', '2023-01-15 11:09:15'),
('Dublin, Ireland', 53.3498, -6.2603, 'city', '2023-01-15 11:10:10', '2023-01-15 11:10:10'),
('Lisbon, Portugal', 38.7223, -9.1393, 'city', '2023-01-15 11:11:05', '2023-01-15 11:11:05');

-- Sample Journeys with updated visibility field
INSERT INTO journeys (user_id, title, description, start_date, visibility, is_hidden, cover_image, no_edits, created_at, updated_at)
VALUES
-- Traveller 1 - Multiple Journeys
(1, 'Weekend in Vegas', 'Quick weekend getaway to Las Vegas for some entertainment and relaxation.', '2024-02-10', 'public', FALSE, 'vegas_cover.jpg', FALSE, '2024-02-11 10:35:20', '2024-02-12 16:22:30'),
(1, 'Family Reunion in Florida', 'Annual family gathering at our beach house in Florida.', '2023-12-20', 'private', FALSE, 'reunion.jpg', FALSE, '2023-12-21 09:45:12', '2023-12-22 14:30:25'),

-- Traveller 2 - Multiple Journeys
(2, 'Paris Food Tour', 'Culinary adventure exploring the best restaurants and food markets in Paris.', '2023-09-10', 'public', FALSE, 'paris_food_cover.jpg', FALSE, '2023-09-11 15:20:45', '2023-09-12 12:10:30'),

-- Traveller 3 - One Journey (Premium user with published journey)
(3, 'Surfing Australia\'s East Coast', 'My surfing adventure along Australia\'s beautiful east coast beaches.', '2024-01-20', 'published', FALSE, 'australia_surf_cover.jpg', TRUE, '2024-01-21 10:25:35', '2024-01-22 16:40:20'),

-- Traveller 4 - One Journey
(4, 'Art Museums of Europe', 'Touring the great art museums and galleries of Europe.', '2023-11-15', 'public', FALSE, 'art_museums_cover.jpg', FALSE, '2023-11-16 13:15:45', '2023-11-17 09:30:20'),

-- Traveller 6 - Multiple Journeys
(6, 'Winter in the Canadian Rockies', 'Skiing and snowboarding adventure in the beautiful Canadian Rockies.', '2024-01-05', 'published', FALSE, 'canadian_rockies_cover.jpg', FALSE, '2024-01-06 12:30:40', '2024-01-07 17:15:25'),
(6, 'Vancouver Island Exploration', 'Road trip around Vancouver Island discovering hidden gems.', '2023-08-10', 'private', FALSE, NULL, FALSE, '2023-08-11 09:20:15', '2023-08-12 15:40:30'),

-- Traveller 7 - One Journey
(7, 'Architectural Tour of Berlin', 'Exploring Berlin\'s diverse architectural styles from classical to contemporary.', '2023-10-10', 'public', FALSE, 'berlin_architecture_cover.jpg', TRUE, '2023-10-11 14:25:35', '2023-10-12 10:15:45'),

-- Traveller 8 - One Journey
(8, 'Ancient Rome Discovery', 'Exploring the archaeological sites and history of ancient Rome.', '2024-03-15', 'public', FALSE, 'rome_archaeology_cover.jpg', FALSE, '2024-03-16 10:30:20', '2024-03-17 16:45:30'),

-- Traveller 9 - One Journey
(9, 'Barcelona Summer Getaway', 'Two weeks enjoying the beaches, architecture, and nightlife of Barcelona.', '2023-07-15', 'public', FALSE, 'barcelona_summer_cover.jpg', FALSE, '2023-07-16 15:30:42', '2023-07-17 20:15:22'),

-- Traveller 10 - Multiple Journeys (Premium user with published journey)
(10, 'Cycling Through Amsterdam', 'Exploring Amsterdam by bicycle, the local way!', '2023-07-22', 'published', FALSE, 'amsterdam_cycling_cover.jpg', FALSE, '2023-07-23 11:20:35', '2023-07-24 08:45:15'),
(10, 'Tulip Season in Netherlands', 'Visiting during the height of tulip season to see the incredible flower fields.', '2024-04-10', 'private', FALSE, NULL, FALSE, '2024-04-11 09:15:45', '2024-04-12 14:30:25'),

-- Traveller 11 - One Journey
(11, 'Winter Wonderland in Stockholm', 'Experiencing the magical winter atmosphere in Stockholm.', '2023-12-15', 'public', FALSE, 'stockholm_winter_cover.jpg', FALSE, '2023-12-16 09:45:30', '2023-12-17 18:22:15'),

-- Traveller 12 - One Journey
(10, 'Prague Architectural Photography Trip', 'A dedicated trip to capture Prague\'s stunning Gothic and Baroque architecture.', '2024-03-20', 'public', FALSE, 'prague_architecture_cover.jpg', FALSE, '2024-03-21 11:20:18', '2024-03-22 14:35:50'),

-- Traveller 13 - One Journey
(13, 'Istanbul Culinary Adventure', 'Exploring the rich flavors and spices of Turkish cuisine in Istanbul.', '2023-09-05', 'published', FALSE, 'istanbul_food_cover.jpg', TRUE, '2023-09-06 08:15:30', '2023-09-07 21:10:45'),

-- Traveller 14 - One Journey
(14, 'K-Pop Tour in Seoul', 'Following the footsteps of my favorite K-pop stars in Seoul.', '2024-01-10', 'public', FALSE, 'seoul_kpop_cover.jpg', FALSE, '2024-01-11 16:45:22', '2024-01-12 19:30:15'),

-- Traveller 15 - One Journey
(15, 'Budget Backpacking in Thailand', 'Exploring Bangkok and northern Thailand on a tight budget.', '2023-11-10', 'private', FALSE, NULL, FALSE, '2023-11-11 10:20:35', '2023-11-12 17:45:19'),

-- Editor 1 - One Journey
(21, 'San Francisco to LA Road Trip', 'Driving down the California coast from San Francisco to Los Angeles.', '2023-08-01', 'published', FALSE, 'california_coast_cover.jpg', FALSE, '2023-08-02 13:25:40', '2023-08-03 10:45:15'),

-- Editor 2 - One Journey
(22, 'Autumn in Toronto', 'Exploring Toronto\'s neighborhoods during the beautiful fall season.', '2023-09-20', 'public', FALSE, 'toronto_autumn_cover.jpg', FALSE, '2023-09-21 10:30:25', '2023-09-22 16:15:35'),

-- Editor 3 - One Journey
(23, 'Great Ocean Road Adventure', 'Road trip along Australia\'s stunning Great Ocean Road.', '2024-02-15', 'published', FALSE, 'great_ocean_road_cover.jpg', FALSE, '2024-02-16 14:20:45', '2024-02-17 09:35:15'),

-- Admin 2 - One Journey
(27, 'Singapore City Exploration', 'Discovering the unique blend of cultures and ultra-modern architecture in Singapore.', '2023-11-10', 'public', FALSE, 'singapore_city_cover.jpg', FALSE, '2023-11-11 12:15:30', '2023-11-12 08:45:20'),

-- Hidden Journey
(5, 'Spam Journey Test', 'This is a spam journey that should be hidden by moderators.', '2024-01-01', 'public', TRUE, NULL, FALSE, '2024-01-02 10:25:35', '2024-01-03 15:40:20'),
(9, 'Inappropriate Content', 'This journey contains inappropriate content and should be hidden.', '2024-02-15', 'public', TRUE, NULL, FALSE, '2024-02-16 09:15:30', '2024-02-17 14:25:45');

-- Sample Events
INSERT INTO events (journey_id, location_id, title, description, start_datetime, end_datetime, created_at, updated_at)
VALUES
-- Journey 1: Weekend in Vegas (User 1)
(1, 36, 'Flight to Las Vegas', 'Red-eye flight from JFK to Las Vegas.', '2024-02-10 06:30:00', '2024-02-10 09:15:00', '2024-02-10 10:30:15', '2024-02-10 10:30:15'),
(1, 37, 'Check-in at Bellagio', 'Checked into our beautiful room at the Bellagio with fountain view.', '2024-02-10 11:00:00', '2024-02-10 12:30:00', '2024-02-10 13:45:22', '2024-02-10 13:45:22'),
(1, 38, 'Dinner at Gordon Ramsay Hell\'s Kitchen', 'Amazing dinner experience with the famous Beef Wellington.', '2024-02-10 19:00:00', '2024-02-10 21:30:00', '2024-02-10 22:15:30', '2024-02-10 22:15:30'),

-- Journey 2: Family Reunion in Florida (User 1)
(21, 2, 'Flight to Florida', 'Direct flight to Tampa with the entire family.', '2023-12-20 08:00:00', '2023-12-20 11:30:00', '2023-12-20 12:15:20', '2023-12-20 12:15:20'),
(21, 5, 'Beach House Arrival', 'Arrived at our family beach house in Clearwater.', '2023-12-20 13:00:00', '2023-12-20 14:30:00', '2023-12-20 15:10:30', '2023-12-20 15:10:30'),
(21, 6, 'Family Dinner', 'Big welcome dinner with all 25 family members.', '2023-12-20 18:00:00', '2023-12-20 22:00:00', '2023-12-20 22:45:15', '2023-12-20 22:45:15'),

-- Journey 3: Paris Food Tour (User 2)
(3, 2, 'Arrived in Paris', 'Checked into a charming boutique hotel in the Marais district.', '2023-09-10 14:00:00', '2023-09-10 16:00:00', '2023-09-10 16:30:20', '2023-09-10 16:30:20'),
(3, 2, 'Le Marais Food Walking Tour', 'Guided food tour through Le Marais, sampling pastries, cheeses, and wine.', '2023-09-11 10:00:00', '2023-09-11 14:00:00', '2023-09-11 14:45:30', '2023-09-11 14:45:30'),
(3, 2, 'Cooking Class with French Chef', 'Learned to make authentic French cuisine with a renowned local chef.', '2023-09-12 09:00:00', '2023-09-12 13:00:00', '2023-09-12 13:30:20', '2023-09-12 13:30:20'),

-- Journey 4: Surfing Australia's East Coast (User 3)
(4, 4, 'Arrived in Sydney', 'Landed in Sydney and picked up my rental car for the coastal journey.', '2024-01-20 08:00:00', '2024-01-20 11:00:00', '2024-01-20 11:30:20', '2024-01-20 11:30:20'),
(4, 4, 'Bondi Beach Surfing', 'First surf session at the iconic Bondi Beach.', '2024-01-21 07:00:00', '2024-01-21 11:00:00', '2024-01-21 11:45:30', '2024-01-21 11:45:30'),
(4, 4, 'Drive to Byron Bay', 'Scenic coastal drive north from Sydney to Byron Bay.', '2024-01-22 09:00:00', '2024-01-22 18:00:00', '2024-01-22 18:30:15', '2024-01-22 18:30:15'),
(4, 4, 'Surfing at Byron Bay', 'Amazing day catching waves at the world-famous Byron Bay.', '2024-01-23 06:30:00', '2024-01-23 12:00:00', '2024-01-23 12:30:25', '2024-01-23 12:30:25'),

-- Journey 5: Art Museums of Europe (User 4)
(5, 2, 'Arrived in Paris', 'Started my art tour in Paris, checked into hotel near the Louvre.', '2023-11-15 09:00:00', '2023-11-15 12:00:00', '2023-11-15 12:30:20', '2023-11-15 12:30:20'),
(5, 2, 'Louvre Day One', 'First of two days exploring the massive Louvre collection.', '2023-11-16 09:00:00', '2023-11-16 18:00:00', '2023-11-16 18:30:30', '2023-11-16 18:30:30'),
(5, 2, 'Louvre Day Two', 'Second day at the Louvre focusing on Renaissance art.', '2023-11-17 09:00:00', '2023-11-17 17:00:00', '2023-11-17 17:30:15', '2023-11-17 17:30:15'),

-- Journey 9: Winter in the Canadian Rockies (User 6)
(9, 28, 'Arrived in Banff', 'Landed in Calgary and drove to Banff National Park.', '2024-01-05 12:00:00', '2024-01-05 16:00:00', '2024-01-05 16:30:20', '2024-01-05 16:30:20'),
(9, 27, 'First Day Skiing at Lake Louise', 'Amazing powder day at Lake Louise Ski Resort.', '2024-01-06 08:30:00', '2024-01-06 16:00:00', '2024-01-06 16:30:30', '2024-01-06 16:30:30'),
(9, 25, 'Dog Sledding Adventure', 'Unforgettable dog sledding experience through snowy forests.', '2024-01-07 10:00:00', '2024-01-07 13:00:00', '2024-01-07 13:30:15', '2024-01-07 13:30:15'),

-- Journey 10: Cycling Through Amsterdam (User 10)
(20, 2, 'Arrived in Amsterdam', 'Picked up rental bike and checked into canal-side hotel.', '2023-07-22 10:00:00', '2023-07-22 13:00:00', '2023-07-22 13:30:20', '2023-07-22 13:30:20'),
(20, 8, 'Vondelpark Cycling', 'Relaxing ride through Amsterdam\'s beautiful Vondelpark.', '2023-07-23 09:00:00', '2023-07-23 12:00:00', '2023-07-23 12:30:30', '2023-07-23 12:30:30'),
(20, 9, 'Amsterdam North Ferry Ride', 'Took the free ferry to explore Amsterdam North by bike.', '2023-07-23 14:00:00', '2023-07-23 18:00:00', '2023-07-23 18:30:15', '2023-07-23 18:30:15'),

-- Journey 13: Istanbul Culinary Adventure (User 13) - Premium user with multiple photos per event
(14, 13, 'Arrived in Istanbul', 'Checked into hotel in the historic Sultanahmet district.', '2023-09-05 14:00:00', '2023-09-05 16:00:00', '2023-09-05 16:30:20', '2023-09-05 16:30:20'),
(14, 10, 'Spice Bazaar Food Tour', 'Guided tour of the famous Spice Bazaar, tasting everything!', '2023-09-06 10:00:00', '2023-09-06 14:00:00', '2023-09-06 14:45:30', '2023-09-06 14:45:30'),
(14, 11, 'Turkish Coffee Workshop', 'Learned the art of making perfect Turkish coffee.', '2023-09-07 11:00:00', '2023-09-07 13:00:00', '2023-09-07 13:30:20', '2023-09-07 13:30:20'),
(14, 12, 'Baklava Tasting Session', 'Sampled the best baklava in Istanbul from multiple famous shops.', '2023-09-08 14:00:00', '2023-09-08 16:00:00', '2023-09-08 16:30:30', '2023-09-08 16:30:30'),

-- Journey 18: Great Ocean Road Adventure (Editor 3)
(18, 4, 'Started in Melbourne', 'Picked up rental car and prepared for the iconic coastal drive.', '2024-02-15 09:00:00', '2024-02-15 11:00:00', '2024-02-15 11:30:20', '2024-02-15 11:30:20'),
(18, 4, 'Twelve Apostles', 'Arrived at the highlight of the Great Ocean Road - the stunning Twelve Apostles limestone stacks.', '2024-02-16 16:00:00', '2024-02-16 18:30:00', '2024-02-16 19:00:20', '2024-02-16 19:00:20'),
(18, 4, 'Loch Ard Gorge', 'Explored the beautiful Loch Ard Gorge and heard the shipwreck stories.', '2024-02-17 09:00:00', '2024-02-17 11:00:00', '2024-02-17 11:30:10', '2024-02-17 11:30:10');

-- Add Event Destinations (for Epic 7: Location Features)
INSERT INTO event_destinations (event_id, destination_location_id, created_at, updated_at)
VALUES
-- Flight events with destinations
(1, 1, '2024-02-10 10:32:15', '2024-02-10 10:32:15'), -- Vegas flight: JFK to Vegas
(4, 38, '2023-12-20 12:18:20', '2023-12-20 12:18:20'), -- Florida flight: NY to Tampa
(12, 4, '2024-01-22 18:32:15', '2024-01-22 18:32:15'); -- Sydney to Byron Bay

-- Add Event Images
INSERT INTO event_images (event_id, image_filename, caption, is_primary, created_at, updated_at)
VALUES
-- Journey 1: Weekend in Vegas (User
(1, 'las_vegas_flight.png', 'Taking off from JFK', TRUE, '2024-02-10 10:35:20', '2024-02-10 10:35:20'),
(2, 'bellagio_room.png', 'Our luxurious room with fountain view', TRUE, '2024-02-10 13:50:22', '2024-02-10 13:50:22'),
(1, 'hells_kitchen.png', 'The famous Beef Wellington at Hell\'s Kitchen', TRUE, '2024-02-10 22:20:30', '2024-02-10 22:20:30'),
(3, 'hells_kitchen_interior.png', 'The elegant restaurant interior', TRUE, '2024-02-10 22:25:30', '2024-02-10 22:25:30'),

-- Journey 2: Family Reunion in Florida
(4, 'florida_flight.png', 'Family taking selfies on the plane', TRUE, '2023-12-20 12:20:20', '2023-12-20 12:20:20'),
(5, 'beach_house.png', 'Our beautiful beach house in Clearwater', TRUE, '2023-12-20 15:15:30', '2023-12-20 15:15:30'),
(6, 'family_dinner.png', 'The whole family gathered for dinner', TRUE, '2023-12-20 22:50:15', '2023-12-20 22:50:15'),

-- Journey 3: Paris Food Tour
(7, 'paris_arrival.png', 'My charming hotel in Le Marais', TRUE, '2023-09-10 16:35:20', '2023-09-10 16:35:20'),
(8, 'marais_food_tour.png', 'Sampling cheeses on the food tour', TRUE, '2023-09-11 14:50:30', '2023-09-11 14:50:30'),
(9, 'french_cooking_class.png', 'Learning to make Coq au Vin', TRUE, '2023-09-12 13:35:20', '2023-09-12 13:35:20'),

-- Journey 4: Surfing Australia's East Coast (Premium user with multiple photos)
(4, 'sydney_arrival.png', 'First glimpse of Sydney Harbor', TRUE, '2024-01-20 11:35:20', '2024-01-20 11:35:20'),
(10, 'sydney_opera_house.png', 'The iconic Opera House', TRUE, '2024-01-20 11:40:20', '2024-01-20 11:40:20'),
(10, 'sydney_harbor_bridge.png', 'Sydney Harbor Bridge at sunset', FALSE, '2024-01-20 11:45:20', '2024-01-20 11:45:20'),
(11, 'bondi_surfing.png', 'Catching my first wave at Bondi', TRUE, '2024-01-21 11:50:30', '2024-01-21 11:50:30'),
(11, 'bondi_beach_panorama.png', 'Panoramic view of Bondi Beach', FALSE, '2024-01-21 11:55:30', '2024-01-21 11:55:30'),
(12, 'byron_bay_drive.png', 'Stunning coastal scenery en route to Byron Bay', TRUE, '2024-01-22 18:35:15', '2024-01-22 18:35:15'),
(12, 'coastal_stop.png', 'Lunch break at a scenic coastal lookout', FALSE, '2024-01-22 18:40:15', '2024-01-22 18:40:15'),
(13, 'byron_bay_surfing.png', 'Perfect waves at Byron Bay', TRUE, '2024-01-23 12:35:25', '2024-01-23 12:35:25'),
(13, 'byron_bay_lighthouse.png', 'The iconic Byron Bay lighthouse', FALSE, '2024-01-23 12:40:25', '2024-01-23 12:40:25'),
(13, 'byron_sunset.png', 'Beautiful sunset at Byron Bay', FALSE, '2024-01-23 12:45:25', '2024-01-23 12:45:25'),

-- Journey 5: Art Museums of Europe
(14, 'paris_art_arrival.png', 'Hotel view of the Louvre pyramid', TRUE, '2023-11-15 12:35:20', '2023-11-15 12:35:20'),
(15, 'louvre_day1.png', 'Standing in front of the Mona Lisa', TRUE, '2023-11-16 18:35:30', '2023-11-16 18:35:30'),
(16, 'louvre_day2.png', 'Venus de Milo statue', TRUE, '2023-11-17 17:35:15', '2023-11-17 17:35:15'),

-- Journey 9: Winter in the Canadian Rockies
(17, 'banff_arrival.png', 'Arriving in snow-covered Banff', TRUE, '2024-01-05 16:35:20', '2024-01-05 16:35:20'),
(18, 'lake_louise_skiing.png', 'Skiing with mountain views at Lake Louise', TRUE, '2024-01-06 16:35:30', '2024-01-06 16:35:30'),
(19, 'dog_sledding.png', 'My dog sledding team through the forest', TRUE, '2024-01-07 13:35:15', '2024-01-07 13:35:15'),

-- Journey 10: Cycling Through Amsterdam (Premium user with multiple images)
(20, 'amsterdam_arrival.png', 'My rental bike by the canal', TRUE, '2023-07-22 13:35:20', '2023-07-22 13:35:20'),
(20, 'amsterdam_hotel.png', 'View from my canal-side hotel', FALSE, '2023-07-22 13:40:20', '2023-07-22 13:40:20'),
(21, 'vondelpark_cycling.png', 'Cycling through the lush Vondelpark', TRUE, '2023-07-23 12:35:30', '2023-07-23 12:35:30'),
(21, 'vondelpark_picnic.png', 'Picnic lunch in the park', FALSE, '2023-07-23 12:40:30', '2023-07-23 12:40:30'),
(22, 'amsterdam_north_ferry.png', 'Taking the ferry with my bike', TRUE, '2023-07-23 18:35:15', '2023-07-23 18:35:15'),
(22, 'amsterdam_north_view.png', 'Panoramic view of Amsterdam from the north side', FALSE, '2023-07-23 18:40:15', '2023-07-23 18:40:15'),

-- Journey 13: Istanbul Culinary Adventure (Premium user with multiple images)
(23, 'istanbul_arrival.png', 'View of the Blue Mosque from my hotel window', TRUE, '2023-09-05 16:35:20', '2023-09-05 16:35:20'),
(23, 'istanbul_hotel.png', 'My traditional Turkish-style hotel room', FALSE, '2023-09-05 16:40:20', '2023-09-05 16:40:20'),
(24, 'spice_bazaar.png', 'Colorful spice displays at the bazaar', TRUE, '2023-09-06 14:50:30', '2023-09-06 14:50:30'),
(24, 'turkish_delight.png', 'Sampling various Turkish delights', FALSE, '2023-09-06 14:55:30', '2023-09-06 14:55:30'),
(24, 'spice_vendor.png', 'Local spice vendor explaining different varieties', FALSE, '2023-09-06 15:00:30', '2023-09-06 15:00:30'),
(25, 'turkish_coffee.png', 'Learning to prepare traditional Turkish coffee', TRUE, '2023-09-07 13:35:20', '2023-09-07 13:35:20'),
(25, 'coffee_grounds.png', 'Reading my fortune in the coffee grounds', FALSE, '2023-09-07 13:40:20', '2023-09-07 13:40:20'),
(26, 'baklava_tasting.png', 'Plate of different baklava varieties', TRUE, '2023-09-08 16:35:30', '2023-09-08 16:35:30'),
(26, 'baklava_shop.png', 'Famous baklava shop in Istanbul', FALSE, '2023-09-08 16:40:30', '2023-09-08 16:40:30'),
(26, 'baklava_making.png', 'Watching the master baker prepare baklava', FALSE, '2023-09-08 16:45:30', '2023-09-08 16:45:30'),

-- Journey 18: Great Ocean Road Adventure
(27, 'melbourne_departure.png', 'Setting off from Melbourne with our rental car', TRUE, '2024-02-15 11:35:20', '2024-02-15 11:35:20'),
(28, 'twelve_apostles.png', 'The magnificent Twelve Apostles at sunset', TRUE, '2024-02-16 19:05:20', '2024-02-16 19:05:20'),
(28, 'twelve_apostles_panorama.png', 'Panoramic view of the coastline', FALSE, '2024-02-16 19:10:20', '2024-02-16 19:10:20'),
(29, 'loch_ard_gorge.png', 'The dramatic cliffs of Loch Ard Gorge', TRUE, '2024-02-17 11:35:10', '2024-02-17 11:35:10');

-- Add Subscription Plans
INSERT INTO subscription_plans
(plan_code, name, period_months, base_price, discount_percentage, display_order, description, is_premium, is_selectable, is_active)
VALUES
('free_trial', 'Free Trial', 1, 0.00, 0, 0, 'One month free trial of premium features', FALSE, FALSE, TRUE),
('premium_month', 'Premium - One Month', 1, 5.22, 0, 1, 'One Month of premium access', TRUE, TRUE, TRUE),
('premium_quarter', 'Premium - Three Months', 3, 15.66, 10, 2, 'Three months of premium access with 10% discount', TRUE, TRUE, TRUE),
('premium_year', 'Premium - One Year', 12, 62.61, 25, 3, 'Full year of premium access with 25% discount', TRUE, TRUE, TRUE),
('admin_gift', 'Admin Gifted', 0, 0.00, 0, 999, 'Subscription gifted by administrators', TRUE, FALSE, TRUE);

-- Add Subscriptions - using plan_code instead of subscription_type
INSERT INTO subscriptions (user_id, plan_code, start_date, end_date, months, is_active, created_at, updated_at) VALUES
(27, 'premium_month', '2024-01-01', '2024-02-01', 1, FALSE, '2024-01-01', '2024-01-01'),
(2, 'premium_quarter', '2024-01-01', '2024-04-01', 3, FALSE, '2024-01-01', '2024-01-01'),
(3, 'premium_year', '2025-01-01', '2026-01-01', 12, TRUE, '2025-01-01', '2025-01-01'),
(4, 'premium_month', '2024-02-01', '2024-03-01', 1, FALSE, '2024-02-01', '2024-02-01'),
(1, 'premium_quarter', '2024-02-01', '2024-05-01', 3, FALSE, '2024-02-01', '2024-02-01'),
(6, 'premium_year', '2025-02-01', '2026-02-01', 12, TRUE, '2025-02-01', '2025-02-01'),
(7, 'premium_month', '2024-03-01', '2024-04-01', 1, FALSE, '2024-03-01', '2024-03-01'),
(8, 'premium_quarter', '2024-03-01', '2024-06-01', 3, FALSE, '2024-03-01', '2024-03-01'),
(9, 'premium_year', '2024-03-01', '2025-03-01', 12, FALSE, '2024-03-01', '2024-03-01'),
(10, 'premium_month', '2024-04-01', '2024-05-01', 1, FALSE, '2024-04-01', '2024-04-01'),
(11, 'premium_quarter', '2024-04-01', '2024-07-01', 3, FALSE, '2024-04-01', '2024-04-01'),
(12, 'premium_year', '2024-04-01', '2025-04-01', 12, FALSE, '2024-04-01', '2024-04-01'),
(13, 'premium_year', '2025-06-01', '2026-06-01', 12, TRUE, '2025-06-01', '2025-06-01'),
(14, 'premium_quarter', '2024-05-01', '2024-08-01', 3, FALSE, '2024-05-01', '2024-05-01'),
(15, 'premium_year', '2024-05-01', '2025-05-01', 12, FALSE, '2024-05-01', '2024-05-01'),
(16, 'free_trial', '2024-06-01', '2024-07-01', 1, FALSE, '2024-06-01', '2024-06-01'),
(10, 'admin_gift', '2025-06-01', '2025-07-01', 1, TRUE, '2024-06-01', '2024-06-01'),
(18, 'premium_month', '2024-07-01', '2024-08-01', 1, FALSE, '2024-07-01', '2024-07-01'),
(19, 'premium_quarter', '2024-07-01', '2024-10-01', 3, FALSE, '2024-07-01', '2024-07-01'),
(20, 'premium_year', '2024-07-01', '2025-07-01', 12, TRUE, '2024-07-01', '2024-07-01');

-- Add Countries
INSERT INTO countries (name, code, has_gst, gst_rate, currency_code, created_at, updated_at)
VALUES
('New Zealand', 'NZ', TRUE, 15.00, 'NZD', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Australia', 'AU', FALSE, 10.00, 'AUD', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('United States', 'US', FALSE, NULL, 'USD', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('United Kingdom', 'GB', FALSE, 20.00, 'GBP', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Germany', 'DE', FALSE, 19.00, 'EUR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('France', 'FR', FALSE, 20.00, 'EUR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Italy', 'IT', FALSE, 22.00, 'EUR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Spain', 'ES', FALSE, 21.00, 'EUR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Netherlands', 'NL', FALSE, 21.00, 'EUR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Japan', 'JP', FALSE, 10.00, 'JPY', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('China', 'CN', FALSE, 13.00, 'CNY', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Canada', 'CA', FALSE, 5.00, 'CAD', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Brazil', 'BR', FALSE, 17.00, 'BRL', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('India', 'IN', FALSE, 18.00, 'INR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('South Africa', 'ZA', FALSE, 15.00, 'ZAR', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Mexico', 'MX', FALSE, 16.00, 'MXN', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Egypt', 'EG', FALSE, 14.00, 'EGP', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Turkey', 'TR', FALSE, 18.00, 'TRY', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('South Korea', 'KR', FALSE, 10.00, 'KRW', '2023-01-01 08:00:00', '2023-01-01 08:00:00'),
('Singapore', 'SG', FALSE, 7.00, 'SGD', '2023-01-01 08:00:00', '2023-01-01 08:00:00');

INSERT INTO payments (user_id, subscription_id, amount, gst_amount, payment_date, payment_method, country_id, billing_address, card_last_four, transaction_reference, created_at) VALUES
(27, 1, 5.22, 0.78, '2024-01-01', 'credit_card', 1, '123 Example St', '1234', 'TXN-0001', '2024-01-01'),
(2, 2, 14.09, 2.11, '2024-01-01', 'credit_card', 1, '456 Example Ave', '5678', 'TXN-0002', '2024-01-01'),
(3, 3, 46.96, 7.04, '2024-01-01', 'credit_card', 1, '789 Example Blvd', '9012', 'TXN-0003', '2024-01-01'),
(4, 4, 5.22, 0.78, '2024-02-01', 'credit_card', 1, '234 Example St', '2345', 'TXN-0004', '2024-02-01'),
(5, 5, 14.09, 2.11, '2024-02-01', 'credit_card', 1, '567 Example Ave', '6789', 'TXN-0005', '2024-02-01'),
(6, 6, 46.96, 7.04, '2024-02-01', 'credit_card', 1, '890 Example Blvd', '0123', 'TXN-0006', '2024-02-01'),
(7, 7, 5.22, 0.78, '2024-03-01', 'credit_card', 1, '345 Example St', '3456', 'TXN-0007', '2024-03-01'),
(8, 8, 14.09, 2.11, '2024-03-01', 'credit_card', 1, '678 Example Ave', '7890', 'TXN-0008', '2024-03-01'),
(9, 9, 46.96, 7.04, '2024-03-01', 'credit_card', 1, '901 Example Blvd', '1234', 'TXN-0009', '2024-03-01'),
(10, 10, 5.22, 0.78, '2024-04-01', 'credit_card', 1, '456 Example St', '4567', 'TXN-0010', '2024-04-01'),
(11, 11, 14.09, 2.11, '2024-04-01', 'credit_card', 1, '789 Example Ave', '8901', 'TXN-0011', '2024-04-01'),
(12, 12, 46.96, 7.04, '2024-04-01', 'credit_card', 1, '012 Example Blvd', '2345', 'TXN-0012', '2024-04-01'),
(13, 13, 5.22, 0.78, '2024-05-01', 'credit_card', 1, '567 Example St', '5678', 'TXN-0013', '2024-05-01'),
(14, 14, 14.09, 2.11, '2024-05-01', 'credit_card', 1, '890 Example Ave', '9012', 'TXN-0014', '2024-05-01'),
(15, 15, 46.96, 7.04, '2024-05-01', 'credit_card', 1, '123 Example Blvd', '3456', 'TXN-0015', '2024-05-01'),
(16, 16, 0.00, 0.00, '2024-06-01', 'free_trial', 1, NULL, NULL, 'FREE-TRIAL-0016', '2024-06-01'),
(17, 17, 0.00, 0.00, '2024-06-01', 'admin_gift', 1, NULL, NULL, 'ADMIN-GIFT-0017', '2024-06-01'),
(18, 18, 5.22, 0.78, '2024-07-01', 'credit_card', 1, '678 Example St', '6789', 'TXN-0018', '2024-07-01'),
(19, 19, 14.09, 2.11, '2024-07-01', 'credit_card', 1, '901 Example Ave', '0123', 'TXN-0019', '2024-07-01'),
(20, 20, 46.96, 7.04, '2024-07-01', 'credit_card', 1, '234 Example Blvd', '4567', 'TXN-0020', '2024-07-01');

-- Sample Event Likes
INSERT INTO event_likes (event_id, user_id, created_at)
VALUES
-- Surfing at Byron Bay (Event 13) gets multiple likes
(13, 1, '2024-01-25 09:15:30'),
(13, 2, '2024-01-25 10:20:45'),
(13, 7, '2024-01-25 11:45:20'),
(13, 10, '2024-01-25 14:30:15'),
(13, 21, '2024-01-25 16:25:40'),
-- Turkish Coffee Workshop (Event 25) gets multiple likes
(25, 4, '2023-09-10 12:15:30'),
(25, 5, '2023-09-10 14:20:45'),
(25, 8, '2023-09-11 09:45:20'),
(25, 11, '2023-09-11 16:30:15'),
-- Twelve Apostles (Event 28) gets likes
(28, 3, '2024-02-18 08:15:30'),
(28, 6, '2024-02-18 10:25:45'),
(28, 15, '2024-02-19 14:30:20'),
-- Other random likes
(3, 9, '2024-02-15 16:45:30'),  -- Las Vegas dinner
(8, 2, '2023-09-15 11:20:45'),  -- Paris food tour
(11, 15, '2024-01-30 09:15:30'), -- Bondi Beach surfing
(18, 12, '2024-01-10 14:25:40'), -- Lake Louise skiing
(21, 5, '2023-07-25 13:45:30'),  -- Vondelpark cycling
(24, 6, '2023-09-10 15:30:45');  -- Spice Bazaar

-- Sample Event Comments
INSERT INTO event_comments (event_id, user_id, content, is_hidden, created_at, updated_at)
VALUES
-- Comments on Surfing at Byron Bay (Event 13)
(20, 10, 'The waves look perfect! I need to visit Byron Bay soon.', FALSE, '2024-01-25 09:20:30', '2024-01-25 09:20:30'),
(20, 3, 'Thanks! It was an incredible surfing day, definitely recommend visiting during summer.', FALSE, '2024-01-25 10:30:45', '2024-01-25 10:30:45'),
(13, 7, 'How crowded was it? I\'ve heard it gets packed during peak season.', FALSE, '2024-01-25 12:45:20', '2024-01-25 12:45:20'),
(13, 3, 'It was busy but not overwhelming. If you go early morning you can avoid the crowds.', FALSE, '2024-01-25 14:15:45', '2024-01-25 14:15:45'),
-- Comments on Turkish Coffee Workshop (Event 25)
(9, 10, 'The patterns in the coffee grounds look fascinating! Did they read your fortune?', FALSE, '2023-09-10 12:20:30', '2023-09-10 12:20:30'),
(9, 13, 'Yes! The fortune telling was the best part. Apparently I\'m going on another journey soon!', FALSE, '2023-09-10 13:15:45', '2023-09-10 13:15:45'),
(25, 8, 'How difficult was it to make the coffee properly? I always mess up the foam.', FALSE, '2023-09-11 09:50:20', '2023-09-11 09:50:20'),
(25, 13, 'It takes practice! The key is to heat it slowly and not stir after the foam forms.', FALSE, '2023-09-11 10:25:45', '2023-09-11 10:25:45'),
-- Comments on Twelve Apostles (Event 28)
(4, 3, 'The light in this image is absolutely stunning! What time of day was this?', FALSE, '2024-02-18 08:20:30', '2024-02-18 08:20:30'),
(4, 23, 'Thank you! This was about 30 minutes before sunset, the golden hour there is magical.', FALSE, '2024-02-18 09:15:45', '2024-02-18 09:15:45'),
(14, 10, 'How long did you stay at this spot? I want to make sure I allocate enough time when I visit.', FALSE, '2024-02-18 10:30:45', '2024-02-18 10:30:45'),
(14, 23, 'We spent about 2 hours here. You could do less, but it\'s worth staying for sunset if you can.', FALSE, '2024-02-18 11:20:15', '2024-02-18 11:20:15'),
-- Hidden spam comment
(25, 9, 'Check out my travel blog at www.spamtravel.com for cheap deals!', FALSE, '2024-02-19 10:15:30', '2024-02-19 10:15:30'),
(23, 8, 'Check out my travel blog at www.spamtravel.com for cheap deals!', FALSE, '2024-02-19 10:15:30', '2024-02-19 10:15:30'),

-- Other comments
(3, 9, 'Gordon Ramsay\'s restaurants never disappoint! What else did you try besides the Wellington?', FALSE, '2024-02-15 16:50:30', '2024-02-15 16:50:30'),
(3, 1, 'We also had the scallops and sticky toffee pudding. Everything was amazing!', FALSE, '2024-02-15 18:25:45', '2024-02-15 18:25:45'),
(18, 12, 'The powder looks amazing! Was it crowded on the slopes?', FALSE, '2024-01-10 14:30:40', '2024-01-10 14:30:40'),
(1, 6, 'Not too bad! January weekdays are perfect for avoiding crowds at Lake Louise.', FALSE, '2024-01-10 15:45:20', '2024-01-10 15:45:20'),
(24, 6, 'Those spices look so colorful! Did you bring any home with you?', FALSE, '2023-09-10 15:35:45', '2023-09-10 15:35:45'),
(1, 13, 'Yes! I bought some sumac, za\'atar, and the best saffron I\'ve ever found.', FALSE, '2023-09-10 16:20:30', '2023-09-10 16:20:30'),
(23, 27, 'How difficult was it to make the coffee properly? I always mess up the foam.', FALSE, '2023-09-11 09:50:20', '2023-09-11 09:50:20'),
(23, 13, 'It takes practice! The key is to heat it slowly and not stir after the foam forms.', FALSE, '2023-09-11 10:25:45', '2023-09-11 10:25:45'),
(17, 7, 'How crowded was it? I\'ve heard it gets packed during peak season.', FALSE, '2024-01-25 12:45:20', '2024-01-25 12:45:20'),
(17, 3, 'It was busy but not overwhelming. If you go early morning you can avoid the crowds.', FALSE, '2024-01-25 14:15:45', '2024-01-25 14:15:45');

-- Sample Comment Interactions
INSERT INTO comment_interactions (comment_id, user_id, interaction_type, created_at)
VALUES
-- Likes
(1, 2, 'like', '2024-01-25 10:10:30'),
(1, 10, 'like', '2024-01-25 11:25:45'),
(2, 1, 'like', '2024-01-25 11:30:45'),
(2, 6, 'like', '2024-01-25 12:15:20'),
(3, 3, 'like', '2024-01-25 13:45:30'),
(4, 7, 'like', '2024-01-25 15:20:15'),
(5, 5, 'like', '2023-09-10 13:10:30'),
(5, 13, 'like', '2023-09-10 14:25:45'),
(6, 4, 'like', '2023-09-10 14:45:30'),
(7, 13, 'like', '2023-09-11 10:15:20'),
(8, 8, 'like', '2023-09-11 11:30:45'),
(9, 6, 'like', '2024-02-18 09:10:30'),
(9, 15, 'like', '2024-02-18 09:45:15'),
(10, 3, 'like', '2024-02-18 10:15:45'),
-- Dislikes
(1, 5, 'dislike', '2024-01-25 12:20:30'),
(3, 10, 'dislike', '2024-01-25 13:35:45'),
(9, 12, 'dislike', '2024-02-18 10:30:45');

-- Sample Reports (using the new reports table)
INSERT INTO reports (reporter_id, content_type, content_id, reason, status, reviewed_by, reviewed_at, created_at)
VALUES
-- Comment report (spam)
(1, 'comment', 15, 'Spam with external link', 'new', null, null, '2025-02-19 10:20:30'),
(12, 'comment', 12, 'Commercial spam', 'new', null, null, '2025-02-19 10:35:45'),
(27, 'comment', 13, 'Unsolicited advertising', 'new', null, null, '2025-02-19 10:45:20'),
-- comment reports
(1, 'comment', 6, 'Inappropriate content', 'resolved', 26, '2024-02-17 15:30:20', '2024-02-17 09:45:30'),
(11, 'comment', 20, 'Spam content', 'resolved', 27, '2024-01-03 16:20:15', '2024-01-03 10:15:40'),
-- comment report
(8, 'comment', 9, 'Copyright violation', 'new', NULL, NULL, '2023-11-17 09:30:25'),
-- comment report
(10, 'comment', 19, 'Spammer', 'resolved', 27, '2023-10-20 12:30:15', '2023-10-20 09:15:30');

-- Sample Private Messages
INSERT INTO private_messages (sender_id, recipient_id, content, is_read, created_at)
VALUES
-- Conversation between Users 1 and 3
(1, 3, 'Hi! I loved your Byron Bay surfing photos. Any tips for a beginner surfer visiting Australia?', TRUE, '2024-01-26 09:15:30'),
(3, 1, 'Thanks! Definitely start at Byron Bay - it has breaks for all levels. I recommend getting lessons your first day.', TRUE, '2024-01-26 10:30:45'),
(1, 3, 'Great advice, thanks! Any specific surf schools you\'d recommend?', TRUE, '2024-01-26 11:45:20'),
(3, 1, 'Byron Bay Surf School is great for beginners. Ask for Mike if he\'s still teaching there!', TRUE, '2024-01-26 14:20:30'),
-- Conversation between Users 2 and 13
(2, 13, 'Your Istanbul food tour looks amazing! I\'m planning a trip there next month. Any must-try dishes?', TRUE, '2024-02-05 15:35:45'),
(13, 2, 'Oh you\'ll love it! Definitely try pide (Turkish pizza), manti (dumplings), and kunefe for dessert.', TRUE, '2024-02-05 16:45:30'),
(2, 13, 'Thanks for the recommendations! Any specific restaurants you\'d suggest?', TRUE, '2024-02-06 09:20:45'),
(13, 2, 'For authentic food, go to Çiya Sofrası in Kadıköy. Also try Karaköy Lokantası for traditional Turkish cuisine.', FALSE, '2024-02-06 10:30:20'),
-- Conversation between Users 10 and 7
(10, 7, 'Hi! I noticed you liked my cycling images from Amsterdam. I\'m heading to Berlin next month. Any architecture sites you recommend?', TRUE, '2024-03-10 11:15:30'),
(7, 10, 'Berlin has amazing architecture! Don\'t miss the Reichstag dome, Bauhaus Archive, and just walking around Kreuzberg for street art.', TRUE, '2024-03-10 12:30:45'),
(10, 7, 'Thanks! Is Berlin as bike-friendly as Amsterdam?', TRUE, '2024-03-10 14:45:20'),
(7, 10, 'Not quite as extensive as Amsterdam, but still very good! You can rent bikes easily, and there are dedicated lanes in most areas.', TRUE, '2024-03-10 16:20:30'),
(10, 7, 'Perfect! Looking forward to exploring by bike. Any particular neighborhoods best for cycling?', FALSE, '2024-03-11 09:15:45'),
-- Message from non-premium user (can't send, just receive)
(15, 3, 'Hi! Your surfing journey looks amazing. I\'m planning a budget trip to Australia. Any tips for keeping costs down?', TRUE, '2024-04-02 10:25:30'),
-- Unanswered message (recipient doesn't have premium to reply)
(3, 15, 'Hey there! For budget travel in Australia, hostels are your best bet. Cook your own meals and use public transport. Happy to share more tips!', FALSE, '2024-04-02 11:40:15');

-- Sample Edit History (using new edit_history and edit_field_changes tables)
INSERT INTO edit_history (editor_id, content_type, content_id, reason, created_at)
VALUES
-- Journey edits
(21, 'journey', 1, 'Fixed typo in title', '2023-11-18 09:15:30'),
(22, 'journey', 1, 'Enhanced description clarity', '2023-09-10 14:30:45'),
-- Event edits
(21, 'event', 1, 'Fixed typo in title', '2024-02-12 10:25:20'),
(23, 'event', 1, 'Enhanced description with more detail', '2024-02-18 11:45:30'),
-- Location edits
(22, 'location', 26, 'Fixed typo in country name', '2023-05-10 09:30:15');

-- Add edit field changes
INSERT INTO edit_field_changes (edit_id, field_name, old_value, new_value)
VALUES
(1, 'title', '	Weekenb in vagas', 'Weekend in Vegas'),
(2, 'description', 'Quick weekend getaway to Las Vegas for some entertainment relaxation.', 'Quick weekend getaway to Las Vegas for some entertainment and relaxation.'),
(3, 'title', 'Flight Las Vegas', 'Flight to Las Vegas'),
(4, 'description', 'Red-eye JFK to Las Vegas.', 'Red-eye flight from JFK to Las Vegas.'),
(5, 'name', 'Kyoto, Japn', 'Kyoto, Japan');

-- Sample Notifications
INSERT INTO notifications (user_id, notification_type, content, related_id, is_read, created_at)
VALUES
-- Edit notifications
(4, 'edit', 'Editor Richard Wilson fixed a typo in your journey "Art Museums of Europe"', 1, TRUE, '2023-11-18 09:20:30'),
(13, 'edit', 'Editor Patricia Moore enhanced the description of your journey "Istanbul Culinary Adventure"', 2, TRUE, '2023-09-10 14:35:45'),
(1, 'edit', 'Editor Richard Wilson updated the title of your event "Dinner at Gordon Ramsay Hell\'s Kitchen"', 3, FALSE, '2024-02-12 10:30:20'),
-- Comment notifications
(3, 'comment', 'John Smith commented on your event "Surfing at Byron Bay"', 1, TRUE, '2024-01-25 09:25:30'),
(13, 'comment', 'Jessica Davis commented on your event "Turkish Coffee Workshop"', 5, TRUE, '2023-09-10 12:25:30'),
(23, 'comment', 'Michael Brown commented on your event "Twelve Apostles"', 9, FALSE, '2024-02-18 08:25:30'),
-- Like notifications
(3, 'like', 'John Smith liked your event "Surfing at Byron Bay"', 13, TRUE, '2024-01-25 09:20:30'),
(13, 'like', 'Jessica Davis liked your event "Turkish Coffee Workshop"', 25, TRUE, '2023-09-10 12:20:30'),
(23, 'like', 'Michael Brown liked your event "Twelve Apostles"', 28, FALSE, '2024-02-18 08:20:30'),
-- Message notifications
(3, 'message', 'New message from John Smith', 1, TRUE, '2024-01-26 09:20:30'),
(13, 'message', 'New message from Emily Johnson', 5, TRUE, '2024-02-05 15:40:45'),
(7, 'message', 'New message from Elizabeth Jackson', 9, FALSE, '2024-03-10 11:20:30'),
-- Subscription notifications
(1, 'subscription', 'Your premium subscription is expiring in 7 days. Renew now to keep enjoying premium features!', NULL, FALSE, '2024-02-22 10:15:30'),
(10, 'subscription', 'Your premium subscription is expiring in 7 days. Renew now to keep enjoying premium features!', NULL, FALSE, '2024-06-24 09:30:45'),
(13, 'subscription', 'Your premium subscription is expiring in 7 days. Renew now to keep enjoying premium features!', NULL, FALSE, '2024-05-08 14:25:20');

-- Sample Announcements
INSERT INTO announcements (author_id, title, content, created_at, updated_at)
VALUES
(26, 'Welcome to Footprints!', 'Welcome to our new travel journaling platform! We\'re excited to have you share your adventures with our community. If you have any questions or feedback, please reach out to our support team.', '2023-01-05 09:15:30', '2023-01-05 09:15:30'),
(26, 'New Feature: Location Tagging', 'We\'ve added a new feature that allows you to easily tag and search for locations in your journey events. Try it out on your next adventure!', '2023-03-15 11:30:45', '2023-03-15 11:30:45'),
(21, 'Community Guidelines Update', 'We\'ve updated our community guidelines to ensure a positive experience for all users. Please review the updated terms in your account settings.', '2023-06-20 14:45:10', '2023-06-20 14:45:10'),
(22, 'Scheduled Maintenance', 'We will be performing scheduled maintenance on July 15, 2024, from 2:00 AM to 4:00 AM UTC. The site may be temporarily unavailable during this time.', '2024-07-10 16:20:35', '2024-07-10 16:20:35'),
(27, 'Introducing Premium Features!', 'We\'re excited to announce our new premium subscription plans! Enjoy features like multiple images per event, journey cover images, and more. Check out the subscription page for details.', '2024-03-05 10:15:40', '2024-03-05 10:15:40'),
(27, 'New Premium Feature: Event Likes', 'Premium subscribers can now like and comment on events! Engage with the community and share your thoughts on their adventures.', '2024-03-10 13:25:30', '2024-03-10 13:25:30'),
(23, 'Image Storage Increase', 'Good news! We\'ve increased the storage capacity for images on all accounts. Premium users can now add even more high-quality images to their journey events.', '2024-01-25 13:25:30', '2024-01-25 13:25:30');

-- Mark some announcements as read for some users
INSERT INTO user_announcements (user_id, announcement_id, read_at)
VALUES
(1, 1, '2023-01-06 10:15:25'),
(2, 1, '2023-01-07 14:30:40'),
(3, 1, '2023-01-08 11:45:15'),
(4, 1, '2023-01-06 16:20:50'),
(5, 1, '2023-01-09 09:10:35'),
(1, 2, '2023-03-16 08:25:15'),
(2, 2, '2023-03-17 12:40:30'),
(3, 2, '2023-03-18 15:15:45'),
(1, 3, '2023-06-21 10:30:20'),
(2, 3, '2023-06-22 16:45:35'),
(6, 1, '2023-01-10 13:20:15'),
(7, 1, '2023-01-11 17:30:40'),
(8, 1, '2023-01-12 09:45:25'),
(9, 1, '2023-01-13 14:15:50'),
(10, 1, '2023-01-14 11:30:35'),
(21, 5, '2024-03-06 09:15:30'),
(22, 5, '2024-03-06 11:25:45'),
(23, 5, '2024-03-06 14:40:20'),
(26, 5, '2024-03-05 16:30:15'),
(1, 5, '2024-03-06 10:15:30'),
(2, 5, '2024-03-06 12:25:45'),
(3, 5, '2024-03-06 14:40:20'),
(3, 6, '2024-03-11 09:15:30'),
(10, 6, '2024-03-11 11:25:45'),
(13, 6, '2024-03-11 14:40:20');

-- Set specific users with restriction flags
-- Robert Thomas is blocked from sharing journeys
UPDATE users SET is_blocked = TRUE, updated_at = '2023-09-15 14:25:30' WHERE id = 19;

-- Andrew Rodriguez is banned
UPDATE users SET is_banned = TRUE, updated_at = '2023-10-20 11:40:15' WHERE id = 20;

-- Sample follow relationships for Departure Board (Epic 4)
INSERT INTO user_follows_journey (user_id, journey_id, created_at)
VALUES
-- User 1 follows journeys
(1, 4, '2024-01-25 09:20:30'),  -- Follows Surfing Australia
(1, 10, '2023-07-25 14:15:45'), -- Follows Cycling Through Amsterdam
(1, 13, '2023-09-10 11:30:25'), -- Follows Istanbul Culinary Adventure

-- User 2 follows journeys
(2, 4, '2024-01-24 16:45:30'),  -- Follows Surfing Australia
(2, 18, '2024-02-18 10:15:20'), -- Follows Great Ocean Road Adventure

-- User 27 follows journeys
(27, 4, '2024-01-24 16:45:30'),  -- Follows Surfing Australia
(27, 18, '2024-02-18 10:15:20'), -- Follows Great Ocean Road Adventure


-- User 3 follows journeys
(3, 10, '2023-07-24 09:30:45'), -- Follows Cycling Through Amsterdam
(3, 18, '2024-02-17 15:25:10'); -- Follows Great Ocean Road Adventure

-- Users following other users
INSERT INTO user_follows_user (follower_id, followed_id, created_at)
VALUES
-- User 1 follows users
(1, 3, '2024-01-25 09:25:30'),  -- Follows Michael (surfer)
(1, 10, '2023-07-25 14:20:30'), -- Follows Elizabeth (cyclist)
(1, 13, '2023-09-10 11:35:15'), -- Follows Daniel (food enthusiast)

-- User 2 follows users
(2, 3, '2024-01-24 16:50:25'),  -- Follows Michael
(2, 23, '2024-02-18 10:20:35'), -- Follows Thomas (editor/photographer)

-- User 27 follows users
(27, 3, '2024-01-24 16:50:25'),  -- Follows Michael
(27, 23, '2024-02-18 10:20:35'), -- Follows Thomas (editor/photographer)


-- User 10 follows users
(10, 7, '2024-03-10 11:20:30'); -- Follows James (architecture enthusiast)

-- Users following locations
INSERT INTO user_follows_location (user_id, location_id, created_at)
VALUES
-- User 1 follows locations
(1, 4, '2024-01-25 09:30:25'),  -- Follows Sydney, Australia
(1, 11, '2023-07-25 14:25:30'), -- Follows Amsterdam, Netherlands
(1, 13, '2023-09-10 11:40:20'), -- Follows Istanbul, Turkey

-- User 2 follows locations
(2, 2, '2023-09-12 15:30:25'),  -- Follows Paris, France
(2, 4, '2024-01-24 16:55:30'),  -- Follows Sydney, Australia

-- User 3 follows locations
(3, 11, '2023-07-25 10:15:40'), -- Follows Amsterdam, Netherlands
(3, 10, '2023-08-15 14:20:30'), -- Follows Barcelona, Spain

-- User 27 follows locations
(27, 11, '2023-07-25 10:15:40'), -- Follows Amsterdam, Netherlands
(27, 10, '2023-08-15 14:20:30'); -- Follows Barcelona, Spain

-- Sample helpdesk requests (Epic 6)
INSERT INTO helpdesk_requests (user_id, subject, description, request_type, status, assigned_to, created_at, updated_at, resolved_at)
VALUES
-- New requests
(1, 'Unable to upload images', 'I\'m trying to upload images to my Vegas journey but keep getting an error.', 'help', 'new', NULL, '2024-04-20 10:15:30', '2024-04-20 10:15:30', NULL),
(27, 'Feature suggestion', 'It would be great if we could tag friends in images from shared journeys.', 'other', 'new', NULL, '2024-04-21 14:30:45', '2024-04-21 14:30:45', NULL),

-- Open requests
(1, 'Cannot renew subscription', 'I\'m trying to renew my premium subscription but the payment page gives an error.', 'help', 'open', 25, '2024-04-18 09:45:20', '2024-04-18 10:30:15', NULL),
(11, 'Location missing from map', 'The Rome Colosseum doesn\'t show up correctly on the map.', 'bug', 'open', 23, '2024-04-19 15:20:35', '2024-04-19 16:10:40', NULL),

-- Stalled requests
(7, 'Account merge request', 'I have two accounts and would like to merge them.', 'help', 'stalled', 25, '2024-04-15 11:30:25', '2024-04-16 09:15:40', NULL),

-- Resolved requests
(27, 'Image not displaying correctly', 'One of my Paris images appears rotated incorrectly.', 'bug', 'resolved', 23, '2024-04-10 14:45:30', '2024-04-12 10:25:15', '2024-04-12 10:25:15'),
(3, 'Subscription question', 'Can I upgrade from monthly to yearly during my current subscription?', 'help', 'resolved', 27, '2024-04-12 09:30:45', '2024-04-12 13:15:20', '2024-04-12 13:15:20'),

-- Appeal request
(9, 'Appeal sharing block', 'I would like to appeal my sharing block.', 'appeal', 'resolved', 27, '2024-03-09 10:15:30', '2024-03-11 11:45:20', '2024-03-11 11:45:20');

-- Sample helpdesk replies
INSERT INTO helpdesk_replies (request_id, user_id, content, is_internal, created_at)
VALUES
-- Replies for open subscription issue
(3, 26, 'I\'ve checked your account and there seems to be an issue with our payment processor. Let me investigate further.', FALSE, '2024-04-18 10:30:15'),
(3, 26, 'Contacted payment gateway support - they reported an outage. Need to follow up tomorrow.', TRUE, '2024-04-18 11:45:30'),
(3, 8, 'Thank you for looking into this. Please let me know when it\'s resolved.', FALSE, '2024-04-18 14:20:15'),

-- Replies for location bug
(4, 23, 'Thanks for reporting this. I\'m checking our location data for Rome Colosseum.', FALSE, '2024-04-19 16:10:40'),
(4, 23, 'Found the issue - coordinates are slightly off. Will need to update in the database.', TRUE, '2024-04-19 16:30:25'),
(4, 11, 'Thanks for the quick response! Hope it can be fixed soon.', FALSE, '2024-04-19 17:45:10'),

-- Replies for resolved image issue
(6, 23, 'I\'ve fixed the orientation metadata for your image. It should display correctly now.', FALSE, '2024-04-11 09:15:30'),
(6, 2, 'Perfect! It\'s working correctly now. Thank you!', FALSE, '2024-04-11 10:30:45'),
(6, 23, 'You\'re welcome! Glad we could resolve this for you.', FALSE, '2024-04-12 10:25:15'),

-- Replies for subscription question
(7, 27, 'Yes, you can upgrade from monthly to yearly anytime. The cost of your remaining days on the monthly plan will be credited toward your yearly subscription.', FALSE, '2024-04-12 10:45:30'),
(7, 3, 'Great! I\'ll do that right away. Thanks for the information.', FALSE, '2024-04-12 11:30:15'),
(7, 27, 'You\'re welcome! Let us know if you have any other questions.', FALSE, '2024-04-12 13:15:20'),

-- Replies for appeal
(8, 27, 'I\'ve reviewed your appeal and your sharing history. As this is your first violation, I\'ll remove the sharing block.', FALSE, '2024-03-10 14:30:15'),
(8, 9, 'Thank you so much! I promise to follow the community guidelines in the future.', FALSE, '2024-03-10 15:45:30'),
(8, 27, 'Appeal approved. Sharing block removed. Please adhere to our community guidelines going forward.', FALSE, '2024-03-11 11:45:20');

-- -- Set specific users with restriction flags
-- -- Robert Thomas is blocked from sharing journeys
-- UPDATE users SET is_blocked = TRUE, updated_at = '2023-09-15 14:25:30' WHERE id = 9;

-- -- Andrew Rodriguez is banned
-- UPDATE users SET is_banned = TRUE, updated_at = '2023-10-20 11:40:15' WHERE id = 19;