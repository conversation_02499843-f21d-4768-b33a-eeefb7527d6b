-- Populate database with sample data
-- This script adds test data for the Footprints application

-- Sample users (passwords are 'Password123!')
-- Actual hash for 'Password123!' is '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC'

-- Travellers (20)
INSERT INTO users (username, email, password_hash, first_name, last_name, location, role, description)
VALUES 
('traveller1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'John', 'Smith', 'New York, USA', 'traveller', 'I love exploring new cities and trying local foods.'),
('traveller2', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', '<PERSON>', '<PERSON>', 'London, UK', 'traveller', 'Passionate about hiking and nature photography.'),
('traveller3', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Michael', 'Brown', 'Sydney, Australia', 'traveller', 'Beach lover and surfing enthusiast.'),
('traveller4', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Jessica', 'Davis', 'Paris, France', 'traveller', 'Art and history buff, always visiting museums.'),
('traveller5', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'David', 'Miller', 'Tokyo, Japan', 'traveller', 'Foodie exploring cuisines around the world.'),
('traveller6', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Sarah', 'Wilson', 'Vancouver, Canada', 'traveller', 'Mountain climbing and winter sports enthusiast.'),
('traveller7', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'James', 'Taylor', 'Berlin, Germany', 'traveller', 'Love exploring historical sites and architecture.'),
('traveller8', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Jennifer', 'Anderson', 'Rome, Italy', 'traveller', 'Passionate about ancient history and archaeology.'),
('traveller9', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Robert', 'Thomas', 'Barcelona, Spain', 'traveller', 'Beach and nightlife enthusiast.'),
('traveller10', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Elizabeth', 'Jackson', 'Amsterdam, Netherlands', 'traveller', 'Cycling around the world one city at a time.'),
('traveller11', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'William', 'White', 'Stockholm, Sweden', 'traveller', 'Winter travel specialist.'),
('traveller12', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Olivia', 'Harris', 'Prague, Czech Republic', 'traveller', 'Photography enthusiast capturing Gothic architecture.'),
('traveller13', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Daniel', 'Martin', 'Istanbul, Turkey', 'traveller', 'Cultural explorer and food enthusiast.'),
('traveller14', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Sophia', 'Thompson', 'Seoul, South Korea', 'traveller', 'K-pop fan traveling Asia.'),
('traveller15', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Matthew', 'Garcia', 'Bangkok, Thailand', 'traveller', 'Backpacker on a budget seeing the world.'),
('traveller16', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Ava', 'Martinez', 'Buenos Aires, Argentina', 'traveller', 'Tango dancer exploring South America.'),
('traveller17', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Christopher', 'Robinson', 'Cairo, Egypt', 'traveller', 'Ancient history buff and desert explorer.'),
('traveller18', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Emma', 'Clark', 'Dublin, Ireland', 'traveller', 'Literary tours and pub crawls.'),
('traveller19', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Andrew', 'Rodriguez', 'Mexico City, Mexico', 'traveller', 'Street food connoisseur and cultural explorer.'),
('traveller20', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Mia', 'Lewis', 'Cape Town, South Africa', 'traveller', 'Wildlife photographer and safari enthusiast.');

-- Editors (5)
INSERT INTO users (username, email, password_hash, first_name, last_name, location, role, description)
VALUES 
('editor1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Richard', 'Wilson', 'San Francisco, USA', 'editor', 'Senior travel editor with 10 years of experience.'),
('editor2', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Patricia', 'Moore', 'Toronto, Canada', 'editor', 'Former travel writer, now helping others share their stories.'),
('editor3', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Thomas', 'Taylor', 'Melbourne, Australia', 'editor', 'Passionate about quality travel content and photography.'),
('editor4', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Linda', 'Anderson', 'Edinburgh, UK', 'editor', 'Editor specializing in adventure travel and extreme sports.'),
('editor5', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Charles', 'White', 'Wellington, New Zealand', 'editor', 'Former guidebook editor helping travelers share authentic experiences.');

-- Admin (2) 
INSERT INTO users (username, email, password_hash, first_name, last_name, location, role, description)
VALUES 
('admin1', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'System', 'Administrator', 'Server Room', 'admin', 'Main system administrator.'),
('admin2', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Margaret', 'Harris', 'Singapore', 'admin', 'System administrator and travel enthusiast.'),
('admin3', '<EMAIL>', '$2b$12$KHrcyfCa/mpRj1rVQIYw8Oms4GJcKEj7ktxBTEOQ6MTqcOrVOA9dC', 'Steven', 'Clark', 'Oslo, Norway', 'admin', 'Technical administrator and backpacker.');

-- Sample Locations
INSERT INTO locations (name)
VALUES 
('New York, USA'),
('Paris, France'),
('Tokyo, Japan'),
('Sydney, Australia'),
('Rome, Italy'),
('London, UK'),
('Bangkok, Thailand'),
('Cairo, Egypt'),
('Rio de Janeiro, Brazil'),
('Barcelona, Spain'),
('Amsterdam, Netherlands'),
('Berlin, Germany'),
('Istanbul, Turkey'),
('Beijing, China'),
('Dubai, UAE'),
('Marrakech, Morocco'),
('Cape Town, South Africa'),
('Mexico City, Mexico'),
('Mumbai, India'),
('Athens, Greece'),
('Stockholm, Sweden'),
('Prague, Czech Republic'),
('Vienna, Austria'),
('Budapest, Hungary'),
('Edinburgh, UK'),
('Kyoto, Japan'),
('Queenstown, New Zealand'),
('Vancouver, Canada'),
('Santorini, Greece'),
('Machu Picchu, Peru');

-- Sample Journeys (20)
INSERT INTO journeys (user_id, title, description, start_date, is_public, is_hidden)
VALUES 
-- Traveller 1 - Multiple Journeys
(1, 'European Adventure 2024', 'My three-week journey through Western Europe visiting major capitals and historic sites.', '2024-06-15', TRUE, FALSE),
(1, 'Weekend in Vegas', 'Quick weekend getaway to Las Vegas for some entertainment and relaxation.', '2024-02-10', FALSE, FALSE),
(1, 'Family Reunion in Florida', 'Annual family gathering at our beach house in Florida.', '2023-12-20', FALSE, FALSE),

-- Traveller 2 - Multiple Journeys
(2, 'Hiking the Scottish Highlands', 'A week-long hiking trip through the beautiful Scottish Highlands.', '2024-05-05', TRUE, FALSE),
(2, 'Paris Food Tour', 'Culinary adventure exploring the best restaurants and food markets in Paris.', '2023-09-10', TRUE, FALSE),

-- Traveller 3 - One Journey
(3, 'Surfing Australia\'s East Coast', 'My surfing adventure along Australia\'s beautiful east coast beaches.', '2024-01-20', TRUE, FALSE),

-- Traveller 4 - One Journey
(4, 'Art Museums of Europe', 'Touring the great art museums and galleries of Europe.', '2023-11-15', TRUE, FALSE),

-- Traveller 5 - One Journey
(5, 'Tokyo Food Exploration', 'Sampling the incredible variety of foods available in Tokyo.', '2024-04-01', TRUE, FALSE),

-- Traveller 6 - Multiple Journeys
(6, 'Winter in the Canadian Rockies', 'Skiing and snowboarding adventure in the beautiful Canadian Rockies.', '2024-01-05', TRUE, FALSE),
(6, 'Vancouver Island Exploration', 'Road trip around Vancouver Island discovering hidden gems.', '2023-08-10', FALSE, FALSE),

-- Traveller 7 - One Journey
(7, 'Architectural Tour of Berlin', 'Exploring Berlin\'s diverse architectural styles from classical to contemporary.', '2023-10-10', TRUE, FALSE),

-- Traveller 8 - One Journey
(8, 'Ancient Rome Discovery', 'Exploring the archaeological sites and history of ancient Rome.', '2024-03-15', TRUE, FALSE),

-- Traveller 9 - No Journeys

-- Traveller 10 - Multiple Journeys
(10, 'Cycling Through Amsterdam', 'Exploring Amsterdam by bicycle, the local way!', '2023-07-22', TRUE, FALSE),
(10, 'Tulip Season in Netherlands', 'Visiting during the height of tulip season to see the incredible flower fields.', '2024-04-10', FALSE, FALSE),

-- Editor 1 - One Journey
(21, 'San Francisco to LA Road Trip', 'Driving down the California coast from San Francisco to Los Angeles.', '2023-08-01', TRUE, FALSE),

-- Editor 2 - One Journey
(22, 'Autumn in Toronto', 'Exploring Toronto\'s neighborhoods during the beautiful fall season.', '2023-09-20', TRUE, FALSE),

-- Editor 3 - One Journey
(23, 'Great Ocean Road Adventure', 'Road trip along Australia\'s stunning Great Ocean Road.', '2024-02-15', TRUE, FALSE),

-- Admin 2 - One Journey
(27, 'Singapore City Exploration', 'Discovering the unique blend of cultures and ultra-modern architecture in Singapore.', '2023-11-10', TRUE, FALSE),

-- Hidden Journey
(5, 'Spam Journey Test', 'This is a spam journey that should be hidden by moderators.', '2024-01-01', TRUE, TRUE),
(9, 'Inappropriate Content', 'This journey contains inappropriate content and should be hidden.', '2024-02-15', TRUE, TRUE);

-- Sample Events (5-10 per journey)
-- Journey 1: European Adventure 2024 (User 1)
INSERT INTO events (journey_id, location_id, title, description, start_datetime, end_datetime)
VALUES
(1, 6, 'Arrived in London', 'Landed at Heathrow and checked into my hotel in Covent Garden.', '2024-06-15 14:30:00', '2024-06-15 17:00:00'),
(1, 6, 'British Museum Visit', 'Spent the day exploring the incredible collections at the British Museum.', '2024-06-16 10:00:00', '2024-06-16 16:00:00'),
(1, 6, 'Tower of London Tour', 'Fascinating tour of the historic Tower of London and Crown Jewels.', '2024-06-17 11:00:00', '2024-06-17 14:00:00'),
(1, 2, 'Eurostar to Paris', 'Took the Eurostar train from London to Paris. Quick and comfortable journey.', '2024-06-18 09:00:00', '2024-06-18 12:30:00'),
(1, 2, 'Eiffel Tower Visit', 'Finally visited the iconic Eiffel Tower and enjoyed the panoramic views of Paris.', '2024-06-19 10:00:00', '2024-06-19 13:00:00'),
(1, 2, 'Louvre Museum Day', 'Spent an entire day at the Louvre, still didn\'t see everything!', '2024-06-20 09:30:00', '2024-06-20 18:00:00'),
(1, 12, 'Train to Berlin', 'Took the high-speed train from Paris to Berlin.', '2024-06-21 08:00:00', '2024-06-21 14:00:00'),
(1, 12, 'Berlin Wall Memorial', 'Visited the Berlin Wall Memorial and learned about the city\'s divided history.', '2024-06-22 11:00:00', '2024-06-22 14:00:00'),
(1, 5, 'Flight to Rome', 'Flew from Berlin to Rome for the final leg of my European adventure.', '2024-06-23 10:00:00', '2024-06-23 12:30:00'),
(1, 5, 'Colosseum Tour', 'Amazing guided tour of the Colosseum and Roman Forum.', '2024-06-24 09:00:00', '2024-06-24 13:00:00');

-- Journey 4: Hiking the Scottish Highlands (User 2)
INSERT INTO events (journey_id, location_id, title, description, start_datetime, end_datetime)
VALUES
(4, 25, 'Arrived in Edinburgh', 'Landed in Edinburgh and prepared for my highland adventure.', '2024-05-05 11:00:00', '2024-05-05 14:00:00'),
(4, 25, 'Hiking Supplies Shopping', 'Bought last-minute supplies in Edinburgh before heading to the Highlands.', '2024-05-06 09:00:00', '2024-05-06 12:00:00'),
(4, 25, 'Drive to Fort William', 'Rented a car and drove to Fort William, gateway to the Highlands.', '2024-05-06 14:00:00', '2024-05-06 18:00:00'),
(4, 25, 'Ben Nevis Hike', 'Challenging but rewarding hike up Ben Nevis, the UK\'s highest peak.', '2024-05-07 07:00:00', '2024-05-07 16:00:00'),
(4, 25, 'Rest Day in Glencoe', 'Resting in the beautiful valley of Glencoe after yesterday\'s strenuous hike.', '2024-05-08 10:00:00', '2024-05-08 17:00:00'),
(4, 25, 'Isle of Skye Ferry', 'Took the ferry to the magical Isle of Skye.', '2024-05-09 09:00:00', '2024-05-09 11:00:00'),
(4, 25, 'The Old Man of Storr', 'Hiked to the iconic Old Man of Storr rock formation.', '2024-05-10 08:00:00', '2024-05-10 12:00:00'),
(4, 25, 'Return to Edinburgh', 'The long drive back to Edinburgh from Skye.', '2024-05-11 09:00:00', '2024-05-11 17:00:00');

-- Journey 5: Paris Food Tour (User 2)
INSERT INTO events (journey_id, location_id, title, description, start_datetime, end_datetime)
VALUES
(5, 2, 'Arrived in Paris', 'Checked into a charming boutique hotel in the Marais district.', '2023-09-10 14:00:00', '2023-09-10 16:00:00'),
(5, 2, 'Le Marais Food Walking Tour', 'Guided food tour through Le Marais, sampling pastries, cheeses, and wine.', '2023-09-11 10:00:00', '2023-09-11 14:00:00'),
(5, 2, 'Cooking Class', 'Learned to make authentic French croissants and macarons.', '2023-09-12 09:00:00', '2023-09-12 13:00:00'),
(5, 2, 'Cheese Tasting Workshop', 'Incredible cheese tasting workshop with expert fromager.', '2023-09-13 15:00:00', '2023-09-13 17:00:00'),
(5, 2, 'Wine Tour in Montmartre', 'Walking tour of Montmartre with wine tastings at local establishments.', '2023-09-14 16:00:00', '2023-09-14 19:00:00'),
(5, 2, 'Fine Dining Experience', 'Splurged on a multiple-course dinner at a Michelin-starred restaurant.', '2023-09-15 19:00:00', '2023-09-15 22:30:00');

-- Add more events for other journeys as needed...
-- Journey 8: Ancient Rome Discovery (User 8)
INSERT INTO events (journey_id, location_id, title, description, start_datetime, end_datetime)
VALUES
(12, 5, 'Arrived in Rome', 'Checked into hotel near the historic center.', '2024-03-15 13:00:00', '2024-03-15 15:00:00'),
(12, 5, 'Colosseum and Forum Tour', 'Guided tour of the Colosseum and Roman Forum with an archaeologist.', '2024-03-16 09:00:00', '2024-03-16 14:00:00'),
(12, 5, 'Vatican Museums', 'Explored the Vatican Museums and Sistine Chapel.', '2024-03-17 08:30:00', '2024-03-17 13:00:00'),
(12, 5, 'Palatine Hill Exploration', 'Wandered through the ancient ruins on Palatine Hill.', '2024-03-18 10:00:00', '2024-03-18 13:00:00'),
(12, 5, 'Ostia Antica Day Trip', 'Day trip to the ancient Roman port city of Ostia Antica.', '2024-03-19 09:00:00', '2024-03-19 16:00:00'),
(12, 5, 'Catacombs Tour', 'Fascinating tour of the ancient catacombs beneath the city.', '2024-03-20 11:00:00', '2024-03-20 13:00:00'),
(12, 5, 'Baths of Caracalla', 'Visited the massive ruins of the Baths of Caracalla.', '2024-03-21 10:00:00', '2024-03-21 12:00:00');

-- Sample Announcements
INSERT INTO announcements (author_id, title, content)
VALUES
(1, 'Welcome to Footprints!', 'Welcome to our new Footprintsing platform! We\'re excited to have you share your adventures with our community. If you have any questions or feedback, please reach out to our support team.'),
(2, 'New Feature: Location Tagging', 'We\'ve added a new feature that allows you to easily tag and search for locations in your journey events. Try it out on your next adventure!'),
(21, 'Community Guidelines Update', 'We\'ve updated our community guidelines to ensure a positive experience for all users. Please review the updated terms in your account settings.'),
(22, 'Scheduled Maintenance', 'We will be performing scheduled maintenance on July 15, 2024, from 2:00 AM to 4:00 AM UTC. The site may be temporarily unavailable during this time.');

-- Mark some announcements as read
-- Only add these after the announcements are successfully inserted
INSERT INTO user_announcements (user_id, announcement_id)
VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(1, 2),
(2, 2),
(3, 2),
(1, 3);

-- Set one user as blocked from sharing
UPDATE users SET is_blocked = TRUE WHERE id = 9;

-- Set one user as banned
UPDATE users SET is_banned = TRUE WHERE id = 19;