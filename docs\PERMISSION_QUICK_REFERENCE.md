# Permission System Quick Reference

## 🚀 Quick Start

### Import What You Need

```python
from utils.permissions import PermissionChecker, PermissionGroups, Roles
from utils.security import content_manager_required, report_manager_required, staff_required
```

## 🔍 Common Permission Checks

### Python Code

```python
# Content management (journeys, events)
if PermissionChecker.can_manage_content():
    # Editor, Support_Tech, Admin

# Staff features
if PermissionChecker.is_staff():
    # Moderator, Editor, Support_Tech, Admin

# Administrative features
if PermissionChecker.can_administrate():
    # Support_Tech, Admin

# Role changes (admin only)
if PermissionChecker.can_change_user_roles():
    # Admin only

# Report management
if PermissionChecker.can_manage_reports():
    # Moderator, Editor, Support_Tech, Admin

# Staff features (includes messaging privileges)
if PermissionChecker.is_staff():
    # All staff roles (Moderator, Editor, Support_Tech, Admin)

# Edit history access
if PermissionChecker.can_access_edit_history():
    # Editor, Support_Tech, Admin
```

### Templates

```html
<!-- Content management -->
{% if can_manage_content() %}
<button>Edit Content</button>
{% endif %}

<!-- Staff features (includes messaging privileges) -->
{% if is_staff() %}
<div class="staff-panel">...</div>
<button>Send Message</button>
{% endif %}

<!-- Admin features -->
{% if can_administrate() %}
<a href="/admin">Admin Panel</a>
{% endif %}

<!-- Role changes (admin only) -->
{% if can_change_user_roles() %}
<button>Change Role</button>
{% endif %}

<!-- Role badge -->
<span class="badge {{ get_role_badge_class(user.role) }}">
  <i class="{{ get_role_icon(user.role) }}"></i>
  {{ user.role|capitalize }}
</span>
```

## 🛡️ Security Decorators

```python
@login_required                    # Must be logged in
@admin_required                    # Admin only
@content_manager_required         # Editor, Support_Tech, Admin
@report_manager_required          # Moderator, Editor, Support_Tech, Admin
@staff_required                   # All staff roles
@admin_or_support_tech_required   # Support_Tech, Admin
@role_required(['editor', 'admin']) # Specific roles
```

## 📊 Role Hierarchy

```
Admin (Full access)
├── Support_Tech (Admin access except role changes)
├── Editor (Content management)
├── Moderator (Report management)
└── Traveller (Basic user)
```

## 🎯 Permission Groups

```python
PermissionGroups.STAFF              # [moderator, editor, support_tech, admin]
PermissionGroups.CONTENT_MANAGERS   # [editor, support_tech, admin]
PermissionGroups.ADMINISTRATORS     # [support_tech, admin]
PermissionGroups.FULL_ADMIN         # [admin]
PermissionGroups.REPORT_MANAGERS    # [moderator, editor, support_tech, admin]
```

## ✅ Do's and ❌ Don'ts

### ✅ DO

```python
# Use permission checker
if PermissionChecker.can_manage_content():
    pass

# Use permission groups
if user['role'] in PermissionGroups.STAFF:
    pass

# Use role constants
if user['role'] == Roles.ADMIN:
    pass

# Use centralized decorators
@content_manager_required  # Clear, descriptive naming
def my_route():
    pass
```

### ❌ DON'T

```python
# Don't hardcode role lists
if session.get('role') in ['editor', 'admin', 'support_tech']:
    pass

# Don't hardcode role names
if user['role'] == 'admin':
    pass

# Don't use inline role checks in templates
{% if session.role == 'admin' or session.role == 'editor' %}
    <button>Edit</button>
{% endif %}
```

## 🔧 Common Patterns

### Route Permission Check

```python
@bp.route('/admin/feature')
@login_required
@admin_or_support_tech_required
def admin_feature():
    return render_template('admin/feature.html')
```

### Service Layer Check

```python
def update_content(user_id, content_id):
    if not PermissionChecker.can_manage_content():
        return False, "Permission denied"
    # Continue with update
```

### Template Conditional

```html
{% if can_manage_content() %}
<div class="admin-controls">
  <button>Edit</button>
  <button>Delete</button>
</div>
{% endif %}
```

### Role-based Redirect

```python
if PermissionChecker.can_administrate():
    return redirect(url_for('admin.dashboard'))
elif PermissionChecker.is_staff():
    return redirect(url_for('staff.dashboard'))
else:
    return redirect(url_for('user.dashboard'))
```

## 🚨 Remember

1. **Always include `support_tech`** in admin-level permissions
2. **Use the most specific permission check** available
3. **Test all role levels** when making changes
4. **Update both routes AND templates** when changing permissions
5. **Don't mix old and new permission systems**

## 📱 Support_Tech Special Notes

`support_tech` role has:

- ✅ All admin permissions
- ✅ Content management
- ✅ User management
- ✅ Report management
- ❌ Cannot change user roles (admin only)

Always include `support_tech` in admin-level checks except for role changes!
