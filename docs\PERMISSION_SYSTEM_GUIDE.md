# Permission System Guide

## Overview

This document explains the new centralized permission system implemented in the codebase. The system provides a modular, maintainable approach to role-based access control.

## 🎯 Key Benefits

- **Single source of truth** for all role permissions
- **Consistent `support_tech` inclusion** across all features
- **Easy maintenance** - change roles in one place
- **Reduced security vulnerabilities**
- **Better developer experience** with clear APIs

## 📁 File Structure

```
utils/
├── permissions.py          # Centralized permission system
└── security.py            # Security decorators (updated)
```

## 🔧 Core Components

### 1. Role Definitions (`utils/permissions.py`)

```python
class Roles:
    TRAVELLER = 'traveller'
    MODERATOR = 'moderator'
    EDITOR = 'editor'
    SUPPORT_TECH = 'support_tech'
    ADMIN = 'admin'
```

### 2. Permission Groups

```python
class PermissionGroups:
    # Basic staff roles (can moderate content)
    STAFF = [Roles.MODERATOR, Roles.EDITOR, Roles.SUPPORT_TECH, Roles.ADMIN]

    # Content management roles (can edit journeys, events, etc.)
    CONTENT_MANAGERS = [Roles.EDITOR, Roles.SUPPORT_TECH, Roles.ADMIN]

    # Administrative roles (can manage users, but not necessarily change roles)
    ADMINISTRATORS = [Roles.SUPPORT_TECH, Roles.ADMIN]

    # Full admin (can change user roles)
    FULL_ADMIN = [Roles.ADMIN]

    # Report management (moderator manages reported comments, support_tech has admin permissions)
    REPORT_MANAGERS = [Roles.MODERATOR, Roles.EDITOR, Roles.SUPPORT_TECH, Roles.ADMIN]

    # Premium feature access (staff get premium features automatically)
    PREMIUM_ACCESS = STAFF

    # Edit history access
    EDIT_HISTORY_ACCESS = CONTENT_MANAGERS
```

### 3. Permission Checker Class

```python
class PermissionChecker:
    @staticmethod
    def is_staff() -> bool:
        """Check if current user is staff"""

    @staticmethod
    def can_manage_content() -> bool:
        """Check if current user can manage content (edit journeys, events, etc.)"""

    @staticmethod
    def can_administrate() -> bool:
        """Check if current user has administrative privileges"""

    @staticmethod
    def can_change_user_roles() -> bool:
        """Check if current user can change other users' roles"""

    @staticmethod
    def can_manage_reports() -> bool:
        """Check if current user can manage reports"""

    @staticmethod
    def can_access_edit_history() -> bool:
        """Check if current user can access edit history"""
```

## 🚀 How to Use

### In Python Code (Services/Routes)

#### ✅ DO - Use Permission Checker

```python
from utils.permissions import PermissionChecker, PermissionGroups

# Check permissions
if PermissionChecker.can_manage_content():
    # User can edit content
    pass

# Check role membership
if user['role'] in PermissionGroups.STAFF:
    # User is staff
    pass
```

#### ❌ DON'T - Hardcode role lists

```python
# DON'T DO THIS
if session.get('role') in ['editor', 'admin', 'support_tech']:
    pass

# DON'T DO THIS
if user['role'] == 'admin' or user['role'] == 'editor':
    pass
```

### In Templates (Jinja2)

#### ✅ DO - Use Template Helper Functions

```html
<!-- Check if user can manage content -->
{% if can_manage_content() %}
<button>Edit Journey</button>
{% endif %}

<!-- Check if user is staff -->
{% if is_staff() %}
<div class="staff-panel">...</div>
{% endif %}

<!-- Check if user can administrate -->
{% if can_administrate() %}
<a href="/admin">Admin Panel</a>
{% endif %}

<!-- Display role badge -->
<span class="badge {{ get_role_badge_class(user.role) }}">
  <i class="{{ get_role_icon(user.role) }}"></i>
  {{ user.role|capitalize }}
</span>
```

#### ❌ DON'T - Hardcode role checks

```html
<!-- DON'T DO THIS -->
{% if session.role == 'admin' or session.role == 'editor' %}
<button>Edit</button>
{% endif %}

<!-- DON'T DO THIS -->
{% if session.role in ['editor', 'admin', 'support_tech'] %}
<div>Staff content</div>
{% endif %}
```

### Security Decorators

#### ✅ DO - Use Centralized Decorators

```python
from utils.security import (
    login_required,
    admin_required,
    content_manager_required,  # Content management permissions (editor, support_tech, admin)
    report_manager_required,   # Report management permissions (moderator, editor, support_tech, admin)
    staff_required,
    admin_or_support_tech_required
)

@content_manager_required  # Uses PermissionGroups.CONTENT_MANAGERS (editor, support_tech, admin)
def edit_journey():
    pass

@report_manager_required  # Uses PermissionGroups.REPORT_MANAGERS (moderator, editor, support_tech, admin)
def manage_reports():
    pass

@staff_required  # Uses PermissionGroups.STAFF (all staff roles)
def staff_dashboard():
    pass

@admin_or_support_tech_required  # Uses PermissionGroups.ADMINISTRATORS
def user_management():
    pass
```

## 📋 Role Permission Matrix

| Feature              | Traveller | Moderator | Editor | Support_Tech | Admin |
| -------------------- | --------- | --------- | ------ | ------------ | ----- |
| Create Journeys      | ✅        | ✅        | ✅     | ✅           | ✅    |
| Edit Own Content     | ✅        | ✅        | ✅     | ✅           | ✅    |
| Edit Others' Content | ❌        | ❌        | ✅     | ✅           | ✅    |
| Hide/Unhide Journeys | ❌        | ❌        | ✅     | ✅           | ✅    |
| Manage Reports       | ❌        | ✅        | ✅     | ✅           | ✅    |
| Premium Features     | 💰        | ✅        | ✅     | ✅           | ✅    |
| Messaging            | 💰        | ✅        | ✅     | ✅           | ✅    |
| Edit History Access  | Owner     | ❌        | ✅     | ✅           | ✅    |
| User Management      | ❌        | ❌        | ❌     | ✅           | ✅    |
| Change User Roles    | ❌        | ❌        | ❌     | ❌           | ✅    |

**Legend:**

- ✅ = Full Access
- ❌ = No Access
- 💰 = Requires Premium Subscription
- Owner = Only for own content

## 🔄 Migration Guide

### Step 1: Replace Hardcoded Role Checks

**Before:**

```python
if session.get('role') in ['editor', 'admin', 'support_tech']:
    # Do something
```

**After:**

```python
from utils.permissions import PermissionChecker
if PermissionChecker.can_manage_content():
    # Do something
```

### Step 2: Update Templates

**Before:**

```html
{% if session.role == 'admin' or session.role == 'editor' %}
<button>Edit</button>
{% endif %}
```

**After:**

```html
{% if can_manage_content() %}
<button>Edit</button>
{% endif %}
```

### Step 3: Use New Security Decorators

**Before:**

```python
@role_required(['editor', 'admin', 'support_tech'])
def some_route():
    pass
```

**After:**

```python
@content_manager_required  # Clear, descriptive decorator name
def some_route():
    pass
```

## 🛡️ Security Best Practices

1. **Always use centralized permission functions** instead of hardcoding role lists
2. **Use appropriate permission level** - don't give more access than needed
3. **Test permission changes thoroughly** - especially for security-critical features
4. **Document new permission requirements** when adding features
5. **Use the most specific permission check** available

## 🧪 Testing

When adding new features or modifying existing ones:

1. **Test all role levels** to ensure proper access control
2. **Verify `support_tech` has appropriate access** (same as admin except role changes)
3. **Check that permission denials work correctly**
4. **Test edge cases** like expired subscriptions with existing permissions

## 🚨 Common Pitfalls

1. **Don't mix old and new permission systems** - migrate completely
2. **Don't forget to update templates** when changing route permissions
3. **Don't hardcode role names** - use `Roles` constants
4. **Don't assume role hierarchy** - use explicit permission groups
5. **Don't forget `support_tech`** in new permission checks

## 📞 Support

If you have questions about the permission system:

1. Check this documentation first
2. Look at existing implementations in the codebase
3. Ask the team lead for clarification
4. Update this documentation if you find gaps

---

**Remember:** The goal is consistent, maintainable, and secure permission management across the entire application.
