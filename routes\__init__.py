"""
Routes Package

This package contains all route blueprints for the application.
"""

from flask import Flask

# Import blueprints using relative imports
from .auth_routes import bp as auth_bp
from .main_routes import bp as main_bp
from .account_routes import bp as account_bp
from .journey_routes import bp as journey_bp
from .user_routes import bp as admin_bp
from .announcement_routes import bp as announcement_bp
from .event_routes import bp as event_bp
from .location_routes import bp as location_bp
from .subscription_routes import bp as subscription_bp
from .message_routes import bp as message_bp
from .api_routes import bp as api_bp
from .report_routes import bp as report_bp
from .departure_board_routes import bp as departure_board_bp
from .helpdesk_routes import bp as helpdesk_bp

def init_routes(app: Flask) -> None:
    """
    Initialize and register all route blueprints with the Flask application.
    
    Args:
        app (Flask): The Flask application instance.
    """
    
    app.register_blueprint(auth_bp)
    app.register_blueprint(main_bp)
    app.register_blueprint(account_bp)
    app.register_blueprint(journey_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(announcement_bp)
    app.register_blueprint(event_bp)
    app.register_blueprint(location_bp)
    app.register_blueprint(subscription_bp)
    app.register_blueprint(message_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(report_bp)
    app.register_blueprint(departure_board_bp)
    app.register_blueprint(helpdesk_bp)
