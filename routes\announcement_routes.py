from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from services import announcement_service as announcement_service
from utils.security import content_manager_required, login_required

bp = Blueprint('announcement', __name__, url_prefix='/announcement')


@bp.route('', methods=['GET'])
@login_required
def get_announcements():
    """Get announcements for current user with tabs (unread/read/all)"""
    page = request.args.get('page', 1, type=int)
    active_tab = request.args.get('active_tab')
    limit = 10
    offset = (page - 1) * limit

    if active_tab is None:
        return redirect(url_for('announcement.get_announcements', active_tab='all', page=page))

    if active_tab == 'read':
        announcements = announcement_service.get_read_announcements(user_id=session['user_id'], limit=limit, offset=offset)
        total_count = announcement_service.get_read_announcements_count(user_id=session['user_id'])
    elif active_tab == 'all':
        announcements = announcement_service.get_user_announcements(user_id=session['user_id'], limit=limit, offset=offset)
        total_count = announcement_service.get_user_announcements_count(user_id=session['user_id'])
    else:  # unread (default)
        announcements = announcement_service.get_unread_announcements(user_id=session['user_id'], limit=limit, offset=offset)
        total_count = announcement_service.get_unread_announcements_count(user_id=session['user_id'])

    total_pages = (total_count + limit - 1) // limit

    return render_template('announcement/list.html',
                         announcements=announcements,
                         page=page,
                         total_pages=total_pages,
                         total_count=total_count,
                         active_tab=active_tab)

@bp.route('/unread', methods=['GET'])
@login_required
def get_user_unread_announcements():
    """Get announcements for current user"""
    page = request.args.get('page', 1, type=int)

    limit = 10
    offset = (page - 1) * limit

    # Get announcements with pagination
    unread_announcements = announcement_service.get_unread_announcements(
        user_id=session['user_id'],
        limit=limit,
        offset=offset,
    )

    # Get total count for pagination
    total_count = announcement_service.get_unread_announcements_count(
        user_id=session['user_id'],
    )

    total_pages = (total_count + limit - 1) // limit

    return render_template('announcement/list.html',
                         announcements=unread_announcements,
                         page=page,
                         total_pages=total_pages,
                         total_count=total_count)

@bp.route('/read', methods=['GET'])
@login_required
def get_user_read_announcements():
    """Get announcements for current user"""
    page = request.args.get('page', 1, type=int)

    limit = 10
    offset = (page - 1) * limit

    # Get announcements with pagination
    announcements = announcement_service.get_read_announcements(
        user_id=session['user_id'],
        limit=limit,
        offset=offset,
    )

    # Get total count for pagination
    total_count = announcement_service.get_read_announcements_count(
        user_id=session['user_id'],
    )

    total_pages = (total_count + limit - 1) // limit

    return render_template('announcement/list.html',
                         announcements=announcements,
                         page=page,
                         total_pages=total_pages,
                         total_count=total_count)


@bp.route('/manage', methods=['GET'])
@content_manager_required
def get_all_announcements():
    """Manage announcements (admin/editor only)"""
    page = request.args.get('page', 1, type=int)

    limit = 10
    offset = (page - 1) * limit

    # Get announcements with pagination
    announcements = announcement_service.get_all_announcements(
        limit=limit,
        offset=offset,
    )

    # Get total count for pagination
    total_count = announcement_service.get_all_announcements_count(
    )

    total_pages = (total_count + limit - 1) // limit

    return render_template('admin/announcement/list.html',
                         announcements=announcements,
                         page=page,
                         total_pages=total_pages,
                         total_count=total_count)


@bp.route('/<int:announcement_id>/mark-read', methods=['POST'])
@login_required
def mark_announcement_as_read(announcement_id):
    """Mark an announcement as read"""
    success, message = announcement_service.mark_announcement_as_read(
        user_id=session['user_id'],
        announcement_id=announcement_id
    )

    flash(message, 'success' if success else 'danger')

    # Get the next parameter or default to unread announcements
    next_page = request.args.get('next', 'announcement.get_user_unread_announcements')

    return redirect(url_for(next_page))

@bp.route('/<int:announcement_id>/mark-dashboard-read', methods=['POST'])
@login_required
def mark_dashboard_announcement_as_read(announcement_id):
    """Mark an announcement as read"""
    success, message = announcement_service.mark_announcement_as_read(
        user_id=session['user_id'],
        announcement_id=announcement_id
    )

    flash(message, 'success' if success else 'danger')

    from utils.permissions import Roles
    user_role = session.get('role', Roles.TRAVELLER)
    if user_role == Roles.ADMIN:
        return redirect(url_for('main.get_admin_dashboard'))
    elif user_role == Roles.EDITOR:
        return redirect(url_for('main.get_editor_dashboard'))
    else:
        return redirect(url_for('main.get_user_dashboard'))

@bp.route('/<int:announcement_id>', methods=['GET'])
@login_required
def get_announcement(announcement_id):
    """Get a specific announcement by ID"""
    # Get the announcement
    announcement = announcement_service.get_announcement(announcement_id)

    if not announcement:
        flash('Announcement not found', 'danger')
        return redirect(url_for('announcement.get_user_unread_announcements'))

    # Mark as read automatically when viewing
    announcement_service.mark_announcement_as_read(
        user_id=session['user_id'],
        announcement_id=announcement_id
    )

    return render_template('announcement/edit.html', announcement=announcement)

@bp.route('/create', methods=['GET', 'POST'])
@content_manager_required
def create_announcement():
    """Create a new announcement"""
    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')

        success, message, announcement_id = announcement_service.create_announcement(
            author_id=session['user_id'],
            title=title,
            content=content
        )

        flash(message, 'success' if success else 'danger')
        if success:
            return redirect(url_for('announcement.get_all_announcements'))

    return render_template('announcement/create.html')

@bp.route('/<int:announcement_id>/edit', methods=['GET', 'POST'])
@content_manager_required
def update_announcement(announcement_id):
    """Update an announcement"""
    announcement = announcement_service.get_announcement(announcement_id)

    if not announcement:
        flash('Announcement not found', 'danger')
        return redirect(url_for('announcement.get_all_announcements'))

    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')

        success, message = announcement_service.update_announcement(
            announcement_id=announcement_id,
            user_id=session['user_id'],
            title=title,
            content=content,
        )

        flash(message, 'success' if success else 'danger')
        if success:
            return redirect(url_for('announcement.get_all_announcements'))

    return render_template('announcement/edit.html', announcement=announcement)


@bp.route('/<int:announcement_id>/delete', methods=['POST'])
@content_manager_required
def delete_announcement(announcement_id):
    """Delete an announcement"""
    success, message = announcement_service.delete_announcement(
        announcement_id=announcement_id,
        user_id=session['user_id']
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('announcement.get_all_announcements'))

