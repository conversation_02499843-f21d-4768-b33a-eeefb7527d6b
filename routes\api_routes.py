"""
API Routes Module

This module handles all API endpoints for the application.
"""

from flask import Blueprint, jsonify, request, g, session
from utils.security import login_required
from services import location_service, message_service, user_service, notification_service
from utils.file_utils import get_safe_image_url

bp = Blueprint('api', __name__, url_prefix='/api')

# User API endpoints
@bp.route('/users/search', methods=['GET'])
@login_required
def search_users():
    """Search for users by username"""
    query = request.args.get('q', '')
    if not query or len(query) < 2:
        return jsonify([])

    users = user_service.search_users(query)
    users = [u for u in users if u.get('id') != session['user_id']]
    return jsonify(users)

@bp.route('/users/<int:user_id>', methods=['GET'])
@login_required
def get_user(user_id):
    """Get user details by ID"""
    user = user_service.get_user_by_id(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404

    # Online status not available in non-real-time mode
    user['online'] = False

    return jsonify(user)

# Message API endpoints
@bp.route('/messages/conversations', methods=['GET'])
@login_required
def get_conversations():
    """Get all conversations for the current user"""
    limit = request.args.get('limit', type=int)
    conversations = message_service.get_conversations(session['user_id'])

    # Get user details for each conversation
    for conversation in conversations:
        other_user = user_service.get_user_by_id(conversation['other_user_id'])
        if other_user:
            conversation['other_user'] = other_user

            # Online status not available in non-real-time mode
            conversation['online'] = False

    # Apply limit if specified (for navbar dropdown)
    if limit:
        conversations = conversations[:limit]

    return jsonify(conversations)

@bp.route('/messages/<int:other_user_id>', methods=['GET'])
@login_required
def get_messages(other_user_id):
    """Get messages between current user and another user"""
    messages = message_service.get_messages_between(session['user_id'], other_user_id)
    return jsonify(messages)

@bp.route('/messages/unread-count', methods=['GET'])
@login_required
def get_unread_count():
    """Get count of unread messages for current user"""
    count = message_service.get_unread_messages_count(session['user_id'])
    return jsonify({'count': count})

@bp.route('/notifications/unread-count', methods=['GET'])
@login_required
def get_notification_unread_count():
    """Get count of unread notifications for current user"""
    count = notification_service.count_unread_notifications(session['user_id'])
    return jsonify({'count': count})

# Placeholder API for testing
@bp.route('/placeholder/<int:width>/<int:height>', methods=['GET'])
def placeholder_image(width, height):
    """Return a placeholder image URL"""
    return f"https://via.placeholder.com/{width}x{height}"


@bp.route('/location-coords')
def get_location_coords():
    location_name = request.args.get('name')
    if not location_name:
        return jsonify({'error': 'Location name is required'}), 400

    location = location_service.get_location_by_name(location_name)
    if not location:
        return jsonify({'error': 'Location not found'}), 404

    return jsonify({'lat': location['latitude'], 'lng': location['longitude']})


@bp.route('/safe-image-url', methods=['GET'])
def get_safe_image_url_api():
    """Get a safe image URL that falls back to placeholder if file doesn't exist"""
    image_filename = request.args.get('filename')
    image_type = request.args.get('type', 'profile')
    
    if not image_filename:
        return jsonify({'error': 'Image filename is required'}), 400
    
    if image_type not in ['event', 'profile', 'journey_cover']:
        return jsonify({'error': 'Invalid image type'}), 400
    
    safe_url = get_safe_image_url(image_filename, image_type)
    return jsonify({'url': safe_url})

@bp.route('/journey-visibility/<int:journey_id>', methods=['GET'])
def get_journey_visibility(journey_id):
    """Get the visibility status of a journey for notification navigation"""
    from services import journey_service
    
    try:
        # Get journey details - allow both authenticated and anonymous access for visibility check
        success, message, journey = journey_service.get_journey(
            journey_id=journey_id,
            user_id=session.get('user_id')  # May be None for anonymous users
        )
        
        if not success:
            return jsonify({'success': False, 'error': 'Journey not found'}), 404
            
        return jsonify({
            'success': True, 
            'visibility': journey['visibility']
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': 'Internal server error'}), 500
