from flask import Blueprint, g, render_template, request, session, redirect, url_for, flash
from services import departure_board_service
from utils.security import  login_required, premium_required

bp = Blueprint('departure_board', __name__, url_prefix='/departure_board')

@bp.route('', methods=['GET'])
@login_required
@premium_required
def get_departure_list():
    """Get the list of events in the departure board"""
    session['journey_page'] = 'departure_board'
    filter_type = request.args.get('filter', 'user')
    tab = request.args.get('tab', '')
    page = request.args.get('page', 1, type=int)
    search = request.args.get('q', '')

    if tab == 'manage':
        limit = 5
    else:
        limit = 8
    offset = (page - 1) * limit

    success, message, events = departure_board_service.get_departure_board(user_id=session['user_id'], search=search,
    limit=1000,
    offset=0 )
    success, message, grouped_events = departure_board_service.get_events_grouped_by(user_id=session['user_id'], group_by=filter_type,
    limit=limit,
    offset=offset)
    # Deduplicate by event_id and merge follow_types
    deduplicated = {}
    for event in events:
        event_id = event["event_id"]
        follow_type = event.get("follow_type")
        if event_id not in deduplicated:
            deduplicated[event_id] = dict(event)
            deduplicated[event_id]["follow_types"] = []
        if follow_type and follow_type not in deduplicated[event_id]["follow_types"]:
            deduplicated[event_id]["follow_types"].append(follow_type)

    unique_events = list(deduplicated.values())
    total_count = len(unique_events)
    total_pages = (total_count + limit - 1) // limit
    paginated_events = unique_events[offset:offset + limit]

    if tab == 'manage':
        total_count = departure_board_service.get_grouped_events_count(
        user_id=session['user_id'],
        group_by=filter_type
    )
    else:
        total_count = departure_board_service.get_departure_board_count(
        user_id=session['user_id'],
        search=search
    )
   

    total_pages = (total_count + limit - 1) // limit

    return render_template('departure_board/list.html', events=paginated_events, all_events=unique_events, grouped_events=grouped_events, page=page,
        total_pages=total_pages, total_count=total_count, search_term=search, filter=filter_type)

@bp.route('/grouped-list/<type>', methods=['GET'])
@login_required
@premium_required
def get_grouped_list(type):
    """Get the list of events in the departure board"""
    success, message, events = departure_board_service.get_events_grouped_by(user_id=session['user_id'], group_by=type)

    return render_template('departure_board/grouped_list.html', events=events)


@bp.route('/follow/<int:journey_id>', methods=['POST'])
@login_required
@premium_required
def follow_journey(journey_id):
    """Follow a public journey"""
    user_id = session['user_id']

    success, message = departure_board_service.follow_journey(user_id=user_id, journey_id=journey_id)
    flash(message, 'success' if success else 'danger')

    return redirect(request.referrer)

@bp.route('/unfollow/<int:journey_id>', methods=['POST'])
@login_required
@premium_required
def unfollow_journey(journey_id):
    """Unfollow a public journey"""
    user_id = session['user_id']

    success, message = departure_board_service.unfollow_journey(user_id=user_id, journey_id=journey_id)
    flash(message, 'success' if success else 'danger')

    return redirect(request.referrer)


@bp.route('/follow/user/<int:followed_id>', methods=['POST'])
@login_required
@premium_required
def follow_user(followed_id):
    """Follow a public user"""
    follower_id = session['user_id']

    success, message = departure_board_service.follow_user(follower_id=follower_id, followed_id=followed_id)
    flash(message, 'success' if success else 'danger')

    return redirect(request.referrer)

@bp.route('/unfollow/user/<int:followed_id>', methods=['POST'])
@login_required
@premium_required
def unfollow_user(followed_id):
    """Unfollow a public user"""
    follower_id = session['user_id']

    success, message = departure_board_service.unfollow_user(follower_id=follower_id, followed_id=followed_id)
    flash(message, 'success' if success else 'danger')

    return redirect(request.referrer)



@bp.route('/follow/location/<int:location_id>', methods=['POST'])
@login_required
@premium_required
def follow_location(location_id):
    """Follow a location"""
    user_id = session['user_id']

    success, message = departure_board_service.follow_location(user_id=user_id, location_id=location_id)
    flash(message, 'success' if success else 'danger')
    return redirect(request.referrer)

@bp.route('/unfollow/location/<int:location_id>', methods=['POST'])
@login_required
@premium_required
def unfollow_location(location_id):
    """Unfollow a location"""
    user_id = session['user_id']

    success, message = departure_board_service.unfollow_location(user_id=user_id, location_id=location_id)
    flash(message, 'success' if success else 'danger')
    return redirect(request.referrer)

