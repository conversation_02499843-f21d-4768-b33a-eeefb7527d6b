from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify, g
from services import community_service, departure_board_service, location_service as location_service, report_service, subscription_service
from services import event_service as event_service
from services import journey_service as journey_service
from utils.security import content_manager_required, login_required
from utils.file_utils import save_event_image, allowed_file, ALLOWED_EXTENSIONS, MAX_FILE_SIZE, get_safe_image_url
from datetime import datetime
import os
from utils.logger import get_logger

logger = get_logger(__name__)

bp = Blueprint('event', __name__, url_prefix='/event')


@bp.route('/new/<int:journey_id>', methods=['GET'])
@login_required
def get_event_form(journey_id):
    """Get the form for creating a new event"""
    success, message, journey = journey_service.get_journey(journey_id, user_id=session.get('user_id'))
    premium_access = subscription_service.check_can_use_premium_features(user_id=session['user_id'])
    return render_template('event/create.html', journey=journey, premium_access=premium_access)


@bp.route('/new/<int:journey_id>', methods=['POST'])
@login_required
def create_event(journey_id):
    """Create a new event for a journey"""
    # Check if journey exists and user has permission
    success, message, journey_data = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.private_journey', journey_id=journey_id))

    # Check if user is the owner
    if journey_data['user_id'] != session['user_id']:
        flash('Only the owner can add events to a journey.', 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

    # Get form data
    title = request.form.get('title')
    description = request.form.get('description')
    location_name = request.form.get('location')
    start_datetime = request.form.get('start_datetime')
    end_datetime = request.form.get('end_datetime') or None
    longitude_str = request.form.get('longitude')
    latitude_str = request.form.get('latitude')

    # Convert longitude and latitude to float if provided
    longitude = None
    latitude = None
    try:
        if longitude_str and longitude_str.strip():
            longitude = float(longitude_str)
        if latitude_str and latitude_str.strip():
            latitude = float(latitude_str)
    except ValueError as e:
        flash('Invalid location coordinates provided.', 'danger')
        return redirect(url_for('event.get_event_form', journey_id=journey_id))

    # Convert datetime strings to datetime objects
    try:
        start_datetime = datetime.strptime(start_datetime, '%Y-%m-%dT%H:%M')
        if end_datetime:
            end_datetime = datetime.strptime(end_datetime, '%Y-%m-%dT%H:%M')
    except ValueError:
        flash('Invalid date/time format.', 'danger')
        return redirect(url_for('event.get_event_form', journey_id=journey_id))

    if end_datetime and start_datetime >= end_datetime:
        flash("End date and time must be after start date and time.", 'danger')
        return render_template(
                'journey/detail.html',
                journey=journey_data,
                title=title,
                description=description,
                location=location_name,
                start_datetime=request.form.get('start_datetime'),
                end_datetime=request.form.get('end_datetime'),
                photos=photos,
                showMoal=True
            )

    # Check if images were uploaded
    photos = request.files.getlist('images')

  # Validate images
    valid_images = []
    for image in photos:
        if image and image.filename:
            # Validate file size
            image.seek(0, os.SEEK_END)  # Move to the end of the file
            file_size = image.tell()  # Get file size in bytes
            image.seek(0)  # Reset file pointer to the beginning

            if file_size > 5 * 1024 * 1024:
                flash(f"Image '{image.filename}' exceeds the 5MB size limit and will be skipped.", "warning")
                continue

            valid_images.append(image)

    # Create event
    success, message, event_id = event_service.create_event(
        journey_id=journey_id,
        user_id=session['user_id'],
        location_name=location_name,
        title=title,
        description=description,
        start_datetime=start_datetime,
        end_datetime=end_datetime,
        images=valid_images,
        latitude=latitude,
        longitude=longitude
    )

    # Check if this is an AJAX request
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or 'application/json' in request.headers.get('Accept', ''):
        # Return JSON response for AJAX requests
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'event_id': event_id,
                'redirect_url': url_for('event.get_event_details', event_id=event_id)
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            })

    flash(message, 'success' if success else 'danger')

    # Check if 'public' or 'private' parameter is in the URL referer
    referer = request.referrer or ""

    if 'public' in referer:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    elif 'private' in referer:
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        # Default fallback to the standard journey view
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))

@bp.route('/<int:event_id>/edit', methods=['GET'])
@login_required
def get_event_edit_form(event_id):
    """Render the form for editing an event"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )
    premium_access = subscription_service.check_can_use_premium_features(
        user_id=session['user_id']
    )
    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    success, message, journey = journey_service.get_journey(event['journey_id'], user_id=session['user_id'],)
    return render_template('event/edit.html', event=event, journey=journey, premium_access=premium_access)

@bp.route('/<int:event_id>/edit', methods=['POST'])
@login_required
def update_event(event_id):
    """Update an event"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    journey_id = event.get('journey_id')

    # Get form data
    title = request.form.get('title')
    description = request.form.get('description')
    location_name = request.form.get('location')
    start_datetime_str = request.form.get('start_datetime')
    end_datetime_str = request.form.get('end_datetime') or None
    edit_reason = request.form.get('edit_reason')
    longitude_str = request.form.get('longitude')
    latitude_str = request.form.get('latitude')
    staff_location_scope = request.form.get('staff_location_scope')

    # Convert longitude and latitude to float if provided
    longitude = None
    latitude = None
    try:
        if longitude_str and longitude_str.strip():
            longitude = float(longitude_str)
        if latitude_str and latitude_str.strip():
            latitude = float(latitude_str)
    except ValueError as e:
        flash('Invalid location coordinates provided.', 'danger')
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    # Parse datetime fields - they may be None for content manager staff edits
    start_datetime = None
    end_datetime = None
    try:
        if start_datetime_str:
            start_datetime = datetime.strptime(start_datetime_str, '%Y-%m-%dT%H:%M')
        if end_datetime_str:
            end_datetime = datetime.strptime(end_datetime_str, '%Y-%m-%dT%H:%M')
    except ValueError:
        flash('Invalid date/time format.', 'danger')
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    # Only pass datetime values to service if they have actually changed
    # Compare with original event values (normalize to minute precision)
    if start_datetime and event['start_datetime']:
        original_start_normalized = event['start_datetime'].replace(second=0, microsecond=0)
        new_start_normalized = start_datetime.replace(second=0, microsecond=0)
        if original_start_normalized == new_start_normalized:
            start_datetime = None  # No change, don't pass to service

    if end_datetime and event.get('end_datetime'):
        original_end_normalized = event['end_datetime'].replace(second=0, microsecond=0)
        new_end_normalized = end_datetime.replace(second=0, microsecond=0)
        if original_end_normalized == new_end_normalized:
            end_datetime = None  # No change, don't pass to service

    # Deleted image IDs
    deleted_images_str = request.form.get('deleted_images', '')
    deleted_image_ids = [int(id_str) for id_str in deleted_images_str.split(',') if id_str.strip().isdigit()]

    # Get journey details to determine ownership
    success, message, journey = journey_service.get_journey(event['journey_id'], user_id=session['user_id'])
    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    # Check if user is a staff member editing someone else's event
    from utils.permissions import PermissionGroups
    is_staff_edit = session.get('role') in PermissionGroups.CONTENT_MANAGERS and journey['user_id'] != session['user_id']

    # Staff editing other users' content must provide a reason
    if is_staff_edit and (not edit_reason or not edit_reason.strip()):
        flash('You must provide a reason when editing a user\'s event.', 'danger')
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    # Content manager staff cannot modify coordinates - set them to None to prevent service validation errors
    if is_staff_edit:
        longitude = None
        latitude = None

    # Process new images
    images = {}
    if 'new_images[]' in request.files:
        images['new'] = []
        for file in request.files.getlist('new_images[]'):
            if file and file.filename:
                images['new'].append({'file': file})

    # Process deleted images
    if deleted_image_ids:
        images['deleted'] = deleted_image_ids

    # Update event
    success, message = event_service.update_event(
        event_id=event_id,
        user_id=session['user_id'],
        title=title,
        description=description,
        start_datetime=start_datetime,
        end_datetime=end_datetime,
        location_name=location_name,
        images=images,
        edit_reason=edit_reason,
        longitude=longitude,
        latitude=latitude,
        staff_location_scope=staff_location_scope
    )

    # Check if this is an AJAX request
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or 'application/json' in request.headers.get('Accept', ''):
        # Handle the special "no_changes" case
        if success == "no_changes":
            return jsonify({
                'success': True,
                'message': message,
                'no_changes': True,
                'redirect_url': url_for('event.get_event_details', event_id=event_id)
            })

        # Return JSON response for AJAX requests
        return jsonify({
            'success': success,
            'message': message,
            'redirect_url': url_for('event.get_event_details', event_id=event_id)
        })

    # Handle the special "no_changes" case for non-AJAX requests
    if success == "no_changes":
        flash(message, 'info')
        success = True
    elif success:
        flash(message, 'success')
    else:
        flash(message, 'danger')

    # Check referrer to determine where to redirect
    referrer = request.referrer or ""

    # If the edit was initiated from the event detail page, go back there
    if '/event/' + str(event_id) in referrer:
        return redirect(url_for('event.get_event_details', event_id=event_id))

    # Otherwise redirect to the journey page
    if 'journey_page' not in session:
        if 'public' in referrer:
            session['journey_page'] = 'public'
        else:
            session['journey_page'] = 'private'

    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))

# New image management routes
@bp.route('/<int:event_id>/image/add', methods=['POST'])
@login_required
def add_event_image_route(event_id):
    """Add an image to an event"""
    if 'image' not in request.files or not request.files['image'].filename:
        flash('No file selected', 'danger')
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    image = request.files['image']
    caption = request.form.get('caption', '')
    is_primary = request.form.get('is_primary') == 'on'

    # Validate file size
    image.seek(0, os.SEEK_END)
    file_size = image.tell()
    image.seek(0)

    if file_size > 5 * 1024 * 1024:
        flash("Image size must be less than 5MB.", "danger")
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    journey_id = event.get('journey_id')

    # Add the image
    success, message, image_id = event_service.add_event_image(
        event_id=event_id,
        user_id=session['user_id'],
        image=image,
        caption=caption,
        is_primary=is_primary
    )

    flash(message, 'success' if success else 'danger')

    # Determine redirect based on session
    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))



# New image routes
@bp.route('/<int:event_id>/image/<int:image_id>/update', methods=['POST'])
@login_required
def update_event_image_route(event_id, image_id):
    """Update an event image properties"""
    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    journey_id = event.get('journey_id')

    caption = request.form.get('caption', '')
    is_primary = request.form.get('is_primary') == 'on'

    # Update the image metadata
    success, message = event_service.update_event_image(
        image_id=image_id,
        user_id=session['user_id'],
        caption=caption,
        is_primary=is_primary
    )

    flash(message, 'success' if success else 'danger')

    # Determine redirect based on session
    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))



@bp.route('/<int:event_id>/image/<int:image_id>/replace', methods=['POST'])
@login_required
def replace_event_image_route(event_id, image_id):
    """Replace an event's image file"""
    if 'image' not in request.files or not request.files['image'].filename:
        flash('No file selected', 'danger')
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    image = request.files['image']

    # Validate file size
    image.seek(0, os.SEEK_END)
    file_size = image.tell()
    image.seek(0)

    if file_size > MAX_FILE_SIZE:
        flash("Image size must be less than 5MB.", "danger")
        return redirect(url_for('event.get_event_edit_form', event_id=event_id))

    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    journey_id = event.get('journey_id')

    # Update the image file
    success, message = event_service.update_event_image(
        image_id=image_id,
        user_id=session['user_id'],
        new_image=image
    )

    flash(message, 'success' if success else 'danger')

    # Determine redirect based on session
    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))



@bp.route('/<int:event_id>/image/<int:image_id>/delete', methods=['POST'])
@login_required
def delete_event_image_route(event_id, image_id):
    """Delete an event image"""
    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        # Check if this is an AJAX request
        if request.headers.get('Content-Type') == 'application/json' or request.is_json:
            return jsonify({'success': False, 'message': message})
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    journey_id = event.get('journey_id')

    # Delete the image
    success, message = event_service.delete_event_image(
        image_id=image_id,
        user_id=session['user_id'],
    )

    # Check if this is an AJAX request
    if request.headers.get('Content-Type') == 'application/json' or request.is_json:
        return jsonify({'success': success, 'message': message})

    flash(message, 'success' if success else 'danger')

    # Determine redirect based on session
    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))

@bp.route('/<int:event_id>/image/<int:image_id>/staff-delete', methods=['POST'])
@login_required
def delete_event_image_staff_route(event_id, image_id):
    """Delete an event image with edit reason (for content manager staff)"""
    from utils.permissions import PermissionGroups
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Staff delete request: event_id={event_id}, image_id={image_id}, user_id={session.get('user_id')}")

    # Check if user is content manager staff
    if session.get('role') not in PermissionGroups.CONTENT_MANAGERS:
        logger.warning(f"Access denied for user {session.get('user_id')} - not content manager")
        return jsonify({'success': False, 'message': 'Access denied. Content manager role required.'})

    # Get edit reason from request
    edit_reason = request.form.get('edit_reason', '').strip()
    logger.info(f"Edit reason provided: '{edit_reason}'")

    if not edit_reason:
        logger.warning("No edit reason provided")
        return jsonify({'success': False, 'message': 'Edit reason is required for staff image deletions.'})

    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        logger.error(f"Failed to get event {event_id}: {message}")
        return jsonify({'success': False, 'message': message})

    journey_id = event.get('journey_id')
    logger.info(f"Event found, journey_id={journey_id}")

    # Delete the image with edit reason
    try:
        success, message = event_service.delete_event_image(
            image_id=image_id,
            user_id=session['user_id'],
            edit_reason=edit_reason
        )
        logger.info(f"Delete result: success={success}, message='{message}'")
    except Exception as e:
        logger.error(f"Exception during image deletion: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error deleting image: {str(e)}'})

    if success:
        # Determine redirect URL based on journey visibility
        journey_visibility = event.get('journey', {}).get('visibility', 'private')
        if journey_visibility == 'published':
            redirect_url = url_for('journey.get_published_journey_detail', journey_id=journey_id)
        elif journey_visibility == 'public':
            redirect_url = url_for('journey.get_public_journey', journey_id=journey_id)
        else:
            redirect_url = url_for('journey.get_private_journey', journey_id=journey_id)

        logger.info(f"Deletion successful, redirect_url={redirect_url}")
        return jsonify({
            'success': True,
            'message': message,
            'redirect_url': redirect_url
        })
    else:
        logger.error(f"Deletion failed: {message}")
        return jsonify({'success': False, 'message': message})

@bp.route('/<int:event_id>/images/staff-delete-batch', methods=['POST'])
@login_required
def delete_event_images_staff_batch_route(event_id):
    """Delete multiple event images with edit reason (for content manager staff)"""
    from utils.permissions import PermissionGroups
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Staff batch delete request: event_id={event_id}, user_id={session.get('user_id')}")

    # Check if user is content manager staff
    if session.get('role') not in PermissionGroups.CONTENT_MANAGERS:
        logger.warning(f"Access denied for user {session.get('user_id')} - not content manager")
        return jsonify({'success': False, 'message': 'Access denied. Content manager role required.'})

    # Get image IDs and edit reason from request
    image_ids_str = request.form.get('image_ids', '')
    edit_reason = request.form.get('edit_reason', '').strip()

    logger.info(f"Image IDs: '{image_ids_str}', Edit reason: '{edit_reason}'")

    if not edit_reason:
        logger.warning("No edit reason provided")
        return jsonify({'success': False, 'message': 'Edit reason is required for staff image deletions.'})

    # Parse image IDs
    try:
        image_ids = [int(id_str.strip()) for id_str in image_ids_str.split(',') if id_str.strip().isdigit()]
    except ValueError:
        logger.error(f"Invalid image IDs format: {image_ids_str}")
        return jsonify({'success': False, 'message': 'Invalid image IDs provided.'})

    if not image_ids:
        logger.warning("No valid image IDs provided")
        return jsonify({'success': False, 'message': 'No valid image IDs provided.'})

    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        logger.error(f"Failed to get event {event_id}: {message}")
        return jsonify({'success': False, 'message': message})

    journey_id = event.get('journey_id')
    logger.info(f"Event found, journey_id={journey_id}")

    # Delete the images with edit reason using batch function
    try:
        success, message, success_count, fail_count = event_service.delete_multiple_event_images(
            event_id=event_id,
            image_ids=image_ids,
            user_id=session['user_id'],
            edit_reason=edit_reason
        )
        logger.info(f"Batch delete result: success={success}, message='{message}', success_count={success_count}, fail_count={fail_count}")
    except Exception as e:
        logger.error(f"Exception during batch image deletion: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': f'Error deleting images: {str(e)}'})

    if success:
        # Determine redirect URL based on journey visibility
        journey_visibility = event.get('journey', {}).get('visibility', 'private')
        if journey_visibility == 'published':
            redirect_url = url_for('journey.get_published_journey_detail', journey_id=journey_id)
        elif journey_visibility == 'public':
            redirect_url = url_for('journey.get_public_journey', journey_id=journey_id)
        else:
            redirect_url = url_for('journey.get_private_journey', journey_id=journey_id)

        logger.info(f"Batch deletion successful, redirect_url={redirect_url}")
        return jsonify({
            'success': True,
            'message': message,
            'success_count': success_count,
            'fail_count': fail_count,
            'redirect_url': redirect_url
        })
    else:
        logger.error(f"Batch deletion failed: {message}")
        return jsonify({'success': False, 'message': message})

@bp.route('/<int:event_id>/images', methods=['GET'])
@login_required
def get_event_images_route(event_id):
    """Get all images for an event (for staff image management)"""
    import os
    from utils.permissions import PermissionGroups
    from utils.file_utils import get_safe_image_url

    # Check if user is content manager staff or event owner
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        return jsonify({'success': False, 'message': message})

    # Get journey to check permissions
    journey_id = event.get('journey_id')
    success, message, journey = journey_service.get_journey(journey_id, session['user_id'])

    if not success:
        return jsonify({'success': False, 'message': 'Journey not found'})

    # Check permissions
    is_owner = journey.get('user_id') == session['user_id']
    is_staff = session.get('role') in PermissionGroups.CONTENT_MANAGERS

    if not (is_owner or is_staff):
        return jsonify({'success': False, 'message': 'Access denied. You do not have permission to view these images.'})

    # Get images for the event
    success, message, images = event_service.get_event_images(event_id, session['user_id'])

    if not success:
        images = []

    # Format the images for the frontend, but only include images that actually exist
    formatted_images = []
    for image in images or []:
        image_filename = image.get('image_filename')
        if image_filename:
            # Check if the actual image file exists
            file_path = os.path.join('static', 'uploads', 'event_images', image_filename)
            if os.path.exists(file_path):
                formatted_images.append({
                    'id': image.get('id'),
                    'url': url_for('static', filename=f'uploads/event_images/{image_filename}'),
                    'image_filename': image_filename,
                    'is_primary': image.get('is_primary', False),
                    'caption': image.get('caption', ''),
                    'created_at': image.get('created_at'),
                    'updated_at': image.get('updated_at')
                })

    return jsonify({
        'success': True,
        'images': formatted_images,
        'is_owner': is_owner,
        'is_staff': is_staff
    })

@bp.route('/<int:event_id>/image/set-primary/<int:image_id>', methods=['POST'])
@login_required
def set_primary_event_image_route(event_id, image_id):
    """Set an image as the primary image for an event"""
    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_journeys'))

    journey_id = event.get('journey_id')

    # Set the image as primary
    success, message = event_service.update_event_image(
        image_id=image_id,
        user_id=session['user_id'],
        is_primary=True
    )

    flash(message, 'success' if success else 'danger')

    # Determine redirect based on session
    if session.get('journey_page') == 'private':
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    else:
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))



@bp.route('/<int:event_id>/delete', methods=['POST'])
@login_required
def delete_event(event_id):
    """Delete an event"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_private_journey', journey_id=event['journey_id']))

    journey_id = event['journey_id']

    success, message = event_service.delete_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    # Ensure journey_page is set for proper redirect
    if 'journey_page' not in session:
        # Try to determine from referrer
        if request.referrer and 'public' in request.referrer:
            session['journey_page'] = 'public'
        else:
            session['journey_page'] = 'private'

    if success and session.get('journey_page') == 'private':
        flash('Event deleted successfully!', 'success')
        return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
    elif success and session.get('journey_page') == 'public':
        flash('Event deleted successfully!', 'success')
        return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
    else:
        flash(message, 'danger')
        # If journey_page is not set, try to determine from referrer
        if request.referrer and 'public' in request.referrer:
            return redirect(url_for('journey.get_public_journey', journey_id=journey_id))
        else:
            return redirect(url_for('journey.get_private_journey', journey_id=journey_id))

# admin
@bp.route('/manage/<int:event_id>/edit', methods=['GET'])
@login_required
@content_manager_required
def get_admin_event_edit_form(event_id):
    """Get the form for editing an event (admin view)"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'],
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    success, message, journey = journey_service.get_journey(
        journey_id=event['journey_id'],
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    return render_template(
        'admin/event/edit.html',
        event=event,
        journey=journey
    )

@bp.route('/manage/<int:event_id>/edit', methods=['POST'])
@login_required
@content_manager_required
def update_admin_event(event_id):
    """Update an event as an admin"""
    # Check if event exists
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    journey_id = event.get('journey_id')

    # Get form data
    title = request.form.get('title')
    description = request.form.get('description')
    location_name = request.form.get('location')
    start_datetime = request.form.get('start_datetime')
    end_datetime = request.form.get('end_datetime') or None

    # Convert datetime strings to datetime objects
    try:
        start_datetime = datetime.strptime(start_datetime, '%Y-%m-%dT%H:%M')
        if end_datetime:
            end_datetime = datetime.strptime(end_datetime, '%Y-%m-%dT%H:%M')
    except ValueError:
        flash('Invalid date/time format.', 'danger')
        return redirect(url_for('event.get_admin_event_edit_form', event_id=event_id))

    # Update event details (photo updates handled separately)
    success, message = event_service.update_event(
        event_id=event_id,
        user_id=session['user_id'],
        title=title,
        description=description,
        start_datetime=start_datetime,
        end_datetime=end_datetime,
        location_name=location_name
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))



@bp.route('/manage/<int:event_id>/image/<int:image_id>/update', methods=['POST'])
@login_required
@content_manager_required
def update_admin_event_image(event_id, image_id):
    """Update an event image properties (admin view)"""
    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    journey_id = event.get('journey_id')

    # Check if admin is also the journey owner - only owners can update images
    if event['journey']['user_id'] != session['user_id']:
        flash('Only the journey owner can update event images', 'danger')
        return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

    caption = request.form.get('caption', '')
    is_primary = request.form.get('is_primary') == 'on'

    # Update the image metadata
    success, message = event_service.update_event_image(
        image_id=image_id,
        user_id=session['user_id'],
        caption=caption,
        is_primary=is_primary
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/<int:event_id>/image/<int:image_id>/replace', methods=['POST'])
@login_required
@content_manager_required
def replace_admin_event_image(event_id, image_id):
    """Replace an event's image file (admin view)"""
    if 'photo' not in request.files or not request.files['photo'].filename:
        flash('No file selected', 'danger')
        return redirect(url_for('event.get_admin_event_edit_form', event_id=event_id))

    photo = request.files['photo']

    # Validate file size
    photo.seek(0, os.SEEK_END)
    file_size = photo.tell()
    photo.seek(0)

    if file_size > MAX_FILE_SIZE:
        flash("Image size must be less than 5MB.", "danger")
        return redirect(url_for('event.get_admin_event_edit_form', event_id=event_id))

    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    journey_id = event.get('journey_id')

    # Check if admin is also the journey owner - only owners can replace images
    if event['journey']['user_id'] != session['user_id']:
        flash('Only the journey owner can replace event images', 'danger')
        return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

    # Update the image file
    success, message = event_service.update_event_image(
        image_id=image_id,
        user_id=session['user_id'],
        new_image=photo
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/<int:event_id>/image/<int:image_id>/delete', methods=['POST'])
@login_required
@content_manager_required
def delete_admin_event_image(event_id, image_id):
    """Delete an event image (admin view)"""
    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    journey_id = event.get('journey_id')

    # Delete the image
    success, message = event_service.delete_event_image(
        image_id=image_id,
        user_id=session['user_id']
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/<int:event_id>/image/set-primary/<int:image_id>', methods=['POST'])
@login_required
@content_manager_required
def set_primary_admin_event_image(event_id, image_id):
    """Set an image as the primary image for an event (admin view)"""
    # Get event to get journey_id for later redirect
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_admin_events'))

    journey_id = event.get('journey_id')

    # Set the image as primary
    success, message = event_service.update_event_image(
        image_id=image_id,
        user_id=session['user_id'],
        is_primary=True
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/<int:journey_id>/<int:event_id>/delete', methods=['POST'])
@login_required
@content_manager_required
def delete_admin_event_by_journey(journey_id, event_id):
    """Delete an event (admin view)"""
    success, message = event_service.delete_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/new/<int:journey_id>', methods=['GET'])
@login_required
@content_manager_required
def get_admin_event_form(journey_id):
    """Get the form for creating a new event (admin view)"""
    success, message, journey = journey_service.get_journey(journey_id, user_id=session.get('user_id'))

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.admin_hidden_journeys'))

    return render_template('admin/event/create.html', journey=journey)

@bp.route('/manage/new/<int:journey_id>', methods=['POST'])
@login_required
@content_manager_required
def create_admin_event(journey_id):
    """Create a new event for a journey (admin view)"""
    # Check if journey exists and user has permission
    success, message, journey_data = journey_service.get_journey(
        journey_id=journey_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.admin_hidden_journeys'))

    # Get form data
    title = request.form.get('title')
    description = request.form.get('description')
    location_name = request.form.get('location')
    start_datetime = request.form.get('start_datetime')
    end_datetime = request.form.get('end_datetime') or None

    # Convert datetime strings to datetime objects
    try:
        start_datetime = datetime.strptime(start_datetime, '%Y-%m-%dT%H:%M')
        if end_datetime:
            end_datetime = datetime.strptime(end_datetime, '%Y-%m-%dT%H:%M')
    except ValueError:
        flash('Invalid date/time format.', 'danger')
        return redirect(url_for('event.get_admin_event_form', journey_id=journey_id))

    # Check if image was uploaded
    photo = request.files.get('photo')

    if photo and photo.filename:
        # Validate file size
        photo.seek(0, os.SEEK_END)  # Move to the end of the file
        file_size = photo.tell()  # Get file size in bytes
        photo.seek(0)  # Reset file pointer to the beginning

        if file_size > 5 * 1024 * 1024:
            flash("Image size must be less than 5MB.", "danger")
            return redirect(url_for('event.get_admin_event_form', journey_id=journey_id))

    if end_datetime and start_datetime >= end_datetime:
        flash("End date and time must be after start date and time.", 'danger')
        return redirect(url_for('event.get_admin_event_form', journey_id=journey_id))

    # Create event
    success, message, event_id = event_service.create_event(
        journey_id=journey_id,
        user_id=session['user_id'],
        location_name=location_name,
        title=title,
        description=description,
        start_datetime=start_datetime,
        end_datetime=end_datetime,
        photo=photo
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/<int:event_id>', methods=['GET'])
@login_required
@content_manager_required
def get_admin_event(event_id):
    """Get an event by ID (admin view)"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.admin_hidden_journeys'))

    journey_id = event.get('journey_id')

    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))

@bp.route('/manage/<int:journey_id>/<int:event_id>/delete', methods=['POST'])
@login_required
@content_manager_required
def delete_admin_event(journey_id, event_id):
    """Delete an event (admin view)"""
    success, message = event_service.delete_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('journey.get_admin_journey', journey_id=journey_id))



# New image gallery route
@bp.route('/image/<int:event_id>', methods=['GET'])
def get_image_gallery(event_id):
    """Display a gallery of images for an event"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'] if g.current_user else None
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('journey.get_public_journeys'))

    # Get images from event_images table
    success, message, images = event_service.get_event_images(event_id, session['user_id'] if g.current_user else None)

    if not success:
        images = []

    # Filter out images where the actual files don't exist
    # This prevents showing placeholder images when database has records but files are missing
    if images:
        import os

        filtered_images = []
        for image in images:
            image_filename = image.get('image_filename')
            if image_filename:
                # Check if the actual file exists
                file_path = os.path.join('static', 'uploads', 'event_images', image_filename)
                if os.path.exists(file_path):
                    filtered_images.append(image)

        images = filtered_images if filtered_images else None

    return render_template(
        'event/image.html', event=event, event_images=images
    )

@bp.route('/location/<int:event_id>', methods=['GET'])
@login_required
def get_location(event_id):
    """Display a location from event details"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )
    is_following_location, message = departure_board_service.check_following_location(user_id=session['user_id'], location_id=event['location_id'])
    return render_template(
        'event/location.html', event=event, is_following_location=is_following_location
    )

@bp.route('/<int:event_id>/detail', methods=['GET'])
def get_event_details(event_id):
    """Render the details page for an event"""
    # Get event details, passing None as user_id for anonymous users
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id'] if g.current_user else None
    )

    if not success:
        # For anonymous users accessing public journeys, provide a more helpful message
        if not g.current_user and "permission" in message.lower():
            flash("Please log in to access this page.", "warning")
            return redirect(url_for('auth.login'))
        else:
            flash(message, 'danger')
            return redirect(url_for('journey.get_public_journeys'))

    # Get journey details
    success, message, journey = journey_service.get_journey(
        event['journey_id'],
        user_id=session['user_id'] if g.current_user else None
    )

    if not success:
        # This shouldn't happen if event access was granted, but handle gracefully
        if not g.current_user:
            flash("Please log in to access this page.", "warning")
            return redirect(url_for('auth.login'))
        else:
            flash(message, 'danger')
            return redirect(url_for('journey.get_public_journeys'))

    # Set journey_page session based on journey visibility for proper back button navigation
    # Only update if not already set to 'notifications' or 'departure_board' (preserve navigation context)
    if session.get('journey_page') not in ['notifications', 'departure_board']:
        if journey['visibility'] == 'published':
            session['journey_page'] = 'published'
        elif journey['visibility'] == 'public':
            session['journey_page'] = 'public'
        else:  # private
            session['journey_page'] = 'private'

    # Initialize variables for logged-in users
    comments = []
    user_liked_event = False
    premium_access = False

    # Get like count for both logged-in and non-logged-in users
    event_likes = community_service.count_event_likes(event_id=event_id)

    # Only get additional data if user is logged in
    if g.current_user:
        success, message, comments = community_service.get_event_comments(event_id=event_id, include_hidden=True)
        user_liked_event = community_service.check_user_liked_event(session['user_id'], event_id)
        premium_access = subscription_service.check_can_use_premium_features(user_id=session['user_id'],)

        for comment in comments:
            comment['user_has_liked'] = community_service.check_user_interaction(
                session['user_id'], comment['id'], 'like')
            comment['user_has_disliked'] = community_service.check_user_interaction(
                session['user_id'], comment['id'], 'dislike')
            comment['user_has_reported'] = report_service.has_user_flagged_comment(
                session['user_id'], comment['id'])
    else:
        # For non-logged-in users, get public comments only
        success, message, comments = community_service.get_event_comments(event_id=event_id, include_hidden=False)

    # Get images using the event_images table
    success, message, images = event_service.get_event_images(event_id, session['user_id'] if g.current_user else None)
    if not success:
        images = []

    # Filter out images where the actual files don't exist
    # This prevents showing placeholder images when database has records but files are missing
    if images:
        import os

        filtered_images = []
        for image in images:
            image_filename = image.get('image_filename')
            if image_filename:
                # Check if the actual file exists
                file_path = os.path.join('static', 'uploads', 'event_images', image_filename)
                if os.path.exists(file_path):
                    filtered_images.append(image)
                else:
                    logger.warning(f"Image file not found: {file_path} (removing from display)")

        images = filtered_images if filtered_images else None

    return render_template('event/detail.html',
                         event=event,
                         journey=journey,
                         comments=comments,
                         images=images,
                         event_likes=event_likes,
                         user_liked_event=user_liked_event,
                         premium_access=premium_access)

@bp.route('/<int:event_id>/comment', methods=['POST'])
@login_required
def add_event_comment(event_id):
    """Add a comment to an event"""
    content = request.form.get('content', '').strip()

    if not content:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Comment cannot be empty.'})
        flash('Comment cannot be empty.', 'warning')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    success, message, comment_id = event_service.add_event_comment(
        event_id=event_id,
        user_id=session['user_id'],
        content=content
    )

    # Handle AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if success:
            # Get the newly created comment data
            from data import community_data
            comment = community_data.get_comment(comment_id)
            if comment:
                # Add user interaction status (new comment, so no likes yet)
                comment['user_has_liked'] = False
                comment['user_has_disliked'] = False
                comment['user_has_reported'] = False

                return jsonify({
                    'success': True,
                    'message': message,
                    'comment': comment
                })

        return jsonify({'success': False, 'message': message})

    # Handle regular form submissions (fallback)
    flash(message, 'success' if success else 'danger')
    return redirect(url_for('event.get_event_details', event_id=event_id))


@bp.route('/<int:event_id>/comment/<int:comment_id>/interact', methods=['POST'])
@login_required
def interact_with_event_comment(event_id, comment_id):
    """Like or dislike a comment on an event"""
    interaction_type = request.form.get('action')
    if interaction_type not in ('like', 'dislike'):
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Invalid interaction type.'})
        flash('Invalid interaction type.', 'danger')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    success, message, interaction_id = event_service.interact_with_comment(
        comment_id=comment_id,
        user_id=session['user_id'],
        interaction_type=interaction_type
    )

    # Handle AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if success:
            # Get updated comment data for response
            from data import community_data
            comment = community_data.get_comment(comment_id)
            if comment:
                # Check user's current interaction status
                user_has_liked = community_service.check_user_interaction(
                    session['user_id'], comment_id, 'like')
                user_has_disliked = community_service.check_user_interaction(
                    session['user_id'], comment_id, 'dislike')

                return jsonify({
                    'success': True,
                    'message': message,
                    'comment_id': comment_id,
                    'like_count': comment['like_count'],
                    'dislike_count': comment['dislike_count'],
                    'user_has_liked': user_has_liked[0] if user_has_liked else False,
                    'user_has_disliked': user_has_disliked[0] if user_has_disliked else False
                })

        return jsonify({'success': False, 'message': message})

    # Handle regular form submissions (fallback)
    flash(message, 'success' if success else 'danger')
    return redirect(url_for('event.get_event_details', event_id=event_id))

@bp.route('/<int:event_id>/comment/<int:comment_id>/delete', methods=['POST'])
@login_required
def delete_event_comment(event_id, comment_id):
    """Delete a comment on an event"""
    success, message = community_service.delete_comment(
        comment_id=comment_id,
        user_id=session['user_id'],
    )

    # Handle AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({
            'success': success,
            'message': message,
            'comment_id': comment_id
        })

    # Handle regular form submissions (fallback)
    flash(message, 'success' if success else 'danger')
    return redirect(url_for('event.get_event_details', event_id=event_id))


@bp.route('/<int:event_id>/like', methods=['POST'])
@login_required
def like_event(event_id):
    """Like an event"""
    success, message = community_service.like_event(
        user_id=session['user_id'],
        event_id=event_id,
    )

    flash(message, 'success' if success else 'danger')
    return redirect(url_for('event.get_event_details', event_id=event_id))


@bp.route('/comment/<int:comment_id>/report', methods=['POST'])
def report_comment(comment_id):
    reporter_id = session.get('user_id')
    if not reporter_id:
        flash("You must be logged in to report a comment.", "danger")
        return redirect(url_for('auth.login'))

    if report_service.has_user_flagged_comment(reporter_id, comment_id):
        flash("You have already reported this comment.", "warning")
        return redirect(request.referrer)

    reason = request.form.get('reason', '').strip()

    success, message, report_id = report_service.report_content(
        reporter_id=reporter_id,
        content_type='comment',
        content_id=comment_id,
        reason=reason
    )

    flash(message, 'success' if success else 'danger')
    return redirect(request.referrer)

@bp.route('/<int:event_id>/images-data', methods=['GET'])
@login_required
def get_event_images_data(event_id):
    """Get event images as JSON for the image management interface"""
    import os
    from utils.file_utils import get_safe_image_url
    
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        return jsonify({'success': False, 'message': message}), 404

    # Get journey to check if user has permission
    journey_id = event.get('journey_id')
    success, message, journey = journey_service.get_journey(journey_id, session['user_id'])

    if not success:
        return jsonify({'success': False, 'message': 'Journey not found'}), 404

    # Get event images
    success, message, images = event_service.get_event_images(event_id, session['user_id'])

    if not success:
        images = []

    # Format the images for the frontend, but only include images that actually exist
    formatted_images = []
    for image in images:
        image_filename = image.get('image_filename')
        if image_filename:
            # Check if the actual image file exists
            file_path = os.path.join('static', 'uploads', 'event_images', image_filename)
            if os.path.exists(file_path):
                formatted_images.append({
                    'id': image.get('id'),
                    'url': url_for('static', filename=f'uploads/event_images/{image_filename}'),
                    'filename': image_filename
                })

    # Check premium access
    premium_access = subscription_service.check_can_use_premium_features(session['user_id'])

    return jsonify({
        'success': True,
        'images': formatted_images,
        'is_owner': journey.get('user_id') == session['user_id'],
        'premium_access': premium_access
    })

@bp.route('/<int:event_id>/upload-images', methods=['POST'])
@login_required
def upload_event_images(event_id):
    """Upload images for an event"""
    success, message, event = event_service.get_event(
        event_id=event_id,
        user_id=session['user_id']
    )

    if not success:
        flash(message, 'danger')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    # Get journey to check if user has permission
    journey_id = event.get('journey_id')
    success, message, journey = journey_service.get_journey(journey_id, session['user_id'])

    if not success or journey.get('user_id') != session['user_id']:
        flash('You do not have permission to modify this event', 'danger')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    # Check if images were included
    if 'images[]' not in request.files:
        flash('No files selected', 'danger')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    files = request.files.getlist('images[]')

    # Check if any files are valid
    valid_files = [f for f in files if f.filename and allowed_file(f.filename)]
    if not valid_files:
        flash(f'No valid files selected. Allowed types: {", ".join(ALLOWED_EXTENSIONS)}', 'danger')
        return redirect(url_for('event.get_event_details', event_id=event_id))

    # Check premium restrictions
    premium_access = subscription_service.check_can_use_premium_features(session['user_id'])
    if not premium_access:
        # Get current image count
        success, message, current_images = event_service.get_event_images(event_id, session['user_id'])
        if success and current_images and len(current_images) >= 1:
            flash('Free users can upload only one image. Upgrade to Premium to add more.', 'warning')
            return redirect(url_for('event.get_event_details', event_id=event_id))

        # If multiple files submitted, use only the first one
        if len(valid_files) > 1:
            valid_files = [valid_files[0]]
            flash('Free users can upload only one image. Only the first image will be processed.', 'warning')

    # Process the files
    uploaded_count = 0
    for file in valid_files:
        # Validate file size
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 5 * 1024 * 1024:  # 5MB limit
            flash(f"File {file.filename} exceeds the 5MB size limit and will be skipped.", "warning")
            continue

        success, message, image_id = event_service.upload_event_image(
            event_id=event_id,
            user_id=session['user_id'],
            file=file
        )

        if success:
            uploaded_count += 1
        else:
            flash(message, 'danger')

    if uploaded_count > 0:
        flash(f'Successfully uploaded {uploaded_count} image(s)', 'success')

    return redirect(url_for('event.get_event_details', event_id=event_id))


# API Endpoints for Event Operations
@bp.route('/api/<int:event_id>/like', methods=['POST'])
@login_required
def api_toggle_event_like(event_id):
    """Toggle like status for an event - API endpoint"""
    try:
        # Check current like status before toggling
        from data import community_data
        user_liked_before = community_data.check_user_liked_event(session['user_id'], event_id)

        # Use the like_event service which handles toggling automatically
        success, message = community_service.like_event(
            user_id=session['user_id'],
            event_id=event_id
        )

        if success:
            # Determine the action based on the previous state
            action = 'unliked' if user_liked_before else 'liked'
            user_liked_after = not user_liked_before
        else:
            # If operation failed, keep the original state
            action = 'failed'
            user_liked_after = user_liked_before

        # Get current like count
        like_count = community_data.count_event_likes(event_id)

        return jsonify({
            'success': success,
            'message': message,
            'action': action,
            'likes_count': like_count,
            'user_liked': user_liked_after
        })

    except Exception as e:
        logger.error(f"Error toggling event like: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while updating like status'
        }), 500
