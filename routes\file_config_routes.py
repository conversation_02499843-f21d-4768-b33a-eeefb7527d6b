from flask import Blueprint, jsonify
from utils.file_utils import get_frontend_config

bp = Blueprint('file_config', __name__, url_prefix='/api/file-config')

@bp.route('/', methods=['GET'])
def get_file_config():
    """
    Get file validation configuration for frontend
    
    Returns:
        JSON: Configuration object with file validation settings
    """
    return jsonify(get_frontend_config())
