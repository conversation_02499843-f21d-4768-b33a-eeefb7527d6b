from services import helpdesk_service, journey_service, announcement_service, notification_service, report_service
from flask import Blueprint, render_template, session, redirect, url_for, jsonify, request
from utils.security import login_required, get_current_user
from utils.filters import format_time_ago

bp = Blueprint('main', __name__)


@bp.route('/')
def get_landing_page():
    """Get the landing page"""
    user = get_current_user()
    journeys = journey_service.get_published_journeys()
    if user:
        from utils.permissions import Roles
        role = user.get('role')
        if role == Roles.ADMIN:
            return redirect(url_for('main.get_admin_dashboard'))
        elif role == Roles.SUPPORT_TECH:
            return redirect(url_for('main.get_support_tech_dashboard'))
        elif role == Roles.EDITOR:
            return redirect(url_for('main.get_editor_dashboard'))
        elif role == Roles.MODERATOR:
            return redirect(url_for('main.get_moderator_dashboard'))
        else:  # traveller or any other role
            return redirect(url_for('main.get_user_dashboard'))
    request_types = helpdesk_service.get_request_type_enum_values()
    return render_template('index.html', journeys=journeys, request_types=request_types)

@bp.route('/dashboard')
@login_required
def get_user_dashboard():
    """Get the user dashboard with recent public and private journeys"""
    # Get all public journeys (already sorted by updated_at DESC from the database)
    public_journeys = journey_service.get_public_journeys(limit=8)

    # Get user's private journeys
    private_journeys = journey_service.get_private_journeys(session['user_id'], limit=3)

    # Get announcements
    unread_announcements = announcement_service.get_unread_announcements(session['user_id'])
    read_announcements = announcement_service.get_read_announcements(session['user_id'])

    return render_template('dashboard/traveller.html',
                          public_journeys=public_journeys,
                          private_journeys=private_journeys,
                          unread_announcements=unread_announcements,
                          read_announcements=read_announcements)

@bp.route('/dashboard/moderator')
@login_required
def get_moderator_dashboard():
    """Get the moderator dashboard"""
    from utils.permissions import PermissionChecker, Roles
    if session.get('role') != Roles.MODERATOR and session.get('role') != Roles.ADMIN:
        return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

    # Get announcements
    unread_announcements = announcement_service.get_unread_announcements(session['user_id'])
    read_announcements = announcement_service.get_read_announcements(session['user_id'])
    comment_reports = report_service.get_comment_reports(new=True, limit=5)

    return render_template('dashboard/moderator.html',
                           unread_announcements=unread_announcements,
                           read_announcements=read_announcements,
                           comment_reports=comment_reports)

@bp.route('/dashboard/editor')
@login_required
def get_editor_dashboard():
    """Get the editor dashboard"""
    from utils.permissions import PermissionChecker
    if not PermissionChecker.can_manage_content():
        return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

    # Get announcements
    unread_announcements = announcement_service.get_unread_announcements(session['user_id'])
    read_announcements = announcement_service.get_read_announcements(session['user_id'])

    return render_template('dashboard/editor.html',
                           unread_announcements=unread_announcements,
                           read_announcements=read_announcements)

@bp.route('/dashboard/support_tech')
@login_required
def get_support_tech_dashboard():
    """Get the support_tech dashboard"""
    from utils.permissions import Roles
    if session.get('role') != Roles.SUPPORT_TECH and session.get('role') != Roles.ADMIN:
        return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

    # Get announcements
    unread_announcements = announcement_service.get_unread_announcements(session['user_id'])
    read_announcements = announcement_service.get_read_announcements(session['user_id'])
    tickets = helpdesk_service.get_tickets(status_filter='new', limit=8)
    return render_template('dashboard/support_tech.html',
                           unread_announcements=unread_announcements,
                           read_announcements=read_announcements, tickets=tickets)

@bp.route('/dashboard/admin')
@login_required
def get_admin_dashboard():
    """Get the admin dashboard"""
    from utils.permissions import Roles
    if session.get('role') != Roles.ADMIN:
        return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

    # Get announcements
    unread_announcements = announcement_service.get_unread_announcements(session['user_id'])
    read_announcements = announcement_service.get_read_announcements(session['user_id'])

    return render_template('dashboard/admin.html',
                           unread_announcements=unread_announcements,
                           read_announcements=read_announcements)

@bp.route('/published_journey', methods=['GET'])
def get_published_journey():
    try:
        # Set session context for smart navigation
        session['journey_page'] = 'published'

        journeys = journey_service.get_published_journeys()
        total_count = journey_service.get_published_journey_count()

        return render_template(
            'published_journey.html',
            journeys=journeys,
            total_count=total_count
        )
    except Exception as e:
        print(f"Error in get_published_journey: {str(e)}")
        return render_template('error.html', error_code='500', message="An error occurred while loading journeys."), 500

@bp.route('/notifications', methods=['GET'])
@login_required
def get_notifications():
    """Return the current user's notifications as JSON (AJAX)."""
    limit = int(request.args.get('limit', 5))
    offset = int(request.args.get('offset', 0))

    notifications = notification_service.get_user_notifications(
        session['user_id'], limit, offset, False
    )

    # Add formatted time strings for each notification
    for notification in notifications:
        if 'created_at' in notification:
            notification['formatted_time'] = format_time_ago(notification['created_at'])

    return jsonify({'success': True, 'notifications': notifications})

@bp.route('/notifications/all', methods=['GET'])
@login_required
def view_all_notifications():
    """Display all notifications for the current user."""
    # Set session context for smart navigation
    session['journey_page'] = 'notifications'
    
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page

    # Get notifications with pagination
    notifications = notification_service.get_user_notifications(
        session['user_id'], limit=per_page, offset=offset, include_read=True
    )

    # Count total notifications for pagination
    total = notification_service.count_user_notifications(session['user_id'], include_read=True)

    return render_template(
        'notifications/list.html',
        notifications=notifications,
        page=page,
        per_page=per_page,
        total=total,
        total_pages=(total + per_page - 1) // per_page
    )

@bp.route('/notifications/<int:noti_id>/read', methods=['POST'])
@login_required
def mark_notification_read(noti_id):
    """Mark a notification as read"""
    success, message = notification_service.mark_notification_as_read(session['user_id'], noti_id)
    return jsonify({'success': success, 'message': message})

@bp.route('/notifications/mark-all-read', methods=['POST'])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read for the current user."""
    notification_service.mark_all_as_read(session['user_id'])
    return jsonify({'success': True})
