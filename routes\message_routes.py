"""
Message Routes Module

This module handles all message-related routes including:
- Viewing conversations and messages
- Sending and deleting messages
- Managing message read status
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify, g
from utils.security import login_required
from services import message_service, user_service, subscription_service

bp = Blueprint('message', __name__, url_prefix='/messages')


@bp.route('', methods=['GET'])
@login_required
def get_conversations():
    """Get all conversations for the current user with pagination"""
    page = request.args.get('page', 1, type=int)
    per_page = 8  # Show 8 conversations per page

    # Get all conversations first to calculate total
    all_conversations = message_service.get_conversations(session['user_id'])
    total_conversations = len(all_conversations)

    # Calculate pagination
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    conversations = all_conversations[start_idx:end_idx]

    # Calculate pagination info
    total_pages = (total_conversations + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages

    # Get user details for each conversation
    for conversation in conversations:
        other_user = user_service.get_user_by_id(conversation['other_user_id'])
        if other_user:
            conversation['other_user'] = other_user

    # Check premium access status
    premium_access = subscription_service.check_can_use_premium_features(
        user_id=session['user_id']
    )

        # Check if user can reply (premium or staff)
    return render_template(
        'message/list.html',
        conversations=conversations,
        premium_access=premium_access,
        unread_count=g.unread_messages_count,
        page=page,
        total_pages=total_pages,
        has_prev=has_prev,
        has_next=has_next,
        total_conversations=total_conversations,
    )

@bp.route('/send', methods=['POST'])
@login_required
def send_message():
    """Send a message to another user"""
    recipient_id = request.form.get('recipient_id', type=int)
    content = request.form.get('content')

    if not recipient_id or not content:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Missing required fields'}), 400
        flash('Missing required fields', 'danger')
        return redirect(url_for('message.get_conversations'))

    # Send message
    success, message, message_id = message_service.send_message(
        sender_id=session['user_id'],
        recipient_id=recipient_id,
        content=content
    )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': success, 'message': message})

    if success:
        flash('Message sent successfully', 'success')
        # Redirect to conversation with this user
        return redirect(url_for('message.get_conversations'))
    else:
        flash(message, 'danger')

        # If premium required, redirect to subscription page
        if "premium subscription" in message:
            return redirect(url_for('subscription.get_plans'))

        return redirect(url_for('message.get_conversations'))


@bp.route('/delete/<int:message_id>', methods=['POST'])
@login_required
def delete_message(message_id):
    """Delete a message"""
    success = message_service.delete_message(
        user_id=session['user_id'],
        message_id=message_id
    )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': 'Failed to delete message'}), 400

    if success:
        flash('Message deleted successfully', 'success')
    else:
        flash('Failed to delete message', 'danger')

    # Get the next parameter for redirection
    next_page = request.args.get('next')
    if next_page:
        return redirect(next_page)

    return redirect(url_for('message.get_conversations'))


@bp.route('/mark-read/<int:message_id>', methods=['POST'])
@login_required
def mark_message_as_read(message_id):
    """Mark a message as read"""
    success = message_service.mark_message_as_read(
        user_id=session['user_id'],
        message_id=message_id
    )

    # If AJAX request, return JSON
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({
            'success': success,
            'message': 'Message marked as read' if success else 'Failed to mark message as read'
        })

    # Otherwise redirect
    if success:
        flash('Message marked as read', 'success')
    else:
        flash('Failed to mark message as read', 'danger')

    # Get the next parameter for redirection
    next_page = request.args.get('next')
    if next_page:
        return redirect(next_page)

    return redirect(url_for('message.get_conversations'))


@bp.route('/mark-read', methods=['POST'])
@login_required
def mark_conversation_as_read():
    """Mark all messages from another user as read"""
    other_user_id = request.form.get('other_user_id')

    if not other_user_id:
        return jsonify({'success': False, 'message': 'Missing other_user_id parameter'}), 400

    try:
        other_user_id = int(other_user_id)
        # Mark all messages from other_user to current_user as read
        from data.community_data import mark_conversation_as_read
        rows_affected = mark_conversation_as_read(other_user_id, session['user_id'])

        success = rows_affected >= 0  # Even 0 is success (no unread messages)
        return jsonify({
            'success': success,
            'message': f'Marked {rows_affected} messages as read' if success else 'Failed to mark messages as read'
        })
    except (ValueError, TypeError):
        return jsonify({'success': False, 'message': 'Invalid other_user_id parameter'}), 400
    except Exception as e:
        return jsonify({'success': False, 'message': f'Error: {str(e)}'}), 500

@bp.route('/create/<int:recipient_id>')
@login_required
def create_conversation(recipient_id):
    """Create or navigate to an existing conversation with another user"""
    user_id = session.get('user_id')

    # Check if recipient exists and is valid
    recipient = user_service.get_user_by_id(recipient_id)
    if not recipient:
        flash('User not found', 'danger')
        return redirect(url_for('message.get_message_list'))

    # Check if a conversation already exists
    conversation = message_service.get_conversation_between(user_id, recipient_id)

    # If conversation exists, redirect to it
    # If not, create a new conversation and redirect to it
    return redirect(url_for('message.get_chat_detail', other_user_id=recipient_id))

@bp.route('/conversation_partial/<int:other_user_id>', methods=['GET'])
@login_required
def get_conversation_partial(other_user_id):
    """Get conversation partial for AJAX loading"""
    other_user = user_service.get_user_by_id(other_user_id)
    if not other_user:
        return jsonify({'error': 'User not found'}), 404

    messages = message_service.get_messages_between(session['user_id'], other_user_id)
    premium_access = subscription_service.check_can_use_premium_features(user_id=session['user_id'])
    can_send = message_service.check_can_message(session['user_id'], other_user_id)

    return render_template(
        'message/conversation_partial.html',
        other_user=other_user,
        messages=messages,
        premium_access=premium_access,
        can_send=can_send
    )
