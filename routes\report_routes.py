from flask import Blueprint, g, render_template, request, session, redirect, url_for, flash
from services import community_service, event_service, journey_service, report_service, user_service
from utils.security import admin_required, login_required, report_manager_required

bp = Blueprint('report', __name__, url_prefix='/report')


@bp.route('/manage', methods=['GET'])
@login_required
@report_manager_required
def get_reports():
    """Get a filtered list of comment reports based on tab selection"""

    page = request.args.get('page', 1, type=int)
    search = request.args.get('q', '')
    filter_type = request.args.get('filter')

    # Set default tab based on user role
    default_tab = 'all'
    active_tab = request.args.get('active_tab', default_tab)

    limit = 8
    offset = (page - 1) * limit

    # Call the appropriate report fetch based on the active_tab
    if active_tab == 'resolved':
        comment_reports = report_service.get_comment_reports(
            limit=limit,
            offset=offset,
            search=search,
            resolved=True
        )
        total_count = report_service.get_comment_reports_count(search=search, resolved=True)
    elif active_tab == 'escalated':
        comment_reports = report_service.get_comment_reports(
            limit=limit,
            offset=offset,
            search=search,
            escalated=True
        )
        total_count = report_service.get_comment_reports_count(search=search, escalated=True)
    elif active_tab == 'active':
        comment_reports = report_service.get_comment_reports(
            limit=limit,
            offset=offset,
            search=search,
            active=True
        )
        total_count = report_service.get_comment_reports_count(search=search, active=True)
    elif active_tab == 'all':
        comment_reports = report_service.get_comment_reports(
            limit=limit,
            offset=offset,
            search=search
        )
        total_count = report_service.get_comment_reports_count(search=search)
    else:
        comment_reports = report_service.get_comment_reports(
            limit=limit,
            offset=offset,
            search=search
        )
        total_count = report_service.get_comment_reports_count(search=search)
    
    total_pages = (total_count + limit - 1) // limit
    return render_template(
        'report/list.html',
        comment_reports=comment_reports,
        page=page,
        filter=filter_type,
        active_tab=active_tab,
        search_term=search,
        total_pages=total_pages,
        offset=offset,
        total_count=total_count
    )

@bp.route('', methods=['GET'])
@login_required
def get_my_reports():
    page = request.args.get('page', 1, type=int)
    user_id = session['user_id']
    limit = 10
    offset = (page - 1) * limit
    active_tab = request.args.get('active_tab', 'all')
    if active_tab not in ['active', 'resolved', 'all']:
        active_tab = 'all'

    if active_tab == 'resolved':
        comment_reports = report_service.get_my_comment_reports(user_id=user_id, limit=limit, offset=offset, resolved=True)
        total_count = report_service.get_my_comment_reports_count(user_id=user_id, resolved=True)
    elif active_tab == 'all':
        comment_reports = report_service.get_my_comment_reports(user_id=user_id, limit=limit, offset=offset)
        total_count = report_service.get_my_comment_reports_count(user_id=user_id)
    else:
        comment_reports = report_service.get_my_comment_reports(user_id=user_id, limit=limit, offset=offset, active=True)
        total_count = report_service.get_my_comment_reports_count(user_id=user_id, active=True)
    total_pages = (total_count + limit - 1) // limit

    def my_report_tab_url(tab):
        return url_for('report.get_my_reports', active_tab=tab)

    return render_template(
        'report/list.html',
        comment_reports=comment_reports,
        page=page,
        total_pages=total_pages,
        offset=offset,
        total_count=total_count,
        active_tab=active_tab,
        is_my_report_page=True,
        my_report_tab_url=my_report_tab_url
    )


@bp.route('/detail/<int:report_id>', methods=['GET'])
@login_required
def get_report_details(report_id):
    """Get details of a comment report"""
    # Try to get report by ID
    report = report_service.get_report(report_id=report_id)

    # If no direct match, check if this might be a content ID from a legacy notification
    if report is None:
        reports = report_service.get_reports_by_content('comment', report_id)
        if reports and len(reports) > 0:
            # Redirect to the correct report detail page using the actual report ID
            return redirect(url_for('report.get_report_details', report_id=reports[0]['id']))

        flash('Report not found', 'danger')
        return redirect(url_for('report.get_my_reports'))

    # Check authorization: user must be either the reporter or have report management permissions
    current_user_id = session['user_id']
    from utils.permissions import PermissionChecker
    
    if not (report['reporter_id'] == current_user_id or PermissionChecker.can_manage_reports()):
        flash('You are not authorized to view this report', 'danger')
        return redirect(url_for('report.get_my_reports'))

    success, message, reported_comment = community_service.get_comment(report['content_id'])
    if not success:
        flash('Comment not found', 'danger')
        return redirect(url_for('report.get_reports'))

    event_id=reported_comment['event_id']

    success, message, event = event_service.get_event(event_id=event_id, user_id=session['user_id'])
    if event is None:
        # Journey/event is private - show special template with dismiss option
        back_url = request.args.get('back')
        return render_template('report/journey_unavailable.html', report=report, back=back_url)
    
    success, message, journey = journey_service.get_journey(event['journey_id'], user_id=session.get('user_id'))
    success, message, comments = community_service.get_event_comments(event_id=event_id, include_hidden=True)
    success, message, images = event_service.get_event_images(event_id=event_id, user_id=session['user_id'])
    if not success:
        images = []
    event_likes = community_service.get_event_likes(event_id=event_id)

    back_url = request.args.get('back')
    
    # Pass permission status to template
    is_report_manager = PermissionChecker.can_manage_reports()
    is_reporter = report['reporter_id'] == current_user_id

    return render_template('report/detail.html', report=report, event=event, journey=journey, comments=comments, images=images, event_likes=event_likes, back=back_url, is_report_manager=is_report_manager, is_reporter=is_reporter)

@bp.route('/hide/<int:comment_id>/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def hide_comment(comment_id, report_id):
    """Hide a comment (without auto-resolving the report)"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='open')
    success, message = community_service.moderate_comment(moderator_id=session['user_id'], comment_id=comment_id, action='hide')
    flash(message, 'success' if success else 'danger')
    
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/unhide/<int:comment_id>/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def unhide_comment(comment_id, report_id):
    """Reverse the hide action on a comment"""
    success, message = community_service.moderate_comment(moderator_id=session['user_id'], comment_id=comment_id, action='unhide')
    flash(message, 'success' if success else 'danger')
    
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/escalate/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def escalate_comment(report_id):
    """Escalate a comment report to admin for further action"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='open')
    success, message = report_service.escalate_comment_to_admin(report_id=report_id, escalate=1, escalated_by_id=session['user_id'])
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/unescalate/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def unescalate_comment(report_id):
    """Remove escalation from a comment report"""
    success, message = report_service.escalate_comment_to_admin(report_id=report_id, escalate=0, escalated_by_id=session['user_id'])
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/ban/<int:report_id>', methods=['POST'])
@login_required
@admin_required
def ban_user(report_id):
    """Ban a user for their reported comment (without auto-resolving)"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='open')
    report = report_service.get_report(report_id=report_id)
    success, message, reported_comment = community_service.get_comment(report['content_id'])
    
    if success:
        success, message = user_service.update_user_ban_status(admin_id=session['user_id'], user_id=reported_comment['user_id'], is_banned=True)
    
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/unban/<int:report_id>', methods=['POST'])
@login_required
@admin_required
def unban_user(report_id):
    """Unban a user for their reported comment"""
    report = report_service.get_report(report_id=report_id)
    success, message, reported_comment = community_service.get_comment(report['content_id'])
    
    if success:
        success, message = user_service.update_user_ban_status(admin_id=session['user_id'], user_id=reported_comment['user_id'], is_banned=False)
    
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))

@bp.route('/dismiss/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def dismiss_comment_report(report_id):
    """Dismiss a comment report"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='dismissed')
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    print('back url', back_url)
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/undismiss/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def undismiss_comment_report(report_id):
    """Undismiss a comment report"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='open')
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    print('back url', back_url)
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))

@bp.route('/resolve/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def resolve_report(report_id):
    """Manually mark a report as resolved"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='resolved')
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))


@bp.route('/reopen/<int:report_id>', methods=['POST'])
@login_required
@report_manager_required
def reopen_report(report_id):
    """Reopen a resolved report"""
    success, message = report_service.review_report(staff_id=session['user_id'], report_id=report_id, status='open')
    flash(message, 'success' if success else 'danger')
    back_url = request.form.get('back_url')
    return redirect(url_for('report.get_report_details', report_id=report_id, back=back_url))