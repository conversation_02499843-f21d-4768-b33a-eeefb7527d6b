from flask import Blueprint, render_template, request, session, redirect, url_for, flash, jsonify
from utils.security import login_required
from services import subscription_service, user_service
from datetime import datetime
from utils.logger import get_logger

bp = Blueprint('subscription', __name__, url_prefix='/subscription')

# Initialize logger
logger = get_logger(__name__)

@bp.route('/plans')
def get_plans():
    """Display subscription plans for users to select"""
    # Get user subscription status if logged in
    subscription_status = None
    if session.get('user_id'):
        subscription_status = subscription_service.get_user_subscription_status(session.get('user_id'))
    
    # Get plans
    subscription_plans = subscription_service.get_selectable_plans()
    
    return render_template('subscription/plans.html',
                          subscription_plans=subscription_plans,
                          subscription_status=subscription_status)

@bp.route('/payment-form')
@login_required
def get_payment_form():
    """Display payment form for subscription"""
    # Redirect to create subscription page which handles the payment form
    return redirect(url_for('subscription.create_subscription'))

@bp.route('/subscription-details')
@login_required
def get_subscription_details():
    """Redirect to subscription details page"""
    return redirect(url_for('subscription.get_user_subscription'))

@bp.route('/detail')
@login_required
def get_user_subscription():
    user_id = session.get('user_id')
    role = session.get('role')
    page = request.args.get('page', 1, type=int)
    per_page = 10


    # Get subscription info with pagination
    subscriptions, total = subscription_service.get_user_subscription_history(user_id, per_page, (page - 1) * per_page)
    total_pages = (total + per_page - 1) // per_page

    # Get active subscription and payment info
    active_subscription = subscription_service.get_user_active_subscription(user_id)
    payments = subscription_service.get_user_payments(user_id)
    latest_payment = payments[0] if payments else None

    # Only premium & selectable plans
    subscription_plans = subscription_service.get_selectable_plans()
    has_had_free_trial = subscription_service.check_user_had_free_trial(user_id)

    return render_template('account/subscription.html',
                         active_subscription=active_subscription,
                         all_subscriptions=subscriptions,
                         payments=payments,
                         latest_payment=latest_payment,
                         page=page,
                         total_pages=total_pages,
                         subscription_plans=subscription_plans,
                         has_had_free_trial=has_had_free_trial,
                         user_role=role)

@bp.route('/receipt/<int:payment_id>', methods=['GET'])
@login_required
def get_receipt(payment_id):
    try:
        subscription_id = request.args.get('subscription_id', type=int)
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            user_id = session.get('user_id')
        user = user_service.get_user_by_id(user_id)
        if not user:
            flash('User not found.', 'danger')
            return redirect(url_for('subscription.get_user_subscription'))
        payment = subscription_service.get_payment_by_subscription_id(subscription_id)
        if not payment:
            flash('Payment not found.', 'danger')
            return redirect(url_for('subscription.get_user_subscription'))
        if not payment.get('paid_at'):
            payment['paid_at'] = datetime.now()
        return render_template('account/subscription/receipt.html', payment=payment, user=user)
    except Exception as e:
        logger.error(f"Error getting receipt: {str(e)}")
        flash('An error occurred while retrieving the receipt.', 'danger')
        return render_template('account/subscription/receipt.html', payment=payment, user=user)

@bp.route('/update-plan', methods=['POST'])
@login_required
def update_plan():
    selected_plan = request.form.get('plan')
    user_id = session.get('user_id')

    if selected_plan == 'free_trial':
        success, msg, subscription_id = subscription_service.create_free_trial(user_id)
        logger.info(f"Free trial creation result: success={success}, msg={msg}, subscription_id={subscription_id}")
        if success and subscription_id:
            session['subscription_id'] = subscription_id
            session['subscription_type'] = 'Free Trial'
            session['is_premium'] = True
            flash('You are now on a free trial.', 'success')
            return jsonify({'success': True, 'message': 'You are now on a free trial.'})
        else:
            flash(msg or 'Failed to activate free trial.', 'danger')
            return jsonify({'success': False, 'message': msg or 'Failed to activate free trial.'}), 400
    elif selected_plan in ['monthly', 'quarterly', 'annually']:
        success, msg, subscription_id = subscription_service.create_basic_premium_subscription(user_id, selected_plan)
        if success and subscription_id:
            session['subscription_id'] = subscription_id
            session['subscription_type'] = 'Premium'
            session['is_premium'] = True
            flash('Premium plan activated successfully.', 'success')
            return jsonify({'success': True, 'message': 'Premium plan activated successfully.'})
        else:
            flash(msg or 'Failed to activate premium plan.', 'danger')
            return jsonify({'success': False, 'message': msg or 'Failed to activate premium plan.'}), 400
    else:
        flash('Invalid plan selected.', 'danger')
        return jsonify({'success': False, 'message': 'Invalid plan selected.'}), 400

@bp.route('/selectable-plans', methods=['GET'])
@login_required
def get_selectable_plans():
    user_id = session.get('user_id')
    has_had_free_trial = subscription_service.check_user_had_free_trial(user_id)
    subscription_plans = subscription_service.get_selectable_plans()
    plans_json = []
    for plan in subscription_plans:
        base_price = float(plan['base_price'])
        discount_percentage = float(plan.get('discount_percentage', 0))
        discounted_amount = round(base_price * (1 - discount_percentage / 100), 2)
        plans_json.append({
            'plan_code': plan['plan_code'],
            'name': plan['name'],
            'base_price': plan['base_price'],
            'currency_code': plan.get('currency_code', 'NZD'),
            'discount_percentage': plan.get('discount_percentage', 0),
            'period_months': plan['period_months'],
            'discounted_amount': discounted_amount
        })
    return jsonify({
        'plans': plans_json,
        'has_had_free_trial': has_had_free_trial
    })

@bp.route('/create-subscription', methods=['POST'])
@login_required
def create_subscription():
    user_id = session.get('user_id')
    plan_code = request.form.get('plan_code')  # This comes directly from the dropdown in the payment form
    name = request.form.get('name')
    card_number = request.form.get('card_number')
    expiration_date = request.form.get('expiration_date')
    security_code = request.form.get('security_code')
    country_id = request.form.get('country')
    billing_address = request.form.get('address')
    email = request.form.get('email')

    # Extract last 4 digits of card
    card_last_four = card_number[-4:] if card_number else None

    plan = subscription_service.get_subscription_plan(plan_code)
    if not plan:
        flash('Invalid plan selected.', 'danger')
        return redirect(url_for('subscription.get_user_subscription'))

    # Calculate price (including GST)
    price_info = subscription_service.calculate_subscription_price(plan_code, int(country_id))
    amount = price_info['amount']
    gst_amount = price_info['gst_amount']
    currency = price_info['currency_code']

    # Generate dummy payment transaction reference
    import uuid
    transaction_reference = f"WEB-{uuid.uuid4().hex[:8].upper()}"

    # Actually create subscription/payment
    success, msg, subscription_id = subscription_service.create_paid_subscription(
        user_id=user_id,
        plan_code=plan_code,
        amount=amount,
        currency=currency,
        country_id=int(country_id),
        billing_address=billing_address,
        card_last_four=card_last_four,
        transaction_reference=transaction_reference,
        gst_amount=gst_amount,
        reason=None
    )

    if success:
        session['is_premium'] = True
        flash('Subscription and payment completed successfully!', 'success')
    else:
        flash(msg or 'Failed to create subscription/payment.', 'danger')

    return redirect(url_for('account.get_profile', active_tab='subscription'))

@bp.route('/countries', methods=['GET'])
@login_required
def get_countries():
    countries = subscription_service.get_countries()
    return jsonify({'countries': countries})

@bp.route('/receipt/<int:payment_id>/js-download', methods=['GET'])
@login_required
def download_receipt_js(payment_id):
    """
    Generate PDF on the client side using JavaScript
    """
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            user_id = session.get('user_id')
            
        user = user_service.get_user_by_id(user_id)
        if not user:
            flash('User not found.', 'danger')
            return redirect(url_for('subscription.get_user_subscription'))
            
        payment = subscription_service.get_payment_by_id(payment_id)
        if not payment:
            flash('Payment not found.', 'danger')
            return redirect(url_for('subscription.get_user_subscription'))
            
        if not payment.get('paid_at'):
            payment['paid_at'] = datetime.now()
            
        # Function for current date/time (used in template)
        def now():
            return datetime.now()
            
        # Render JS-based PDF template
        return render_template('account/receipt_js.html', 
                               payment=payment, 
                               user=user, 
                               now=now)
            
    except Exception as e:
        logger.error(f"Error in download_receipt_js: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        flash('An error occurred while generating the receipt.', 'danger')
        return redirect(url_for('subscription.get_receipt', payment_id=payment_id, user_id=user_id))
    