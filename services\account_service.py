"""
Account Service Module

This module handles all user account operations including:
- Account information retrieval and updates
- Profile image management
- Password change functionality
"""

import os
from typing import Dict, Tuple, Optional, Any
from data import user_data, privacy_settings_data
from utils.security import check_password, hash_password
from utils.file_utils import save_profile_image
from utils.logger import get_logger
from flask import current_app
from werkzeug.datastructures import FileStorage

# Set up logging
logger = get_logger(__name__)

# ===== Profile Information =====

def get_user_profile(user_id: int) -> Optional[Dict[str, Any]]:
    """Get user's profile data.

    Args:
        user_id: The ID of the user to get the profile for

    Returns:
        The user's profile data if found, None otherwise
    """
    try:
        return user_data.get_user_by_id(user_id)
    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        return None

def get_user_by_username(username: str) -> Optional[Dict[str, Any]]:
    """Get user profile by username.

    Args:
        username: The username to look up

    Returns:
        User profile dictionary if found, None otherwise
    """
    try:
        return user_data.get_user_by_username(username)
    except Exception as e:
        logger.error(f"Error getting user by username '{username}': {str(e)}")
        return None
    
def update_user_profile(
    user_id: int,
    email: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    location: Optional[str] = None,
    description: Optional[str] = None,
    interests: Optional[str] = None,
    is_public: Optional[bool] = None,
    show_first_name: Optional[bool] = None,
    show_last_name: Optional[bool] = None,
    show_email: Optional[bool] = None,
    show_username: Optional[bool] = None,
    show_location: Optional[bool] = None,
    show_description: Optional[bool] = None,
    show_interests: Optional[bool] = None,
    show_recent_likes: Optional[bool] = None,
    show_recent_comments: Optional[bool] = None,
    show_public_journeys: Optional[bool] = None,
    show_visited_places: Optional[bool] = None
) -> Tuple[bool, str]:
    """Update user profile information.

    Args:
        user_id: ID of the user
        email: New email
        first_name: New first name
        last_name: New last name
        location: New location
        description: New description
        interests: User interests (comma-separated)
        is_public: Whether the profile is publicly visible
        show_first_name: Whether to show first name on public profile
        show_last_name: Whether to show last name on public profile
        show_email: Whether to show email on public profile
        show_username: Whether to show username on public profile
        show_location: Whether to show location on public profile
        show_description: Whether to show description on public profile
        show_interests: Whether to show interests on public profile
        show_recent_likes: Whether to show recent likes on public profile
        show_recent_comments: Whether to show recent comments on public profile
        show_public_journeys: Whether to show public journeys on public profile
        show_visited_places: Whether to show visited places on public profile

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Log all visibility parameters at the start
        visibility_params = {
            'show_first_name': show_first_name,
            'show_last_name': show_last_name,
            'show_email': show_email,
            'show_username': show_username,
            'show_location': show_location,
            'show_description': show_description,
            'show_interests': show_interests,
            'show_recent_likes': show_recent_likes,
            'show_recent_comments': show_recent_comments,
            'show_public_journeys': show_public_journeys,
            'show_visited_places': show_visited_places
        }
        logger.debug(f"Visibility parameters received: {visibility_params}")

        # Check if email is taken if changing email
        if email:
            existing_user = user_data.get_user_by_email(email)
            if existing_user and existing_user['id'] != user_id:
                return False, "Email is already in use"

        # Log each parameter for debugging
        logger.debug(f"Profile update params - user_id: {user_id}, email: {email}, first_name: {first_name}, " +
                    f"last_name: {last_name}, location: {location}, interests: {interests}, is_public: {is_public}")

        # Update is_public separately first to ensure it works
        if is_public is not None:
            logger.debug(f"Setting is_public to {is_public} for user {user_id}")
            public_result = user_data.toggle_user_public_status(user_id, is_public)
            logger.debug(f"public_result: {public_result}")

        # Update basic profile information
        user_data.update_user_profile(
            user_id=user_id,
            email=email,
            first_name=first_name,
            last_name=last_name,
            location=location,
            description=description,
            interests=interests
        )

        # Map form field names to privacy settings field names
        field_mapping = {
            'first_name': show_first_name,
            'last_name': show_last_name,
            'email': show_email,
            'username': show_username,
            'location': show_location,
            'description': show_description,
            'interests': show_interests,
            'recent_likes': show_recent_likes,
            'recent_comments': show_recent_comments,
            'public_journeys': show_public_journeys,
            'visited_places': show_visited_places
        }

        # Remove None values from the dictionary
        field_mapping = {k: v for k, v in field_mapping.items() if v is not None}

        # Log what fields will be updated
        logger.debug(f"Updating visibility for fields: {list(field_mapping.keys())}")

        # First, get current settings to compare after update
        current_settings = privacy_settings_data.get_user_privacy_settings(user_id)
        logger.debug(f"Current privacy settings before update: {current_settings}")

        # Update each field's visibility setting
        visibility_results = {}
        for field_name, is_visible in field_mapping.items():
            visibility = 'public' if is_visible else 'private'
            logger.debug(f"Setting visibility for {field_name} to {visibility} (is_visible: {is_visible})")

            # Check if the value is actually changing
            current_value = current_settings.get(field_name)
            if current_value == visibility:
                logger.debug(f"No change needed for {field_name}, already set to {visibility}")
                visibility_results[field_name] = 0
                continue

            # Update visibility setting
            try:
                # Set new visibility value - no need to reset first as the function now handles updates
                result = privacy_settings_data.set_attribute_visibility(user_id, field_name, visibility)
                visibility_results[field_name] = result
                logger.debug(f"Set visibility for {field_name} result: {result}")
            except Exception as e:
                logger.error(f"Error setting visibility for {field_name}: {str(e)}", exc_info=True)
                # Continue with other fields despite error

        # Log results of visibility updates
        logger.debug(f"Visibility update results: {visibility_results}")

        # Get updated privacy settings for verification
        updated_settings = privacy_settings_data.get_user_privacy_settings(user_id)
        logger.debug(f"Updated privacy settings: {updated_settings}")

        # Verify the visibility settings
        for field_name, is_visible in field_mapping.items():
            expected = 'public' if is_visible else 'private'
            actual = updated_settings.get(field_name, 'unknown')
            if expected != actual:
                logger.warning(f"Visibility mismatch for {field_name}: expected={expected}, actual={actual}")
                # Try to fix the issue
                try:
                    fix_result = privacy_settings_data.set_attribute_visibility(user_id, field_name, expected)
                    logger.info(f"Fixed visibility setting for {field_name}: {fix_result}")
                except Exception as e:
                    logger.error(f"Failed to fix visibility for {field_name}: {str(e)}")

        logger.info(f"Profile updated for user {user_id}")
        return True, "Profile updated successfully"
    except Exception as e:
        logger.error(f"Profile update failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Profile update failed: {str(e)}"

# ===== Profile Image Management =====

def update_user_profile_image(user_id: int, file: FileStorage) -> Tuple[bool, str]:
    """Update user profile image.

    Args:
        user_id: ID of the user
        file: Image file object

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Get current user data to check for existing image
        user = user_data.get_user_by_id(user_id)
        if not user:
            logger.warning(f"Attempt to update profile image for non-existent user ID: {user_id}")
            return False, "User not found"

        # Save new image
        filename = save_profile_image(file)
        if not filename:
            logger.warning(f"Failed to save profile image for user {user_id}")
            return False, "Failed to save image"

        # Delete old image if exists
        if user.get('profile_image'):
            try:
                delete_profile_image(user['profile_image'])
                logger.debug(f"Deleted old profile image: {user['profile_image']}")
            except Exception as e:
                logger.warning(f"Failed to delete old profile image: {str(e)}")

        # Update user profile with new image filename
        rows_affected = user_data.update_user_profile_image(
            user_id=user_id,
            image_path=filename
        )

        if rows_affected == 0:
            logger.warning(f"No changes made to user {user_id} profile image")
            return False, "No changes were made to the profile image"

        logger.info(f"Profile image updated for user {user_id}")
        return True, "Profile image updated successfully"
    except Exception as e:
        logger.error(f"Profile image update failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Profile image update failed: {str(e)}"


def remove_user_profile_image(user_id: int) -> Tuple[bool, str]:
    """Remove user profile image.

    Args:
        user_id: ID of the user

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Get current image filename
        user = user_data.get_user_by_id(user_id)
        if not user:
            logger.warning(f"Attempt to remove profile image for non-existent user ID: {user_id}")
            return False, "User not found"

        if not user.get('profile_image'):
            logger.info(f"User {user_id} has no profile image to remove")
            return False, "No profile image to remove"

        # Delete image file
        try:
            delete_profile_image(user['profile_image'])
            logger.debug(f"Deleted profile image: {user['profile_image']}")
        except Exception as e:
            logger.warning(f"Failed to delete profile image file: {str(e)}")

        # Update user profile to remove image filename
        rows_affected = user_data.update_user_profile_image(
            user_id=user_id,
            image_path=None
        )

        if rows_affected == 0:
            logger.warning(f"No changes made to user {user_id} profile image record - treating as successful since desired state is achieved")
            # Treat as success since the desired end state (image removed) is already achieved

        logger.info(f"Profile image removed for user {user_id}")
        return True, "Profile image removed successfully"
    except Exception as e:
        logger.error(f"Profile image removal failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Profile image removal failed: {str(e)}"


def delete_profile_image(filename: str) -> bool:
    """Delete a profile image from the file system.

    Args:
        filename: Name of the file to delete

    Returns:
        True if successful, False otherwise
    """
    if not filename:
        return False

    try:
        # Get the absolute path
        profile_dir = os.path.join(current_app.static_folder, 'uploads', 'profile_images')
        filepath = os.path.join(profile_dir, filename)

        # Check if file exists and delete it
        if os.path.exists(filepath):
            os.remove(filepath)
            logger.debug(f"Profile image deleted: {filepath}")
            return True
        else:
            logger.warning(f"Profile image not found for deletion: {filepath}")
            return False
    except Exception as e:
        logger.error(f"Error deleting profile image {filename}: {str(e)}", exc_info=True)
        return False

# ===== Password Management =====

def change_user_password(
    user_id: int,
    current_password: str,
    new_password: str,
    confirm_password: str
) -> Tuple[bool, str]:
    """Change user password.

    Args:
        user_id: ID of the user
        current_password: Current password
        new_password: New password
        confirm_password: Confirmation of new password

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Get user
        user = user_data.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        # Verify current password
        if not check_password(user['password_hash'], current_password):
            return False, "Current password is incorrect"

        # Verify new password matches confirmation
        if new_password != confirm_password:
            return False, "New passwords do not match"

        # Update password
        hashed_password = hash_password(new_password)
        user_data.update_user_profile(
            user_id=user_id,
            password=hashed_password
        )
        logger.info(f"Password changed for user {user_id}")
        return True, "Password changed successfully"
    except Exception as e:
        logger.error(f"Password change failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Password change failed: {str(e)}"