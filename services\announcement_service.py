"""
Announcement Service Module

This module handles all announcement-related operations including:
- User announcement management
- Admin/Editor announcement operations
- Announcement read status tracking
"""

from typing import Dict, List, Optional, Any, Tuple
from data import announcement_data
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# ===== User Announcement Operations =====

def get_announcement(announcement_id: int) -> Optional[Dict[str, Any]]:
    """Get a specific announcement by ID.
    
    Args:
        announcement_id: ID of the announcement.
        
    Returns:
        Dict[str, Any]: Announcement object if found, None otherwise.
    """
    try:
        return announcement_data.get_announcement(announcement_id)
    except Exception as e:
        logger.error(f"Error retrieving announcement {announcement_id}: {str(e)}")
        return None

def get_user_announcements(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get paginated announcements for a specific user.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of announcements to return.
        offset: Number of announcements to skip.
        
    Returns:
        List[Dict[str, Any]]: List of announcement objects or empty list on error.
    """
    try:
        return announcement_data.get_user_announcements(user_id, limit, offset)
    except Exception as e:
        logger.error(f"Error retrieving announcements for user {user_id}: {str(e)}")
        return []

def get_user_announcements_count(user_id: int) -> int:
    """Get total count of announcements for a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        int: Total number of announcements or 0 on error.
    """
    try:
        return announcement_data.get_user_announcements_count(user_id)
    except Exception as e:
        logger.error(f"Error counting announcements for user {user_id}: {str(e)}")
        return 0

def get_unread_announcements(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get paginated unread announcements for a user.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of announcements to return.
        offset: Number of announcements to skip.
        
    Returns:
        List[Dict[str, Any]]: List of unread announcement objects.
    """
    try:
        return announcement_data.get_unread_announcements(user_id, limit, offset)
    except Exception as e:
        logger.error(f"Error retrieving unread announcements for user {user_id}: {str(e)}")
        return []

def get_unread_announcements_count(user_id: int) -> int:
    """Get total count of unread announcements for a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        int: Number of unread announcements.
    """
    try:
        return announcement_data.get_unread_announcements_count(user_id)
    except Exception as e:
        logger.error(f"Error counting unread announcements for user {user_id}: {str(e)}")
        return 0

def get_read_announcements(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get paginated read announcements for a user.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of announcements to return.
        offset: Number of announcements to skip.
        
    Returns:
        List[Dict[str, Any]]: List of read announcement objects.
    """
    try:
        return announcement_data.get_read_announcements(user_id, limit, offset)
    except Exception as e:
        logger.error(f"Error retrieving read announcements for user {user_id}: {str(e)}")
        return []

def get_read_announcements_count(user_id: int) -> int:
    """Get total count of read announcements for a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        int: Number of read announcements.
    """
    try:
        return announcement_data.get_read_announcements_count(user_id)
    except Exception as e:
        logger.error(f"Error counting read announcements for user {user_id}: {str(e)}")
        return 0

def mark_announcement_as_read(user_id: int, announcement_id: int) -> Tuple[bool, str]:
    """Mark an announcement as read for a specific user.
    
    Args:
        user_id: ID of the user.
        announcement_id: ID of the announcement.
        
    Returns:
        Tuple[bool, str]: Tuple containing success flag and message.
    """
    try:
        announcement = announcement_data.get_announcement(announcement_id)
        if not announcement:
            logger.warning(f"Attempt to mark non-existent announcement {announcement_id} as read")
            return False, "Announcement not found"
        
        announcement_data.mark_announcement_as_read(user_id, announcement_id)
        logger.info(f"Announcement {announcement_id} marked as read by user {user_id}")
        return True, "Announcement marked as read"
    except Exception as e:
        logger.error(f"Failed to mark announcement {announcement_id} as read for user {user_id}: {str(e)}")
        return False, f"Failed to mark announcement as read: {str(e)}"

# ===== Admin/Editor Operations =====

def get_all_announcements(limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all announcements with pagination (editor/admin only).
    
    Args:
        limit: Maximum number of announcements to return.
        offset: Number of announcements to skip.
        
    Returns:
        List[Dict[str, Any]]: List of all announcement objects or empty list on error.
    """
    try:
        return announcement_data.get_all_announcements(limit, offset)
    except Exception as e:
        logger.error(f"Error retrieving all announcements: {str(e)}")
        return []

def get_all_announcements_count() -> int:
    """Get total count of all announcements.
    
    Returns:
        int: Total number of announcements in the system or 0 on error.
    """
    try:
        return announcement_data.get_all_announcements_count()
    except Exception as e:
        logger.error(f"Error counting all announcements: {str(e)}")
        return 0

def create_announcement(author_id: int, title: str, content: str) -> Tuple[bool, str, Optional[int]]:
    """Create a new announcement (editor/admin only).
    
    Args:
        author_id: ID of the user creating the announcement.
        title: Title of the announcement.
        content: Content of the announcement.
        
    Returns:
        Tuple[bool, str, Optional[int]]: Tuple containing success flag, message, and announcement ID if created.
    """
    try:
        if not title or not title.strip():
            logger.warning(f"Attempt to create announcement with empty title by user {author_id}")
            return False, "Title cannot be empty", None
        
        if not content or not content.strip():
            logger.warning(f"Attempt to create announcement with empty content by user {author_id}")
            return False, "Content cannot be empty", None
            
        announcement_id = announcement_data.create_announcement(
            author_id=author_id,
            title=title.strip(),
            content=content.strip(),
        )
        
        logger.info(f"User {author_id} created announcement {announcement_id}: {title}")
        return True, "Announcement created successfully", announcement_id
    except Exception as e:
        logger.error(f"Error creating announcement by user {author_id}: {str(e)}")
        return False, f"Failed to create announcement: {str(e)}", None

def update_announcement(announcement_id: int, user_id: int, title: Optional[str] = None, 
                       content: Optional[str] = None) -> Tuple[bool, str]:
    """Update an existing announcement (editor/admin only).
    
    Args:
        announcement_id: ID of the announcement to update.
        user_id: ID of the user making the update.
        title: Optional new title for the announcement.
        content: Optional new content for the announcement.
        
    Returns:
        Tuple[bool, str]: Tuple containing success flag and message.
    """
    try:
        # Verify announcement exists
        announcement = announcement_data.get_announcement(announcement_id)
        if not announcement:
            logger.warning(f"Attempt to update non-existent announcement {announcement_id} by user {user_id}")
            return False, "Announcement not found"
        
        # Clean up inputs
        if title is not None:
            title = title.strip()
            if not title:
                logger.warning(f"Attempt to update announcement {announcement_id} with empty title by user {user_id}")
                return False, "Title cannot be empty"
                
        if content is not None:
            content = content.strip()
            if not content:
                logger.warning(f"Attempt to update announcement {announcement_id} with empty content by user {user_id}")
                return False, "Content cannot be empty"
        
        # Update announcement
        rows_affected = announcement_data.update_announcement(
            announcement_id=announcement_id,
            title=title,
            content=content,
        )
        
        if rows_affected > 0:
            logger.info(f"User {user_id} updated announcement {announcement_id}")
            return True, "Announcement updated successfully"
        else:
            logger.info(f"No changes made to announcement {announcement_id} by user {user_id}")
            return True, "No changes made to the announcement"
    except Exception as e:
        logger.error(f"Error updating announcement {announcement_id} by user {user_id}: {str(e)}")
        return False, f"Failed to update announcement: {str(e)}"

def delete_announcement(announcement_id: int, user_id: int) -> Tuple[bool, str]:
    """Delete an announcement (editor/admin only).
    
    Args:
        announcement_id: ID of the announcement to delete.
        user_id: ID of the user performing the deletion.
        
    Returns:
        Tuple[bool, str]: Tuple containing success flag and message.
    """
    try:
        # Verify announcement exists
        announcement = announcement_data.get_announcement(announcement_id)
        if not announcement:
            logger.warning(f"Attempt to delete non-existent announcement {announcement_id} by user {user_id}")
            return False, "Announcement not found"
            
        # Delete announcement
        rows_affected = announcement_data.delete_announcement(announcement_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} deleted announcement {announcement_id}")
            return True, "Announcement deleted successfully"
        else:
            logger.warning(f"Failed to delete announcement {announcement_id} by user {user_id}")
            return False, "Failed to delete announcement"
    except Exception as e:
        logger.error(f"Error deleting announcement {announcement_id} by user {user_id}: {str(e)}")
        return False, f"Failed to delete announcement: {str(e)}"
