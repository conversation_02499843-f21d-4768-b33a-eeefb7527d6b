"""
Authentication Service Module

This module handles user authentication operations including:
- User registration
- User login/authentication
- User logout
- Password management and validation
"""

from typing import Dict, Tu<PERSON>, Optional, Any
from data import user_data
from utils.security import hash_password, check_password, validate_password_strength, validate_username, validate_email, validate_name
from utils.logger import get_logger
from flask import session

# Set up logging
logger = get_logger(__name__)

# ===== User Registration =====

def register_user(
    username: str, 
    email: str, 
    password: str, 
    confirm_password: str, 
    first_name: Optional[str] = None, 
    last_name: Optional[str] = None, 
    location: Optional[str] = None
) -> Tuple[bool, str, Optional[int]]:
    """Register a new user.
    
    Args:
        username: Username for the new user
        email: Email address for the new user
        password: Password for the new user
        confirm_password: Password confirmation
        first_name: First name
        last_name: Last name
        location: User's location
        
    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - int or None: User ID if successful, None otherwise
    """
    try:
        # Validate username format
        is_valid, error_message = validate_username(username)
        if not is_valid:
            return False, error_message, None
        
        # Validate email format
        is_valid, error_message = validate_email(email)
        if not is_valid:
            return False, error_message, None
        
        # Check if passwords match
        if password != confirm_password:
            return False, "Passwords do not match", None
        
        # Validate password strength
        is_valid, error_message = validate_password_strength(password)
        if not is_valid:
            return False, error_message, None
        
        # Validate first_name if provided
        if first_name:
            is_valid, error_message = validate_name(first_name)
            if not is_valid:
                return False, f"First name: {error_message}", None
        
        # Validate last_name if provided
        if last_name:
            is_valid, error_message = validate_name(last_name)
            if not is_valid:
                return False, f"Last name: {error_message}", None
        
        # Check if username or email already exists
        if user_data.check_user_exists(username=username):
            return False, "Username already exists", None
        
        if user_data.check_user_exists(email=email):
            return False, "Email address already exists", None
        
        # Hash password
        password_hash = hash_password(password)
        
        # Create user
        user_id = user_data.create_user(
            username=username,
            email=email,
            password_hash=password_hash,
            first_name=first_name,
            last_name=last_name,
            location=location
        )
        
        logger.info(f"User registered successfully: {username} (ID: {user_id})")
        return True, "Registration successful", user_id
    except Exception as e:
        logger.error(f"Registration failed for username '{username}': {str(e)}", exc_info=True)
        return False, f"Registration failed: {str(e)}", None

# ===== User Authentication =====

def authenticate_user(username: str, password: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
    """Authenticate a user with username and password.
    
    Args:
        username: Username
        password: Password
        
    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
            - dict or None: User data if successful, None otherwise
    """
    try:
        user = user_data.get_user_by_username(username)
        
        if not user:
            return False, "Invalid username or password", None

        # Check password first before revealing ban status
        if not check_password(user['password_hash'], password):
            return False, "Invalid username or password", None

        # Only show ban message if credentials are correct
        if user['is_banned']:
            from flask import url_for
            appeal_url = url_for('helpdesk.ban_appeal_form', username=username)
            return False, f"Your account has been banned. You can submit an appeal by <a href='{appeal_url}' target='_blank'>clicking here</a>.", None
        
        # Remove password_hash from user dict before returning
        if 'password_hash' in user:
            del user['password_hash']
        
        logger.info(f"User logged in successfully: {username} (ID: {user['id']})")
        return True, "Login successful", user
    except Exception as e:
        logger.error(f"Authentication failed for username '{username}': {str(e)}", exc_info=True)
        return False, f"Authentication failed: {str(e)}", None

def logout_user(user_id: int) -> Tuple[bool, str]:
    """Log out a user.
    
    Args:
        user_id: ID of the user to log out
        
    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        # Clear session data
        session.clear()
        
        logger.info(f"User logged out successfully: (ID: {user_id})")
        return True, "Logout successful"
    except Exception as e:
        logger.error(f"Logout failed for user ID '{user_id}': {str(e)}", exc_info=True)
        return False, f"Logout failed: {str(e)}"