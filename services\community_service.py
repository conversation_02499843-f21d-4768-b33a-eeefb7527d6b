"""
Community Service Module

This module handles all community-related operations including:
- Event likes and comments
- Private messaging
- User following
"""

from typing import Dict, List, Optional, Any, Tuple
from data import community_data, user_data, event_data
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# ===== Event Likes =====

def like_event(user_id: int, event_id: int) -> Tuple[bool, str]:
    """Like an event.
    
    Args:
        user_id: ID of the user.
        event_id: ID of the event.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if event exists
        event = event_data.get_event(event_id)
        if not event:
            logger.warning(f"Attempt to like non-existent event {event_id}")
            return False, "Event not found"
        
        # Add like
        like_id = community_data.like_event(user_id, event_id)
        if like_id == -1:
            # Successfully removed the like (toggled off)
            return True, "Event unliked successfully"
        
        logger.info(f"User {user_id} liked event {event_id}")
        return True, "Event liked successfully"
    except Exception as e:
        logger.error(f"Error liking event {event_id} by user {user_id}: {str(e)}")
        return False, f"Error liking event: {str(e)}"

def unlike_event(user_id: int, event_id: int) -> Tuple[bool, str]:
    """Unlike an event.
    
    Args:
        user_id: ID of the user.
        event_id: ID of the event.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if event exists
        event = event_data.get_event(event_id)
        if not event:
            logger.warning(f"Attempt to unlike non-existent event {event_id}")
            return False, "Event not found"
        
        # Check if user has liked the event
        has_liked = community_data.check_user_liked_event(user_id, event_id)
        if not has_liked:
            logger.info(f"User {user_id} has not liked event {event_id}")
            return False, "You have not liked this event"
        
        # Remove like
        rows_affected = community_data.unlike_event(user_id, event_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} unliked event {event_id}")
            return True, "Event unliked successfully"
        else:
            logger.warning(f"Failed to unlike event {event_id} for user {user_id}")
            return False, "Failed to unlike event"
    except Exception as e:
        logger.error(f"Error unliking event {event_id} by user {user_id}: {str(e)}")
        return False, f"Error unliking event: {str(e)}"

def get_event_likes(event_id: int) -> List[Dict[str, Any]]:
    """Get all likes for an event.
    
    Args:
        event_id: ID of the event.
        
    Returns:
        List[Dict[str, Any]]: List of users who liked the event.
    """
    try:
        return community_data.get_event_likes(event_id)
    except Exception as e:
        logger.error(f"Error getting likes for event {event_id}: {str(e)}")
        return []

def count_event_likes(event_id: int) -> int:
    """Count likes for an event.
    
    Args:
        event_id: ID of the event.
        
    Returns:
        int: Number of likes.
    """
    try:
        return community_data.count_event_likes(event_id)
    except Exception as e:
        logger.error(f"Error counting likes for event {event_id}: {str(e)}")
        return 0
    
def check_user_liked_event(user_id: int, event_id: int) -> bool:
    try:
        return community_data.check_user_liked_event(user_id, event_id)
    except Exception as e:
        logger.error(f"Error checking if user {user_id} liked event {event_id}: {str(e)}")
        return False

def get_user_liked_events(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all events liked by a user with pagination.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of event records with journey and user information.
    """
    try:
        return community_data.get_user_liked_events(user_id, limit, offset)
    except Exception as e:
        logger.error(f"Error getting liked events for user {user_id}: {str(e)}")
        return []

def get_user_comments(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all comments made by a user with pagination.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of results to return.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of comment records with event and journey information.
    """
    try:
        return community_data.get_user_comments(user_id, limit, offset)
    except Exception as e:
        logger.error(f"Error getting comments for user {user_id}: {str(e)}")
        return []

# ===== Event Comments =====

def get_comment(comment_id: int) -> Tuple[bool, str, Optional[dict]]:
    """Retrieve a comment by its ID.
    
    Args:
        comment_id: ID of the comment to retrieve.
        
    Returns:
        Tuple[bool, str, Optional[dict]]: Success flag, message, and the comment data if found.
    """
    try:
        # Fetch the comment from the data layer
        comment = community_data.get_comment(comment_id)
        
        # Check if the comment exists
        if not comment:
            logger.warning(f"Comment with ID {comment_id} not found.")
            return False, "Comment not found", None
        
        logger.info(f"Successfully retrieved comment with ID {comment_id}")
        return True, "Comment found", comment
    except Exception as e:
        logger.error(f"Error retrieving comment {comment_id}: {str(e)}")
        return False, f"Error retrieving comment: {str(e)}", None


def add_comment(user_id: int, event_id: int, content: str) -> Tuple[bool, str, Optional[int]]:
    """Add a comment to an event.
    
    Args:
        user_id: ID of the user.
        event_id: ID of the event.
        content: Comment text.
        
    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and comment ID if created.
    """
    try:
        # Check if event exists
        event = event_data.get_event(event_id)
        if not event:
            logger.warning(f"Attempt to comment on non-existent event {event_id}")
            return False, "Event not found", None
        
        # Validate content
        if not content or not content.strip():
            logger.warning(f"User {user_id} attempted to add empty comment")
            return False, "Comment cannot be empty", None
        
        # Add comment
        comment_id = community_data.add_comment(user_id, event_id, content.strip())
        
        logger.info(f"User {user_id} added comment {comment_id} to event {event_id}")
        
        # # Check for achievement
        # from services import achievement_service
        # achievement_service.update_achievement_progress(user_id, 10, current_progress=1)  # First Comment achievement
        
        return True, "Comment added successfully", comment_id
    except Exception as e:
        logger.error(f"Error adding comment to event {event_id} by user {user_id}: {str(e)}")
        return False, f"Error adding comment: {str(e)}", None

def update_comment(user_id: int, comment_id: int, content: str) -> Tuple[bool, str]:
    """Update a comment.
    
    Args:
        user_id: ID of the user.
        comment_id: ID of the comment.
        content: New comment text.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if comment exists
        comment = community_data.get_comment(comment_id)
        if not comment:
            logger.warning(f"Attempt to update non-existent comment {comment_id}")
            return False, "Comment not found"
        
        # Check if user is the owner
        if comment['user_id'] != user_id:
            logger.warning(f"User {user_id} attempted to update comment {comment_id} owned by user {comment['user_id']}")
            return False, "You can only edit your own comments"
        
        # Validate content
        if not content or not content.strip():
            logger.warning(f"User {user_id} attempted to update comment with empty content")
            return False, "Comment cannot be empty"
        
        # Update comment
        rows_affected = community_data.update_comment(comment_id, content.strip())
        
        if rows_affected > 0:
            logger.info(f"User {user_id} updated comment {comment_id}")
            return True, "Comment updated successfully"
        else:
            logger.warning(f"Failed to update comment {comment_id}")
            return False, "Failed to update comment"
    except Exception as e:
        logger.error(f"Error updating comment {comment_id} by user {user_id}: {str(e)}")
        return False, f"Error updating comment: {str(e)}"

def delete_comment(user_id: int, comment_id: int) -> Tuple[bool, str]:
    """Delete a comment.
    
    Args:
        user_id: ID of the user.
        comment_id: ID of the comment.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if comment exists
        comment = community_data.get_comment(comment_id)
        if not comment:
            logger.warning(f"Attempt to delete non-existent comment {comment_id}")
            return False, "Comment not found"
        
        # Check if user is the owner or a moderator/editor/admin
        is_owner = comment['user_id'] == user_id
        
        user = user_data.get_user_by_id(user_id)
        is_staff = user and user['role'] in ['moderator', 'editor', 'admin']
        
        if not is_owner and not is_staff:
            logger.warning(f"User {user_id} attempted to delete comment {comment_id} without permission")
            return False, "You can only delete your own comments"
        
        # Delete comment
        rows_affected = community_data.delete_comment(comment_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} deleted comment {comment_id}")
            return True, "Comment deleted successfully"
        else:
            logger.warning(f"Failed to delete comment {comment_id}")
            return False, "Failed to delete comment"
    except Exception as e:
        logger.error(f"Error deleting comment {comment_id} by user {user_id}: {str(e)}")
        return False, f"Error deleting comment: {str(e)}"

def moderate_comment(moderator_id: int, comment_id: int, action: str) -> Tuple[bool, str]:
    """Moderate a comment (hide/unhide).
    
    Args:
        moderator_id: ID of the moderator.
        comment_id: ID of the comment.
        action: Moderation action ('hide' or 'unhide').
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if user is a moderator/editor/admin
        user = user_data.get_user_by_id(moderator_id)
        if not user or user['role'] not in ['moderator', 'editor', 'admin', 'support_tech']:
            logger.warning(f"User {moderator_id} attempted to moderate comment without permission")
            return False, "Only staff can perform this action"
        
        # Check if comment exists
        comment = community_data.get_comment(comment_id)
        if not comment:
            logger.warning(f"Attempt to moderate non-existent comment {comment_id}")
            return False, "Comment not found"
        
        # Perform moderation action
        if action == 'hide':
            rows_affected = community_data.hide_comment(comment_id)
            message = "Comment hidden successfully"
        elif action == 'unhide':
            rows_affected = community_data.unhide_comment(comment_id)
            message = "Comment unhidden successfully"
        else:
            logger.warning(f"Invalid moderation action: {action}")
            return False, f"Invalid moderation action: {action}"
        
        if rows_affected > 0:
            logger.info(f"Moderator {moderator_id} {action}d comment {comment_id}")
            return True, message
        else:
            logger.warning(f"Failed to {action} comment {comment_id}")
            return False, f"Failed to {action} comment"
    except Exception as e:
        logger.error(f"Error moderating comment {comment_id} by user {moderator_id}: {str(e)}")
        return False, f"Error moderating comment: {str(e)}"

def get_event_comments(event_id: int, include_hidden: bool = False) -> List[Dict[str, Any]]:
    """Get all comments for an event.
    
    Args:
        event_id: ID of the event.
        include_hidden: Whether to include hidden comments.
        
    Returns:
        List[Dict[str, Any]]: List of comments.
    """
    try:
        return  True, f"Comments retrieved successfully", community_data.get_event_comments(event_id, include_hidden)
    except Exception as e:
        logger.error(f"Error getting comments for event {event_id}: {str(e)}")
        return []

# ===== Comment Interactions (Like/Dislike/Report) =====

def interact_with_comment(user_id: int, comment_id: int, interaction_type: str, 
                         report_reason: Optional[str] = None) -> Tuple[bool, str]:
    """Interact with a comment (like, dislike, report).
    
    Args:
        user_id: ID of the user.
        comment_id: ID of the comment.
        interaction_type: Type of interaction ('like', 'dislike', 'report').
        report_reason: Reason for report (required for 'report' type).
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if comment exists
        comment = community_data.get_comment(comment_id)
        if not comment:
            logger.warning(f"Attempt to interact with non-existent comment {comment_id}")
            return False, "Comment not found"
        
        # Validate interaction type
        valid_types = ['like', 'dislike', 'report']
        if interaction_type not in valid_types:
            logger.warning(f"Invalid interaction type: {interaction_type}")
            return False, f"Invalid interaction type: {interaction_type}"
        
        # For reports, validate report reason
        if interaction_type == 'report' and not report_reason:
            logger.warning(f"Missing report reason for comment {comment_id}")
            return False, "Report reason is required"
        
        # Add interaction
        interaction_id = community_data.interact_with_comment(
            user_id, comment_id, interaction_type, report_reason
        )
        
        # If already interacted, return appropriate message
        if interaction_id == 0:
            logger.info(f"User {user_id} already {interaction_type}d comment {comment_id}")
            return False, f"You have already {interaction_type}d this comment"
        
        logger.info(f"User {user_id} {interaction_type}d comment {comment_id}")
        return True, f"Comment {interaction_type}d successfully"
    except Exception as e:
        logger.error(f"Error {interaction_type}ing comment {comment_id} by user {user_id}: {str(e)}")
        return False, f"Error {interaction_type}ing comment: {str(e)}"

def remove_comment_interaction(user_id: int, comment_id: int, interaction_type: str) -> Tuple[bool, str]:
    """Remove an interaction from a comment.
    
    Args:
        user_id: ID of the user.
        comment_id: ID of the comment.
        interaction_type: Type of interaction ('like', 'dislike', 'report').
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if comment exists
        comment = community_data.get_comment(comment_id)
        if not comment:
            logger.warning(f"Attempt to remove interaction from non-existent comment {comment_id}")
            return False, "Comment not found"
        
        # Validate interaction type
        valid_types = ['like', 'dislike', 'report']
        if interaction_type not in valid_types:
            logger.warning(f"Invalid interaction type: {interaction_type}")
            return False, f"Invalid interaction type: {interaction_type}"
        
        # Check if interaction exists
        has_interaction = community_data.check_user_interaction_with_comment(
            user_id, comment_id, interaction_type
        )
        
        if not has_interaction:
            logger.info(f"User {user_id} has not {interaction_type}d comment {comment_id}")
            return False, f"You have not {interaction_type}d this comment"
        
        # Remove interaction
        rows_affected = community_data.remove_comment_interaction(
            user_id, comment_id, interaction_type
        )
        
        if rows_affected > 0:
            logger.info(f"User {user_id} removed {interaction_type} from comment {comment_id}")
            return True, f"Comment {interaction_type} removed successfully"
        else:
            logger.warning(f"Failed to remove {interaction_type} from comment {comment_id}")
            return False, f"Failed to remove {interaction_type}"
    except Exception as e:
        logger.error(f"Error removing {interaction_type} from comment {comment_id} by user {user_id}: {str(e)}")
        return False, f"Error removing {interaction_type}: {str(e)}"

from typing import Tuple

def check_user_interaction(user_id: int, comment_id: int, interaction_type: str) -> Tuple[bool, str]:
    """Check if a user has interacted with a comment in a specific way.
    
    Args:
        user_id: ID of the user to check.
        comment_id: ID of the comment to check.
        interaction_type: Type of interaction to check ('like', 'dislike', 'report').
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if comment exists
        comment = community_data.get_comment(comment_id)
        if not comment:
            logger.warning(f"Attempt to check interaction on non-existent comment {comment_id}")
            return False, "Comment not found"
    
        valid_types = ['like', 'dislike', 'report']
        if interaction_type not in valid_types:
            logger.warning(f"Invalid interaction type: {interaction_type}")
            return False, f"Invalid interaction type: {interaction_type}"
        
        if interaction_type == 'report':
            # Use reports table check for 'report'
            has_interaction = community_data.check_user_reported_comment(user_id, comment_id)
        else:
            # Use comment_interactions table check for 'like' and 'dislike'
            has_interaction = community_data.check_user_interaction_with_comment(
                user_id, comment_id, interaction_type
            )
        
        if has_interaction:
            logger.info(f"User {user_id} has {interaction_type}d comment {comment_id}")
            return True, f"You have {interaction_type}d this comment"
        else:
            logger.info(f"User {user_id} has not {interaction_type}d comment {comment_id}")
            return False, f"You have not {interaction_type}d this comment"
    
    except Exception as e:
        logger.error(f"Error checking {interaction_type} interaction on comment {comment_id} by user {user_id}: {str(e)}")
        return False, f"Error checking {interaction_type}: {str(e)}"


# ===== Private Messages =====

def send_message(sender_id: int, recipient_id: int, content: str) -> Tuple[bool, str, Optional[int]]:
    """Send a private message to another user.
    
    Args:
        sender_id: ID of the sender.
        recipient_id: ID of the recipient.
        content: Message content.
        
    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and message ID if sent.
    """
    try:
        # Check if sender and recipient exist
        sender = user_data.get_user_by_id(sender_id)
        if not sender:
            logger.warning(f"Attempt to send message from non-existent user {sender_id}")
            return False, "Sender not found", None
        
        recipient = user_data.get_user_by_id(recipient_id)
        if not recipient:
            logger.warning(f"Attempt to send message to non-existent user {recipient_id}")
            return False, "Recipient not found", None
        
        # Check if sender can send messages (premium feature)
        can_send = community_data.check_can_message(sender_id, recipient_id)
        if not can_send:
            logger.warning(f"User {sender_id} attempted to send message without premium subscription")
            return False, "Sending messages requires a premium subscription", None
        
        # Validate content
        if not content or not content.strip():
            logger.warning(f"User {sender_id} attempted to send empty message")
            return False, "Message cannot be empty", None
        
        # Send message
        message_id = community_data.send_message(sender_id, recipient_id, content.strip())
        
        logger.info(f"User {sender_id} sent message {message_id} to user {recipient_id}")
        
        # # Check for achievement
        # from services import achievement_service
        # achievement_service.update_achievement_progress(sender_id, 11, current_progress=1)  # First Message achievement
        
        return True, "Message sent successfully", message_id
    except Exception as e:
        logger.error(f"Error sending message from user {sender_id} to user {recipient_id}: {str(e)}")
        return False, f"Error sending message: {str(e)}", None

def mark_message_as_read(user_id: int, message_id: int) -> Tuple[bool, str]:
    """Mark a message as read.
    
    Args:
        user_id: ID of the user.
        message_id: ID of the message.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if message exists
        message = community_data.get_message(message_id)
        if not message:
            logger.warning(f"Attempt to mark non-existent message {message_id} as read")
            return False, "Message not found"
        
        # Check if user is the recipient
        if message['recipient_id'] != user_id:
            logger.warning(f"User {user_id} attempted to mark message {message_id} as read without being the recipient")
            return False, "You can only mark messages sent to you as read"
        
        # Mark as read
        rows_affected = community_data.mark_message_as_read(message_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} marked message {message_id} as read")
            return True, "Message marked as read"
        else:
            logger.warning(f"Failed to mark message {message_id} as read")
            return False, "Failed to mark message as read"
    except Exception as e:
        logger.error(f"Error marking message {message_id} as read by user {user_id}: {str(e)}")
        return False, f"Error marking message as read: {str(e)}"

def delete_message(user_id: int, message_id: int) -> Tuple[bool, str]:
    """Delete a message.
    
    Args:
        user_id: ID of the user.
        message_id: ID of the message.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Check if message exists
        message = community_data.get_message(message_id)
        if not message:
            logger.warning(f"Attempt to delete non-existent message {message_id}")
            return False, "Message not found"
        
        # Check if user is the sender or recipient
        if message['sender_id'] != user_id and message['recipient_id'] != user_id:
            logger.warning(f"User {user_id} attempted to delete message {message_id} without permission")
            return False, "You can only delete messages you've sent or received"
        
        # Delete message
        rows_affected = community_data.delete_message(message_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} deleted message {message_id}")
            return True, "Message deleted successfully"
        else:
            logger.warning(f"Failed to delete message {message_id}")
            return False, "Failed to delete message"
    except Exception as e:
        logger.error(f"Error deleting message {message_id} by user {user_id}: {str(e)}")
        return False, f"Error deleting message: {str(e)}"

def get_conversation(user_id: int, other_user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get conversation between two users.
    
    Args:
        user_id: ID of the current user.
        other_user_id: ID of the other user.
        limit: Maximum number of messages to return.
        offset: Number of messages to skip.
        
    Returns:
        List[Dict[str, Any]]: List of messages in the conversation.
    """
    try:
        return community_data.get_conversation(user_id, other_user_id, limit, offset)
    except Exception as e:
        logger.error(f"Error getting conversation between user {user_id} and user {other_user_id}: {str(e)}")
        return []

def get_user_conversations(user_id: int) -> List[Dict[str, Any]]:
    """Get all conversations a user is part of.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        List[Dict[str, Any]]: List of conversation summaries.
    """
    try:
        return community_data.get_user_conversations(user_id)
    except Exception as e:
        logger.error(f"Error getting conversations for user {user_id}: {str(e)}")
        return []

def count_unread_messages(user_id: int) -> int:
    """Count unread messages for a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        int: Number of unread messages.
    """
    try:
        return community_data.count_unread_messages(user_id)
    except Exception as e:
        logger.error(f"Error counting unread messages for user {user_id}: {str(e)}")
        return 0

def check_can_message(user_id: int, recipient_id: int) -> bool:
    """Check if a user can send messages.
    
    Args:
        user_id: ID of the user to check.
        recipient_id: ID of the recipient user.
        
    Returns:
        bool: True if the user can send messages, False otherwise.
    """
    try:
        return community_data.check_can_message(user_id, recipient_id)
    except Exception as e:
        logger.error(f"Error checking if user {user_id} can message user {recipient_id}: {str(e)}")
        return False