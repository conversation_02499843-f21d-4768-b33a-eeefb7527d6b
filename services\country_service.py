"""
Country Service Module

This module handles operations related to country data for the application.
"""

from typing import List, Dict, Any
from data import country_data
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def get_countries_for_dropdown() -> List[Dict[str, Any]]:
    """Get all countries formatted for dropdown UI.
    
    Returns:
        List[Dict[str, Any]]: List of countries with id, name, code, and formatted display value.
    """
    try:
        countries = country_data.get_all_countries()
        
        # Format countries for dropdown (adding display text with flag emoji if needed)
        formatted_countries = []
        for country in countries:
            formatted_countries.append({
                'id': country['id'],
                'name': country['name'],
                'code': country['code'],
                'code3': country['code3'],
                'currency_code': country['currency_code'],
                'currency_symbol': country['currency_symbol'],
                'has_gst': country['has_gst'],
                'gst_rate': country['gst_rate'],
                'display': f"{country['name']} ({country['code']})"
            })
        
        return formatted_countries
    except Exception as e:
        logger.error(f"Error getting countries for dropdown: {str(e)}")
        return []