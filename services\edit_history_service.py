"""
Edit History Service Module

This module handles operations related to edit history including:
- Recording and retrieving edit history
"""

from typing import Dict, List, Optional, Any, Tuple
from data import edit_history_data, journey_data, event_data, user_data
from utils.logger import get_logger
from services import notification_service

# Initialize logger
logger = get_logger(__name__)

def record_edit(editor_id: int, content_type: str, content_id: int,
               field_changes: Dict[str, Tuple[str, str]], reason: str) -> <PERSON>ple[bool, str, Optional[int]]:
    """Record an edit to content.

    Args:
        editor_id: ID of the editor making the change.
        content_type: Type of content ('journey', 'event', 'location').
        content_id: ID of the content.
        field_changes: Dictionary of field names mapped to (old_value, new_value) tuples.
        reason: Reason for the edit.

    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and edit ID if created.
    """
    try:
        # Validate content type
        valid_types = ['journey', 'event', 'location']
        if content_type not in valid_types:
            logger.warning(f"Invalid content type: {content_type}")
            return False, f"Invalid content type: {content_type}", None

        # Validate reason
        if not reason or not reason.strip():
            logger.warning(f"Missing reason for edit by editor {editor_id}")
            return False, "Edit reason is required", None

        # Check if editor is staff
        from utils.permissions import PermissionGroups
        editor = user_data.get_user_by_id(editor_id)
        if not editor or editor['role'] not in PermissionGroups.CONTENT_MANAGERS:
            logger.warning(f"User {editor_id} attempted to record edit without permission")
            return False, "Only editors, support_tech, and admins can record edits", None

        # Check if content exists
        content_exists = False
        if content_type == 'journey':
            content_exists = journey_data.check_journey_exists(content_id)
        elif content_type == 'event':
            content_exists = event_data.check_event_exists(content_id)

        if not content_exists:
            logger.warning(f"Attempt to record edit for non-existent {content_type} {content_id}")
            return False, f"{content_type.capitalize()} not found", None

        # Check if journey has no_edits flag
        if content_type == 'journey':
            no_edits = journey_data.check_journey_no_edits(content_id)
            if no_edits:
                logger.warning(f"Editor {editor_id} attempted to edit journey {content_id} with no_edits flag")
                return False, "This journey is protected from edits", None

        # Record edit
        edit_id = edit_history_data.record_edit(
            editor_id=editor_id,
            content_type=content_type,
            content_id=content_id,
            field_changes=field_changes,
            reason=reason.strip()
        )

        if edit_id:
            # Create notification about the edit
            notification_id = create_edit_notification(content_type, content_id, editor_id, field_changes, reason.strip())
            logger.info(f"Editor {editor_id} recorded edit {edit_id} for {content_type} {content_id}, notification: {notification_id}")
            return True, "Edit recorded successfully", edit_id
        else:
            logger.error(f"Failed to record edit by editor {editor_id} for {content_type} {content_id}")
            return False, "Failed to record edit", None
    except Exception as e:
        logger.error(f"Error recording edit by editor {editor_id}: {str(e)}")
        return False, f"Error recording edit: {str(e)}", None

def create_edit_notification(content_type: str, content_id: int, editor_id: int, field_changes: Dict[str, Tuple[str, str]], reason: str) -> Optional[int]:
    """Create a notification for the owner of content that was edited.

    Args:
        content_type: Type of content that was edited.
        content_id: ID of the content that was edited.
        editor_id: ID of the editor who made the edit.
        field_changes: Dictionary of field names mapped to (old_value, new_value) tuples.
        reason: The reason for the edit.

    Returns:
        Optional[int]: ID of the newly created notification, or None if creation failed.
    """
    logger.debug(f"Creating edit notification for {content_type} ID: {content_id}")

    try:
        owner_id = None
        editor_name = None
        content_title = None

        # Get editor's username
        editor = user_data.get_user_by_id(editor_id)
        if not editor:
            logger.warning(f"Editor with ID {editor_id} not found")
            return None

        editor_name = editor['username'] if editor else "An editor"

        # Get content owner and title based on content type
        if content_type == 'journey':
            # Using the journey getter function that exists in journey_data
            journey = journey_data.get_journey(content_id)
            if journey:
                owner_id = journey['user_id']
                content_title = journey['title']
            else:
                logger.warning(f"Journey with ID {content_id} not found")
                return None

        elif content_type == 'event':
            # Using the event getter function that exists in event_data
            event = event_data.get_event(content_id)
            if not event:
                logger.warning(f"Event with ID {content_id} not found")
                return None

            journey_id = event.get('journey_id')
            if not journey_id:
                logger.warning(f"Event {content_id} has no journey_id")
                return None

            journey = journey_data.get_journey(journey_id)
            if journey:
                owner_id = journey['user_id']
                content_title = event['title']
            else:
                logger.warning(f"Journey for event {content_id} not found")
                return None

        elif content_type == 'location':
            # Locations don't have a specific owner, so no notification
            logger.debug("Locations don't have an owner, skipping notification")
            return None

        elif content_type == 'photo':
            # Photo logic would need to be implemented if required
            logger.debug("Photo notification not implemented")
            return None

        # Don't notify the editor about their own edits
        if not owner_id:
            logger.warning(f"Could not determine owner_id for {content_type} {content_id}")
            return None

        if owner_id == editor_id:
            logger.debug(f"Editor {editor_id} is the owner, skipping notification")
            return None

        # Create enhanced notification content
        notification_content = _create_detailed_edit_message(editor_name, content_type, content_title, field_changes, reason)

        success, message, notification_id = notification_service.create_notification(
            user_id=owner_id,
            notification_type='edit',
            content=notification_content,
            related_id=content_id
        )

        if success:
            logger.debug(f"Created edit notification with ID: {notification_id}")
            return notification_id
        else:
            logger.warning(f"Failed to create notification: {message}")
            return None

    except Exception as e:
        logger.error(f"Error creating edit notification: {str(e)}")
        return None

def _create_detailed_edit_message(editor_name: str, content_type: str, content_title: str,
                                 field_changes: Dict[str, Tuple[str, str]], reason: str) -> str:
    """Create a detailed edit notification message.

    Args:
        editor_name: Name of the editor who made the change.
        content_type: Type of content that was edited.
        content_title: Title of the content that was edited.
        field_changes: Dictionary of field names mapped to (old_value, new_value) tuples.
        reason: The reason for the edit.

    Returns:
        str: Formatted notification message.
    """
    # Create action descriptions for different field changes
    actions = []

    for field_name, (old_value, new_value) in field_changes.items():
        if field_name == 'cover_image':
            if old_value and not new_value:
                actions.append("removed the cover image")
            elif not old_value and new_value:
                actions.append("added a cover image")
            else:
                actions.append("changed the cover image")
        elif field_name == 'title':
            actions.append(f"changed the title from '{old_value}' to '{new_value}'")
        elif field_name == 'description':
            actions.append("updated the description")
        elif field_name == 'visibility':
            actions.append(f"changed visibility from '{old_value}' to '{new_value}'")
        elif field_name == 'start_date':
            actions.append(f"changed the start date from '{old_value}' to '{new_value}'")
        elif field_name == 'start_datetime':
            actions.append(f"changed the start date/time from '{old_value}' to '{new_value}'")
        elif field_name == 'end_datetime':
            actions.append(f"changed the end date/time from '{old_value}' to '{new_value}'")
        elif field_name == 'location_name':
            actions.append(f"changed the location from '{old_value}' to '{new_value}'")
        elif field_name == 'no_edits':
            if new_value == 'True':
                actions.append("enabled edit protection")
            else:
                actions.append("disabled edit protection")
        elif field_name == 'event_images':
            # Handle image count changes like "5 images" -> "3 images"
            try:
                old_count = int(old_value.split()[0]) if old_value and 'images' in old_value else 0
                new_count = int(new_value.split()[0]) if new_value and 'images' in new_value else 0

                if old_count > new_count:
                    deleted_count = old_count - new_count
                    if deleted_count == 1:
                        actions.append("removed 1 image")
                    else:
                        actions.append(f"removed {deleted_count} images")
                elif new_count > old_count:
                    added_count = new_count - old_count
                    if added_count == 1:
                        actions.append("added 1 image")
                    else:
                        actions.append(f"added {added_count} images")
                else:
                    actions.append("modified images")
            except (ValueError, IndexError, AttributeError):
                # Fallback for any parsing errors
                actions.append("modified event images")
        elif field_name == 'event_image':
            # Legacy single image handling (kept for backward compatibility)
            if new_value == 'deleted':
                actions.append("removed an image")
            elif old_value == '' and new_value != '':
                actions.append("added an image")
            else:
                actions.append("modified an image")
        else:
            # Generic fallback for other fields
            actions.append(f"changed the {field_name}")

    # Combine actions into a readable sentence
    if len(actions) == 1:
        action_text = actions[0]
    elif len(actions) == 2:
        action_text = f"{actions[0]} and {actions[1]}"
    else:
        action_text = f"{', '.join(actions[:-1])}, and {actions[-1]}"

    # Create the main message
    message = f"{editor_name} {action_text} of your {content_type} '{content_title}'"

    # Add the reason
    if reason and reason.strip():
        message += f"\n\nReason: {reason.strip()}"

    return message

def get_content_edit_history(content_type: str, content_id: int) -> List[Dict[str, Any]]:
    """Get edit history for a piece of content.

    Args:
        content_type: Type of content ('journey', 'event', 'location').
        content_id: ID of the content.

    Returns:
        List[Dict[str, Any]]: List of edit history records.
    """
    try:
        return edit_history_data.get_content_edit_history(content_type, content_id)
    except Exception as e:
        logger.error(f"Error getting edit history for {content_type} {content_id}: {str(e)}")
        return []

def has_edit_history(content_type: str, content_id: int) -> bool:
    """Check if a piece of content has any edit history.

    Args:
        content_type: Type of content ('journey', 'event', 'location').
        content_id: ID of the content.

    Returns:
        bool: True if edit history exists, False otherwise.
    """
    try:
        edit_history = edit_history_data.get_content_edit_history(content_type, content_id)
        return len(edit_history) > 0
    except Exception as e:
        logger.error(f"Error checking edit history for {content_type} {content_id}: {str(e)}")
        return False

def get_user_content_edit_history(user_id: int) -> List[Dict[str, Any]]:
    """Get edit history for all content owned by a user.

    Args:
        user_id: ID of the user.

    Returns:
        List[Dict[str, Any]]: List of edit history records.
    """
    try:
        return edit_history_data.get_user_content_edit_history(user_id)
    except Exception as e:
        logger.error(f"Error getting edit history for user {user_id}'s content: {str(e)}")
        return []

def get_editor_edit_history(editor_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get edit history for edits made by a specific editor.

    Args:
        editor_id: ID of the editor.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of edit history records.
    """
    try:
        return edit_history_data.get_editor_edit_history(editor_id, limit, offset)
    except Exception as e:
        logger.error(f"Error getting edit history for editor {editor_id}: {str(e)}")
        return []