"""
Location Service Module

This module handles all location-related operations including:
- Location retrieval and search
- Location management (create, update)
- Location merging
"""

from typing import Dict, List, Optional, Any, Tuple
from data import location_data
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

# ===== Location Retrieval Operations =====

def get_location(location_id: int) -> Optional[Dict[str, Any]]:
    """Get a location by ID.
    
    Args:
        location_id: ID of the location.
        
    Returns:
        Dict[str, Any]: Location object or None if not found.
    """
    try:
        location = location_data.get_location(location_id)
        return location
    except Exception as e:
        logger.error(f"Error retrieving location with ID {location_id}: {str(e)}", exc_info=True)
        return None

def get_locations(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all locations with pagination and usage count.
    
    Args:
        limit: Maximum number of results.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of location objects with event count.
    """
    try:
        logger.debug(f"Getting locations with count (limit: {limit}, offset: {offset})")
        locations = location_data.get_locations_with_count(limit=limit, offset=offset)
        logger.debug(f"Retrieved {len(locations) if locations else 0} locations")
        return locations
    except Exception as e:
        logger.error(f"Error retrieving all locations: {str(e)}", exc_info=True)
        return []

def get_locations_count() -> int:
    """Get total number of locations.
    
    Returns:
        int: Total number of locations in the system.
    """
    try:
        count = location_data.get_total_locations_count()
        logger.debug(f"Location count: {count}")
        return count
    except Exception as e:
        logger.error(f"Error counting locations: {str(e)}", exc_info=True)
        return 0

# ===== Location Search Operations =====

def search_locations(search_term: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for locations by name with pagination and usage count.
    
    Args:
        search_term: Search term to match against location names.
        limit: Maximum number of results.
        offset: Number of results to skip.
        
    Returns:
        List[Dict[str, Any]]: List of matching location objects with event count.
    """
    try:
        if not search_term or len(search_term.strip()) == 0:
            logger.debug("Empty search term provided, returning empty list")
            return []
        
        # Clean the search term
        search_term = search_term.strip()
        logger.debug(f"Searching locations with term: '{search_term}', limit: {limit}, offset: {offset}")
        
        locations = location_data.search_locations_with_count(search_term, limit=limit, offset=offset)
        logger.debug(f"Search returned {len(locations) if locations else 0} results")
        return locations
    except Exception as e:
        logger.error(f"Error searching locations with term '{search_term}': {str(e)}", exc_info=True)
        return []

def count_search_results(search_term: str) -> int:
    """Get total count of locations matching a search term.
    
    Args:
        search_term: Search term to match.
        
    Returns:
        int: Total number of matching locations.
    """
    try:
        if not search_term or len(search_term.strip()) == 0:
            logger.debug("Empty search term provided, returning count 0")
            return 0
        
        # Clean the search term
        search_term = search_term.strip()
        logger.debug(f"Counting locations matching: '{search_term}'")
        
        count = location_data.count_search_locations(search_term)
        logger.debug(f"Search count result: {count}")
        return count
    except Exception as e:
        logger.error(f"Error counting locations with search term '{search_term}': {str(e)}", exc_info=True)
        return 0

# ===== Location Management Operations =====

def create_location(name: str, longitude: float, latitude: float) -> Tuple[bool, str, Optional[int]]:
    """Create a new location.
    
    Args:
        name: Name of the location.
        
    Returns:
        Tuple[bool, str, Optional[int]]: Tuple containing success flag, message, and location ID if created.
    """
    try:
        if not name or not name.strip():
            logger.warning("Attempt to create location with empty name")
            return False, "Location name cannot be empty", None
            
        name = name.strip()
        
        # Check if location already exists
        existing = location_data.get_location_by_name(name)
        if existing:
            logger.info(f"Location '{name}' already exists with ID {existing['id']}")
            return False, f"Location '{name}' already exists", existing['id']
        
        location_id = location_data.create_location(name, longitude, latitude)
        logger.info(f"Created new location '{name}' with ID {location_id}")
        return True, f"Location '{name}' created successfully", location_id
    except Exception as e:
        logger.error(f"Error creating location '{name}': {str(e)}", exc_info=True)
        return False, f"Failed to create location: {str(e)}", None

def update_location(location_id: int, name: str, user_id: int, edit_reason: Optional[str] = None) -> Tuple[bool, str]:
    """Update a location's name.
    
    Args:
        location_id: ID of the location.
        name: New name for the location.
        user_id: ID of the user making the update.
        edit_reason: Reason for the edit (required for staff).
        
    Returns:
        Tuple[bool, str]: Tuple containing success flag and message.
    """
    try:
        if not name or not name.strip():
            logger.warning(f"Attempt to update location ID {location_id} with empty name")
            return False, "Location name cannot be empty"
            
        name = name.strip()
        
        # Check if location exists
        location = location_data.get_location(location_id)
        if not location:
            logger.warning(f"Attempt to update non-existent location ID {location_id}")
            return False, "Location not found"
        
        # Check if user is staff
        from data import user_data
        user = user_data.get_user_by_id(user_id)
        is_staff = user and user['role'] in ['editor', 'admin']
        
        if not is_staff:
            logger.warning(f"Non-staff user {user_id} attempted to update location")
            return False, "Only editors and admins can update locations"
        
        # Check if an edit reason was provided
        if not edit_reason or not edit_reason.strip():
            logger.warning(f"Staff user {user_id} attempted to edit location {location_id} without providing a reason")
            return False, "Staff must provide a reason when editing a location"
        
        if location['name'] == name:
            logger.info(f"No change needed for location ID {location_id} (name already '{name}')")
            return True, f"No changes were made as the name is the same"
            
        # Track field changes for edit history
        field_changes = {'name': (location['name'], name)}
            
        # Check if new name already exists for a different location
        existing = location_data.get_location_by_name(name)
        if existing and existing['id'] != location_id:
            logger.warning(f"Location name '{name}' already exists with ID {existing['id']}. Cannot update location {location_id} to duplicate name.")
            return False, f"Location name '{name}' already exists. Please choose a unique name."

        # Update the location name
        rows_affected = location_data.update_location(location_id, name)
        if rows_affected > 0:
            # Record the edit in edit history
            from services import edit_history_service
            success, message, edit_id = edit_history_service.record_edit(
                editor_id=user_id,
                content_type='location',
                content_id=location_id,
                field_changes=field_changes,
                reason=edit_reason.strip()
            )

            logger.info(f"Updated location ID {location_id} from '{location['name']}' to '{name}'")
            return True, f"Location updated successfully to '{name}'"
        else:
            logger.warning(f"Failed to update location ID {location_id} to '{name}'")
            return False, "Failed to update location"
    except Exception as e:
        logger.error(f"Error updating location ID {location_id} to '{name}': {str(e)}", exc_info=True)
        return False, f"Failed to update location: {str(e)}"
    
def merge_location(location_id, name):
    """
    Update a location's name
    
    Args:
        location_id (int): ID of the location
        name (str): New name for the location
        
    Returns:
        tuple: (success (bool), message (str))
    """
    try:
        # Check if location exists
        location = location_data.get_location(location_id)
        if not location:
            return False, "Location not found"
        
        if location['name'] == name:
            return True, f"No changes were made as the name is the same"
            
        # Check if new name already exists for a different location
        existing = location_data.get_location_by_name(name)
        if existing and existing['id'] != location_id:
            logger.info(f"Location name '{name}' already exists with ID {existing['id']}. Merging {location_id} into it.")
            try:
                location_data.merge_locations(location_id, existing['id'])
                logger.info(f"Successfully merged location '{location['name']}' (ID: {location_id}) into '{existing['name']}' (ID: {existing['id']})")
                return True, f"Location was merged with existing '{name}'"
            except Exception as merge_error:
                logger.error(f"Error during merge: {str(merge_error)}", exc_info=True)
                return False, f"Failed to merge with existing location: {str(merge_error)}"
        else:
            location_data.update_location(location_id, name)
            logger.info(f"Updated location ID {location_id} to '{name}'")
            return True, f"Location updated successfully to '{name}'"
    except Exception as e:
        logger.error(f"Error updating location ID {location_id} to '{name}': {str(e)}", exc_info=True)
        return False, f"Failed to update location: {str(e)}"


def get_locations_for_journey(journey_id: int) -> Optional[List[Dict[str, Any]]]:
    """Get all locations associated with events in a specific journey.

    Args:
        journey_id: ID of the journey.

    Returns:
        List[Dict[str, Any]]: List of location objects, or None if an error occurs.
    """
    try:
        locations = location_data.get_locations_for_journey(journey_id)
        return locations
    except Exception as e:
        logger.error(f"Error retrieving locations for journey ID {journey_id}: {str(e)}", exc_info=True)
        return None


def get_location_by_name(name: str) -> Optional[Dict[str, Any]]:
    """Get location details by it's name

    Args:
        name: name of the location

    Returns:
        Optional[Dict[str, Any]]]: Location object or None if error occurs
    """
    try:
        location = location_data.get_location_by_name(name)
        return location
    except Exception as e:
        logger.error(f"Error retrieving location details for {name}: {str(e)}", exc_info=True)
        return None

