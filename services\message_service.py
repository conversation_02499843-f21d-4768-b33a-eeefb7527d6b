from typing import List, Dict, Any, Optional, Tuple
from data import message_data
from utils.logger import get_logger

logger = get_logger(__name__)

def get_conversations(user_id: int) -> List[Dict[str, Any]]:
    """
    Get all conversations for a user.

    Args:
        user_id (int): The ID of the user.

    Returns:
        List[Dict[str, Any]]: A list of conversation data.
    """
    return message_data.get_conversations(user_id)

def get_conversation_between(user_id, other_user_id):
    """
    Check if a conversation exists between two users.

    Args:
        user_id: ID of the current user
        other_user_id: ID of the other user

    Returns:
        Dictionary with conversation info if exists, None otherwise
    """
    return message_data.get_conversation_between(user_id, other_user_id)

def get_messages_between(user_id: int, other_user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
    """
    Get messages exchanged between two users.

    Args:
        user_id (int): The ID of the current user.
        other_user_id (int): The ID of the other user.
        limit (int): Maximum number of messages to return.

    Returns:
        List[Dict[str, Any]]: A list of message records.
    """
    return message_data.get_messages_between(user_id, other_user_id, limit)

def send_message(sender_id: int, recipient_id: int, content: str) -> Tuple[bool, str, Optional[int]]:
    """
    Send a new private message.

    Args:
        sender_id (int): The ID of the sender.
        recipient_id (int): The ID of the recipient.
        content (str): The message content.

    Returns:
        Tuple[bool, str, Optional[int]]: Success status, message, and message ID if successful
    """
    # Check if user can send messages
    can_send, reason = check_can_message(sender_id, recipient_id)
    if not can_send:
        return False, reason, None

    message_id = message_data.send_message(sender_id, recipient_id, content)
    if message_id:
        return True, "Message sent successfully", message_id
    else:
        return False, "Failed to send message", None

def delete_message(message_id: int, user_id: int) -> bool:
    """
    Delete a message sent by the current user.

    Args:
        message_id (int): The ID of the message to delete.
        user_id (int): The ID of the user (must be the sender).

    Returns:
        bool: True if the message was deleted, False otherwise.
    """
    rows_affected = message_data.delete_message(message_id, user_id)
    return rows_affected > 0

def mark_message_as_read(message_id: int, user_id: int) -> bool:
    """
    Mark a message as read by the recipient.

    Args:
        message_id (int): The ID of the message to mark as read.
        user_id (int): The ID of the user (must be the recipient).

    Returns:
        bool: True if the message was marked as read, False otherwise.
    """
    return message_data.mark_message_as_read(message_id, user_id)

def get_unread_messages_count(user_id: int) -> int:
    """
    Get the count of unread messages for a user.

    Args:
        user_id (int): The ID of the user.

    Returns:
        int: The number of unread messages.
    """
    return message_data.get_unread_messages_count(user_id)

def get_user_by_id(user_id: int) -> Optional[Dict[str, Any]]:
    """
    Get user information by ID.

    Args:
        user_id (int): The ID of the user.

    Returns:
        Optional[Dict[str, Any]]: The user data or None if not found.
    """
    from services import user_service
    return user_service.get_user_by_id(user_id)

def check_can_message(user_id: int, recipient_id: int) -> Tuple[bool, str]:
    """
    Check if a user can send messages to another user.

    Args:
        user_id (int): The ID of the sender.
        recipient_id (int): The ID of the recipient.

    Returns:
        Tuple[bool, str]: (Can send, reason if cannot)
    """
    # Get the user's role
    from services import user_service
    user = user_service.get_user_by_id(user_id)

    if not user:
        return False, "User not found"

    # For testing purposes, allow all messages
    return True, ""

    # Staff can always send messages
    from utils.permissions import PermissionGroups
    role = user.get('role')
    if role and role in PermissionGroups.STAFF:
        return True, ""

    # Check if user has premium subscription
    from services import subscription_service
    is_premium = subscription_service.has_active_subscription(user_id)

    if is_premium:
        return True, ""
    else:
        return False, "You need a premium subscription to send messages"
