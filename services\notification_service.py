"""
Notification Service Module

This module handles user notifications including:
- Creating and retrieving notifications
- Marking notifications as read
"""

from typing import Dict, List, Optional, Any, Tuple
from data import notification_data
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def create_notification(user_id: int, notification_type: str, content: str, 
                       related_id: Optional[int] = None) -> Tuple[bool, str, Optional[int]]:
    """Create a notification for a user.
    
    Args:
        user_id: ID of the user to notify.
        notification_type: Type of notification ('edit', 'comment', 'like', 'dislike', 'message', 'subscription', 'achievement', 'report').
        content: Notification text.
        related_id: Optional ID related to the notification (e.g., comment ID for comment notifications).
        
    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and notification ID if created.
    """
    try:
        # Validate notification type
        valid_types = ['edit', 'comment', 'like', 'dislike', 'message', 'subscription', 'achievement', 'report', 'helpdesk', 'appeal']
        if notification_type not in valid_types:
            logger.warning(f"Invalid notification type: {notification_type}")
            return False, f"Invalid notification type: {notification_type}", None
        
        # Validate content
        if not content or not content.strip():
            logger.warning(f"Attempt to create notification with empty content")
            return False, "Notification content is required", None
        
        # Create notification
        notification_id = notification_data.create_notification(
            user_id=user_id,
            notification_type=notification_type,
            content=content.strip(),
            related_id=related_id
        )
        
        logger.info(f"Created {notification_type} notification {notification_id} for user {user_id}")
        return True, "Notification created successfully", notification_id
    except Exception as e:
        logger.error(f"Error creating notification for user {user_id}: {str(e)}")
        return False, f"Error creating notification: {str(e)}", None

def get_user_notifications(user_id: int, limit: int = 50, offset: int = 0, 
                          include_read: bool = False) -> List[Dict[str, Any]]:
    """Get notifications for a user.
    
    Args:
        user_id: ID of the user.
        limit: Maximum number of notifications to return.
        offset: Number of notifications to skip.
        include_read: Whether to include read notifications.
        
    Returns:
        List[Dict[str, Any]]: List of notification records.
    """
    try:
        return notification_data.get_user_notifications(user_id, limit, offset, include_read)
    except Exception as e:
        logger.error(f"Error getting notifications for user {user_id}: {str(e)}")
        return []

def mark_notification_as_read(user_id: int, notification_id: int) -> Tuple[bool, str]:
    """Mark a notification as read.
    
    Args:
        user_id: ID of the user.
        notification_id: ID of the notification.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Get the notification to check ownership
        notifications = notification_data.get_user_notifications(user_id, 1000, 0, True)
        notification = next((n for n in notifications if n['id'] == notification_id), None)
        
        if not notification:
            logger.warning(f"User {user_id} attempted to mark non-existent or unauthorized notification {notification_id} as read")
            return False, "Notification not found"
        
        # Mark as read
        rows_affected = notification_data.mark_notification_as_read(notification_id)
        
        if rows_affected > 0:
            logger.info(f"User {user_id} marked notification {notification_id} as read")
            return True, "Notification marked as read"
        else:
            logger.warning(f"Failed to mark notification {notification_id} as read")
            return False, "Failed to mark notification as read"
    except Exception as e:
        logger.error(f"Error marking notification {notification_id} as read by user {user_id}: {str(e)}")
        return False, f"Error marking notification as read: {str(e)}"

def mark_all_notifications_as_read(user_id: int) -> Tuple[bool, str]:
    """Mark all notifications for a user as read.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Mark all as read
        rows_affected = notification_data.mark_all_notifications_as_read(user_id)
        
        logger.info(f"User {user_id} marked all notifications as read ({rows_affected} notifications)")
        return True, f"{rows_affected} notifications marked as read"
    except Exception as e:
        logger.error(f"Error marking all notifications as read for user {user_id}: {str(e)}")
        return False, f"Error marking notifications as read: {str(e)}"

def count_unread_notifications(user_id: int) -> int:
    """Count unread notifications for a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        int: Number of unread notifications.
    """
    try:
        return notification_data.count_unread_notifications(user_id)
    except Exception as e:
        logger.error(f"Error counting unread notifications for user {user_id}: {str(e)}")
        return 0

def count_user_notifications(user_id: int, include_read: bool = False) -> int:
    """Count all notifications for a user.
    
    Args:
        user_id: ID of the user.
        include_read: Whether to include read notifications.
        
    Returns:
        int: Total number of notifications.
    """
    try:
        return notification_data.count_user_notifications(user_id, include_read)
    except Exception as e:
        logger.error(f"Error counting notifications for user {user_id}: {str(e)}")
        return 0

def mark_all_as_read(user_id: int) -> Tuple[bool, str, int]:
    """Mark all notifications as read for a user.
    
    Args:
        user_id: ID of the user.
        
    Returns:
        Tuple[bool, str, int]: Success flag, message, and number of notifications marked as read.
    """
    try:
        rows_affected = notification_data.mark_all_notifications_as_read(user_id)
        logger.info(f"Marked {rows_affected} notifications as read for user {user_id}")
        return True, f"Marked {rows_affected} notifications as read", rows_affected
    except Exception as e:
        logger.error(f"Error marking all notifications as read for user {user_id}: {str(e)}")
        return False, f"Error marking notifications as read: {str(e)}", 0

def notification_exists(user_id: int, notification_type: str, content: str) -> bool:
    """
    Check if a notification with the same type and content already exists for the user (read or unread).
    """
    notifications = notification_data.get_user_notifications(user_id, limit=100, offset=0, include_read=True)
    for n in notifications:
        if n['notification_type'] == notification_type and n['content'] == content:
            return True
    return False
