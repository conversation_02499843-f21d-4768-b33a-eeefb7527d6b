"""
Report Service Module

This module handles content reports for moderation including:
- Creating and managing reports
- Getting and reviewing reports
"""

from typing import Dict, List, Optional, Any, Tuple
from data import report_data, user_data
from utils.logger import get_logger
from utils.permissions import PermissionGroups


# Initialize logger
logger = get_logger(__name__)

def report_content(reporter_id: int, content_type: str, content_id: int,
                  reason: str) -> Tuple[bool, str, Optional[int]]:
    """Report a content item for moderation.

    Args:
        reporter_id: ID of the user reporting the content.
        content_type: Type of content ('comment', 'event', 'journey', 'image', 'user', 'location').
        content_id: ID of the content.
        reason: Reason for the report.

    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and report ID if created.
    """
    try:
        # Validate content type
        valid_types = ['comment', 'event', 'journey', 'image', 'user', 'location']
        if content_type not in valid_types:
            logger.warning(f"Invalid content type: {content_type}")
            return False, f"Invalid content type: {content_type}", None

        # Validate reason
        if not reason or not reason.strip():
            logger.warning(f"User {reporter_id} attempted to report without providing a reason")
            return False, "Report reason is required", None

        # Create report
        report_id = report_data.report_content(
            reporter_id=reporter_id,
            content_type=content_type,
            content_id=content_id,
            reason=reason.strip()
        )

        # If already reported, return appropriate message
        if report_id == 0:
            logger.info(f"User {reporter_id} already reported {content_type} {content_id}")
            return False, f"You have already reported this {content_type}", None

        logger.info(f"User {reporter_id} reported {content_type} {content_id}, report ID: {report_id}")
        return True, f"{content_type.capitalize()} reported successfully", report_id
    except Exception as e:
        logger.error(f"Error reporting {content_type} {content_id} by user {reporter_id}: {str(e)}")
        return False, f"Error reporting {content_type}: {str(e)}", None

def get_report(report_id: int) -> Optional[Dict[str, Any]]:
    """Get a report by ID.

    Args:
        report_id: ID of the report.

    Returns:
        Dict[str, Any]: Report record if found, None otherwise.
    """
    try:
        return report_data.get_report(report_id)
    except Exception as e:
        logger.error(f"Error getting report {report_id}: {str(e)}")
        return None

def get_comment_report(report_id: int) -> Optional[Dict[str, Any]]:
    """Get a comment report by ID.

    Args:
        report_id: ID of the report.

    Returns:
        Dict[str, Any]: Report record if found, None otherwise.
    """
    try:
        return report_data.get_comment_report(report_id)
    except Exception as e:
        logger.error(f"Error getting report {report_id}: {str(e)}")
        return None

def get_reports_by_content(content_type: str, content_id: int) -> List[Dict[str, Any]]:
    """Get all reports for a specific content item.

    Args:
        content_type: Type of content.
        content_id: ID of the content.

    Returns:
        List[Dict[str, Any]]: List of report records.
    """
    try:
        return report_data.get_reports_by_content(content_type, content_id)
    except Exception as e:
        logger.error(f"Error getting reports for {content_type} {content_id}: {str(e)}")
        return []


def review_report(staff_id: int, report_id: int, status: str) -> Tuple[bool, str]:
    """Review a report and update its status.

    Args:
        staff_id: ID of the staff member reviewing the report.
        report_id: ID of the report.
        status: New status ('reviewed', 'dismissed', 'resolved', 'open').

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        # Validate status
        valid_statuses = ['reviewed', 'dismissed', 'resolved', 'open']
        if status not in valid_statuses:
            logger.warning(f"Invalid report status: {status}")
            return False, f"Invalid report status: {status}"

        # Check if report exists
        report = report_data.get_report(report_id)
        if not report:
            logger.warning(f"Attempt to review non-existent report {report_id}")
            return False, "Report not found"

        if status == 'dismissed' and report.get('status') == 'dismissed':
            logger.warning(f"Report {report_id} already dismissed")
            return False, "Report has already been dismissed"

        # Check if staff user exists and has appropriate role
        staff = user_data.get_user_by_id(staff_id)
        if not staff or staff['role'] not in PermissionGroups.REPORT_MANAGERS:
            logger.warning(f"User {staff_id} attempted to review report without permission")
            return False, "Only moderators, editors, support_tech, and admins can review reports"

        # Update report status
        rows_affected = report_data.review_report(report_id, staff_id, status)

        if rows_affected > 0:
            logger.info(f"Staff {staff_id} set report {report_id} status to {status}")
            return True, f"Report marked as {status}"
        else:
            logger.warning(f"Failed to update report {report_id} status")
            return False, "Failed to update report status"
    except Exception as e:
        logger.error(f"Error reviewing report {report_id} by staff {staff_id}: {str(e)}")
        return False, f"Error reviewing report: {str(e)}"


def has_user_flagged_comment(user_id: int, comment_id: int) -> bool:
    """Check if the user has already flagged a specific comment.

    Args:
        user_id: ID of the user.
        comment_id: ID of the comment.

    Returns:
        bool: True if the user has flagged the comment, False otherwise.
    """
    try:
        return report_data.has_user_flagged_comment(user_id, comment_id)
    except Exception as e:
        logger.error(f"Error checking if user {user_id} flagged comment {comment_id}: {str(e)}")
        return False

def get_comment_reports(limit=10, offset=0, search='', active=None, escalated=None, resolved=None, new=None) -> List[Dict[str, Any]]:
    """Get filtered and paginated comment reports."""
    try:
        if search:
            return report_data.search_comment_reports(search_term=search, limit=limit, offset=offset, active=active, escalated=escalated, resolved=resolved)
        return report_data.get_comment_reports(limit=limit, offset=offset, active=active, new=new, escalated=escalated, resolved=resolved)
    except Exception as e:
        logger.error(f"Error getting comment reports: {str(e)}")
        return []


def get_comment_reports_count(search='', active=None, escalated=None, resolved=None) -> int:
    """Returns count of comment reports with optional search and filter."""
    try:
        if search:
            return report_data.get_search_comment_reports_count(search, active, escalated, resolved)
        return report_data.get_comment_reports_count(active, escalated, resolved)
    except Exception as e:
        logger.error(f"Error counting comment reports: {str(e)}")
        return 0


def escalate_comment_to_admin(report_id: int, escalate: int, escalated_by_id: int) -> Tuple[bool, str]:
    """Escalate a comment to admin review by setting escalated_to_admin = 1.

    Args:
        report_id: ID of the report associated with the comment.
        escalate: boolean of escalation status
        escalated_by_admin: ID of staff member who escalated the comment report to admin

    Returns:
        Tuple[bool, str]: Success flag and message.
    """
    try:
        report = report_data.get_report(report_id)
        if not report:
            logger.warning(f"Report ID {report_id} not found for escalation")
            return False, "Report not found"

        if report['content_type'] != 'comment':
            logger.warning(f"Cannot escalate non-comment content type: {report['content_type']}")
            return False, "Only comments can be escalated"


        success = report_data.escalate_comment_to_admin(report_id, escalate, escalated_by_id)
        if success:
            logger.info(f"Comment escalated to admin via report {report_id}")
            action = "un" if escalate == 0 else ""
            return True, f"Comment {action}escalated to admin"
        else:
            logger.error(f"Failed to escalate comment via report {report_id}")
            return False, "Failed to escalate comment"
    except Exception as e:
        logger.error(f"Error escalating comment via report {report_id}: {str(e)}")
        return False, f"Error escalating comment: {str(e)}"

def get_my_comment_reports(user_id, limit=10, offset=0, search='', active=None, resolved=None):
    return report_data.get_my_comment_reports(user_id, limit, offset, active=active, resolved=resolved)

def get_my_comment_reports_count(user_id, active=None, resolved=None):
    return report_data.get_my_comment_reports_count(user_id, active=active, resolved=resolved)
