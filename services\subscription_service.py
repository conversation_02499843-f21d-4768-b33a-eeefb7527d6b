"""
Subscription Service Module

This module handles all subscription-related operations including:
- Subscription creation and management
- Payment processing
- Premium feature access checks
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import date, timedelta
from data import subscription_data, user_data
from utils.logger import get_logger
from datetime import date
from data import subscription_data

# Initialize logger
logger = get_logger(__name__)

def get_user_active_subscription(user_id: int) -> Optional[Dict[str, Any]]:
    """Get the active subscription for a user, including plan info from the view."""
    try:
        subscriptions = subscription_data.get_user_subscriptions(user_id)
        if not subscriptions:
            return None
        for subscription in subscriptions:
            if (subscription.get('is_active', False) and
                subscription.get('end_date') and
                subscription['end_date'] >= date.today()):
                return subscription
        return None
    except Exception as e:
        logger.error(f"Error getting subscription for user {user_id}: {str(e)}")
        return None

def get_user_subscription_history(user_id: int, limit: int, offset: int) -> Tuple[List[Dict[str, Any]], int]:
    """Get paginated subscription history with payment information for a user.

    Args:
        user_id: The user ID
        limit: Number of records per page
        offset: Offset for pagination

    Returns:
        Tuple[List[Dict[str, Any]], int]: List of subscription records and total count
    """
    # Get all subscriptions for total count
    all_subscriptions = subscription_data.get_user_subscriptions(user_id)
    total = len(all_subscriptions)
    paginated = all_subscriptions[offset:offset+limit]
    for sub in paginated:
        base_price = float(sub.get('base_price', 0))
        discount_percentage = float(sub.get('discount_percentage', 0))
        discounted_amount = round(base_price * (1 - discount_percentage / 100), 2)
        sub['discounted_amount'] = discounted_amount
    return paginated, total

def create_free_trial(user_id: int) -> Tuple[bool, str, Optional[int]]:
    """Create a free trial subscription for a user, and also create a payment record for history/receipt."""
    try:
        current_end_date = get_subscription_end_date(user_id)
        if current_end_date and current_end_date >= date.today():
            start_date = current_end_date + timedelta(days=1)
        else:
            start_date = date.today()
        end_date = subscription_data.calculate_subscription_end_date(start_date, 'free_trial')
        subscription_id = subscription_data.create_subscription(
            user_id=user_id,
            plan_code='free_trial',
            start_date=start_date,
            end_date=end_date,
            subscription_type='Free Trial',
            reason='Free Trial',
            months=1
        )
        # Create a payment record for the free trial (amount 0, payment_method 'free_trial')
        payment_id = subscription_data.record_payment(
            user_id=user_id,
            subscription_id=subscription_id,
            amount=0.0,
            gst_amount=0.0,
            payment_method='free_trial',
            billing_address='',
            country_id=1,  # NZ by default, or use actual country id
            card_last_four=None,
            transaction_reference='FREE-TRIAL'
        )
        logger.info(f"Created free trial subscription for user {user_id}, ID: {subscription_id}, payment ID: {payment_id}")
        return True, "Free trial started successfully", subscription_id
    except Exception as e:
        logger.error(f"Error creating free trial for user {user_id}: {str(e)}")
        return False, f"Free trial creation failed: {str(e)}", None

def create_paid_subscription(user_id: int, plan_code: str,
                           amount: float, currency: str, country_id: int,
                           billing_address: str, card_last_four: str,
                           transaction_reference: str, gst_amount: Optional[float] = None,
                           reason: Optional[str] = None) -> Tuple[bool, str, Optional[int]]:
    """Create a paid subscription for a user.

    Args:
        user_id: ID of the user.
        plan_code: Code of the subscription plan.
        amount: Payment amount.
        currency: Payment currency.
        country_id: ID of the billing country.
        billing_address: Billing address.
        card_last_four: Last four digits of credit card.
        transaction_reference: Payment transaction reference.
        gst_amount: Optional GST amount (for applicable countries).
        reason: Optional reason for the subscription.

    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and subscription ID if created.
    """
    try:
        db_plan_code = plan_code

        plan = subscription_data.get_subscription_plan(db_plan_code)
        if not plan:
            logger.warning(f"Invalid subscription plan: {db_plan_code}")
            return False, f"Invalid subscription plan: {db_plan_code}", None

        # Get current subscription end date if exists, otherwise use today
        current_end_date = get_subscription_end_date(user_id)
        if current_end_date and current_end_date >= date.today():
            start_date = current_end_date + timedelta(days=1)
        else:
            start_date = date.today()

        # Calculate end date based on plan
        end_date = subscription_data.calculate_subscription_end_date(start_date, db_plan_code)

        # Create new subscription
        subscription_id = subscription_data.create_subscription(
            user_id=user_id,
            plan_code=db_plan_code,
            start_date=start_date,
            end_date=end_date,
            reason=reason,
            subscription_type='Premium'
        )

        # Record payment
        payment_id = subscription_data.record_payment(
            user_id=user_id,
            subscription_id=subscription_id,
            amount=amount,
            gst_amount=gst_amount,
            country_id=country_id,
            billing_address=billing_address,
            card_last_four=card_last_four,
            transaction_reference=transaction_reference
        )
        if not payment_id:
            logger.error(f"Payment record failed for user {user_id}, subscription {subscription_id}")
            return False, "Payment record failed.", None

        # Create a notification for the user
        from data import notification_data
        notification_content = f"Your Premium subscription has been activated until {end_date.strftime('%B %d, %Y')}"
        notification_data.create_notification(
            user_id=user_id,
            notification_type='subscription',
            content=notification_content,
            related_id=subscription_id
        )

        logger.info(f"Created {db_plan_code} subscription for user {user_id}, ID: {subscription_id}, payment ID: {payment_id}")
        return True, f"Subscription created successfully until {end_date.strftime('%B %d, %Y')}", subscription_id
    except Exception as e:
        logger.error(f"Error creating subscription for user {user_id}: {str(e)}")
        return False, f"Subscription creation failed: {str(e)}", None

def create_gift_subscription(admin_id: int, user_id: int, months: int, reason: Optional[str] = None) -> Tuple[bool, str, Optional[int]]:
    """Gift a subscription to a user (admin only).

    Args:
        admin_id: ID of the admin giving the gift.
        user_id: ID of the user receiving the gift.
        months: Number of months (1-12).
        reason: Optional reason for gifting.

    Returns:
        Tuple[bool, str, Optional[int]]: Success flag, message, and subscription ID if created.
    """
    try:
        # Validate months
        if months < 1 or months > 12:
            logger.warning(f"Invalid gift subscription duration: {months}")
            return False, "Gift subscription must be between 1 and 12 months", None

        # Check if admin user exists and has administrative privileges
        admin = user_data.get_user_by_id(admin_id)
        from utils.permissions import PermissionGroups
        if not admin or admin['role'] not in PermissionGroups.ADMINISTRATORS:
            logger.warning(f"User {admin_id} attempted to gift subscription without admin rights")
            return False, "Only administrators can gift subscriptions", None
        # Create gift subscription
        subscription_id = subscription_data.create_gift_subscription(
            user_id=user_id,
            months=months,
            reason=reason
        )

        # Get end date for notification
        subscription = subscription_data.get_subscription(subscription_id)
        end_date = subscription['end_date'] if subscription else None

        # Create a notification for the user
        from data import notification_data
        notification_content = f"You've been gifted a {months} month subscription"
        if end_date:
            notification_content += f" until {end_date.strftime('%B %d, %Y')}"
        if reason:
            notification_content += f"\nReason: {reason}"

        notification_data.create_notification(
            user_id=user_id,
            notification_type='subscription',
            content=notification_content,
            related_id=subscription_id
        )

        logger.info(f"Admin {admin_id} gifted {months} month subscription to user {user_id}, ID: {subscription_id}")
        return True, f"Gift subscription of {months} months created successfully", subscription_id
    except Exception as e:
        logger.error(f"Error gifting subscription to user {user_id}: {str(e)}")
        return False, f"Gift subscription failed: {str(e)}", None

def get_user_subscription_status(user_id: int) -> Dict[str, Any]:
    """Get a user's subscription status.

    Args:
        user_id: ID of the user.

    Returns:
        Dict[str, Any]: Dictionary with subscription status information.
    """
    try:
        # Get subscription status
        status = subscription_data.get_subscription_status(user_id)

        # Get subscription end date if active
        end_date = None
        if status != 'free':
            end_date = subscription_data.get_subscription_end_date(user_id)

        return {
            'status': status,
            'end_date': end_date,
            'is_premium': status in ['trial', 'premium']
        }
    except Exception as e:
        logger.error(f"Error getting subscription status for user {user_id}: {str(e)}")
        return {
            'status': 'free',
            'end_date': None,
            'is_premium': False
        }

def get_user_payments(user_id: int) -> List[Dict[str, Any]]:
    """Get all payments made by a user.

    Args:
        user_id: ID of the user.

    Returns:
        List[Dict[str, Any]]: List of payment records.
    """
    try:
        return subscription_data.get_user_payments(user_id)
    except Exception as e:
        logger.error(f"Error getting payments for user {user_id}: {str(e)}")
        return []

def get_payment_by_id(payment_id: int) -> Optional[Dict[str, Any]]:
    """Get payment details by ID, including country info."""
    try:
        payment = subscription_data.get_payment(payment_id)
        if not payment:
            return None
        # Ensure amount is a float
        amount = float(payment.get('amount', 0))
        payment['amount'] = amount
        # Calculate GST if applicable (from view fields)
        if payment.get('has_gst') and payment.get('gst_rate'):
            gst_rate = float(payment.get('gst_rate', 0))
            payment['tax'] = round(amount * (gst_rate / 100), 2)
            payment['total'] = round(amount * (1 + gst_rate / 100), 2)
        else:
            payment['tax'] = 0
            payment['total'] = amount
        return payment
    except Exception as e:
        logger.error(f"Error getting payment {payment_id}: {str(e)}")
        return None

def get_payment_by_subscription_id(subscription_id: int) -> Optional[Dict[str, Any]]:
    """Get payment details by subscription ID."""
    try:
        payment = subscription_data.get_payment_by_subscription_id(subscription_id)
        if not payment:
            return None
        # Ensure amount is a float
        amount = float(payment.get('amount', 0))
        payment['amount'] = amount
        # Calculate GST if applicable (from view fields)
        if payment.get('has_gst') and payment.get('gst_rate'):
            gst_rate = float(payment.get('gst_rate', 0))
            payment['tax'] = round(amount * (gst_rate / 100), 2)
            payment['total'] = round(amount * (1 + gst_rate / 100), 2)
        else:
            payment['tax'] = 0
            payment['total'] = amount
        return payment
    except Exception as e:
        logger.error(f"Error getting payment {subscription_id}: {str(e)}")
        return None

def check_can_use_premium_features(user_id: int) -> bool:
    """Check if a user can use premium features.

    Args:
        user_id: ID of the user.

    Returns:
        bool: Whether the user can use premium features.
    """
    try:
        return subscription_data.can_use_premium_features(user_id)
    except Exception as e:
        logger.error(f"Error checking premium features for user {user_id}: {str(e)}")
        return False

def get_users_expiring_soon(days: int = 7) -> List[Dict[str, Any]]:
    """Get users whose subscriptions are expiring soon.

    Args:
        days: Number of days to consider as 'expiring soon'.

    Returns:
        List[Dict[str, Any]]: List of user records with expiration dates.
    """
    try:
        return subscription_data.get_users_expiring_soon(days)
    except Exception as e:
        logger.error(f"Error getting users with expiring subscriptions: {str(e)}")
        return []

def get_user_subscription(user_id: int) -> Dict[str, Any]:
    """Get all relevant subscription info for a user (active, all, payments)."""
    return {
        'active_subscription': get_user_active_subscription(user_id),
        'all_subscriptions': subscription_data.get_user_subscriptions(user_id),
        'payments': get_user_payments(user_id)
    }

def get_selectable_plans() -> List[Dict[str, Any]]:
    """Return only premium and is_selectable subscription plans (from data layer)."""
    return subscription_data.get_selectable_plans()


def check_user_had_free_trial(user_id: int) -> bool:
    """Check if a user has had a free trial."""
    try:
        return subscription_data.check_user_had_free_trial(user_id)
    except Exception as e:
        logger.error(f"Error checking user had free trial for user {user_id}: {str(e)}")
        return False

def check_user_ever_had_premium(user_id: int) -> bool:
    """Check if a user ever had premium subscription (including expired).

    This is used to determine if users can maintain existing no_edits flags
    even after their subscription expires.

    Args:
        user_id: ID of the user.

    Returns:
        bool: Whether the user ever had premium subscription.
    """
    try:
        return subscription_data.get_most_recent_premium_or_trial_subscription(user_id) is not None
    except Exception as e:
        logger.error(f"Error checking if user {user_id} ever had premium: {str(e)}")
        return False

def get_countries() -> list:
    """Return a list of all countries (id, name, currency_code) for payment modal country dropdown."""
    from data import subscription_data
    return subscription_data.get_countries()

def get_subscription_end_date(user_id: int) -> date:
    """Get the end date of the user's subscription."""
    return subscription_data.get_subscription_end_date(user_id)

def get_subscription_plan(plan_code: str) -> Optional[Dict[str, Any]]:
    """Get subscription plan details by plan code."""
    return subscription_data.get_subscription_plan(plan_code)

def calculate_subscription_price(plan_code: str, country_id: int) -> Dict[str, float]:
    """Calculate subscription price including GST."""
    return subscription_data.calculate_subscription_price(plan_code, country_id)

def get_user_subscription_status_for_login(user_id: int) -> Dict[str, Any]:
    """
    Get a user's subscription status, including most recent expired premium/trial if no active.
    """
    try:
        # 1. 먼저 활성 구독 체크
        status = subscription_data.get_subscription_status(user_id)
        end_date = None
        if status != 'free':
            end_date = subscription_data.get_subscription_end_date(user_id)
            return {
                'status': status,
                'end_date': end_date,
                'is_premium': status in ['trial', 'premium']
            }
        # 2. 활성 구독 없으면, 최근 premium/trial 구독(만료된 것 포함) 중 가장 마지막 구독을 찾기
        recent = subscription_data.get_most_recent_premium_or_trial_subscription(user_id)
        if recent:
            plan_code = recent['plan_code']
            end_date = recent['end_date']
            if 'trial' in plan_code:
                status = 'trial'
            elif 'premium' in plan_code:
                status = 'premium'
            else:
                status = 'free'
            return {
                'status': status,
                'end_date': end_date,
                'is_premium': status in ['trial', 'premium']
            }
        # 3. 아무것도 없으면 free
        return {
            'status': 'free',
            'end_date': None,
            'is_premium': False
        }
    except Exception as e:
        logger.error(f"Error getting subscription status for user {user_id}: {str(e)}")
        return {
            'status': 'free',
            'end_date': None,
            'is_premium': False
        }

def check_user_had_premium(user_id: int) -> bool:
    """Check if a user has had a premium subscription."""
    try:
        hi = subscription_data.check_user_had_premium(user_id)
        return subscription_data.check_user_had_premium(user_id)
    except Exception as e:
        logger.error(f"Error checking user had premium for user {user_id}: {str(e)}")
        return False
