"""
User Service Module

This module handles all user management operations including:
- User role management
- User status management (block/ban)
- User search and retrieval
"""

from typing import List, Dict, Tuple, Optional, Any
from data import user_data
from utils.logger import get_logger

# Set up logging
logger = get_logger(__name__)

# ===== User Retrieval =====

def get_user_by_id(user_id: int) -> Optional[Dict[str, Any]]:
    """Get a user by their ID.

    Args:
        user_id: ID of the user to retrieve

    Returns:
        Optional[Dict[str, Any]]: User data dictionary if found, None otherwise
    """
    try:
        logger.debug(f"Getting user by ID: {user_id}")
        user = user_data.get_user_by_id(user_id)
        return user
    except Exception as e:
        logger.error(f"Error retrieving user by ID {user_id}: {str(e)}", exc_info=True)
        return None

# ===== User Role Management =====

def update_user_role(admin_id: int, user_id: int, role: str) -> <PERSON><PERSON>[bool, str]:
    """Update a user's role (admin only).

    Args:
        admin_id: ID of the admin making the change
        user_id: ID of the user to update
        role: New role ('traveller', 'editor', 'admin', 'moderator', 'support_tech')

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        if int(admin_id) == int(user_id):
            return False, "You cannot change your own role"

        from utils.permissions import Roles
        valid_roles = [Roles.TRAVELLER, Roles.EDITOR, Roles.MODERATOR, Roles.SUPPORT_TECH, Roles.ADMIN]
        if role not in valid_roles:
            return False, "Invalid role"

        current_user = user_data.get_user_by_id(user_id)
        if current_user['role'] == role:
            return False, f"User is already a {role.title()}"

        user_data.update_user_role(user_id, role)
        logger.info(f"User {user_id} role updated to {role} by admin {admin_id}")
        return True, f"User role updated to {role} successfully"
    except Exception as e:
        logger.error(f"Role update failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Role update failed: {str(e)}"

# ===== User Status Management =====

def update_user_block_status(admin_id: int, user_id: int, is_blocked: bool) -> Tuple[bool, str]:
    """Update a user's block status (admin or support_tech only).

    Args:
        admin_id: ID of the admin or support tech making the change
        user_id: ID of the user to update
        is_blocked: Whether the user should be blocked

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        if int(admin_id) == int(user_id):
            return False, "You cannot modify your own sharing status"

        user_data.update_user_block_status(user_id, is_blocked)
        status = "blocked from sharing" if is_blocked else "allowed to share"
        logger.info(f"User {user_id} is now {status} (updated by admin/support {admin_id})")
        return True, f"User is now {status}"
    except Exception as e:
        logger.error(f"Block status update failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Block status update failed: {str(e)}"

def update_user_ban_status(admin_id: int, user_id: int, is_banned: bool) -> Tuple[bool, str]:
    """Update a user's ban status (admin or support_tech only).

    Args:
        admin_id: ID of the admin or support tech making the change
        user_id: ID of the user to update
        is_banned: Whether the user should be banned

    Returns:
        A tuple containing:
            - bool: Success status
            - str: Message describing the result
    """
    try:
        if int(admin_id) == int(user_id):
            return False, "You cannot ban yourself"

        user_data.update_user_ban_status(user_id, is_banned)
        status = "banned" if is_banned else "unbanned"
        logger.info(f"User {user_id} {status} by admin/support {admin_id}")
        return True, f"User {status} successfully"
    except Exception as e:
        logger.error(f"Ban status update failed for user {user_id}: {str(e)}", exc_info=True)
        return False, f"Ban status update failed: {str(e)}"

# ===== General User Queries =====

def get_users(limit: int = 100, offset: int = 0, public_only: bool = False) -> List[Dict[str, Any]]:
    """Get all users with pagination.

    Args:
        limit: Maximum number of results
        offset: Number of results to skip
        public_only: If True, only return users who have set their profile as public

    Returns:
        List of user objects
    """
    try:
        logger.debug(f"Getting all users (limit: {limit}, offset: {offset}, public_only: {public_only})")
        users = user_data.get_users(limit, offset, public_only)
        logger.debug(f"Retrieved {len(users)} users")
        return users
    except Exception as e:
        logger.error(f"Error retrieving all users: {str(e)}", exc_info=True)
        return []

def get_users_count(public_only: bool = False) -> int:
    """Get total number of users.

    Args:
        public_only: If True, only count users who have set their profile as public

    Returns:
        Total number of users in the system
    """
    try:
        count = user_data.get_total_users_count(public_only)
        return count
    except Exception as e:
        logger.error(f"Error counting users: {str(e)}", exc_info=True)
        return 0
    
def get_travellers(limit: int = 100, offset: int = 0, public_only: bool = False, exclude_user_id: int = None) -> List[Dict[str, Any]]:
    """Get all travellers with pagination.

    Args:
        limit: Maximum number of results to return. 
        offset: Number of results to skip.
        exclude_user_id: ID of the user to exclude from the results

    Returns:
        List[Dict[str, Any]]: List of traveller records.
    """
    try:
        travellers = user_data.get_travellers(limit, offset, public_only, exclude_user_id)    
        return travellers
    except Exception as e:
        logger.error(f"Error retrieving travellers: {str(e)}", exc_info=True)
        return []                   
    
def get_travellers_count(public_only: bool = False, exclude_user_id: int = None) -> int:
    """Get count of travellers.

    Returns:
        int: Count of travellers.
    """ 
    try:
        count = user_data.get_travellers_count(public_only, exclude_user_id)
        return count
    except Exception as e:
        logger.error(f"Error counting travellers: {str(e)}", exc_info=True)
        return 0


# ===== User Search and Retrieval =====

def search_users(
    search_term: str,
    limit: int = 50,
    offset: int = 0,
    filter_role: Optional[List[str]] = None,
    filter_blocked: Optional[bool] = None,
    filter_banned: Optional[bool] = None,
    public_only: bool = False
) -> List[Dict[str, Any]]:
    """Search for users.

    Args:
        search_term: Search term to match against user information
        limit: Maximum number of results
        offset: Number of results to skip
        filter_role: Optional list of roles to filter by
        filter_blocked: Optional filter for blocked status
        filter_banned: Optional filter for banned status
        public_only: If True, only return users who have set their profile as public

    Returns:
        List of matching user objects
    """
    try:
        # Trim search term before processing
        if search_term:
            search_term = search_term.strip()

        if not search_term or len(search_term) == 0:
            return []

        users = user_data.search_users(
            search_term,
            limit=limit,
            offset=offset,
            filter_role=filter_role,
            filter_blocked=filter_blocked,
            filter_banned=filter_banned,
            public_only=public_only
        )
        return users
    except Exception as e:
        logger.error(f"Error searching users with term '{search_term}': {str(e)}", exc_info=True)
        return []

def count_search_results(
    search_term: str,
    filter_role: Optional[List[str]] = None,
    filter_blocked: Optional[bool] = None,
    filter_banned: Optional[bool] = None,
    public_only: bool = False
) -> int:
    """Count users matching a search term.

    Args:
        search_term: Search term to match
        filter_role: Optional list of roles to filter by
        filter_blocked: Optional filter for blocked status
        filter_banned: Optional filter for banned status
        public_only: If True, only count users who have set their profile as public

    Returns:
        Number of matching users
    """
    try:
        # Trim search term before processing
        if search_term:
            search_term = search_term.strip()

        if not search_term or len(search_term) == 0:
            return 0

        count = user_data.count_search_users(
            search_term,
            filter_role=filter_role,
            filter_blocked=filter_blocked,
            filter_banned=filter_banned,
            public_only=public_only
        )
        return count
    except Exception as e:
        logger.error(f"Error counting search results for '{search_term}': {str(e)}", exc_info=True)
        return 0

# ===== Staff Management =====

def get_staff_accounts(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all staff accounts (editors, moderators, support_techs, and admins).

    Args:
        limit: Maximum number of results
        offset: Number of results to skip

    Returns:
        List of staff account objects
    """
    try:
        staff = user_data.get_staff_accounts(limit, offset)
        return staff
    except Exception as e:
        logger.error(f"Error retrieving staff accounts: {str(e)}", exc_info=True)
        return []

def get_staff_count() -> int:
    """Get count of staff accounts.

    Returns:
        Number of staff accounts
    """
    try:
        count = user_data.get_staff_accounts_count()
        return count
    except Exception as e:
        logger.error(f"Error counting staff accounts: {str(e)}", exc_info=True)
        return 0

# ===== User Status Queries =====

def get_blocked_users(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all blocked users.

    Args:
        limit: Maximum number of results
        offset: Number of results to skip

    Returns:
        List of blocked user objects
    """
    try:
        users = user_data.get_blocked_users(limit, offset)
        return users
    except Exception as e:
        logger.error(f"Error retrieving blocked users: {str(e)}", exc_info=True)
        return []

def get_blocked_users_count() -> int:
    """Get count of blocked users.

    Returns:
        Number of blocked users
    """
    try:
        count = user_data.get_blocked_users_count()
        return count
    except Exception as e:
        logger.error(f"Error counting blocked users: {str(e)}", exc_info=True)
        return 0

def get_banned_users(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all banned users.

    Args:
        limit: Maximum number of results
        offset: Number of results to skip

    Returns:
        List of banned user objects
    """
    try:
        users = user_data.get_banned_users(limit, offset)
        return users
    except Exception as e:
        logger.error(f"Error retrieving banned users: {str(e)}", exc_info=True)
        return []

def get_banned_users_count() -> int:
    """Get count of banned users.

    Returns:
        Number of banned users
    """
    try:
        count = user_data.get_banned_users_count()
        return count
    except Exception as e:
        logger.error(f"Error counting banned users: {str(e)}", exc_info=True)
        return 0

def is_user_blocked(user_id: int) -> bool:
    """Check if a user is blocked.

    Args:
        user_id: ID of the user to check

    Returns:
        Whether the user is blocked
    """
    try:
        user = user_data.get_user_by_id(user_id)
        return user and user.get('is_blocked', False)
    except Exception as e:
        logger.error(f"Error checking if user {user_id} is blocked: {str(e)}", exc_info=True)
        return False

def get_user_with_subscription_status(user_id: int) -> Optional[Dict[str, Any]]:
    """Get a user's basic info and subscription status together.
    Args:
        user_id: ID of the user to retrieve
    Returns:
        Optional[Dict[str, Any]]: User data dictionary with subscription_status if found, None otherwise
    """
    try:
        logger.debug(f"Getting user with subscription status by ID: {user_id}")
        user = user_data.get_user_with_subscription_status(user_id)
        return user
    except Exception as e:
        logger.error(f"Error retrieving user with subscription status by ID {user_id}: {str(e)}", exc_info=True)
        return None

