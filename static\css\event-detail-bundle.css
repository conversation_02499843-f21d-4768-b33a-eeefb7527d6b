/* Event Detail Bundle CSS - Combines all event detail related CSS files to reduce HTTP requests */

/* This bundle combines:
 * - common-bundle.css (card layouts, responsive styles, comments, image gallery)
 * - event-detail.css (event page specific styles)
 * - form-layouts.css (modern form styles for edit modals)
 * - location-search.css (location search and map styles)
 * - staff-permissions.css (staff permission badges and styles)
 * - edit-history.css (edit history modal styles)
 *
 * This reduces 6 HTTP requests to just 1 request for better performance
 */

/* ===== COMMON BUNDLE STYLES ===== */

/* Comprehensive Event Card */
/* Combined Title and Actions Header */
.title-actions-header {
  /* padding: 24px 24px 20px 24px; */
  background: white;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.title-actions-header .title-content {
  flex: 1;
  min-width: 0;
}

.title-actions-header .header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

/* Meta Section */
.meta-section {
  flex-shrink: 0;
}

/* Header Section in Left Column */
.header-section {
  flex-shrink: 0;
}

.header-section .section-header {
  display: none; /* Hide the section header for the header section */
}

/* Content Layout Section - 50/50 Split */
.card-content-layout {
  display: flex;
  gap: 0;
  flex: 1;
  min-height: 0;
}

/* Left Column - Content */
.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Right Column - Visual Content */
.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Content Sections */
.content-section {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.content-section:last-child {
  border-bottom: none;
  flex: 1;
}

/* Description section takes more space */
.description-section {
  flex: 1;
}

/* DateTime section is compact */
.datetime-section {
  flex-shrink: 0;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f8f9fa;
  flex-shrink: 0;
}

.section-header h3 {
  font-size: 15px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

/* Section Icons */
.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  font-size: 14px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
  flex-shrink: 0;
}

/* Section Content */
.section-content {
  color: #495057;
  line-height: 1.5;
  flex: 1;
  overflow: hidden;
}

/* Description Section */
.description-section .section-content p {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* DateTime Section */
.datetime-section .datetime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f8f9fa;
}

.datetime-section .datetime-item:last-child {
  border-bottom: none;
}

.datetime-section .modern-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.datetime-section .modern-label i {
  color: #667eea;
  font-size: 12px;
}

.datetime-section .value {
  font-size: 12px;
  font-weight: 600;
  color: #212529;
}

/* Location Section */
.location-section .location-name {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
}

/* .location-section .map-container {
  margin-top: 8px;
} */

.location-section .event-map {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Header Layout Components */
.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.title-section {
  flex: 1;
  min-width: 0;
}

/* Dropdown Menu Fix for All Screen Sizes */
.dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: none !important;
}

/* ===== EVENT DETAIL SPECIFIC STYLES ===== */

/* Event Detail Page Specific Styles */

/* Modern Event Page Design */
.event-page {
  max-width: 100%;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.back-button:hover {
  color: #007bff !important;
}

/* Main Content */
@media (max-width: 767.98px) {
  .event-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 400px);
    overflow: hidden;
  }
}

@media (min-width: 767.98px) {
  .event-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 300px);
    overflow: hidden;
  }
}

.event-info-container {
  position: relative;
  max-height: calc(100vh - 330px);
  overflow-y: auto;
}

.event-content-container {
  height: calc(100vh - 250px);
  /* overflow-y: auto; */
}

.comments-content-container {
  max-height: calc(100vh - 250px);
  height: 100%;
}

/* Page Layout with Sidebar */
.page-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

/* Main Content Area */
.main-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Top Section: Header + DateTime */
.top-section {
  display: flex;
  gap: 24px;
  align-items: stretch;
}

/* Enhanced Event Header */
.event-header {
  background: white;
  border-radius: 16px;
  padding: 24px 24px 20px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f3f4;
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* DateTime Section */
.datetime-section {
  flex-shrink: 0;
  width: 100%;
}

/* Enhanced DateTime Card */
.datetime-card {
  background: white;
  border: none;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.datetime-card .card-header {
  border-bottom: 1px solid #f1f3f4;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.datetime-card .card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
}

.datetime-card h3 {
  color: #212529;
  font-weight: 600;
}

.datetime-card .datetime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.datetime-card .datetime-item:last-child {
  border-bottom: none;
}

/* Modern Labels - aligned with edit modal styling */
.modern-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-label i {
  color: #667eea;
  font-size: 14px;
}

/* Modern labels in modal contexts */
.modal .modern-label {
  margin-bottom: 8px;
  font-size: 14px;
  text-transform: none;
  letter-spacing: normal;
}

.modal .modern-label i {
  font-size: 16px;
}

.datetime-card .datetime-item .label {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.datetime-card .datetime-item .value {
  font-size: 14px;
  font-weight: 600;
  color: #212529;
}

/* Compact DateTime Card */
.datetime-card.compact {
  height: fit-content;
}

.datetime-card.compact .card-content {
  padding: 0;
}

.datetime-card.compact .datetime-item {
  padding: 6px 0;
}

.datetime-card.compact .datetime-item .label {
  font-size: 11px;
}

.datetime-card.compact .datetime-item .value {
  font-size: 13px;
}

/* Status Badges */
.status-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-right: 12px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.status-badge.private {
  background: #fff3cd;
  color: #856404;
}

.status-badge.hidden {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.protected {
  background: #d1ecf1;
  color: #0c5460;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.like-form {
  margin: 0;
}

.like-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 16px;
  color: #495057;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
}

.like-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.like-btn i.bi-heart-fill {
  color: #dc3545;
}

.like-btn.disabled {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.like-btn.disabled:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: none;
}

.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 50%;
  color: #495057;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Journey Breadcrumb */
.journey-breadcrumb {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.journey-breadcrumb i {
  color: #007bff;
  font-size: 11px;
}

/* Event Title */
.event-title {
  font-size: 24px;
  font-weight: 700;
  color: #212529;
  margin: 0;
  line-height: 1.3;
}

/* Event Meta */
.event-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  margin-top: 0;
  margin-bottom: 0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e9ecef;
}

.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.author-name {
  font-weight: 600;
  color: #212529;
  font-size: 13px;
}

.event-date {
  color: #6c757d;
  font-size: 11px;
}

.update-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;
  font-size: 11px;
}

/* Main Layout - Two Panel Design */
.main-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 24px;
  align-items: start;
}

/* Event Details Section */
.event-details-section {
  min-width: 0; /* Prevents grid overflow */
}

/* Images Section */
.images-section .image-gallery {
  position: relative;
}

.images-section .main-image {
  margin-top: 8px;
  position: relative;
}

.images-section .main-image img {
  height: 200px;
  width: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.main-image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  margin-top: 8px;
}

.main-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.2s;
}

.main-image:hover img {
  transform: scale(1.05);
}

.image-count {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Manage Images Button */
.manage-btn-inside {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  color: #495057;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.manage-btn-inside:hover {
  background: rgba(255, 255, 255, 1);
  border-color: #007bff;
  color: #007bff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.manage-btn-inside.staff-manage-btn {
  background: rgba(255, 193, 7, 0.9);
  color: #212529;
}

.manage-btn-inside.staff-manage-btn:hover {
  background: rgba(255, 193, 7, 1);
}

.manage-btn-inside.staff-manage-btn.disabled {
  background: rgba(203, 203, 203, 0.767);
  color: #000000;
  cursor: not-allowed;
  opacity: 1;
}

/* Image management styles */
.empty-images {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa;
}

/* Comments Section */
.comments-sidebar {
  width: 400px;
  flex-shrink: 0;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.comments-header h2 {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.comment-count {
  background: #f8f9fa;
  color: #495057;
  padding: 3px 10px;
  border-radius: 14px;
  font-size: 11px;
  font-weight: 600;
}

.comment-form-container {
  flex-shrink: 0;
  margin-top: 0;
}

.comment-item {
  display: flex;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.comment-author {
  font-weight: 600;
  color: #212529;
  font-size: 14px;
}

.comment-time {
  color: #6c757d;
  font-size: 12px;
}

.comment-text {
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-word;
  overflow-wrap: break-word;
}

.comment-text.hidden {
  color: #dc3545;
  font-style: italic;
}

.hidden-notice {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  font-style: italic;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-actions form {
  margin: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 16px;
  color: #6c757d;
  font-size: 12px;
  transition: all 0.2s;
  cursor: pointer;
}

.action-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.action-btn.like-btn:hover {
  color: #007bff;
}

.action-btn.dislike-btn:hover {
  color: #dc3545;
}

.action-btn.delete-btn:hover {
  color: #dc3545;
}

.action-btn.report-btn:hover {
  color: #ffc107;
}

.action-btn.reported {
  color: #ffc107;
  cursor: default;
}

.action-btn.disabled {
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.action-btn.disabled:hover {
  background: transparent;
  color: #6c757d;
}

.comment-form {
  display: flex;
  gap: 12px;
  align-items: center;
}

.comment-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #dee2e6;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
  min-height: 40px;
}

.comment-input:focus {
  border-color: #007bff;
}

.submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  transition: background 0.2s;
}

.submit-btn:hover {
  background: #0056b3;
}

/* No Comments State */
.no-comments-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;
  height: 100%;
}

.no-comments-icon {
  margin-bottom: 16px;
}

.no-comments-icon i {
  font-size: 48px;
  color: #dee2e6;
  opacity: 0.8;
}

.no-comments-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: #6c757d;
  margin: 0 0 8px 0;
}

.no-comments-content p {
  font-size: 14px;
  color: #adb5bd;
  margin: 0;
  line-height: 1.5;
}

/* Login Prompt */
.login-prompt {
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

/* Manage Images Interface */
.manage-images-container {
  max-width: 100%;
}

.upload-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px dashed #dee2e6;
}

.existing-images-section {
  background: white;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.image-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.image-item:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.image-preview {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.primary-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.image-actions {
  padding: 12px;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.image-actions .btn {
  font-size: 11px;
  padding: 4px 8px;
}

/* Image Upload Preview */
#imagePreviewContainer .col {
  margin-bottom: 8px;
}

#imagePreviewContainer .card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  height: 100%;
}

#imagePreviewContainer .card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

#imagePreviewContainer .card .ratio {
  aspect-ratio: 1;
}

#imagePreviewContainer .card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#imagePreviewContainer .card-body {
  padding: 8px;
}

#imagePreviewContainer .card-body p {
  margin-bottom: 2px;
  font-size: 12px;
}

#imagePreviewContainer .card-body small {
  font-size: 10px;
}

/* Responsive adjustments for image preview */
@media (max-width: 768px) {
  #imagePreviewContainer .card .ratio {
    aspect-ratio: 1;
  }
}

@media (max-width: 576px) {
  #imagePreviewContainer .card .ratio {
    aspect-ratio: 1;
  }
}

/* Preview container and grid */
.preview-container {
  margin-top: 16px;
}

#previewGrid .col {
  margin-bottom: 12px;
}

#previewGrid .preview-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  background: white;
}

#previewGrid .preview-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

#previewGrid .preview-item img {
  width: 100%;
  height: 100px;
  object-fit: cover;
}

#previewGrid .preview-item .preview-info {
  padding: 8px;
}

#previewGrid .preview-item .preview-info p {
  margin-bottom: 2px;
  font-size: 12px;
  font-weight: 500;
}

#previewGrid .preview-item .preview-info small {
  font-size: 10px;
  color: #6c757d;
}

.feedback-container {
  margin-top: 12px;
}

/* Staff Image Management */
.staff-image-manager .alert {
  font-size: 13px;
}

.staff-image-checkbox {
  transform: scale(1.2);
}

/* Report Input */
.report-input {
  min-height: 100px;
  resize: vertical;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-layout {
    grid-template-columns: 1fr 350px;
  }

  .comments-sidebar {
    width: 350px;
  }
}

@media (max-width: 1024px) {
  .main-layout {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .page-layout {
    flex-direction: column;
    gap: 20px;
  }

  .comments-sidebar {
    position: static;
    max-height: none;
    overflow-y: visible;
    width: 100%;
  }

  .comments-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 300px);
    overflow: hidden;
  }
}

@media (max-width: 768px) {
  .event-content {
    padding: 8px;
    max-width: 100%;
  }

  /* Mobile layout adjustments */
  .page-layout {
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .main-content {
    gap: 12px;
    width: 100%;
  }

  .comments-sidebar {
    width: 100%;
    order: 10; /* Move comments to bottom */
  }

  .comments-section {
    max-height: 300px;
    position: static;
    width: 100%;
    padding: 16px;
    margin-top: 8px;
  }

  /* Comprehensive card mobile adjustments */
  .comprehensive-event-card {
    max-height: none;
    margin-bottom: 12px;
  }

  .title-actions-header {
    padding: 16px;
  }

  .event-title {
    font-size: 20px;
  }

  .event-meta {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  /* Mobile layout - stack vertically */
  .card-content-layout {
    flex-direction: column;
  }

  .left-column {
    border-right: none;
    border-bottom: 1px solid #f1f3f4;
  }

  .right-column {
    border-bottom: none;
  }

  .content-section {
    padding: 16px;
  }

  .section-header h3 {
    font-size: 16px;
  }

  .section-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  /* Mobile section adjustments */
  .location-section .event-map {
    height: 180px;
  }

  .images-section .main-image img {
    height: 200px;
  }

  .comment-item {
    gap: 10px;
  }

  .comment-avatar {
    width: 32px;
    height: 32px;
  }

  /* Mobile adjustments for no comments state */
  .comments-list {
    max-height: 250px;
  }

  .no-comments-state {
    padding: 20px 12px;
    min-height: 120px;
  }

  .no-comments-icon i {
    font-size: 32px;
  }

  .no-comments-content h4 {
    font-size: 14px;
  }

  .no-comments-content p {
    font-size: 12px;
  }

  /* Ensure full width on very small screens */
  .page-layout,
  .main-content {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .comments-sidebar,
  .comments-section,
  .comprehensive-event-card {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  /* Maintain logical order on very small screens */
  .comprehensive-event-card {
    order: 1;
  }

  .comments-sidebar {
    order: 10;
  }
}

/* ===== FORM LAYOUTS STYLES ===== */

/* Modern Form Layout Styles */

/* Form Modal Container */
.create-journey-modal,
.create-event-modal,
.edit-event-modal {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.modern-form {
  background: #ffffff;
  /* border-radius: 12px; */
  overflow: hidden;
}

/* Desktop Grid Layout */
.desktop-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.desktop-grid.single-column {
  grid-template-columns: 1fr;
  max-width: 600px;
  margin: 0 auto;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Sections */
.form-section {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 0;
  border: 1px solid #e1e8ed;
  height: fit-content;
}

.form-section.compact {
  padding: 18px;
}

.form-section.image-section,
.form-section.map-section {
  height: fit-content;
}

/* Section Title */
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  flex: 1;
}

/* Form Grid */
.form-grid {
  display: grid;
  gap: 16px;
}

.form-grid.two-columns {
  grid-template-columns: 1fr 1fr;
}

.form-grid .full-width {
  grid-column: 1 / -1;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Modern Labels */
.modern-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.modern-label i {
  color: #667eea;
  font-size: 16px;
}

/* Modern Inputs */
.modern-input,
.modern-textarea,
.modern-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #2d3748;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
}

.modern-input:focus,
.modern-textarea:focus,
.modern-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.modern-input:disabled,
.modern-input:read-only,
.modern-textarea:disabled,
.modern-select:disabled {
  background: #f7fafc;
  color: #a0aec0;
  border-color: #e2e8f0;
  cursor: not-allowed;
}

.modern-input::placeholder,
.modern-textarea::placeholder {
  color: #a0aec0;
}

/* Date Inputs */
.date-input,
.datetime-input {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

/* Modern Select */
.modern-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Modern Checkbox */
.modern-checkbox {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modern-checkbox-input {
  display: none;
}

.modern-checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  cursor: pointer;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
  position: relative;
}

.modern-checkbox-label::before {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  background: #ffffff;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.modern-checkbox-input:checked + .modern-checkbox-label::before {
  background: #667eea;
  border-color: #667eea;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
}

.modern-checkbox-label:hover {
  border-color: #667eea;
  background: #f7faff;
}

.modern-checkbox-input:checked + .modern-checkbox-label {
  border-color: #667eea;
  background: #f0f4ff;
}

.modern-checkbox-label i {
  color: #667eea;
  font-size: 16px;
}

/* Modern Alert */
.modern-alert {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(102, 126, 234, 0.05);
}

.blocked-alert {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.alert-icon {
  color: #ef4444;
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-text {
  flex: 1;
}

.alert-text strong {
  color: #dc2626;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.alert-text p {
  color: #991b1b;
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

/* Image Preview Styles */
.image-preview-area {
  margin-bottom: 16px;
}

.placeholder-container {
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.placeholder-container:hover {
  border-color: #667eea;
}

.placeholder-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.placeholder-image:hover {
  transform: scale(1.02);
}

/* Help Text */
.input-help {
  font-size: 12px;
  color: #718096;
  margin-top: 4px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.input-help i {
  color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.visibility-help {
  line-height: 1.4;
}

.visibility-help strong {
  color: #4a5568;
}

/* Validation Styles */
.modern-input.is-invalid,
.modern-textarea.is-invalid,
.modern-select.is-invalid,
.was-validated .modern-input:invalid,
.was-validated .modern-textarea:invalid,
.was-validated .modern-select:invalid {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.modern-input.is-valid,
.modern-textarea.is-valid,
.modern-select.is-valid,
.was-validated .modern-input:valid,
.was-validated .modern-textarea:valid,
.was-validated .modern-select:valid {
  border-color: #38a169;
  box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.invalid-feedback {
  color: #e53e3e;
  font-size: 0.75rem !important;
  display: none;
  align-items: center;
  gap: 6px;
}

.modern-input.is-invalid + .invalid-feedback,
.modern-textarea.is-invalid + .invalid-feedback,
.modern-select.is-invalid + .invalid-feedback,
.is-invalid ~ .invalid-feedback,
.was-validated .modern-input:invalid + .invalid-feedback,
.was-validated .modern-textarea:invalid + .invalid-feedback,
.was-validated .modern-select:invalid + .invalid-feedback,
.was-validated .is-invalid ~ .invalid-feedback {
  display: flex;
}

/* ===== LOCATION SEARCH STYLES ===== */

/* Location Search and Map Styles */

/* Location Search Container */
.location-search-container,
.map-search-container {
  display: flex;
  align-items: center;
  position: relative;
}

.location-search-container .modern-input,
.map-search-container .modern-input {
  flex: 1;
  margin-bottom: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
}

/* Search Button */
.search-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
  border-color: #e2e8f0;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 50px;
  transition: all 0.3s ease;
  background: #ffffff;
  color: #667eea;
}

.search-button:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.search-button:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* When input is focused, also style the button */
.location-search-container .modern-input:focus + .search-button,
.map-search-container .modern-input:focus + .search-button {
  border-color: #667eea;
}

/* Location Dropdown */
.location-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.location-dropdown.d-none {
  display: none;
}

.location-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s;
}

.location-dropdown-item:hover {
  background: #f8f9fa;
}

.location-dropdown-item:last-child {
  border-bottom: none;
}

.location-dropdown-item.selected {
  background: #e3f2fd;
}

/* Modern Suggestions (for map search) */
.modern-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: none;
}

.modern-suggestions.show {
  display: block;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f3f4;
  background: white;
  color: #2d3748;
}

.suggestion-item:hover {
  background: #667eea;
  color: white;
}

.suggestion-item:last-child {
  border-bottom: none;
}

/* Location Results */
.location-results {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  margin-top: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.results-header {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  color: #495057;
}

.results-list {
  padding: 0;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
  justify-content: space-between;
}

.result-item:hover {
  background: #f8f9fa;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.selected {
  background: #e3f2fd;
  border-color: #2196f3;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-icon {
  width: 32px;
  height: 32px;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2196f3;
}

.result-details h6 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.result-details small {
  color: #666;
  font-size: 12px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-actions .btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* No Results Message */
.no-results-message {
  border-bottom: none !important;
  background: #f8f9fa;
  color: #6c757d;
  cursor: default;
}

.no-results-message:hover {
  background: #f8f9fa !important;
}

.no-results-message .bi {
  color: #adb5bd;
}

.no-results-message p {
  color: #495057;
}

.no-results-message small {
  color: #6c757d;
}

/* Selected Location Info */
.selected-location-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.current-location-display {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

.current-location-info {
  margin-bottom: 12px;
}

.location-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.location-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.change-location-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

/* Map Styles */
.map-container {
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  background: #f8f9fa;
  position: relative;
}

.map-container.desktop-optimized {
  height: 300px;
}

.modern-map {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.map-preview-only {
  pointer-events: none;
  position: relative;
  cursor: default;
}

/* Overlay to make map non-interactive after initialization */
.map-preview-only.map-initialized::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.02);
  pointer-events: none;
  z-index: 1000;
  cursor: default;
}

/* Ensure map tiles, markers, and controls are not clickable in preview mode */
.map-preview-only .leaflet-tile,
.map-preview-only .leaflet-marker-icon,
.map-preview-only .leaflet-control-container,
.map-preview-only .leaflet-interactive {
  pointer-events: none !important;
  cursor: default !important;
}

.map-disabled {
  opacity: 0.7;
  pointer-events: none;
}

/* Map Search Group */
#mapSearchGroup {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
}

#newLocationNameGroup {
  background: #fff3cd;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #ffeaa7;
}

#coordinatesStatus {
  background: #d4edda;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #c3e6cb;
}

/* Location Choice Modal Styles */
.location-choice-options {
  margin-top: 16px;
}

.choice-description {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.choice-benefits {
  margin-top: 8px;
}

.choice-benefits small {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Loading States */
.location-search-container.loading .modern-input,
.map-search-container.loading .modern-input {
  background-image: url("data:image/svg+xml,%3csvg width='20' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23667eea'%3e%3ccircle cx='10' cy='10' r='2'%3e%3canimate attributeName='r' begin='0s' dur='1.8s' values='2;10;2' calcMode='spline' keyTimes='0;0.2;1' keySplines='0.165,0.84,0.44,1;0.3,0.61,0.355,1' repeatCount='indefinite'/%3e%3c/circle%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

/* Map Marker Styles */
.custom-marker {
  background: #667eea;
  border: 3px solid white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.custom-marker.selected {
  background: #e53e3e;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ===== STAFF PERMISSIONS STYLES ===== */

/* Staff Permissions and Badge Styles */

/* Permission Badges */
.permission-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.permission-badge.staff-edit {
  background: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border-color: rgba(220, 53, 69, 0.3);
}

.staff-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.required-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Staff Sections */
.staff-section {
  border-left: 4px solid #dc3545;
  background: rgba(220, 53, 69, 0.02);
}

.staff-section .section-header {
  background: rgba(220, 53, 69, 0.05);
  margin: -18px -18px 16px -18px;
  padding: 16px 18px;
  border-bottom: 1px solid rgba(220, 53, 69, 0.1);
}

.staff-section.compact .section-header {
  margin: -18px -18px 16px -18px;
  padding: 14px 18px;
}

/* Permission Notices */
.permission-notice {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  font-size: 13px;
  color: #4c63d2;
  margin-top: 8px;
}

.permission-notice.compact {
  padding: 8px 12px;
  font-size: 12px;
}

.permission-notice.staff-notice {
  background: rgba(220, 53, 69, 0.05);
  border-color: rgba(220, 53, 69, 0.2);
  color: #b91c1c;
}

.permission-notice i {
  color: inherit;
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

/* Staff Edit Actions */
.staff-edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(220, 53, 69, 0.1);
}

/* Staff Location Choice Modal Enhancements */
.location-choice-options .form-check {
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.location-choice-options .form-check:hover {
  border-color: #667eea;
  background: #f7faff;
}

.location-choice-options .form-check-input:checked + .form-check-label {
  color: #4c63d2;
}

.location-choice-options .form-check-input:checked ~ * {
  border-color: #667eea;
}

/* Staff Form Validation */
.staff-section .modern-input.is-invalid,
.staff-section .modern-textarea.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.staff-section .invalid-feedback {
  color: #dc3545;
}

/* Staff Button Styles */
.btn-staff-primary {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-staff-primary:hover {
  background: #c82333;
  border-color: #bd2130;
  color: white;
}

.btn-staff-secondary {
  background: transparent;
  border-color: #dc3545;
  color: #dc3545;
}

.btn-staff-secondary:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* Staff Modal Enhancements */
.modal-header.staff-header {
  background: rgba(220, 53, 69, 0.05);
  border-bottom: 1px solid rgba(220, 53, 69, 0.2);
}

.modal-header.staff-header .modal-title {
  color: #721c24;
}

/* Staff Tooltips and Help Text */
.staff-help {
  background: rgba(220, 53, 69, 0.05);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: #b91c1c;
  margin-top: 6px;
}

.staff-help i {
  color: #dc3545;
  margin-right: 4px;
}

/* ===== EDIT HISTORY STYLES ===== */

/**
 * Edit History Modal Styles
 * Modern, minimalistic design for edit history display
 * Now works with the common modal system (#commonModal)
 */

/* Modern Timeline Design */
.edit-timeline {
  position: relative;
  padding: 0;
  margin: 0;
}

.edit-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 20px;
  width: 2px;
  background: linear-gradient(to bottom, #007bff, #6c757d);
  border-radius: 1px;
}

.edit-timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 60px;
}

.edit-timeline-item:last-child {
  margin-bottom: 0;
}

.edit-timeline-badge {
  position: absolute;
  top: 0;
  left: 8px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border: 3px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  z-index: 2;
}

.edit-timeline-badge i {
  font-size: 10px;
  color: white;
}

.edit-timeline-panel {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
}

.edit-timeline-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.edit-timeline-panel::before {
  content: "";
  position: absolute;
  top: 12px;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #e9ecef;
}

.edit-timeline-panel::after {
  content: "";
  position: absolute;
  top: 13px;
  left: -7px;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-right: 7px solid #fff;
}

/* Timeline Content */
.edit-timeline-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
}

.edit-timeline-title {
  font-size: 1rem;
  font-weight: 600;
  color: #212529;
  margin: 0 0 0.5rem 0;
}

.edit-timeline-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
}

.edit-timeline-meta i {
  font-size: 0.75rem;
}

.edit-timeline-body {
  margin-bottom: 1rem;
}

.edit-reason {
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 0.75rem 1rem;
  border-radius: 0 8px 8px 0;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.edit-reason strong {
  color: #495057;
  font-weight: 600;
}

/* Changes Table */
.changes-section {
  margin-top: 1rem;
}

.changes-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.changes-title i {
  color: #007bff;
}

.changes-table {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: visible;
  font-size: 0.875rem;
  table-layout: auto;
  width: 100%;
}

.changes-table th {
  background: #f8f9fa;
  border: none;
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: #495057;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.changes-table td {
  border: none;
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-top: 1px solid #f1f3f4;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  max-width: none;
}

.changes-table .field-name {
  font-weight: 500;
  color: #495057;
}

.changes-table .old-value {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.8rem;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  display: inline-block;
  max-width: none;
}

.changes-table .new-value {
  color: #198754;
  background: rgba(25, 135, 84, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.8rem;
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  display: inline-block;
  max-width: none;
}

/* Empty State */
.edit-history-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.edit-history-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.edit-history-empty h4 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.edit-history-empty p {
  margin: 0;
  font-size: 0.9rem;
}

/* Premium Upgrade Section */
.premium-upgrade {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  margin-top: 1rem;
}

.premium-upgrade i {
  font-size: 2.5rem;
  color: #f39c12;
  margin-bottom: 1rem;
}

.premium-upgrade h4 {
  color: #856404;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.premium-upgrade p {
  color: #856404;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.premium-upgrade .btn {
  background: #f39c12;
  border-color: #f39c12;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.premium-upgrade .btn:hover {
  background: #e67e22;
  border-color: #e67e22;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

/* Loading State */
.edit-history-loading {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.edit-history-loading .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

/* Ensure full text display without truncation */
.edit-history-content .old-value,
.edit-history-content .new-value {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  max-width: none !important;
  text-overflow: unset !important;
  overflow: visible !important;
  display: block !important;
  width: 100%;
}

/* Override any Bootstrap table truncation */
.edit-history-content .changes-table td {
  overflow: visible !important;
  text-overflow: unset !important;
  white-space: normal !important;
  max-width: none !important;
  vertical-align: top !important;
}

/* Ensure modal has enough width for content */
.edit-history-content .table-responsive {
  overflow-x: visible !important;
}

/* Specific styling for edit history in modals */
#commonModal .edit-history-content {
  min-width: 0;
  overflow-wrap: break-word;
}

/* ===== RESPONSIVE STYLES FOR ALL BUNDLES ===== */

/* Form Layouts Responsive */
@media (max-width: 992px) {
  .desktop-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .desktop-grid.single-column {
    max-width: 100%;
  }

  .create-journey-modal,
  .create-event-modal,
  .edit-event-modal {
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  /* Location Search Responsive */
  .map-container.desktop-optimized {
    height: 250px;
  }

  .location-results {
    max-height: 250px;
  }

  .result-item {
    padding: 10px 12px;
  }

  .result-icon {
    width: 28px;
    height: 28px;
  }

  .result-details h6 {
    font-size: 13px;
  }

  .result-details small {
    font-size: 11px;
  }

  .selected-location-info,
  .current-location-display {
    padding: 12px;
  }

  .location-actions,
  .change-location-actions {
    gap: 6px;
  }

  .location-actions .btn,
  .change-location-actions .btn {
    font-size: 12px;
    padding: 6px 10px;
  }

  #mapSearchGroup,
  #newLocationNameGroup {
    padding: 12px;
  }

  /* Staff Permissions Responsive */
  .permission-badge,
  .staff-badge,
  .required-badge {
    font-size: 10px;
    padding: 3px 6px;
  }

  .staff-section .section-header {
    margin: -14px -14px 14px -14px;
    padding: 12px 14px;
  }

  .permission-notice {
    padding: 10px 12px;
    font-size: 12px;
  }

  .permission-notice.compact {
    padding: 8px 10px;
    font-size: 11px;
  }

  .staff-edit-actions {
    flex-direction: column;
    gap: 8px;
  }

  .staff-edit-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .location-choice-options .form-check {
    padding: 12px;
  }

  /* Edit History Responsive */
  .edit-timeline-item {
    padding-left: 50px;
  }

  .edit-timeline::before {
    left: 16px;
  }

  .edit-timeline-badge {
    left: 4px;
    width: 20px;
    height: 20px;
  }

  .edit-timeline-badge i {
    font-size: 8px;
  }

  .edit-timeline-panel {
    padding: 1rem;
  }

  .changes-table {
    font-size: 0.8rem;
  }

  .changes-table th,
  .changes-table td {
    padding: 0.5rem 0.75rem;
  }

  /* Form Layouts Responsive */
  .create-journey-modal,
  .create-event-modal,
  .edit-event-modal {
    margin: 0;
    border-radius: 0;
  }

  /* .form-content {
    padding: 20px 16px;
  } */

  .form-section {
    padding: 16px;
    border-radius: 8px;
  }

  .form-section.compact {
    padding: 14px;
  }

  .desktop-grid {
    gap: 16px;
  }

  .left-column,
  .right-column {
    gap: 16px;
  }

  .form-grid {
    gap: 14px;
  }

  .section-header {
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 14px;
    padding-bottom: 10px;
  }

  .section-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .placeholder-image {
    height: 240px;
  }

  .modern-input,
  .modern-textarea,
  .modern-select {
    padding: 10px 12px;
    font-size: 13px;
  }

  .modern-label {
    font-size: 13px;
    gap: 6px;
  }

  .modern-checkbox-label {
    padding: 10px 12px;
    font-size: 13px;
  }
}

.journey-detail-card .card-body,
.comments-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.comments-list {
  flex: 1 1 auto;
  overflow-y: auto;
}

.comment-form-container {
  flex-shrink: 0;
  margin-top: 0;
}

@media (max-width: 1000px) {
  .comments-list {
    max-height: calc(100vh - 440px);
  }
}

@media (max-width: 767.98px) {
  .comments-list {
    max-height: calc(100vh - 300px);
  }
}

@media (min-width: 767.98px) {
  .comments-list {
    max-height: calc(100vh - 380px);
  }
}

/* Scrollable Image Containers for Image Management */
.image-grid-container.scrollable,
.staff-image-grid-container.scrollable,
.scrollable-preview {
  /* Custom scrollbar styling for webkit browsers */
  scrollbar-width: thin;
  scrollbar-color: #6c757d #f8f9fa;
}

.image-grid-container.scrollable::-webkit-scrollbar,
.staff-image-grid-container.scrollable::-webkit-scrollbar,
.scrollable-preview::-webkit-scrollbar {
  width: 8px;
}

.image-grid-container.scrollable::-webkit-scrollbar-track,
.staff-image-grid-container.scrollable::-webkit-scrollbar-track,
.scrollable-preview::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.image-grid-container.scrollable::-webkit-scrollbar-thumb,
.staff-image-grid-container.scrollable::-webkit-scrollbar-thumb,
.scrollable-preview::-webkit-scrollbar-thumb {
  background: #6c757d;
  border-radius: 4px;
}

.image-grid-container.scrollable::-webkit-scrollbar-thumb:hover,
.staff-image-grid-container.scrollable::-webkit-scrollbar-thumb:hover,
.scrollable-preview::-webkit-scrollbar-thumb:hover {
  background: #495057;
}
