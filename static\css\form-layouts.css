/* Modern Form Layout Styles */

/* Form Modal Container */
.create-journey-modal,
.create-event-modal,
.edit-event-modal {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.modern-form {
  background: #ffffff;
  /* border-radius: 12px; */
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Form Content */
.form-content {
  padding: 24px;
  background: #fafbfc;
}

/* Desktop Grid Layout */
.desktop-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.desktop-grid.single-column {
  grid-template-columns: 1fr;
  max-width: 600px;
  margin: 0 auto;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Sections */
.form-section {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 0;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  height: fit-content;
}

.form-section.compact {
  padding: 18px;
}

.form-section:hover {
  border-color: #667eea;
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
}

.form-section.image-section,
.form-section.map-section {
  height: fit-content;
}

/* Section Headers */
.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f1f3f4;
}

.section-icon {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  flex: 1;
}

/* Form Grid */
.form-grid {
  display: grid;
  gap: 16px;
}

.form-grid.two-columns {
  grid-template-columns: 1fr 1fr;
}

.form-grid .full-width {
  grid-column: 1 / -1;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Modern Labels */
.modern-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.modern-label i {
  color: #667eea;
  font-size: 16px;
}

/* Modern Inputs */
.modern-input,
.modern-textarea,
.modern-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #2d3748;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
}

.modern-input:focus,
.modern-textarea:focus,
.modern-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.modern-input:disabled,
.modern-input:read-only,
.modern-textarea:disabled,
.modern-select:disabled {
  background: #f7fafc;
  color: #a0aec0;
  border-color: #e2e8f0;
  cursor: not-allowed;
}

.modern-input::placeholder,
.modern-textarea::placeholder {
  color: #a0aec0;
}

/* Date Inputs */
.date-input,
.datetime-input {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Modern Select */
.modern-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* Modern Checkbox */
.modern-checkbox {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modern-checkbox-input {
  display: none;
}

.modern-checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  cursor: pointer;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.3s ease;
  position: relative;
}

.modern-checkbox-label::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  background: #ffffff;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.modern-checkbox-input:checked + .modern-checkbox-label::before {
  background: #667eea;
  border-color: #667eea;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
}

.modern-checkbox-label:hover {
  border-color: #667eea;
  background: #f7faff;
}

.modern-checkbox-input:checked + .modern-checkbox-label {
  border-color: #667eea;
  background: #f0f4ff;
}

.modern-checkbox-label i {
  color: #667eea;
  font-size: 16px;
}

/* Modern Alert */
.modern-alert {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(102, 126, 234, 0.05);
}

.blocked-alert {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.alert-icon {
  color: #ef4444;
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-text {
  flex: 1;
}

.alert-text strong {
  color: #dc2626;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.alert-text p {
  color: #991b1b;
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

/* Image Preview Styles */
.image-preview-area {
  margin-bottom: 16px;
}

.placeholder-container {
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.placeholder-container:hover {
  border-color: #667eea;
}

.placeholder-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
  transition: all 0.3s ease;
}

.placeholder-image:hover {
  transform: scale(1.02);
}

/* Help Text */
.input-help {
  font-size: 12px;
  color: #718096;
  margin-top: 4px;
  display: flex;
  align-items: flex-start;
  gap: 6px;
}

.input-help i {
  color: #667eea;
  margin-top: 2px;
  flex-shrink: 0;
}

.visibility-help {
  line-height: 1.4;
}

.visibility-help strong {
  color: #4a5568;
}

/* Validation Styles */
.modern-input.is-invalid,
.modern-textarea.is-invalid,
.modern-select.is-invalid,
.was-validated .modern-input:invalid,
.was-validated .modern-textarea:invalid,
.was-validated .modern-select:invalid {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.modern-input.is-valid,
.modern-textarea.is-valid,
.modern-select.is-valid,
.was-validated .modern-input:valid,
.was-validated .modern-textarea:valid,
.was-validated .modern-select:valid {
  border-color: #38a169;
  box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.invalid-feedback {
  color: #e53e3e;
  font-size: 12px;
  margin-top: 4px;
  display: none;
  align-items: center;
  gap: 6px;
}

.modern-input.is-invalid + .invalid-feedback,
.modern-textarea.is-invalid + .invalid-feedback,
.modern-select.is-invalid + .invalid-feedback,
.is-invalid ~ .invalid-feedback,
.was-validated .modern-input:invalid + .invalid-feedback,
.was-validated .modern-textarea:invalid + .invalid-feedback,
.was-validated .modern-select:invalid + .invalid-feedback,
.was-validated .is-invalid ~ .invalid-feedback {
  display: flex;
}

.invalid-feedback::before {
  content: "⚠";
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 992px) {
  .desktop-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .desktop-grid.single-column {
    max-width: 100%;
  }

  .create-journey-modal,
  .create-event-modal,
  .edit-event-modal {
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .create-journey-modal,
  .create-event-modal,
  .edit-event-modal {
    margin: 0;
    border-radius: 0;
  }

  .form-content {
    padding: 20px 16px;
  }

  .form-section {
    padding: 16px;
    border-radius: 8px;
  }

  .form-section.compact {
    padding: 14px;
  }

  .desktop-grid {
    gap: 16px;
  }

  .left-column,
  .right-column {
    gap: 16px;
  }

  .form-grid {
    gap: 14px;
  }

  .section-header {
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 14px;
    padding-bottom: 10px;
  }

  .section-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .placeholder-image {
    height: 240px;
  }

  .modern-input,
  .modern-textarea,
  .modern-select {
    padding: 10px 12px;
    font-size: 13px;
  }

  .modern-label {
    font-size: 13px;
    gap: 6px;
  }

  .modern-checkbox-label {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .form-content {
    padding: 16px 12px;
  }

  .form-section {
    padding: 12px;
  }

  .form-section.compact {
    padding: 10px;
  }

  .desktop-grid {
    gap: 12px;
  }

  .left-column,
  .right-column {
    gap: 12px;
  }

  .placeholder-image {
    height: 200px;
  }

  .section-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .section-title {
    font-size: 13px;
  }

  .modern-input,
  .modern-textarea,
  .modern-select {
    padding: 8px 10px;
    font-size: 12px;
  }

  .modern-label {
    font-size: 12px;
  }

  .modern-checkbox-label {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Animation for smooth transitions */
.form-section,
.modern-input,
.modern-textarea,
.modern-select,
.modern-checkbox-label,
.placeholder-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for better accessibility */
.modern-input:focus-visible,
.modern-textarea:focus-visible,
.modern-select:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.modern-checkbox-label:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}
