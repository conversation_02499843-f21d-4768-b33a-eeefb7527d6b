/**
 * Journey Detail Page Styles
 * Modular CSS for journey detail page specific styling
 */

/* Username link styling */
.username-link {
  transition: all 0.2s ease;
  padding: 2px 6px;
  border-radius: 4px;
  margin: -2px -6px;
  display: inline;
}

.username-link:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transform: translateY(-1px);
}

/* Profile image styling */
.rounded-circle,
.rounded-circle img {
  position: relative;
  z-index: 1;
}

/* Map preview container */
.map-preview-container {
  position: relative;
  display: inline-block;
  z-index: 2;
}

.view-map-link {
  text-decoration: none;
  color: black;
}

.view-map-link:hover {
  text-decoration: none;
  color: grey;
}

/* Map preview popup */
.map-preview {
  position: absolute;
  top: 100%;
  right: 0;
  width: 300px;
  height: 200px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 1080;
  overflow: hidden;
  text-align: center;
}

.map-preview.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.map-preview::before {
  content: "";
  position: absolute;
  top: -8px;
  right: 20px;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #c8e6c9 0%, #fff59d 50%);
  border-left: 2px solid #e0e0e0;
  border-top: 2px solid #e0e0e0;
  transform: rotate(45deg);
  z-index: 1080;
}

/* Journey Map Styling */
.journey-map-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 181px;
}

#journeyMap {
  border-radius: 8px;
}

.leaflet-popup-content {
  margin: 8px 12px;
}

.event-popup h6 {
  color: #495057;
  margin-bottom: 8px;
}

.location-popup h6 {
  color: #495057;
  margin-bottom: 8px;
}

/* Menu button styling - matching event detail design */
.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 50%;
  color: #495057;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.menu-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
}

/* Follow button styling - matching event detail theme */
.btn[data-action="toggle-follow"] {
  transition: all 0.2s ease;
  border-width: 1px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 16px;
}

.btn[data-action="toggle-follow"]:not(.btn-primary) {
  background: #f8f9fa;
  border-color: #dee2e6;
  color: #495057;
}

.btn[data-action="toggle-follow"]:not(.btn-primary):hover {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn[data-action="toggle-follow"].btn-primary {
  background: #007bff;
  border-color: #007bff;
  color: white;
}

.btn[data-action="toggle-follow"].btn-primary:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn[data-action="toggle-follow"]:active {
  transform: translateY(0);
}

.btn[data-action="toggle-follow"] i.bi-heart-fill {
  color: #dc3545;
}

/* Button group spacing - modern approach */
.d-flex.gap-2 {
  gap: 8px;
}

/* Responsive follow button text */
@media (max-width: 575.98px) {
  .btn[data-action="toggle-follow"] .btn-text {
    display: none !important;
  }

  .btn[data-action="toggle-follow"] {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
}

/* Event map styling */
.event-map-container {
  transition: all 0.3s ease;
}

.event-location-button {
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 6px;
}

.event-location-button:hover {
  background-color: rgba(13, 110, 253, 0.1);
  transform: translateY(-1px);
}

.event-location-button .bi-chevron-down {
  font-size: 0.75rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.event-location-button:hover .bi-chevron-down {
  opacity: 1;
}

.map-toggle-icon {
  transition: transform 0.2s ease;
}

.map-toggle-icon.rotate-180 {
  transform: rotate(180deg);
}

.event-main-content {
  transition: background-color 0.2s ease;
}

.event-main-content:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.event-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
  border-color: rgba(78, 107, 255, 0.3);
}

.event-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* Cover image styling */
.cover-image-img {
  width: 80%;
  height: 100px;
  border-radius: 8px;
  display: block;
}

/* Journey container layout */
#journeyContainer {
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: row;
}

#journeyContainer .col-md-4,
#journeyContainer .col-md-8 {
  display: flex;
  flex-direction: column;
}

#journeyContainer .card {
  flex: 1;
  display: flex;
  flex-direction: column;
}

#journeyContainer .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.event-content.d-flex.flex-column.justify-content-center.align-items-center {
  height: 100%;
  flex: 1;
}

.col-md-4 .card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
}

.journey-info-container {
  position: relative;
  max-height: calc(100vh - 330px);
  overflow-y: auto;
}

.journey-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.journey-info.d-flex.flex-column.justify-content-center.align-items-center {
  height: 100%;
  flex: 1;
}

/* ===== TIMELINE STYLES ===== */
/* Override production bundle conflicts with specific selectors */
#journeyContainer .timeline {
  position: relative;
  padding-left: 2rem !important;
}

#journeyContainer .timeline::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  bottom: 0 !important;
  left: 0.35rem !important;
  width: 2px !important;
  background: rgba(0, 0, 0, 0.1) !important;
  z-index: 1 !important;
  height: 100% !important;
}

#journeyContainer .timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
}

#journeyContainer .timeline-date-marker {
  position: relative !important;
  margin-bottom: 1rem !important;
  margin-left: -0.5rem;
  z-index: 2;
  display: block !important;
  visibility: visible !important;
}

#journeyContainer .timeline-date {
  margin-bottom: 0.5rem;
  position: relative !important;
  left: auto !important;
  transform: none !important;
  z-index: 3;
  display: block !important;
  visibility: visible !important;
}

/* Ensure timeline date badges are visible */
#journeyContainer .timeline-date .badge {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 4;
}

#journeyContainer .timeline-content {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 0.5rem;
  width: 100%;
}

#journeyContainer .timeline-content-wrapper {
  position: relative;
  overflow: visible;
  margin-left: 0 !important;
}

/* Remove conflicting pseudo-elements from production bundle */
#journeyContainer .timeline-content-wrapper::before {
  display: none !important;
}

.timeline-content.card {
  border: none !important;
  overflow: hidden;
}

.timeline-image-container {
  width: 130px;
  min-height: 130px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  /* margin-right: 10px; */
  overflow: hidden;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.timeline-image {
  width: 130px;
  min-height: 130px;
  max-height: 130px;
  object-fit: cover;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  display: block;
}

/* ===== COVER IMAGE STYLES ===== */
.cover-image-container {
  margin-top: 15px;
}

.cover-image {
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8f9fa;
  height: 180px;
}

/* Override existing cover-image-img styles */
#journeyContainer .cover-image-img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  display: block;
  border-radius: 0 !important;
}

.cover-image-placeholder {
  height: 180px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.cover-image-controls {
  z-index: 5;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 4px;
}

.cover-image:hover .cover-image-controls {
  opacity: 1;
}

.cover-image-controls .btn {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
}

.cover-image-controls .btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Clickable cover image styling */
.cover-image-clickable {
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cover-image-clickable:hover {
  transform: scale(1.02);
}

.cover-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
}

.cover-image-clickable:hover .cover-image-overlay {
  opacity: 1;
}

.cover-image-overlay i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.cover-image-overlay span {
  font-size: 0.875rem;
}

/* Loading state for cover image upload */
.cover-image-uploading {
  position: relative;
  overflow: hidden;
}

.cover-image-uploading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.cover-image-uploading::before {
  content: "Uploading...";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  z-index: 11;
}

/* ===== IMAGE MODAL STYLES ===== */
.image-modal .modal-dialog {
  max-width: 90vw;
  max-height: 90vh;
}

.image-modal .modal-content {
  background: transparent;
  border: none;
  box-shadow: none;
}

.image-modal .modal-header {
  border: none;
  padding: 1rem;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1050;
  background: none;
}

.image-modal .modal-title {
  display: none;
}

.image-modal .modal-body {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.image-modal .modal-footer {
  display: none;
}

.image-modal img {
  max-width: 100%;
  max-height: 85vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* ===== EMPTY STATE STYLES ===== */
.empty-state {
  max-width: 400px;
  padding: 2rem;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-state-icon {
  margin: 0 auto;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===== MOBILE RESPONSIVE STYLES ===== */
@media (max-width: 767.98px) {
  #journeyContainer .timeline {
    padding-left: 1.5rem !important;
  }

  #journeyContainer .timeline::before {
    left: 0.25rem !important;
  }

  #journeyContainer .timeline-date-marker {
    margin-left: -0.25rem;
  }

  #journeyContainer .timeline-date {
    left: auto !important;
    transform: none !important;
    position: relative !important;
  }

  .timeline-image-container {
    width: 80px;
    height: 80px;
  }

  .timeline-image {
    width: 80px;
    height: 80px;
  }

  /* Mobile image modal */
  .image-modal .modal-dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }

  .image-modal img {
    max-height: 80vh;
  }

}

color: white;
font-weight: bold;
z-index: 11;
}

/* ===== IMAGE MODAL STYLES ===== */
.image-modal .modal-dialog {
max-width: 90vw;
max-height: 90vh;
}

.image-modal .modal-content {
background: transparent;
border: none;
box-shadow: none;
}

.image-modal .modal-header {
border: none;
padding: 1rem;
position: absolute;
top: 0;
right: 0;
z-index: 1050;
background: none;
}

.image-modal .modal-title {
display: none;
}

.image-modal .modal-body {
padding: 0;
display: flex;
align-items: center;
justify-content: center;
min-height: 50vh;
}

.image-modal .modal-footer {
display: none;
}

.image-modal img {
max-width: 100%;
max-height: 85vh;
object-fit: contain;
border-radius: 8px;
box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* ===== EMPTY STATE STYLES ===== */
.empty-state {
max-width: 400px;
padding: 2rem;
margin: auto;
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
}

.empty-state-icon {
margin: 0 auto;
width: 80px;
height: 80px;
display: flex;
align-items: center;
justify-content: center;
}

/* ===== MOBILE RESPONSIVE STYLES ===== */
@media (max-width: 767.98px) {
#journeyContainer .timeline {
  padding-left: 1.5rem !important;
}

#journeyContainer .timeline::before {
  left: 0.25rem !important;
}

#journeyContainer .timeline-date-marker {
  margin-left: -0.25rem;
}

#journeyContainer .timeline-date {
  left: auto !important;
  transform: none !important;
  position: relative !important;
}

.timeline-image-container {
  width: 80px;
  height: 80px;
}

.timeline-image {
  width: 80px;
  height: 80px;
}

/* Mobile image modal */
.image-modal .modal-dialog {
  margin: 1rem;
  max-width: calc(100vw - 2rem);
}

.image-modal img {
  max-height: 80vh;
}
}

.location-popup {
  min-width: 250px;
}

.location-popup h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.carousel-container {
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.carousel-content {
  position: relative;
  min-height: 150px;
  padding: 10px 15px;
}

.carousel-slide {
  width: 100%;
}

.carousel-slide p {
  margin-top: 8px;
  margin-bottom: 5px;
}

.carousel-button {
  position: absolute;
  top: 50%;
  transform: translateY(-40%);
  background-color: rgba(240, 240, 240, 0.8);
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.carousel-button:hover {
  background-color: rgba(220, 220, 220, 1);
}

.carousel-button.prev {
  left: -10px;
}

.carousel-button.next {
  right: -10px;
}

.carousel-indicator {
  text-align: center;
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

.leaflet-popup-content {
  min-width: 250px;
}

.info-title {
  font-size:14px;
  color: #333;
  margin-bottom: 5px;
  font-weight: bold;
}

/* Harmonize author info block with event detail */
.event-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
  margin-bottom: 0;
}
.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}
.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e9ecef;
}
.author-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.author-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
}
.author-name {
  font-weight: 600;
  color: #212529;
  font-size: 13px;
}
.update-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6c757d;
  font-size: 11px;
}
