/* Production Bundle CSS - Combines ALL commonly used CSS files to minimize HTTP requests */

/* ===== CARD LAYOUTS ===== */

.comprehensive-event-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f3f4;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.title-actions-header {
  padding: 24px 24px 20px 24px;
  border-bottom: 1px solid #f1f3f4;
  background: white;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.title-actions-header .title-content {
  flex: 1;
  min-width: 0;
}

.title-actions-header .header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.header-section {
  flex-shrink: 0;
}

.header-section .section-header {
  display: none;
}

.card-content-layout {
  display: flex;
  gap: 0;
  flex: 1;
  min-height: 0;
}

.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.right-column {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.content-section {
  /* padding: 20px;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  flex-direction: column;
  min-height: 0; */
}

.content-section:last-child {
  border-bottom: none;
  flex: 1;
}

.description-section {
  flex: 1;
}

.datetime-section {
  flex-shrink: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f8f9fa;
  flex-shrink: 0;
}

.section-header h3 {
  font-size: 15px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.section-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  font-size: 14px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
  flex-shrink: 0;
}

.section-content {
  color: #495057;
  line-height: 1.5;
  flex: 1;
  overflow: hidden;
}

.description-section .section-content p {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

.datetime-section .datetime-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f8f9fa;
}

.datetime-section .datetime-item:last-child {
  border-bottom: none;
}

.datetime-section .modern-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.datetime-section .modern-label i {
  color: #667eea;
  font-size: 12px;
}

.datetime-section .value {
  font-size: 12px;
  font-weight: 600;
  color: #212529;
}

.location-section .location-name {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0 0 8px 0;
}

.location-section .map-container {
  margin-top: 8px;
}

.location-section .event-map {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.title-section {
  flex: 1;
  min-width: 0;
}

.dropdown-menu {
  background-color: #ffffff !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: none !important;
}

/* Menu button styling - circular dropdown button */
.menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 50%;
  color: #495057;
  transition: all 0.2s;
}

.menu-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.menu-btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.25);
}

/* ===== JOURNEY STYLES ===== */

.journey-page {
  padding: 20px 0;
}

.journey-breadcrumb {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.journey-info {
  border-radius: 8px;
  /* padding: 20px; */
}

.cover-image-container {
  position: relative;
  margin-bottom: 16px;
}

.cover-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-image-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-image-clickable {
  cursor: pointer;
  position: relative;
  width: 100%;
  height: 100%;
}

.cover-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cover-image-clickable:hover .cover-image-overlay {
  opacity: 1;
}

.cover-image-placeholder {
  background: #e9ecef;
  color: #6c757d;
}

.journey-map-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.status-badges {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.private {
  background: #6c757d;
  color: white;
}

.status-badge.hidden {
  background: #ffc107;
  color: #212529;
}

.visibility-badge {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hidden-badge {
  background: #ffc107 !important;
  color: #212529 !important;
}

/* ===== TIMELINE STYLES ===== */

.timeline-container {
  position: relative;
  max-height: calc(100vh - 350px);
  overflow-y: auto;
  padding-right: 8px;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: "";
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
}

.timeline-date-marker {
  position: relative;
  margin-bottom: 20px;
}

.timeline-date {
  position: absolute;
  left: -15px;
  transform: translateX(-50%);
  z-index: 2;
}

.timeline-content-wrapper {
  position: relative;
  margin-left: 15px;
}

.timeline-content-wrapper::before {
  content: "";
  position: absolute;
  left: -23px;
  top: 20px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #667eea;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #667eea;
  z-index: 2;
}

.timeline-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.timeline-content:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.timeline-content h5,
.timeline-content .event-title {
  white-space: normal !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

/* .timeline-image-container {
  width: 120px;
  height: 100%;
  flex-shrink: 0;
} */

.timeline-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-main-content {
  display: flex;
  transition: background-color 0.2s ease;
}

.event-main-content:hover {
  background-color: #f8f9fa;
}

.event-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.event-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.event-location-button {
  transition: color 0.2s ease;
}

.event-location-button:hover {
  color: #0056b3 !important;
}

/* ===== MAP STYLES ===== */

.map-container {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.modern-map {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.modal .map-container {
  margin: 0;
}

/* ===== RESPONSIVE STYLES ===== */

@media (max-width: 768px) {
  .journey-page {
    padding: 10px 0;
  }

  .timeline-container {
    max-height: calc(100vh - 180px);
  }

  .timeline {
    padding-left: 20px;
  }

  .timeline::before {
    left: 10px;
  }

  .timeline-content-wrapper::before {
    left: -13px;
  }

  .timeline-date {
    left: -10px;
  }

  .timeline-image-container {
    width: 80px;
    height: 80px;
    max-height: 80px;
  }

  .cover-image {
    height: 150px;
  }

  .status-badges {
    flex-wrap: wrap;
  }
}

@media (max-width: 576px) {
  .timeline-container {
    max-height: calc(100vh - 330px);
  }

  .event-main-content {
    flex-direction: column;
  }

  .timeline-image-container {
    width: 100% !important;
    height: auto !important;
    aspect-ratio: 16/9;
    max-height: 120px;
  }
  .timeline-image {
    width: 100% !important;
    height: auto !important;
    object-fit: cover;
    aspect-ratio: 16/9;
    border-radius: 8px;
  }

  .timeline-image-container {
    width: 100%;
    max-height: 120px;
  }
}

@media (max-width: 1024px) {
  .event-main-content {
    flex-direction: column !important;
  }
  .timeline-image-container {
    width: 100% !important;
    height: auto !important;
    aspect-ratio: 16/9;
    max-height: 120px;
  }
  .timeline-image {
    width: 100% !important;
    height: auto !important;
    object-fit: cover;
    aspect-ratio: 16/9;
    border-radius: 8px;
  }
}

/* ===== EDIT HISTORY STYLES ===== */

/* Modern Timeline Design */
.edit-timeline {
  position: relative;
  padding: 0;
  margin: 0;
}

.edit-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 20px;
  width: 2px;
  background: linear-gradient(to bottom, #007bff, #6c757d);
  border-radius: 1px;
}

.edit-timeline-item {
  position: relative;
  margin-bottom: 2rem;
  padding-left: 60px;
}

.edit-timeline-item:last-child {
  margin-bottom: 0;
}

.edit-timeline-badge {
  position: absolute;
  top: 0;
  left: 8px;
  width: 24px;
  height: 24px;
  background: #007bff;
  border: 3px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  z-index: 2;
}

.edit-timeline-badge i {
  font-size: 10px;
  color: white;
}

.edit-timeline-panel {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px 20px 0px 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  position: relative;
}

.edit-timeline-panel:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.edit-timeline-panel::before {
  content: "";
  position: absolute;
  top: 12px;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #e9ecef;
}

.edit-timeline-panel::after {
  content: "";
  position: absolute;
  top: 13px;
  left: -7px;
  width: 0;
  height: 0;
  border-top: 7px solid transparent;
  border-bottom: 7px solid transparent;
  border-right: 7px solid #fff;
}

/* Timeline Content */
.edit-timeline-header {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #f1f3f4;
}

.edit-timeline-title {
  font-size: 1rem;
  font-weight: 600;
  color: #212529;
  margin: 0 0 0.5rem 0;
}

.edit-timeline-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: 0.875rem;
}

.edit-timeline-meta i {
  font-size: 0.75rem;
}

.edit-timeline-body {
  margin-bottom: 1rem;
}

.edit-reason {
  background: #f8f9fa;
  border-left: 4px solid #007bff;
  padding: 0.75rem 1rem;
  border-radius: 0 8px 8px 0;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  white-space: normal !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

.edit-reason strong {
  color: #495057;
  font-weight: 600;
}

/* Changes Table */
.changes-section {
  margin-top: 1rem;
}

.changes-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.changes-title i {
  color: #007bff;
}

.changes-table {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  font-size: 0.875rem;
}

.changes-table th {
  background: #f8f9fa;
  border: none;
  font-weight: 600;
  color: #495057;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.changes-table td {
  border: none;
  vertical-align: middle;
  border-top: 1px solid #f1f3f4;
}

.changes-table .field-name {
  font-weight: 500;
  color: #495057;
}

.changes-table .old-value {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.8rem;
}

.changes-table .new-value {
  color: #198754;
  background: rgba(25, 135, 84, 0.1);
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.8rem;
}

/* Empty State */
.edit-history-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.edit-history-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.edit-history-empty h4 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.edit-history-empty p {
  margin: 0;
  font-size: 0.9rem;
}

/* Premium Upgrade Section */
.premium-upgrade {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  margin-top: 1rem;
}

.premium-upgrade i {
  font-size: 2.5rem;
  color: #f39c12;
  margin-bottom: 1rem;
}

.premium-upgrade h4 {
  color: #856404;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.premium-upgrade p {
  color: #856404;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.premium-upgrade .btn {
  background: #f39c12;
  border-color: #f39c12;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.premium-upgrade .btn:hover {
  background: #e67e22;
  border-color: #e67e22;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

/* Loading State */
.edit-history-loading {
  text-align: center;
  padding: 3rem 2rem;
  color: #6c757d;
}

.edit-history-loading .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

/* Edit History Responsive Design */
@media (max-width: 768px) {
  .edit-timeline-item {
    padding-left: 50px;
  }

  .edit-timeline::before {
    left: 16px;
  }

  .edit-timeline-badge {
    left: 4px;
    width: 20px;
    height: 20px;
  }

  .edit-timeline-badge i {
    font-size: 8px;
  }
}

.event-description-truncate {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

@media (min-width: 1025px) {
  .journey-description-truncate {
    max-height: 2.8em;
    overflow-y: auto;
  }
}

@media (min-width: 1025px) {
  .journey-row-custom {
    display: flex;
    flex-wrap: nowrap;
  }

  .journey-col-custom {
    width: 50%;
    max-width: 50%;
  }

  .btn-text {
    display: inline !important;
  }

  .journey-detail-card {
    height: calc(100vh - 240px) !important;
  }
}

@media (max-width: 1025px) {
  .btn-text {
    display: none !important;
  }

  .journey-detail-card {
    height: auto !important;
    min-height: 60vh;
  }
}

@media (min-width: 1025px) {
  .journey-row-custom {
    display: flex;
    flex-wrap: nowrap;
  }

  .journey-col-custom {
    width: 50%;
    max-width: 50%;
  }

  .btn-text {
    display: inline !important;
  }

  .journey-detail-card {
    height: calc(100vh - 240px) !important;
  }
}

@media (max-width: 1025px) {
  .btn-text {
    display: none !important;
  }

  .journey-detail-card {
    height: auto !important;
    min-height: 60vh;
  }
}
