/* Staff Permissions and Badge Styles */

/* Permission Badges */
.permission-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.permission-badge.staff-edit {
  background: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border-color: rgba(220, 53, 69, 0.3);
}

.staff-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.required-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Staff Sections */
.staff-section {
  border-left: 4px solid #dc3545;
  background: rgba(220, 53, 69, 0.02);
}

.staff-section .section-header {
  background: rgba(220, 53, 69, 0.05);
  margin: -18px -18px 16px -18px;
  padding: 16px 18px;
  border-bottom: 1px solid rgba(220, 53, 69, 0.1);
}

.staff-section.compact .section-header {
  margin: -18px -18px 16px -18px;
  padding: 14px 18px;
}

/* Permission Notices */
.permission-notice {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  font-size: 13px;
  color: #4c63d2;
  margin-top: 8px;
}

.permission-notice.compact {
  padding: 8px 12px;
  font-size: 12px;
}

.permission-notice.staff-notice {
  background: rgba(220, 53, 69, 0.05);
  border-color: rgba(220, 53, 69, 0.2);
  color: #b91c1c;
}

.permission-notice i {
  color: inherit;
  font-size: 14px;
  margin-top: 1px;
  flex-shrink: 0;
}

/* Staff Edit Actions */
.staff-edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(220, 53, 69, 0.1);
}

/* Staff Location Choice Modal Enhancements */
.location-choice-options .form-check {
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.location-choice-options .form-check:hover {
  border-color: #667eea;
  background: #f7faff;
}

.location-choice-options .form-check-input:checked + .form-check-label {
  color: #4c63d2;
}

.location-choice-options .form-check-input:checked ~ * {
  border-color: #667eea;
}

.choice-description {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.choice-benefits {
  margin-top: 8px;
}

.choice-benefits small {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Staff Form Validation */
.staff-section .modern-input.is-invalid,
.staff-section .modern-textarea.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.staff-section .invalid-feedback {
  color: #dc3545;
}

/* Staff Button Styles */
.btn-staff-primary {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-staff-primary:hover {
  background: #c82333;
  border-color: #bd2130;
  color: white;
}

.btn-staff-secondary {
  background: transparent;
  border-color: #dc3545;
  color: #dc3545;
}

.btn-staff-secondary:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* Staff Modal Enhancements */
.modal-header.staff-header {
  background: rgba(220, 53, 69, 0.05);
  border-bottom: 1px solid rgba(220, 53, 69, 0.2);
}

.modal-header.staff-header .modal-title {
  color: #721c24;
}

/* Staff Tooltips and Help Text */
.staff-help {
  background: rgba(220, 53, 69, 0.05);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  color: #b91c1c;
  margin-top: 6px;
}

.staff-help i {
  color: #dc3545;
  margin-right: 4px;
}

/* Responsive Staff Styles */
@media (max-width: 768px) {
  .permission-badge,
  .staff-badge,
  .required-badge {
    font-size: 10px;
    padding: 3px 6px;
  }

  .staff-section .section-header {
    margin: -14px -14px 14px -14px;
    padding: 12px 14px;
  }

  .permission-notice {
    padding: 10px 12px;
    font-size: 12px;
  }

  .permission-notice.compact {
    padding: 8px 10px;
    font-size: 11px;
  }

  .staff-edit-actions {
    flex-direction: column;
    gap: 8px;
  }

  .staff-edit-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .location-choice-options .form-check {
    padding: 12px;
  }

  .choice-description {
    font-size: 13px;
  }

  .choice-benefits small {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .permission-badge,
  .staff-badge,
  .required-badge {
    font-size: 9px;
    padding: 2px 5px;
  }

  .staff-section .section-header {
    margin: -10px -10px 12px -10px;
    padding: 10px 10px;
  }

  .permission-notice {
    padding: 8px 10px;
    font-size: 11px;
  }

  .permission-notice.compact {
    padding: 6px 8px;
    font-size: 10px;
  }

  .staff-help {
    padding: 6px 8px;
    font-size: 11px;
  }

  .location-choice-options .form-check {
    padding: 10px;
  }

  .choice-description {
    font-size: 12px;
  }

  .choice-benefits small {
    font-size: 10px;
  }
}

/* Staff Animation Effects */
.staff-section,
.permission-notice,
.staff-badge,
.permission-badge {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.staff-section:hover {
  box-shadow: 0 4px 20px rgba(220, 53, 69, 0.1);
}

/* Staff Focus States */
.staff-section .modern-input:focus,
.staff-section .modern-textarea:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Staff Loading States */
.staff-section.loading {
  opacity: 0.7;
  pointer-events: none;
}

.staff-section.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #dc3545;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
