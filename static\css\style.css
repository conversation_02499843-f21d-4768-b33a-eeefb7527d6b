/* Custom CSS for Footprints */
/* Custom primary color */
:root {
  --primary-color: #4e6bff;
  --primary-rgb: 78, 107, 255;
  --primary-bg-subtle: rgba(var(--primary-rgb), 0.1);
  --primary-darker: #3f56cc;

  --dark-color: #1a1a1a;
  --light-color: #ffffff;
  --white-opacity-50: rgba(255, 255, 255, 0.5);
  --white-opacity-95: rgba(255, 255, 255, 0.95);

  --secondary-color: #6c757d;
  --bs-secondary-color: rgba(33, 37, 41, 0.75);

  --success-rgb: 25, 135, 84;
  --success-bg-subtle: rgba(var(--success-rgb), 0.1);

  --journey-card-height: 350px;
  --journey-image-height: 160px;
  --announcement-line-height: 1.5em;
  --announcement-max-lines: 3;
  --announcement-content-height: calc(
    var(--announcement-line-height) * var(--announcement-max-lines)
  );
}

/* Main content area */
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

textarea {
  resize: none;
}

/* Journey cards */
.journey-card {
  transition: transform 0.2s;
}

.journey-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Event timeline */
.event-timeline {
  position: relative;
  padding-left: 30px;
}

.event-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 15px;
  width: 2px;
  background-color: #dee2e6;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item::before {
  content: "";
  position: absolute;
  left: -30px;
  top: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #007bff;
}

/* Image display */
.event-image {
  max-width: 100%;
  height: auto;
  display: block;
  object-fit: contain;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/* Location autocomplete */
.ui-autocomplete {
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* login-form-label */
.login-form-label {
  margin-top: 60px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* login-form-label-username */
.login-form-label-username {
  margin-top: 20px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* login-form-label-password */
.login-form-label-password {
  margin-top: 20px;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* navaigation bar */
.navbar {
  background-color: rgb(34, 34, 34);
}

/* login-button */
.login-button {
  margin-top: 40px;
}

/* signup-button  */
.signup-button {
  margin-top: 10px;
}

/* login-container */
.login-container {
  display: flex;
  align-items: center;
  min-height: calc(100vh - 240px);
  padding-top: 0;
  padding-bottom: 0;
}

.full-height {
  width: 100%;
}

.login-wrapper {
  overflow: hidden;
  border-radius: 0.5rem;
}

.login-form-section {
  background-color: white;
}

.login-image-wrapper {
  height: 500px;
  overflow: hidden;
}

.register-image-wrapper {
  height: 650px;
  overflow: hidden;
}

.login-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 0.5rem;
}

.register-image {
  width: 96%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 0.5rem;
}

.form-control {
  border: 1px solid #e9ecef;
}

.form-control:focus {
  background-color: #fff;
}

.btn-dark {
  background-color: #212529;
}

.btn-dark:hover {
  background-color: #000;
}

@media (max-width: 767.98px) {
  .login-container {
    padding-top: 2rem;
    padding-bottom: 2rem;
    min-height: auto;
  }
}

/* Pagination styling */
.pagination .page-link:focus {
  box-shadow: none;
}

.invalid-feedback {
  font-size: 0.8em !important;
  margin-top: 0.1rem !important;
}

/* Ensure images and location sections split space equally and images are responsive */
.images-section,
.location-section {
  flex: 1 1 0;
  min-width: 0;
}

/* Status Change Modal Enhancements */
.status-change-form {
  background: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
}

.current-status-display {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.status-change-select {
  border: 2px solid #dee2e6;
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.status-change-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.status-change-select option {
  padding: 0.5rem;
  font-size: 0.9rem;
}

.status-preview {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid #2196f3;
  border-radius: 0.5rem;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.status-guideline {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 0.25rem;
  padding: 0.5rem;
  margin-top: 0.5rem;
}

.status-transition-arrow {
  color: #6c757d;
  font-size: 1.1rem;
}

/* Status badge improvements */
.status-badge {
  font-weight: 500;
  letter-spacing: 0.025em;
  border: 1px solid rgba(0,0,0,0.1);
}

.status-badge i {
  margin-right: 0.25rem;
}
