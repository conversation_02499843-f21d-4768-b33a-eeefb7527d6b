/**
 * Comment Interactions Handler
 * Handles AJAX interactions for comments (like, dislike, delete, add)
 */

class CommentInteractions {
  constructor() {
    this.bindEventListeners();
  }

  /**
   * Bind event listeners for comment interactions
   */
  bindEventListeners() {
    // Handle comment like/dislike buttons
    document.addEventListener("click", (e) => {
      if (e.target.matches(".like-btn, .like-btn *")) {
        e.preventDefault();
        const button = e.target.closest(".like-btn");
        if (button) {
          this.handleCommentInteraction(button, "like");
        }
      }

      if (e.target.matches(".dislike-btn, .dislike-btn *")) {
        e.preventDefault();
        const button = e.target.closest(".dislike-btn");
        if (button) {
          this.handleCommentInteraction(button, "dislike");
        }
      }
    });

    // Handle comment deletion
    document.addEventListener("click", (e) => {
      if (e.target.matches(".delete-btn, .delete-btn *")) {
        e.preventDefault();
        const button = e.target.closest(".delete-btn");
        if (button) {
          this.handleCommentDelete(button);
        }
      }
    });

    // Handle comment form submission
    document.addEventListener("submit", (e) => {
      if (e.target.matches(".comment-form")) {
        e.preventDefault();
        this.handleCommentSubmission(e.target);
      }
    });
  }

  /**
   * Handle comment like/dislike interactions
   */
  async handleCommentInteraction(button, action) {
    try {
      // Disable button during request
      button.disabled = true;

      // Get the form that contains this button
      const form = button.closest("form");
      if (!form) {
        console.error("No form found for comment interaction");
        return;
      }

      const formData = new FormData(form);
      formData.set("action", action); // Ensure correct action

      const response = await fetch(form.getAttribute("action"), {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const data = await response.json();

      if (data.success) {
        // Update the comment UI
        this.updateCommentInteractionUI(data.comment_id, data);

        // Show success message
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "success");
        }
      } else {
        // Show error message
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error handling comment interaction:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "An error occurred. Please try again.",
          "danger"
        );
      }
    } finally {
      // Re-enable button
      button.disabled = false;
    }
  }

  /**
   * Update comment interaction UI (like/dislike counts and button states)
   */
  updateCommentInteractionUI(commentId, data) {
    const commentItem = document.querySelector(
      `[data-comment-id="${commentId}"]`
    );
    if (!commentItem) {
      console.error(`Comment with ID ${commentId} not found`);
      return;
    }

    // Update like button
    const likeBtn = commentItem.querySelector(".like-btn");
    if (likeBtn) {
      const likeIcon = likeBtn.querySelector("i");
      const likeCount = likeBtn.querySelector("span");

      if (likeIcon) {
        likeIcon.className = data.user_has_liked
          ? "bi bi-hand-thumbs-up-fill"
          : "bi bi-hand-thumbs-up";
      }

      if (likeCount) {
        likeCount.textContent = data.like_count || 0;
      }
    }

    // Update dislike button
    const dislikeBtn = commentItem.querySelector(".dislike-btn");
    if (dislikeBtn) {
      const dislikeIcon = dislikeBtn.querySelector("i");
      const dislikeCount = dislikeBtn.querySelector("span");

      if (dislikeIcon) {
        dislikeIcon.className = data.user_has_disliked
          ? "bi bi-hand-thumbs-down-fill"
          : "bi bi-hand-thumbs-down";
      }

      if (dislikeCount) {
        dislikeCount.textContent = data.dislike_count || 0;
      }
    }
  }

  /**
   * Handle comment deletion with confirmation
   */
  async handleCommentDelete(button) {
    // Extract comment and event IDs from the onclick attribute or data attributes
    const commentId = this.extractCommentId(button);
    const eventId = this.extractEventId(button);

    if (!commentId || !eventId) {
      console.error("Could not extract comment or event ID");
      return;
    }

    // Show confirmation modal
    if (typeof showModal === "function") {
      showModal(
        "Delete Comment",
        "Are you sure you want to delete this comment? This action cannot be undone.",
        {
          actionText: "Delete",
          onAction: async () => {
            await this.performCommentDelete(eventId, commentId);
            return true;
          },
        }
      );
    } else {
      // Fallback to confirm dialog
      if (
        confirm(
          "Are you sure you want to delete this comment? This action cannot be undone."
        )
      ) {
        await this.performCommentDelete(eventId, commentId);
      }
    }
  }

  /**
   * Perform the actual comment deletion
   */
  async performCommentDelete(eventId, commentId) {
    try {
      const response = await fetch(
        `/event/${eventId}/comment/${commentId}/delete`,
        {
          method: "POST",
          headers: {
            "X-Requested-With": "XMLHttpRequest",
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        // Remove the comment from the DOM
        this.removeCommentFromDOM(commentId);

        // Show success message
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "success");
        }

        // Close modal if it exists
        if (typeof window.closeModal === "function") {
          window.closeModal();
        }
      } else {
        // Show error message
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error deleting comment:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "An error occurred while deleting the comment.",
          "danger"
        );
      }
    }
  }

  /**
   * Remove comment from DOM
   */
  removeCommentFromDOM(commentId) {
    const commentItem = document.querySelector(
      `[data-comment-id="${commentId}"]`
    );
    if (commentItem) {
      // Add fade out animation
      commentItem.style.transition = "opacity 0.3s ease";
      commentItem.style.opacity = "0";

      // Remove after animation
      setTimeout(() => {
        commentItem.remove();

        // Update comment count
        this.updateCommentCount(-1);

        // Check if there are no comments left and show empty state
        this.checkAndShowEmptyState();
      }, 300);
    }
  }

  /**
   * Check if comments list is empty and show empty state
   */
  checkAndShowEmptyState() {
    const commentsList = document.querySelector(".comments-list");
    const commentItems = commentsList.querySelectorAll(".comment-item");

    if (commentItems.length === 0) {
      commentsList.innerHTML = `
        <div class="no-comments-state">
          <div class="no-comments-icon">
            <i class="bi bi-chat-dots"></i>
          </div>
          <div class="no-comments-content">
            <h4>No comments yet</h4>
            <p>Be the first to share your thoughts about this event!</p>
          </div>
        </div>
      `;
    }
  }

  /**
   * Handle comment form submission
   */
  async handleCommentSubmission(form) {
    try {
      const formData = new FormData(form);
      const content = formData.get("content");

      if (!content || !content.trim()) {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage("Comment cannot be empty.", "warning");
        }
        return;
      }

      // Disable submit button
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        submitBtn.disabled = true;
      }

      const response = await fetch(form.getAttribute("action"), {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const data = await response.json();

      if (data.success) {
        // Add the new comment to the DOM
        await this.addCommentToDOM(data.comment);

        // Clear the form
        form.reset();

        // Show success message
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "success");
        }
      } else {
        // Show error message
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error submitting comment:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "An error occurred while adding your comment.",
          "danger"
        );
      }
    } finally {
      // Re-enable submit button
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        submitBtn.disabled = false;
      }
    }
  }

  /**
   * Add new comment to DOM
   */
  async addCommentToDOM(comment) {
    const commentsList = document.querySelector(".comments-list");

    // Remove empty state if it exists
    const emptyState = commentsList.querySelector(".no-comments-state");
    if (emptyState) {
      emptyState.remove();
    }

    // Create comment HTML
    const commentHtml = await this.createCommentHTML(comment);

    // Add to the end of the comments list (chronological order - newest at bottom)
    commentsList.insertAdjacentHTML("beforeend", commentHtml);

    // Update comment count
    this.updateCommentCount(1);

    // Get the newly added comment (last child)
    const commentItems = commentsList.querySelectorAll(".comment-item");
    const newComment = commentItems[commentItems.length - 1];

    if (newComment) {
      // Add fade in animation
      newComment.style.opacity = "0";
      newComment.style.transition = "opacity 0.3s ease";
      setTimeout(() => {
        newComment.style.opacity = "1";
      }, 10);

      // Scroll to the new comment to show it to the user
      newComment.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
    }
  }

  /**
   * Update comment count in header
   */
  updateCommentCount(change) {
    const commentCountElement = document.querySelector(".comment-count");
    if (commentCountElement) {
      const currentCount = parseInt(commentCountElement.textContent) || 0;
      const newCount = Math.max(0, currentCount + change);
      commentCountElement.textContent = newCount;
    }
  }

  /**
   * Get safe image URL (similar to Flask template function)
   * This method now uses the API to check file existence
   */
  async getSafeImageUrl(imageName, type = "profile") {
    if (!imageName || imageName === "None" || imageName === "") {
      if (type === "profile") {
        return "uploads/profile_images/profile_placeholder.png";
      }
      return "uploads/profile_images/profile_placeholder.png"; // Default fallback
    }

    try {
      const response = await fetch(
        `/api/safe-image-url?filename=${encodeURIComponent(
          imageName
        )}&type=${encodeURIComponent(type)}`
      );
      const data = await response.json();

      if (response.ok && data.url) {
        return data.url;
      } else {
        // Fallback to placeholder if API call fails
        if (type === "profile") {
          return "uploads/profile_images/profile_placeholder.png";
        }
        return "uploads/profile_images/profile_placeholder.png"; // Default fallback
      }
    } catch (error) {
      console.warn("Error fetching safe image URL:", error);
      // Fallback to placeholder if API call fails
      if (type === "profile") {
        return "uploads/profile_images/profile_placeholder.png";
      }
      return "uploads/profile_images/profile_placeholder.png"; // Default fallback
    }
  }

  /**
   * Create HTML for a new comment
   */
  async createCommentHTML(comment) {
    // Get current user info for determining if user can delete
    const currentUserId = this.getCurrentUserId();
    const canDelete = currentUserId && currentUserId == comment.user_id;

    // Format the timestamp
    const timeAgo = this.formatTimeAgo(comment.created_at);

    // Get safe image URL
    const safeImageUrl = await this.getSafeImageUrl(
      comment.profile_image,
      "profile"
    );

    return `
      <div class="comment-item" data-comment-id="${comment.id}">
        <div class="comment-avatar">
          <img src="/static/${safeImageUrl}"
               alt="${comment.username}">
        </div>
        <div class="comment-content">
          <div class="comment-header">
            <span class="comment-author">${comment.username}</span>
            <span class="comment-time">${timeAgo}</span>
          </div>
          <div class="comment-text">
            ${comment.content}
          </div>
          <div class="comment-actions">
            <form action="/event/${this.getEventId()}/comment/${
      comment.id
    }/interact" method="post">
              <input type="hidden" name="action" value="like">
              <button type="submit" class="action-btn like-btn">
                <i class="bi bi-hand-thumbs-up"></i>
                <span>0</span>
              </button>
            </form>
            <form action="/event/${this.getEventId()}/comment/${
      comment.id
    }/interact" method="post">
              <input type="hidden" name="action" value="dislike">
              <button type="submit" class="action-btn dislike-btn">
                <i class="bi bi-hand-thumbs-down"></i>
                <span>0</span>
              </button>
            </form>
            ${
              canDelete
                ? `
              <form method="post" action="/event/${this.getEventId()}/comment/${
                    comment.id
                  }/delete">
                <button type="button" class="action-btn delete-btn" onclick="confirmDeleteComment('${
                  comment.id
                },${this.getEventId()}')">
                  <i class="bi bi-trash"></i>
                </button>
              </form>
            `
                : ""
            }
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Get current user ID from session or page data
   */
  getCurrentUserId() {
    // Try to get from a data attribute on the page
    const eventContent = document.querySelector(".event-content");
    if (eventContent && eventContent.dataset.currentUserId) {
      return eventContent.dataset.currentUserId;
    }

    // Could also try to extract from other page elements if needed
    return null;
  }

  /**
   * Get event ID from page
   */
  getEventId() {
    const eventContent = document.querySelector(".event-content");
    if (eventContent && eventContent.dataset.eventId) {
      return eventContent.dataset.eventId;
    }

    // Fallback: extract from URL
    const match = window.location.pathname.match(/\/event\/(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Format timestamp to "time ago" format
   */
  formatTimeAgo(timestamp) {
    // Simple time ago formatting - could be enhanced
    const now = new Date();
    const commentTime = new Date(timestamp);
    const diffMs = now - commentTime;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    // For older comments, show the date
    return commentTime.toLocaleDateString();
  }

  /**
   * Extract comment ID from button
   */
  extractCommentId(button) {
    // Try to get from data attribute first
    const commentItem = button.closest(".comment-item");
    if (commentItem && commentItem.dataset.commentId) {
      return commentItem.dataset.commentId;
    }

    // Fallback: extract from onclick attribute
    const onclick = button.getAttribute("onclick");
    if (onclick) {
      const match = onclick.match(/confirmDeleteComment\('(\d+),/);
      return match ? match[1] : null;
    }

    return null;
  }

  /**
   * Extract event ID from button
   */
  extractEventId(button) {
    // Try to get from form action
    const form = button.closest("form");
    if (form && form.getAttribute("action")) {
      const match = form.getAttribute("action").match(/\/event\/(\d+)\//);
      if (match) return match[1];
    }

    // Fallback: extract from onclick attribute
    const onclick = button.getAttribute("onclick");
    if (onclick) {
      const match = onclick.match(/,(\d+)'\)/);
      return match ? match[1] : null;
    }

    // Try to get from page URL or data attributes
    const eventContent = document.querySelector(".event-content");
    if (eventContent && eventContent.dataset.eventId) {
      return eventContent.dataset.eventId;
    }

    return null;
  }
}

// Initialize comment interactions when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  new CommentInteractions();
});

// Export for potential use in other modules
window.CommentInteractions = CommentInteractions;
