/**
 * Event Detail Bundle
 *
 * Comprehensive JavaScript bundle for event detail pages
 * Includes image gallery, image management, map functionality, and all interactive features
 * Also includes form validation, image preview, and file validation utilities
 *
 * Dependencies: event-operations.js (loaded separately)
 */

/* ===== FORM VALIDATION UTILITY ===== */

/**
 * Enhanced Form Validation Utility
 * Provides modern form validation with real-time feedback
 */
class EnhancedFormValidation {
  static initializeModernForm(form, options = {}) {
    if (!form) return;

    const defaults = {
      validateOnInput: true,
      validateOnBlur: true,
      showSuccessStates: true,
      customValidators: {},
    };

    const config = { ...defaults, ...options };

    // Add validation event listeners
    if (config.validateOnInput) {
      form.addEventListener("input", (e) => {
        this.validateField(e.target, config);
      });
    }

    if (config.validateOnBlur) {
      form.addEventListener(
        "blur",
        (e) => {
          this.validateField(e.target, config);
        },
        true
      );
    }

    // Prevent form submission if invalid
    form.addEventListener("submit", (e) => {
      if (!this.validateForm(form, config)) {
        e.preventDefault();
        e.stopPropagation();
      }
    });
  }

  static validateField(field, config) {
    if (!field.checkValidity) return true;

    const isValid = field.checkValidity();

    // Remove existing validation classes
    field.classList.remove("is-valid", "is-invalid");

    // Add appropriate class
    if (isValid && config.showSuccessStates && field.value.trim() !== "") {
      field.classList.add("is-valid");
    } else if (!isValid) {
      field.classList.add("is-invalid");
    }

    return isValid;
  }

  static validateForm(form, config) {
    let isValid = true;
    const fields = form.querySelectorAll("input, textarea, select");

    fields.forEach((field) => {
      if (!this.validateField(field, config)) {
        isValid = false;
      }
    });

    return isValid;
  }
}

// Make available globally
window.EnhancedFormValidation = EnhancedFormValidation;

/* ===== IMAGE PREVIEW UTILITY ===== */

/**
 * Image Preview Utility
 * Handles image preview functionality for file inputs
 */
class ImagePreview {
  static initialize(inputSelector, containerSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    const container = document.querySelector(containerSelector);

    if (!input || !container) return;

    const defaults = {
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ["image/jpeg", "image/jpg", "image/png", "image/gif"],
      maxFiles: 1,
      showFileName: true,
      showFileSize: true,
      onImageLoad: null,
      onError: null,
    };

    const config = { ...defaults, ...options };

    input.addEventListener("change", (e) => {
      this.handleFileSelection(e.target, container, config);
    });

    // Store instance for external access
    window.imagePreviewInstance = {
      clearAll: () => this.clearPreviews(container),
    };
  }

  static handleFileSelection(input, container, config) {
    const files = Array.from(input.files);

    if (files.length > config.maxFiles) {
      this.showError(`Maximum ${config.maxFiles} files allowed`, config);
      return;
    }

    this.clearPreviews(container);
    container.style.display = "block";

    files.forEach((file, index) => {
      if (!this.validateFile(file, config)) return;

      this.createPreview(file, container, config, index);
    });
  }

  static validateFile(file, config) {
    if (!config.allowedTypes.includes(file.type)) {
      this.showError(`File type ${file.type} not allowed`, config);
      return false;
    }

    if (file.size > config.maxFileSize) {
      this.showError(
        `File size exceeds ${config.maxFileSize / (1024 * 1024)}MB limit`,
        config
      );
      return false;
    }

    return true;
  }

  static createPreview(file, container, config, index) {
    const reader = new FileReader();

    reader.onload = (e) => {
      const previewElement = this.createPreviewElement(
        file,
        e.target.result,
        config,
        index
      );
      const grid = container.querySelector("#previewGrid") || container;
      grid.appendChild(previewElement);

      if (config.onImageLoad) {
        config.onImageLoad(file, e.target.result);
      }
    };

    reader.readAsDataURL(file);
  }

  static createPreviewElement(file, dataUrl, config, index) {
    const col = document.createElement("div");
    col.className = "col-6 col-md-4";

    const fileSize = config.showFileSize
      ? `(${(file.size / 1024).toFixed(1)}KB)`
      : "";
    const fileName = config.showFileName ? file.name : `Image ${index + 1}`;

    col.innerHTML = `
      <div class="preview-item">
        <img src="${dataUrl}" alt="Preview" class="preview-image">
        <div class="preview-info">
          <small class="preview-name">${fileName} ${fileSize}</small>
          <button type="button" class="btn btn-sm btn-outline-danger remove-preview">
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    `;

    // Add remove functionality
    col.querySelector(".remove-preview").addEventListener("click", () => {
      col.remove();
      if (container.querySelectorAll(".preview-item").length === 0) {
        container.style.display = "none";
      }
    });

    return col;
  }

  static clearPreviews(container) {
    const grid = container.querySelector("#previewGrid") || container;
    grid.innerHTML = "";
    container.style.display = "none";
  }

  static showError(message, config) {
    if (config.onError) {
      config.onError(message);
    } else {
      console.error("Image Preview Error:", message);
    }
  }
}

// Make available globally
window.ImagePreview = ImagePreview;

// Setup image preview function for external use
window.setupImagePreview = function (inputSelector, options = {}) {
  const input = document.querySelector(inputSelector);
  if (!input) return;

  const defaults = {
    maxFiles: 1,
    allowMultiple: false,
    containerSelector: "#imagePreviewContainer",
    gridSelector: "#previewGrid",
    feedbackSelector: "#imagesFeedback",
  };

  const config = { ...defaults, ...options };

  ImagePreview.initialize(inputSelector, config.containerSelector, {
    maxFiles: config.maxFiles,
    onError: (message) => {
      const feedback = document.querySelector(config.feedbackSelector);
      if (feedback) {
        feedback.innerHTML = `<div class="alert alert-danger">${message}</div>`;
      }
    },
  });
};

/* ===== FILE VALIDATION UTILITY ===== */

/**
 * File Validation Utility
 * Handles file upload validation
 */
class FileValidation {
  static validateFile(
    file,
    maxSize = 5 * 1024 * 1024,
    allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
  ) {
    if (!file) return { valid: false, error: "No file selected" };

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds ${maxSize / (1024 * 1024)}MB limit`,
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: `File type ${file.type} not allowed` };
    }

    return { valid: true };
  }

  static setupFileInput(inputSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    if (!input) return;

    const config = {
      maxSize: 5 * 1024 * 1024,
      allowedTypes: ["image/jpeg", "image/jpg", "image/png", "image/gif"],
      ...options,
    };

    input.addEventListener("change", (e) => {
      const files = Array.from(e.target.files);
      files.forEach((file) => {
        const validation = this.validateFile(
          file,
          config.maxSize,
          config.allowedTypes
        );
        if (!validation.valid && config.onError) {
          config.onError(validation.error);
        }
      });
    });
  }
}

window.FileValidation = FileValidation;

// Global variables for map and gallery
let mymap;
let locationMarker;
let redIcon;
let currentGalleryIndex = 0;
let galleryImages = [];
let galleryCleanupFunction = null;

/**
 * Image Gallery Functionality
 */
class EventImageGallery {
  constructor() {
    this.currentIndex = 0;
    this.images = [];
    this.modalElement = null;
    this.init();
  }

  init() {
    this.bindEventListeners();
  }

  bindEventListeners() {
    // Handle image gallery clicks
    document.addEventListener("click", (e) => {
      if (
        e.target.matches(".event-image-button") ||
        e.target.closest(".event-image-button")
      ) {
        e.preventDefault();
        const button = e.target.matches(".event-image-button")
          ? e.target
          : e.target.closest(".event-image-button");
        const eventId = button.getAttribute("event-id");
        this.openGallery(eventId);
      }
    });

    // Handle manage images button
    document.addEventListener("click", (e) => {
      if (e.target.matches("#manageImagesBtn")) {
        e.preventDefault();
        this.openImageManager();
      }
    });

    // Handle add first image button
    document.addEventListener("click", (e) => {
      if (e.target.matches("#addFirstImageBtn")) {
        e.preventDefault();
        this.openImageUploader();
      }
    });

    // Handle staff manage images button
    document.addEventListener("click", (e) => {
      if (e.target.matches("#staffManageImagesBtn")) {
        e.preventDefault();
        this.openStaffImageManager();
      }
    });
  }

  async openGallery(eventId) {
    try {
      const response = await fetch(`/event/image/${eventId}`);
      const html = await response.text();

      const modalResult = showModal("Event Images", html, {
        hideCloseButton: false,
        size: "large",
      });

      // Configure modal for image gallery
      const modalDialog = document.querySelector("#commonModal .modal-dialog");
      if (modalDialog) {
        modalDialog.classList.add("modal-lg", "modal-dialog-image-gallery");
      }

      // Initialize gallery functionality
      setTimeout(() => {
        this.initGallery(document.getElementById("commonModal"));
      }, 200);
    } catch (error) {
      console.error("Error loading images:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Error loading images. Please try again.",
          "danger"
        );
      }
    }
  }

  initGallery(modalElement) {
    const mainImage = document.getElementById("mainImage");
    const prevBtn = document.getElementById("prevImage");
    const nextBtn = document.getElementById("nextImage");
    const thumbnails = document.querySelectorAll(".thumbnail");
    const dots = document.querySelectorAll(".dot");

    if (!mainImage) {
      console.warn(
        "Gallery not properly initialized - missing main image element"
      );
      return;
    }

    this.currentIndex = 0;
    this.images = [];
    this.modalElement = modalElement;

    // Collect images
    thumbnails.forEach((thumb) => {
      this.images.push({
        src: thumb.querySelector("img").src,
        alt: thumb.querySelector("img").alt,
      });
    });

    if (this.images.length === 0) {
      console.warn("No images found in gallery");
      return;
    }

    // Bind event listeners
    thumbnails.forEach((thumb) => {
      thumb.addEventListener("click", () => {
        this.currentIndex = parseInt(thumb.getAttribute("data-index"));
        this.updateGallery();
      });
    });

    dots.forEach((dot) => {
      dot.addEventListener("click", () => {
        this.currentIndex = parseInt(dot.getAttribute("data-index"));
        this.updateGallery();
      });
    });

    if (prevBtn) {
      prevBtn.addEventListener("click", () => {
        this.currentIndex =
          (this.currentIndex - 1 + this.images.length) % this.images.length;
        this.updateGallery();
      });
    }

    if (nextBtn) {
      nextBtn.addEventListener("click", () => {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
        this.updateGallery();
      });
    }

    // Keyboard navigation
    const keydownHandler = (e) => {
      if (e.key === "ArrowLeft") {
        this.currentIndex =
          (this.currentIndex - 1 + this.images.length) % this.images.length;
        this.updateGallery();
      } else if (e.key === "ArrowRight") {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
        this.updateGallery();
      }
    };

    document.addEventListener("keydown", keydownHandler);

    // Cleanup function
    galleryCleanupFunction = () => {
      document.removeEventListener("keydown", keydownHandler);
      galleryCleanupFunction = null;
    };

    // Initial update
    this.updateGallery();

    // Setup cleanup on modal close
    if (modalElement) {
      modalElement.addEventListener("hidden.bs.modal", galleryCleanupFunction, {
        once: true,
      });
    }
  }

  updateGallery() {
    const mainImage = document.getElementById("mainImage");
    const thumbnails = document.querySelectorAll(".thumbnail");
    const dots = document.querySelectorAll(".dot");

    if (mainImage && this.images.length > 0) {
      mainImage.src = this.images[this.currentIndex].src;
      mainImage.alt = this.images[this.currentIndex].alt;

      thumbnails.forEach((thumb) => {
        const thumbIndex = parseInt(thumb.getAttribute("data-index"));
        thumb.classList.toggle("active", thumbIndex === this.currentIndex);
      });

      dots.forEach((dot) => {
        const dotIndex = parseInt(dot.getAttribute("data-index"));
        dot.classList.toggle("active", dotIndex === this.currentIndex);
      });
    }
  }

  openImageManager() {
    const eventContent = document.querySelector(".event-content");
    const eventId = parseInt(eventContent.getAttribute("data-event-id"));
    const isPremiumAccess =
      eventContent.getAttribute("data-premium-access") === "true";
    const uploadUrl = eventContent.getAttribute("data-upload-url");
    const plansUrl = eventContent.getAttribute("data-plans-url");

    showEventImageManager(eventId, isPremiumAccess, uploadUrl, plansUrl);
  }

  openImageUploader() {
    const eventContent = document.querySelector(".event-content");
    const eventId = parseInt(eventContent.getAttribute("data-event-id"));
    const isPremiumAccess =
      eventContent.getAttribute("data-premium-access") === "true";
    const uploadUrl = eventContent.getAttribute("data-upload-url");

    showEventImageUploader(eventId, isPremiumAccess, uploadUrl);
  }

  openStaffImageManager() {
    const eventContent = document.querySelector(".event-content");
    const eventId = parseInt(eventContent.getAttribute("data-event-id"));

    showStaffImageManager(eventId);
  }
}

/**
 * Map Functionality
 */
class EventMapManager {
  constructor() {
    this.map = null;
    this.marker = null;
    this.init();
  }

  init() {
    // Initialize map if map container exists
    const mapContainer = document.getElementById("map");
    console.log("EventMapManager init - Map container found:", !!mapContainer);
    if (mapContainer) {
      console.log("Map container data:", {
        lat: mapContainer.dataset.lat,
        lng: mapContainer.dataset.lng,
        locationName: mapContainer.dataset.locationName,
      });
      this.initializeMap(mapContainer);
    } else {
      console.log("No map container found with id 'map'");
    }
  }

  initializeMap(container) {
    const lat = parseFloat(container.dataset.lat);
    const lng = parseFloat(container.dataset.lng);
    const locationName = container.dataset.locationName;

    console.log("initializeMap called with:", { lat, lng, locationName });
    console.log("Leaflet available:", typeof L !== "undefined");

    if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
      console.warn("Map coordinates not available or invalid:", { lat, lng });
      container.innerHTML =
        '<p class="text-muted">Location coordinates not available</p>';
      return;
    }

    try {
      console.log("Attempting to initialize Leaflet map...");

      // Initialize Leaflet map
      this.map = L.map("map").setView([lat, lng], 13);
      console.log("Map initialized successfully");

      // Add tile layer
      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "© OpenStreetMap contributors",
      }).addTo(this.map);
      console.log("Tile layer added");

      // Add marker
      this.marker = L.marker([lat, lng]).addTo(this.map);
      console.log("Marker added");

      if (locationName) {
        this.marker.bindPopup(locationName).openPopup();
        console.log("Popup added with location name:", locationName);
      }

      console.log("Map initialization completed successfully");
    } catch (error) {
      console.error("Error initializing map:", error);
      container.innerHTML = '<p class="text-muted">Map could not be loaded</p>';
    }
  }
}

/**
 * Image Management Functions
 */

// Enhanced image management system
function showEventImageManager(eventId, isPremiumAccess, uploadUrl, plansUrl) {
  const ALLOWED_EXTENSIONS = ["png", "jpg", "jpeg"];
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB in bytes

  fetch(`/event/${eventId}/images`)
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        showImageManagementModal(
          eventId,
          data.images || [],
          isPremiumAccess,
          uploadUrl,
          plansUrl
        );
      } else {
        showModal(
          "Error",
          data.message || "Failed to load event images. Please try again.",
          {
            actionText: "OK",
            onAction: function () {
              return true;
            },
          }
        );
      }
    })
    .catch((error) => {
      console.error("Error fetching images:", error);
      showModal("Error", "Failed to load event images. Please try again.", {
        actionText: "OK",
        onAction: function () {
          return true;
        },
      });
    });
}

function showEventImageUploader(eventId, isPremiumAccess, uploadUrl) {
  const maxFiles = isPremiumAccess ? 10 : 1;

  if (!uploadUrl || uploadUrl.trim() === "") {
    uploadUrl = `/event/${eventId}/upload-images`;
  }

  // Check if user has existing images (for free users)
  let uploadDisabled = false;
  let premiumMessage = "";

  if (!isPremiumAccess) {
    // For free users, we need to check if they already have images
    // This will be handled by the backend validation, but we can show a warning
    premiumMessage = `
      <div class="alert alert-info" role="alert">
        <i class="bi bi-info-circle me-2"></i> Free users can upload only one image.
        If you already have an image, you'll need to delete it first or
        <a href="/account/profile?active_tab=subscription" class="alert-link">upgrade to Premium</a>.
      </div>`;
  }

  const formHtml = `
    <div class="manage-images-container" data-existing-count="0">
    ${premiumMessage}
    <form id="uploadEventImageForm" action="${uploadUrl}" method="post" enctype="multipart/form-data">
      <div class="mb-3">
        <label for="eventImages" class="form-label">
          <i class="bi bi-images"></i>
          Select images to upload
        </label>
        <input type="file" class="form-control" id="eventImages" name="images[]" accept=".png,.jpg,.jpeg,.gif" ${
          maxFiles > 1 ? "multiple" : ""
        } required
          onchange="validateAndPreviewUploadFiles(this)">
        <div class="invalid-feedback" id="imageValidationFeedback"></div>
        <div class="form-text">
          Maximum file size: 5MB per image.
          ${
            isPremiumAccess
              ? "Premium users can upload up to 10 images."
              : "Free users can upload only one image."
          }
        </div>
      </div>
      <div id="imagePreviewContainer" class="row g-2 mb-3">
      </div>
    </form>
    </div>
  `;

  showModal("Add Event Images", formHtml, {
    actionText: "Upload",
    onAction: function () {
      const form = document.getElementById("uploadEventImageForm");
      const fileInput = document.getElementById("eventImages");

      if (!fileInput.files.length) {
        fileInput.classList.add("is-invalid");
        document.getElementById("imageValidationFeedback").textContent =
          "Please select at least one file.";
        return false;
      }

      // Validate files using the same method as old implementation
      const validationResult = validateFiles(fileInput);
      if (!validationResult.valid) {
        fileInput.classList.add("is-invalid");
        document.getElementById("imageValidationFeedback").textContent =
          validationResult.message;
        return false;
      }

      // Submit form directly
      form.submit();
      return true;
    },
    size: "large",
  });

  // Image preview is handled by inline onchange attribute
}

// Staff Image Management Functions
function showStaffImageManager(eventId) {
  fetch(`/event/${eventId}/images`)
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        showStaffImageGallery(eventId, data.images || []);
      } else {
        showModal(
          "Error",
          data.message || "Failed to load event images. Please try again.",
          {
            actionText: "OK",
            onAction: function () {
              return true;
            },
          }
        );
      }
    })
    .catch((error) => {
      console.error("Error fetching images:", error);
      showModal("Error", "Failed to load event images. Please try again.", {
        actionText: "OK",
        onAction: function () {
          return true;
        },
      });
    });
}

function showStaffImageGallery(eventId, images) {
  let imagesHtml = "";

  if (images && images.length > 0) {
    imagesHtml = `
      <div class="staff-image-grid-container ${images.length > 6 ? 'scrollable' : ''} mb-4" style="${images.length > 6 ? 'max-height: 400px; overflow-y: auto; padding-right: 8px;' : ''}">
        <div class="row g-3">
          ${images
            .map(
              (image, index) =>
                `<div class="col-md-4 col-6" id="staff-image-item-${image.id}">
              <div class="card h-100 position-relative">
                <div class="position-relative">
                  <div class="form-check position-absolute top-0 start-0 m-2">
                    <input class="form-check-input staff-image-checkbox" type="checkbox" value="${image.id}"
                      id="staff-image-check-${image.id}">
                    <label class="form-check-label" for="staff-image-check-${image.id}"></label>
                  </div>
                  <img src="${image.url}" alt="Event image" class="card-img-top" style="height: 160px; object-fit: cover;">
                </div>
              </div>
            </div>`
            )
            .join("")}
        </div>
      </div>

      <!-- Selection Controls -->
      <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
        <div class="btn-group gap-2">
          <button type="button" id="selectAllStaffImages" class="btn btn-sm btn-outline-secondary rounded-pill">
            Select All
          </button>
          <button type="button" id="deselectAllStaffImages" class="btn btn-sm btn-outline-secondary rounded-pill">
            Deselect All
          </button>
        </div>
        <span class="text-muted" id="selectedStaffCount">0 selected</span>
      </div>

      <!-- Delete Section -->
      <div class="border rounded p-3 bg-white">
        <h6 class="text-danger mb-3">
          <i class="bi bi-trash me-2"></i>Delete Selected Images
        </h6>
        <form id="staffDeleteForm" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="staffDeleteReason" class="form-label">
              <strong>Reason for Deletion *</strong>
            </label>
            <textarea
              class="form-control"
              id="staffDeleteReason"
              name="edit_reason"
              rows="3"
              required
              maxlength="500"></textarea>
            <div class="invalid-feedback">
              Please provide a reason for deleting the selected images.
            </div>
            <div class="form-text">
              <i class="bi bi-info-circle me-1"></i>
              This reason will be logged for each image deletion and sent to the event owner.
            </div>
          </div>
          <button type="button" id="executeStaffDelete" class="btn btn-danger" disabled>
            <i class="bi bi-trash me-1"></i>Delete Selected Images
          </button>
        </form>
      </div>
    `;
  } else {
    imagesHtml = `
      <div class="text-center p-4">
        <i class="bi bi-images fs-1 text-secondary"></i>
        <p class="text-muted mt-2">No images found for this event</p>
      </div>
    `;
  }

  const formHtml = `
    <div class="staff-image-manager">
      <div class="alert alert-warning d-flex align-items-center mb-3">
        <i class="bi bi-shield-exclamation me-2"></i>
        <div>
          <small>Select images to delete and provide a reason. </small>
        </div>
      </div>
      ${imagesHtml}
    </div>
  `;

  showModal("Staff Image Management", formHtml, {
    size: "large",
  });

  // Setup staff image selection functionality
  setupStaffImageSelect(eventId);
}

function setupStaffImageSelect(eventId) {
  // Get references to buttons and checkboxes
  const selectAllBtn = document.getElementById("selectAllStaffImages");
  const deselectAllBtn = document.getElementById("deselectAllStaffImages");
  const executeDeleteBtn = document.getElementById("executeStaffDelete");
  const checkboxes = document.querySelectorAll(".staff-image-checkbox");
  const selectedCountEl = document.getElementById("selectedStaffCount");
  const deleteReasonTextarea = document.getElementById("staffDeleteReason");

  if (!checkboxes.length) return;

  // Function to update the selected count and delete button state
  function updateSelectionState() {
    const selectedCount = document.querySelectorAll(
      ".staff-image-checkbox:checked"
    ).length;
    selectedCountEl.textContent = `${selectedCount} selected`;
    executeDeleteBtn.disabled = selectedCount === 0;

    // Update button text based on selection
    if (selectedCount === 0) {
      executeDeleteBtn.innerHTML =
        '<i class="bi bi-trash me-1"></i>Delete Selected Images';
    } else {
      executeDeleteBtn.innerHTML = `<i class="bi bi-trash me-1"></i>Delete ${selectedCount} Image${
        selectedCount > 1 ? "s" : ""
      }`;
    }
  }

  // Add change event listeners to all checkboxes
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener("change", updateSelectionState);

    // Make the entire card clickable to toggle checkbox
    const card = checkbox.closest(".card");
    if (card) {
      card.addEventListener("click", function (e) {
        // Don't toggle if the checkbox itself was clicked (it will toggle itself)
        if (e.target !== checkbox) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event("change"));
        }
      });
    }
  });

  // Select all images
  selectAllBtn?.addEventListener("click", function () {
    checkboxes.forEach((checkbox) => {
      checkbox.checked = true;
    });
    updateSelectionState();
  });

  // Deselect all images
  deselectAllBtn?.addEventListener("click", function () {
    checkboxes.forEach((checkbox) => {
      checkbox.checked = false;
    });
    updateSelectionState();
  });

  // Execute delete with validation
  executeDeleteBtn?.addEventListener("click", async function () {
    const selectedImageIds = Array.from(
      document.querySelectorAll(".staff-image-checkbox:checked")
    ).map((checkbox) => checkbox.value);
    const deleteReason = deleteReasonTextarea?.value?.trim();

    if (selectedImageIds.length === 0) {
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Please select at least one image to delete.",
          "warning"
        );
      }
      return;
    }

    if (!deleteReason) {
      deleteReasonTextarea?.focus();
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Please provide a reason for deletion.",
          "warning"
        );
      }
      return;
    }

    try {
      // Create FormData for the request (matching the route expectation)
      const formData = new FormData();
      formData.append("image_ids", selectedImageIds.join(","));
      formData.append("edit_reason", deleteReason);

      const response = await fetch(
        `/event/${eventId}/images/staff-delete-batch`,
        {
          method: "POST",
          headers: {
            "X-Requested-With": "XMLHttpRequest",
          },
          body: formData,
        }
      );

      const result = await response.json();

      if (result.success) {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "success");
        }

        // Close modal and refresh page
        if (typeof window.closeModal === "function") {
          window.closeModal();
        }

        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error deleting images:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Failed to delete images. Please try again.",
          "danger"
        );
      }
    }
  });
}

// Image Management Modal Function
function showImageManagementModal(
  eventId,
  images,
  isPremiumAccess,
  uploadUrl,
  plansUrl
) {
  let imagesHtml = "";

  if (images && images.length > 0) {
    imagesHtml = `
      <div class="existing-images-section mb-4">
      <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-3">Current Images (${images.length}/10)</h6>
         <button type="button" id="deleteSelectedImages" class="btn btn-sm btn-danger" disabled>
            <i class="bi bi-trash me-1"></i>Delete Selected
          </button>
        </div>
        <div class="d-flex justify-content-between align-items-center mb-3">
          <div class="btn-group gap-2">
            <button type="button" id="selectAllImages" class="btn btn-sm btn-outline-secondary rounded-pill">
              Select All
            </button>
            <button type="button" id="deselectAllImages" class="btn btn-sm btn-outline-secondary rounded-pill">
              Deselect All
            </button>
          </div>
         
          <span class="text-muted" id="selectedCount">0 selected</span>
        </div>
        <div class="image-grid-container ${images.length > 6 ? 'scrollable' : ''}" style="${images.length > 6 ? 'max-height: 400px; overflow-y: auto; padding-right: 8px;' : ''}">
          <div class="row g-3">
            ${images
              .map(
                (image, index) =>
                  `<div class="col-md-4 col-6" id="image-item-${image.id}">
                <div class="card h-100 position-relative">
                  <div class="position-relative">
                    <div class="form-check position-absolute top-0 start-0 m-2">
                      <input class="form-check-input image-checkbox" type="checkbox" value="${image.id}"
                        id="image-check-${image.id}">
                      <label class="form-check-label" for="image-check-${image.id}"></label>
                    </div>
                    <img src="${image.url}" alt="Event image" class="card-img-top" style="height: 160px; object-fit: cover;">
                  </div>
                </div>
              </div>`
              )
              .join("")}
          </div>
        </div>
      </div>
    `;
  }

  const maxFiles = isPremiumAccess ? 10 : 1;
  const currentImageCount = images ? images.length : 0;

  // Check if free user has existing images (premium expired scenario)
  const hasExistingImages = images && images.length > 0;
  const uploadDisabled = !isPremiumAccess && hasExistingImages;
  
  // For premium users, check if they're already at the 10-image limit
  const atImageLimit = isPremiumAccess && currentImageCount >= 10;

  // Premium message for free users with existing images
  let premiumMessage = "";
  if (uploadDisabled) {
    premiumMessage = `<div class="alert alert-info" role="alert">
      <i class="bi bi-info-circle me-2"></i> Free users can upload only one image.
      <a href="/account/profile?active_tab=subscription" class="alert-link">Upgrade to Premium</a> to add more images.
      <br><small class="text-muted">You can delete existing images to upload a new one.</small>
    </div>`;
  } else if (atImageLimit) {
    premiumMessage = `<div class="alert alert-warning" role="alert">
      <i class="bi bi-exclamation-triangle me-2"></i> You've reached the maximum limit of 10 images for this event.
      <br><small class="text-muted">Please delete some existing images before uploading new ones.</small>
    </div>`;
  }

  const formHtml = `
    <div class="manage-images-container" data-existing-count="${currentImageCount}">
      ${imagesHtml}

      <div class="upload-section">
        <h6 class="mb-3">Upload New Images</h6>
        ${premiumMessage}
        <form id="uploadEventImageForm" action="${uploadUrl}" method="post" enctype="multipart/form-data">
          <div class="mb-3">
            <input type="file" class="form-control" id="eventImages" name="images[]" accept=".png,.jpg,.jpeg,.gif"
              ${maxFiles > 1 ? "multiple" : ""} ${
    uploadDisabled || atImageLimit ? "disabled" : ""
  }
              onchange="validateAndPreviewUploadFiles(this)">
            <div class="form-text">
              Maximum file size: 5MB per image. Allowed formats: PNG, JPG, JPEG, GIF.
              ${isPremiumAccess && !atImageLimit ? ` Maximum ${10 - currentImageCount} more image${(10 - currentImageCount) === 1 ? '' : 's'} can be added.` : ""}
              ${
                uploadDisabled
                  ? " Upload is disabled because free users can only have one image."
                  : ""
              }
              ${
                atImageLimit
                  ? " Upload is disabled because you've reached the 10-image limit."
                  : ""
              }
            </div>
            <div class="invalid-feedback" id="imageValidationFeedback">
              Please select at least one valid image to upload.
            </div>
          </div>
          <div id="imagePreviewContainer" class="row g-2 mb-3">
          </div>
        </form>
      </div>
    </div>
  `;

  showModal("Manage Event Images", formHtml, {
    actionText: uploadDisabled || atImageLimit ? "Upload Disabled" : "Upload New Images",
    onAction: function () {
      // If upload is disabled, show appropriate message
      if (uploadDisabled) {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(
            "Free users can upload only one image. Delete existing images or upgrade to Premium to add more.",
            "warning"
          );
        }
        return false;
      }
      
      if (atImageLimit) {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(
            "You've reached the maximum limit of 10 images for this event. Please delete some existing images first.",
            "warning"
          );
        }
        return false;
      }

      const form = document.getElementById("uploadEventImageForm");
      const fileInput = document.getElementById("eventImages");

      if (!fileInput.files.length) {
        fileInput.classList.add("is-invalid");
        document.getElementById("imageValidationFeedback").textContent =
          "Please select at least one image to upload.";
        return false;
      }

      // Validate files using the same method as old implementation
      const validationResult = validateFiles(fileInput);
      if (!validationResult.valid) {
        fileInput.classList.add("is-invalid");
        document.getElementById("imageValidationFeedback").textContent =
          validationResult.message;
        return false;
      }

      // Submit form directly
      form.submit();
      return true;
    },
    size: "large",
  });

  // Image preview is handled by inline onchange attribute

  // Setup bulk delete functionality if images exist
  setTimeout(() => {
    if (images && images.length > 0) {
      setupMultiImageSelect();
    }
  }, 200);
}

// Helper functions for image management

// Bulk delete functionality from old implementation
function setupMultiImageSelect() {
  // Get references to buttons and checkboxes
  const selectAllBtn = document.getElementById("selectAllImages");
  const deselectAllBtn = document.getElementById("deselectAllImages");
  const deleteSelectedBtn = document.getElementById("deleteSelectedImages");
  const checkboxes = document.querySelectorAll(".image-checkbox");
  const selectedCountEl = document.getElementById("selectedCount");

  if (!checkboxes.length) return;

  // Function to update the selected count and delete button state
  function updateSelectionState() {
    const selectedCount = document.querySelectorAll(
      ".image-checkbox:checked"
    ).length;
    selectedCountEl.textContent = `${selectedCount} selected`;
    deleteSelectedBtn.disabled = selectedCount === 0;
  }

  // Add change event listeners to all checkboxes
  checkboxes.forEach((checkbox) => {
    checkbox.addEventListener("change", updateSelectionState);

    // Make the entire card clickable to toggle checkbox
    const card = checkbox.closest(".card");
    if (card) {
      card.addEventListener("click", function (e) {
        // Don't toggle if the checkbox itself was clicked (it will toggle itself)
        if (e.target !== checkbox) {
          checkbox.checked = !checkbox.checked;
          checkbox.dispatchEvent(new Event("change"));
        }
      });
    }
  });

  // Select all images
  selectAllBtn?.addEventListener("click", function () {
    checkboxes.forEach((checkbox) => {
      checkbox.checked = true;
    });
    updateSelectionState();
  });

  // Deselect all images
  deselectAllBtn?.addEventListener("click", function () {
    checkboxes.forEach((checkbox) => {
      checkbox.checked = false;
    });
    updateSelectionState();
  });

  // Delete selected images
  deleteSelectedBtn?.addEventListener("click", function () {
    const selectedImageIds = Array.from(
      document.querySelectorAll(".image-checkbox:checked")
    ).map((checkbox) => checkbox.value);

    if (selectedImageIds.length === 0) return;

    deleteMultipleImages(selectedImageIds);
  });
}

// Function to delete multiple images
function deleteMultipleImages(imageIds) {
  if (!imageIds || imageIds.length === 0) return;

  const numImages = imageIds.length;
  const message = `Are you sure you want to delete ${numImages} image${
    numImages > 1 ? "s" : ""
  }? This action cannot be undone.<br><br>
<div class="alert alert-info">
  <small><i class="bi bi-info-circle me-1"></i> Each image will be deleted individually.
    This may take a moment for multiple images.</small>
</div>`;

  showModal("Delete Images", message, {
    actionText: "Delete",
    onAction: function () {
      console.log(`Initiating deletion of ${numImages} images`);
      performImageDelete(imageIds);
      return true;
    },
  });
}

// Function to handle the actual deletion API call
function performImageDelete(imageIds) {
  if (!imageIds || imageIds.length === 0) {
    console.error("No image IDs provided for deletion");
    return;
  }

  console.log("Attempting to delete images with IDs:", imageIds);

  // Show loading state for all images
  imageIds.forEach((id) => {
    const imageItem = document.getElementById(`image-item-${id}`);
    if (imageItem) {
      imageItem.classList.add("opacity-50");
    }
  });

  // Don't show progress flash message - it interferes with modal

  // Track progress
  let successCount = 0;
  let failCount = 0;
  let completedRequests = 0;
  let errors = [];

  // Process each image deletion sequentially
  const deleteNextImage = (index) => {
    if (index >= imageIds.length) {
      // All images have been processed
      console.log(
        `Completed all image deletions. Success: ${successCount}, Failed: ${failCount}`
      );

      // Simple approach: redirect to event detail page with flash message
      if (failCount === 0) {
        // All deletions were successful
        if (typeof window.storeFlashMessage === "function") {
          window.storeFlashMessage(
            `${successCount} image${
              successCount > 1 ? "s" : ""
            } deleted successfully!`,
            "success"
          );
        }
      } else if (successCount === 0) {
        // All deletions failed
        if (typeof window.storeFlashMessage === "function") {
          window.storeFlashMessage(
            `Failed to delete images. Please try again.`,
            "danger"
          );
        }
      } else {
        // Mixed results
        if (typeof window.storeFlashMessage === "function") {
          window.storeFlashMessage(
            `Deleted ${successCount} images. Failed to delete ${failCount} images.`,
            "warning"
          );
        }
      }

      // Redirect to event detail page
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      return;
    }

    const imageId = imageIds[index];
    // Get event ID from the container
    const container = document.querySelector(".event-content");
    const eventId = container ? container.getAttribute("data-event-id") : null;

    if (!eventId) {
      console.error("Event ID not found");
      failCount++;
      errors.push(`Event ID not found for image ${imageId}`);
      deleteNextImage(index + 1);
      return;
    }

    const url = `/event/${eventId}/image/${imageId}/delete`;
    console.log(
      `Deleting image ${index + 1}/${imageIds.length}, ID: ${imageId}`
    );

    fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "same-origin",
    })
      .then((response) => {
        if (!response.ok) {
          return response.text().then((text) => {
            throw new Error(
              `Failed to delete image ${imageId}: ${response.status} ${response.statusText}`
            );
          });
        }
        return response.json();
      })
      .then((data) => {
        completedRequests++;

        if (data.success) {
          successCount++;
          // Remove the image from the DOM
          const imageElement = document.getElementById(`image-item-${imageId}`);
          if (imageElement) {
            imageElement.remove();
          }
        } else {
          failCount++;
          errors.push(`Image ${imageId}: ${data.message || "Unknown error"}`);

          // Remove loading state for this image
          const imageItem = document.getElementById(`image-item-${imageId}`);
          if (imageItem) {
            imageItem.classList.remove("opacity-50");
          }
        }

        // Process the next image
        deleteNextImage(index + 1);
      })
      .catch((error) => {
        console.error(`Error deleting image ${imageId}:`, error);
        completedRequests++;
        failCount++;
        errors.push(error.message);

        // Remove loading state for this image
        const imageItem = document.getElementById(`image-item-${imageId}`);
        if (imageItem) {
          imageItem.classList.remove("opacity-50");
        }

        // Process the next image
        deleteNextImage(index + 1);
      });
  };

  // Start the deletion process with the first image
  deleteNextImage(0);
}

// File validation and preview functions from old implementation
function validateAndPreviewUploadFiles(input) {
  const ALLOWED_EXTENSIONS = ["png", "jpg", "jpeg"];
  const MAX_FILE_SIZE = 5 * 1024 * 1024;

  const container = document.querySelector(".event-content");
  const isPremiumAccess = container
    ? container.getAttribute("data-premium-access") === "true"
    : false;

  const previewContainer = document.getElementById("imagePreviewContainer");
  const feedback = document.getElementById("imageValidationFeedback");

  if (!previewContainer) return;

  previewContainer.innerHTML = "";
  input.classList.remove("is-invalid");
  
  // Reset scrolling styles when clearing previews
  updatePreviewContainerScrolling(previewContainer, 0);

  if (!input.files || input.files.length === 0) {
    return;
  }

  const validationResult = validateFiles(input);
  if (!validationResult.valid) {
    input.classList.add("is-invalid");
    feedback.textContent = validationResult.message;
    return;
  }

  const maxFiles = isPremiumAccess ? 10 : 1;
  
  // Get existing image count from the modal container
  const modalContainer = document.querySelector(".manage-images-container");
  const existingCount = modalContainer ? parseInt(modalContainer.getAttribute("data-existing-count")) || 0 : 0;
  
  // Calculate how many files we can actually process considering the total limit
  const remainingSlots = isPremiumAccess ? Math.max(0, 10 - existingCount) : 1;
  const filesToProcess = Math.min(input.files.length, remainingSlots);

  // Process files and track when all are loaded
  let loadedCount = 0;
  
  for (let i = 0; i < filesToProcess; i++) {
    const file = input.files[i];
    const reader = new FileReader();
    reader.onload = function (e) {
      const preview = document.createElement("div");
      preview.className = "col-md-4 col-6";
      preview.innerHTML = `
        <div class="card h-100">
          <div class="ratio ratio-4x3">
            <img src="${
              e.target.result
            }" class="card-img-top" alt="Preview" style="object-fit: cover;">
          </div>
          <div class="card-body p-2">
            <p class="small text-truncate mb-0">${file.name}</p>
            <small class="text-muted">${(file.size / (1024 * 1024)).toFixed(
              2
            )} MB</small>
          </div>
        </div>
      `;
      previewContainer.appendChild(preview);
      
      // Increment loaded count and check if all files are processed
      loadedCount++;
      if (loadedCount === filesToProcess) {
        // All files are loaded, now update container style if needed
        updatePreviewContainerScrolling(previewContainer, filesToProcess);
      }
    };

    reader.readAsDataURL(file);
  }
}

// Helper function to update preview container scrolling
function updatePreviewContainerScrolling(container, fileCount) {
  if (!container) return;
  
  if (fileCount > 6) {
    // Add scrolling if more than 6 images (2 rows)
    container.style.maxHeight = '360px';
    container.style.overflowY = 'auto';
    container.style.paddingRight = '8px';
    container.classList.add('scrollable-preview');
  } else {
    // Remove scrolling if 6 or fewer images
    container.style.maxHeight = '';
    container.style.overflowY = '';
    container.style.paddingRight = '';
    container.classList.remove('scrollable-preview');
  }
}

function validateFiles(input) {
  // Get the isPremiumAccess value from the data attribute since it's not in scope
  const container = document.querySelector(".event-content");
  const isPremiumAccess = container
    ? container.getAttribute("data-premium-access") === "true"
    : false;
  const maxFiles = isPremiumAccess ? 10 : 1;

  // Get existing image count from the modal container
  const modalContainer = document.querySelector(".manage-images-container");
  const existingCount = modalContainer ? parseInt(modalContainer.getAttribute("data-existing-count")) || 0 : 0;

  // Check if any files were selected
  if (!input.files || input.files.length === 0) {
    return { valid: false, message: "Please select at least one file." };
  }

  // Check premium access constraint
  if (!isPremiumAccess && input.files.length > 1) {
    return { valid: false, message: "Free users can upload only one image." };
  }

  // Additional check: if free user and input is disabled (has existing images)
  if (!isPremiumAccess && input.disabled) {
    return {
      valid: false,
      message:
        "Free users can upload only one image. Delete existing images first.",
    };
  }

  // Check total image limit for premium users
  if (isPremiumAccess) {
    const totalAfterUpload = existingCount + input.files.length;
    if (totalAfterUpload > 10) {
      const remainingSlots = 10 - existingCount;
      if (remainingSlots <= 0) {
        return {
          valid: false,
          message: "You've reached the maximum limit of 10 images for this event. Please delete some existing images first.",
        };
      } else {
        return {
          valid: false,
          message: `You can only upload ${remainingSlots} more image${remainingSlots === 1 ? '' : 's'}. You selected ${input.files.length} files.`,
        };
      }
    }
  }

  // Use centralized file validation if available
  if (window.FileValidation && window.FileValidation.validateFiles) {
    const validation = window.FileValidation.validateFiles(
      input.files,
      maxFiles
    );
    return validation;
  } else {
    // Fallback validation if FileValidation is not loaded
    console.warn(
      "FileValidation utility not available, using fallback validation"
    );

    // Fallback constants
    const ALLOWED_EXTENSIONS = ["png", "jpg", "jpeg", "gif"];
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

    // Check each file
    for (let i = 0; i < input.files.length; i++) {
      const file = input.files[i];

      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        return {
          valid: false,
          message: `File "${file.name}" exceeds the maximum size of 5MB.`,
        };
      }

      // Check file extension
      const fileExt = file.name.split(".").pop().toLowerCase();
      if (!ALLOWED_EXTENSIONS.includes(fileExt)) {
        return {
          valid: false,
          message: `File "${file.name}" has an invalid extension. Allowed: PNG, JPG, JPEG, GIF.`,
        };
      }
    }

    return { valid: true, message: "" };
  }
}

// Note: Event like functionality is handled by event-operations.js
// Removed duplicate like handler to prevent conflicts

// Open edit event modal function
async function openEditEventModal(eventId) {
  try {
    const response = await fetch(`/event/${eventId}/edit`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const formHtml = await response.text();

    if (typeof showModal === "function") {
      showModal("Edit Event", formHtml, {
        actionText: "Save",
        onAction: async () => {
          return await submitEditEventForm(eventId);
        },
        size: "large",
      });

      // Initialize form components after modal is shown
      setTimeout(() => {
        initializeEditEventForm();
      }, 200);
    } else {
      console.error("showModal function not available");
    }
  } catch (error) {
    console.error("Error opening edit modal:", error);
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage("Failed to load edit form", "danger");
    }
  }
}

// Submit edit event form
async function submitEditEventForm(eventId) {
  const form = document.getElementById("editEventForm");
  if (!form) {
    console.error("Edit form not found");
    return false;
  }

  form.classList.add("was-validated");

  if (!form.checkValidity()) {
    const firstInvalidField = form.querySelector(":invalid");
    if (firstInvalidField) {
      firstInvalidField.focus();
    }
    return false;
  }

  try {
    const formData = new FormData(form);

    const response = await fetch(`/event/${eventId}/edit`, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (response.ok) {
      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        const data = await response.json();

        if (data.success) {
          // Handle "no changes" case differently - don't reload page
          if (data.no_changes) {
            console.log(
              "🔍 NO CHANGES DETECTED in event-detail-bundle.js - showing info message without reload"
            );
            if (typeof window.showFlashMessage === "function") {
              window.showFlashMessage(data.message, "info");
            }
            return true; // Don't reload the page
          }

          // Show success message and reload for actual changes
          if (typeof window.showFlashMessage === "function") {
            window.showFlashMessage(
              data.message || "Event updated successfully!",
              "success"
            );
          }

          // Reload page to show updated content
          setTimeout(() => {
            window.location.reload();
          }, 1000);

          return true;
        } else {
          if (typeof window.showFlashMessage === "function") {
            window.showFlashMessage(
              data.message || "Failed to update event. Please try again.",
              "danger"
            );
          }
          return false;
        }
      } else {
        // Fallback for non-JSON responses
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage("Event updated successfully!", "success");
        }

        // Reload page to show updated content
        setTimeout(() => {
          window.location.reload();
        }, 1000);

        return true;
      }
    } else {
      const errorText = await response.text();
      console.error("Form submission failed:", errorText);

      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Failed to update event. Please try again.",
          "danger"
        );
      }
      return false;
    }
  } catch (error) {
    console.error("Error submitting form:", error);
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(
        "Failed to update event. Please try again.",
        "danger"
      );
    }
    return false;
  }
}

// Initialize edit event form
function initializeEditEventForm() {
  // Initialize location search functionality if available
  if (typeof initializeEditModalLocationSearch === "function") {
    initializeEditModalLocationSearch();
  }

  // Initialize map if coordinates exist
  const latInput = document.getElementById("latitude");
  const lngInput = document.getElementById("longitude");

  if (latInput && lngInput && latInput.value && lngInput.value) {
    const lat = parseFloat(latInput.value);
    const lng = parseFloat(lngInput.value);

    if (!isNaN(lat) && !isNaN(lng)) {
      // Initialize map with existing coordinates
      if (typeof window.initializeEditEventMap === "function") {
        window.initializeEditEventMap(lat, lng);
      }
    }
  }
}

// Edit event handler
document.addEventListener("click", function (e) {
  const editBtn = e.target.closest(".editEventBtn");
  if (editBtn) {
    e.preventDefault();
    const eventId = editBtn.getAttribute("data-event-id");
    openEditEventModal(eventId);
  }
});

// Open event edit history modal function
function openEventEditHistoryModal(eventId, eventTitle) {
  if (!eventId) {
    console.error("Event ID is required");
    return;
  }

  console.log(`Opening edit history modal for event ${eventId}: ${eventTitle}`);

  // Check if showModal function is available
  if (typeof showModal !== "function") {
    console.error(
      "showModal function not available! Cannot open edit history modal."
    );
    return;
  }

  // Show loading state using common modal
  const loadingContent = `
    <div class="edit-history-loading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p>Loading edit history...</p>
    </div>
  `;

  showModal(`Edit History - ${eventTitle}`, loadingContent);

  // Fetch edit history modal content (server-side rendered template)
  fetch(`/edit-history/modal/event/${eventId}`)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.text();
    })
    .then((html) => {
      // Update modal with the server-rendered content using common modal
      showModal(`Edit History - ${eventTitle}`, html);
    })
    .catch((error) => {
      console.error("Error fetching edit history modal:", error);

      // Show error using common modal
      const errorContent = `
        <div class="edit-history-empty">
          <i class="bi bi-exclamation-triangle"></i>
          <h4>Error Loading History</h4>
          <p>Failed to load edit history. Please try again.</p>
        </div>
      `;

      showModal(`Edit History - ${eventTitle}`, errorContent);
    });
}

// Edit history handler
document.addEventListener("click", function (e) {
  const historyBtn = e.target.closest('[data-action="view-edit-history"]');
  if (historyBtn) {
    e.preventDefault();
    const eventId = historyBtn.dataset.eventId;
    const eventTitle = historyBtn.dataset.eventTitle;
    openEventEditHistoryModal(eventId, eventTitle);
  }
});

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize image gallery
  window.eventImageGallery = new EventImageGallery();

  // Initialize map
  window.eventMapManager = new EventMapManager();

  console.log("Event detail bundle initialized");
});

/**
 * Event Location Modal Functionality
 */

/**
 * Open event location modal
 * @param {string|null} latitude - Latitude coordinate
 * @param {string|null} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 * @param {string|null} locationId - Location ID for follow functionality
 */
function openEventLocationModal(latitude, longitude, locationName, locationId) {
  if (!latitude || !longitude) {
    console.warn("Invalid coordinates for event location modal");
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage("Location coordinates not available", "info");
    }
    return;
  }

  const modal = new bootstrap.Modal(
    document.getElementById("eventLocationModal")
  );
  document.getElementById("eventLocationModalLabel").textContent =
    locationName || "Event Location";

  // Store location ID for follow functionality
  const modalElement = document.getElementById("eventLocationModal");
  modalElement.dataset.locationId = locationId || "";

  // Update follow button if location ID is available
  if (locationId) {
    updateLocationFollowButton(locationId);
  }

  modal.show();

  // Initialize event location map after modal is shown
  setTimeout(() => {
    initializeFullEventMap(latitude, longitude, locationName);
  }, 300);
}

/**
 * Initialize full event map in modal
 * @param {string} latitude - Latitude coordinate
 * @param {string} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 */
function initializeFullEventMap(latitude, longitude, locationName) {
  try {
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng)) {
      console.error("Invalid coordinates for event map");
      return;
    }

    // Clear existing map if it exists
    if (window.fullEventMapInstance) {
      window.fullEventMapInstance.remove();
    }

    // Create new map
    window.fullEventMapInstance = L.map("fullEventMap").setView([lat, lng], 15);

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(window.fullEventMapInstance);

    // Add marker with red icon
    const redIcon = L.divIcon({
      className: "custom-div-icon",
      html: '<div style="background-color: #dc3545; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
      iconSize: [20, 20],
      iconAnchor: [10, 10],
    });

    L.marker([lat, lng], { icon: redIcon })
      .addTo(window.fullEventMapInstance)
      .bindPopup(
        `<div class="location-popup"><h6>${
          locationName || "Location"
        }</h6></div>`
      )
      .openPopup();
  } catch (error) {
    console.error("Error initializing full event map:", error);
  }
}

/**
 * Update location follow button based on current follow status
 * @param {string} locationId - Location ID to check follow status for
 */
async function updateLocationFollowButton(locationId) {
  try {
    const response = await fetch(`/location/api/${locationId}/follow-status`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (response.ok) {
      const data = await response.json();
      const followButton = document.querySelector(
        '#eventLocationModal [data-action="toggle-location-follow"]'
      );

      if (followButton) {
        const icon = followButton.querySelector("i");
        const text = followButton.querySelector(".btn-text");

        if (data.is_following) {
          followButton.classList.remove("btn-outline-primary");
          followButton.classList.add("btn-primary");
          if (icon) {
            icon.className = "bi bi-heart-fill me-1";
          }
          if (text) {
            text.textContent = "Following";
          }
        } else {
          followButton.classList.remove("btn-primary");
          followButton.classList.add("btn-outline-primary");
          if (icon) {
            icon.className = "bi bi-heart me-1";
          }
          if (text) {
            text.textContent = "Follow";
          }
        }

        // Update the location ID data attribute
        followButton.dataset.locationId = locationId;
      }
    } else {
      console.error("Failed to get location follow status");

      // Show error message to user if the API call fails
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Unable to check follow status. Please try again.",
          "warning"
        );
      }
    }
  } catch (error) {
    console.error("Error updating location follow button:", error);

    // Show network error message to user
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(
        "Network error while checking follow status.",
        "warning"
      );
    }
  }
}

/**
 * Toggle follow status for a location
 * @param {string} locationId - Location ID to toggle follow for
 * @param {HTMLElement} button - Follow button element
 */
async function toggleLocationFollow(locationId, button) {
  try {
    button.disabled = true;

    const response = await fetch(`/location/api/${locationId}/toggle-follow`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    const data = await response.json();

    if (data.success) {
      // Update button text and icon
      const icon = button.querySelector("i");
      const text = button.querySelector(".btn-text");

      if (data.is_following) {
        button.classList.remove("btn-outline-primary");
        button.classList.add("btn-primary");
        if (icon) {
          icon.className = "bi bi-heart-fill me-1";
        }
        if (text) {
          text.textContent = "Following";
        }
      } else {
        button.classList.remove("btn-primary");
        button.classList.add("btn-outline-primary");
        if (icon) {
          icon.className = "bi bi-heart me-1";
        }
        if (text) {
          text.textContent = "Follow";
        }
      }

      // Show success message to user
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(data.message, "success");
      }
    } else {
      console.error("Failed to toggle location follow:", data.message);

      // Show error message to user
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(data.message, "danger");
      }
    }
  } catch (error) {
    console.error("Error toggling location follow:", error);

    // Show network error message to user
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(
        "Network error. Please check your connection and try again.",
        "danger"
      );
    }
  } finally {
    button.disabled = false;
  }
}

/**
 * Event Listeners for Location Modal Functionality
 */

// Add event listener for location modal button
document.addEventListener("click", (e) => {
  const locationModalButton = e.target.closest(
    '[data-action="open-location-modal"]'
  );
  if (locationModalButton) {
    e.preventDefault();
    e.stopPropagation();
    const latitude = locationModalButton.dataset.latitude;
    const longitude = locationModalButton.dataset.longitude;
    const locationName = locationModalButton.dataset.locationName;
    const locationId = locationModalButton.dataset.locationId;

    openEventLocationModal(latitude, longitude, locationName, locationId);
  }
});

// Add event listener for location follow functionality
document.addEventListener("click", (e) => {
  const locationFollowButton = e.target.closest(
    '[data-action="toggle-location-follow"]'
  );
  if (locationFollowButton) {
    e.preventDefault();
    e.stopPropagation();
    const locationId = locationFollowButton.dataset.locationId;
    if (locationId) {
      toggleLocationFollow(locationId, locationFollowButton);
    }
  }
});
