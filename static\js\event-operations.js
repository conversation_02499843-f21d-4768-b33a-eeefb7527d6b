/**
 * Event Operations Utility
 *
 * Provides centralized functions for event-related operations
 * Moves business logic to backend API calls
 *
 * Dependencies: flash-messages.js, modal.html
 *
 */

class EventOperations {
  constructor() {
    this.baseUrl = "/event/api";
    this.init();
  }

  init() {
    this.bindEventListeners();
  }

  /**
   * Bind event listeners for event operations
   */
  bindEventListeners() {
    // Like buttons
    document.addEventListener("click", (e) => {
      if (
        e.target.matches('[data-action="toggle-like"]') ||
        e.target.closest('[data-action="toggle-like"]')
      ) {
        e.preventDefault();
        const button = e.target.matches('[data-action="toggle-like"]')
          ? e.target
          : e.target.closest('[data-action="toggle-like"]');
        const eventId = button.dataset.eventId;
        this.toggleLike(eventId, button);
      }
    });

    // Delete event buttons
    document.addEventListener("click", (e) => {
      if (e.target.matches('[data-action="delete-event"]')) {
        e.preventDefault();
        const eventId = e.target.dataset.eventId;
        this.confirmDelete(eventId);
      }
    });

    // Create event buttons
    document.addEventListener("click", (e) => {
      if (e.target.matches('[data-action="create-event"]')) {
        e.preventDefault();
        const journeyId = e.target.dataset.journeyId;
        this.openCreateModal(journeyId);
      }
    });
  }

  /**
   * Toggle like status for an event
   */
  async toggleLike(eventId, button) {
    try {
      button.disabled = true;

      const response = await fetch(`${this.baseUrl}/${eventId}/like`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const data = await response.json();

      if (data.success) {
        // Update like button
        this.updateLikeButton(button, data.user_liked, data.likes_count);

        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "success");
        }
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(data.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error toggling like:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "An error occurred. Please try again.",
          "danger"
        );
      }
    } finally {
      button.disabled = false;
    }
  }

  /**
   * Open edit event modal
   */
  async openEditModal(eventId) {
    try {
      const response = await fetch(`/event/${eventId}/edit`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const formHtml = await response.text();

      if (typeof showModal === "function") {
        showModal("Edit Event", formHtml, {
          actionText: "Save",
          onAction: async () => {
            return await this.submitEditForm();
          },
        });

        // Initialize form components after modal is shown
        setTimeout(() => {
          this.initializeEditForm();
        }, 200);
      } else {
        console.error("showModal function not available");
      }
    } catch (error) {
      console.error("Error opening edit modal:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Failed to load edit form. Please try again.",
          "danger"
        );
      }
    }
  }

  /**
   * Open create event modal
   */
  async openCreateModal(journeyId) {
    try {
      const response = await fetch(`/event/new/${journeyId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const formHtml = await response.text();

      if (typeof showModal === "function") {
        showModal("Add New Event", formHtml, {
          actionText: "Create",
          onAction: async () => {
            return await this.submitCreateForm();
          },
        });

        // Initialize form components after modal is shown
        setTimeout(() => {
          this.initializeCreateForm();
        }, 200);
      } else {
        console.error("showModal function not available");
      }
    } catch (error) {
      console.error("Error opening create modal:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Failed to load create form. Please try again.",
          "danger"
        );
      }
    }
  }

  /**
   * Submit edit form
   */
  async submitEditForm() {
    const form = document.getElementById("editEventForm");
    if (!form) return false;

    // Validate form
    form.classList.add("was-validated");

    if (!form.checkValidity()) {
      const firstInvalidField = form.querySelector(":invalid");
      if (firstInvalidField) {
        firstInvalidField.focus();
      }
      return false;
    }

    try {
      const formData = new FormData(form);

      const response = await fetch(form.action, {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      // Check if the response is successful
      if (response.ok) {
        // Try to parse JSON response first
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const result = await response.json();

          if (result.success) {
            // Handle "no changes" case differently - don't redirect
            if (result.no_changes) {
              console.log('No changes detected - showing info message without redirect');
              if (typeof window.showFlashMessage === "function") {
                window.showFlashMessage(result.message, "info");
              }

              // Close the modal gracefully
              if (typeof window.closeModal === "function") {
                window.closeModal();
              }
              return true; // Don't redirect
            }

            // Show success message immediately (no storage needed)
            if (typeof window.showFlashMessage === "function") {
              window.showFlashMessage(result.message, "success");
            }

            // Close the modal gracefully
            if (typeof window.closeModal === "function") {
              window.closeModal();
            }

            // Navigate to the event detail page or stay on current page
            if (result.redirect_url) {
              window.location.href = result.redirect_url;
            } else {
              // If no redirect URL, just show success and stay on current page
              console.log(
                "Event updated successfully, staying on current page"
              );
            }
          } else {
            // Show error message immediately
            if (typeof window.showFlashMessage === "function") {
              window.showFlashMessage(
                result.message || "Failed to save changes. Please try again.",
                "danger"
              );
            }
            return false;
          }
        } else {
          // Handle regular redirect response
          if (response.redirected) {
            window.location.href = response.url;
          } else {
            window.location.reload();
          }
        }
      } else {
        // Handle HTTP error response
        const errorText = await response.text();
        console.error("Server error:", response.status, errorText);

        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(
            "Failed to save changes. Please try again.",
            "danger"
          );
        }
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error submitting edit form:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Failed to save changes. Please try again.",
          "danger"
        );
      }
      return false;
    }
  }

  /**
   * Submit create form
   */
  async submitCreateForm() {
    const form = document.getElementById("createEventForm");
    if (!form) return false;

    // Validate form
    form.classList.add("was-validated");

    if (!form.checkValidity()) {
      const firstInvalidField = form.querySelector(":invalid");
      if (firstInvalidField) {
        firstInvalidField.focus();
      }
      return false;
    }

    // Check location selection
    const locationInput = document.getElementById("location");
    const latInput = document.getElementById("latitude");
    const lngInput = document.getElementById("longitude");

    if (!locationInput?.value || !latInput?.value || !lngInput?.value) {
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Please select a location first.", "warning");
      }
      return false;
    }

    // Validate file input before submission
    const fileInput = document.getElementById("images");
    if (fileInput && fileInput.files.length > 0) {
      const isPremiumAccess = fileInput.getAttribute("data-premium") === "true";
      const maxFiles = isPremiumAccess ? 10 : 1;
      const validation = this.validateFiles(fileInput.files, maxFiles, isPremiumAccess);
      
      if (!validation.valid) {
        this.handleFileValidation(validation, fileInput.files, fileInput);
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(validation.message, "danger");
        }
        return false;
      }
    }

    try {
      const formData = new FormData(form);
      const response = await fetch(form.action, {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const result = await response.json();

      if (result.success) {
        // Show success message immediately
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "success");
        }

        // Close the modal gracefully
        if (typeof window.closeModal === "function") {
          window.closeModal();
        }

        // Update the DOM instead of redirecting
        await this.updateJourneyPageWithNewEvent(result.event_id);
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "danger");
        }
      }

      return true;
    } catch (error) {
      console.error("Error submitting create form:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Failed to create event. Please try again.",
          "danger"
        );
      }
      return false;
    }
  }

  /**
   * Update the journey page with the new event without refreshing
   */
  async updateJourneyPageWithNewEvent(eventId) {
    try {
      // Fetch the updated journey page content
      const currentUrl = window.location.href;
      const response = await fetch(currentUrl);
      const html = await response.text();

      // Parse the response to extract the events section
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, "text/html");

      // Update the events section
      const newEventsSection = doc.querySelector(".col-md-8 .card-body");
      const currentEventsSection = document.querySelector(
        ".col-md-8 .card-body"
      );

      if (newEventsSection && currentEventsSection) {
        currentEventsSection.innerHTML = newEventsSection.innerHTML;

        // Re-initialize any event handlers for the new content
        this.initializeEventHandlers();

        // Scroll to the new event if possible
        const newEventCard = document.querySelector(
          `[data-event-id="${eventId}"]`
        );
        if (newEventCard) {
          newEventCard.scrollIntoView({ behavior: "smooth", block: "center" });

          // Add a highlight effect
          newEventCard.style.transition = "background-color 0.5s ease";
          newEventCard.style.backgroundColor = "#e8f5e8";
          setTimeout(() => {
            newEventCard.style.backgroundColor = "";
          }, 2000);
        }
      }
    } catch (error) {
      console.error("Error updating journey page:", error);
      // Fallback to page refresh if DOM update fails
      window.location.reload();
    }
  }

  /**
   * Initialize edit form components
   */
  initializeEditForm() {
    // Initialize map if needed
    if (typeof initMap === "function") {
      initMap();
    }

    // Initialize location search if needed
    if (typeof initializeModalLocationSearch === "function") {
      initializeModalLocationSearch();
    }
  }

  /**
   * Initialize create form components
   */
  initializeCreateForm() {
    // Wait a bit more for modal DOM to be fully rendered
    setTimeout(() => {
      // Initialize location operations
      if (window.LocationOperations) {
        try {
          // Create new instance for the modal
          const modalLocationOps = new window.LocationOperations();
          console.log("Location operations initialized for create modal");
        } catch (error) {
          console.error("Error initializing location operations:", error);
        }
      }

      // Also try the legacy initialization
      if (typeof window.initializeLocationSelector === "function") {
        try {
          window.initializeLocationSelector();
        } catch (error) {
          console.error("Error initializing location selector:", error);
        }
      }

      // Initialize file input validation with additional delay
      setTimeout(() => {
        this.initializeFileInputValidation();
      }, 200); // Extra delay for file input validation
    }, 300); // Increased delay to ensure modal DOM is ready
  }

  /**
   * Initialize file input validation for create event modal
   */
  async initializeFileInputValidation(retryCount = 0) {
    // Check if we're in the right context (create modal)
    const fileInput = document.getElementById("images");
    const feedbackElement = document.getElementById("imagesFeedback");
    
    if (!fileInput || !feedbackElement) {
      if (retryCount < 3) {

        setTimeout(() => {
          this.initializeFileInputValidation(retryCount + 1);
        }, 300);
        return;
      } else {
        console.error("File input 'images' or feedback element 'imagesFeedback' not found after multiple attempts");
        return;
      }
    }

    // Get premium access status from data attribute
    const isPremiumAccess = fileInput.getAttribute("data-premium") === "true";
    const maxFiles = isPremiumAccess ? 10 : 1;

    

    // Initialize centralized FileValidation first
    if (window.FileValidation && window.FileValidation.init) {
      try {
        await window.FileValidation.init();

      } catch (error) {
        console.error("Failed to initialize FileValidation:", error);
      }
    }

    // Remove any existing event listeners to prevent duplicates
    const newFileInput = fileInput.cloneNode(true);
    fileInput.parentNode.replaceChild(newFileInput, fileInput);

    // Use centralized FileValidation with enhanced validation for file count
    this.setupProperFileValidation(newFileInput, isPremiumAccess, maxFiles, feedbackElement);
  }

  /**
   * Setup proper file validation using centralized system with file count limits
   */
  setupProperFileValidation(fileInput, isPremiumAccess, maxFiles, feedbackElement) {

    
         // Use centralized FileValidation if available
     if (window.FileValidation && window.FileValidation.setupFileInput) {
       try {
         window.FileValidation.setupFileInput(fileInput, {
           maxFiles: maxFiles,
           onValidation: (validation, files) => {
             // The centralized validation already includes file count, but we want custom messages
             let finalValidation = validation;
             
             // Override file count message for better UX
             if (!validation.valid && files && files.length > maxFiles) {
               finalValidation = {
                 valid: false,
                 message: isPremiumAccess 
                   ? `You can only upload a maximum of ${maxFiles} images. You selected ${files.length} images.`
                   : `Free users can upload only one image. You selected ${files.length} images.`
               };
             }
             
             this.handleFileValidation(finalValidation, files, fileInput, feedbackElement);
           },
           onChange: (files, validation) => {
             console.log(`Files changed: ${files.length} selected, Valid: ${validation.valid}`);
           }
         });
 
         return;
       } catch (error) {
         console.error("Error using centralized FileValidation:", error);
       }
     }

    // If centralized validation fails, use manual setup with same logic
    
    this.setupManualFileValidation(fileInput, isPremiumAccess, maxFiles, feedbackElement);
  }



  /**
   * Manual file validation setup when centralized system isn't available
   */
  setupManualFileValidation(fileInput, isPremiumAccess, maxFiles, feedbackElement) {
    fileInput.addEventListener('change', (event) => {
      
      const files = event.target.files;
      
      // Try to use centralized validateFiles function first
      let validation;
      if (window.FileValidation && window.FileValidation.validateFiles) {
        validation = window.FileValidation.validateFiles(files, maxFiles);
        
        // Customize the message for our specific use case
        if (!validation.valid && files.length > maxFiles) {
          validation.message = isPremiumAccess 
            ? `You can only upload a maximum of ${maxFiles} images. You selected ${files.length} images.`
            : `Free users can upload only one image. You selected ${files.length} images.`;
        }
      } else {
        // Ultimate fallback
        validation = this.validateFiles(files, maxFiles, isPremiumAccess);
      }
      
      this.handleFileValidation(validation, files, fileInput, feedbackElement);
    });

    
  }

  /**
   * Handle file validation results
   */
  handleFileValidation(validation, files, fileInput, feedbackElement = null) {
    const feedback = feedbackElement || document.getElementById("imagesFeedback");
    
    if (validation.valid) {
      fileInput.classList.remove("is-invalid");
      fileInput.classList.add("is-valid");
      if (feedback) {
        feedback.textContent = "";
        feedback.style.display = "none";
        feedback.classList.add("invalid-feedback");
        feedback.classList.remove("d-block");
      }
    } else {
      fileInput.classList.remove("is-valid");
      fileInput.classList.add("is-invalid");
      if (feedback) {
        feedback.textContent = validation.message;
        feedback.style.display = "block";
        feedback.classList.add("invalid-feedback", "d-block");
      }
    }
  }

  /**
   * Clear file validation state
   */
  clearFileValidation(fileInput) {
    const feedback = document.getElementById("imagesFeedback");
    
    fileInput.classList.remove("is-valid", "is-invalid");
    if (feedback) {
      feedback.textContent = "";
      feedback.style.display = "none";
    }

  }

  /**
   * Handle file change events (for future preview functionality)
   */
  handleFileChange(files, validation, fileInput) {
    // This can be extended in the future to show image previews
    // Handle file change event
  }

  /**
   * Validate files with 10-image limit and 5MB size check
   */
  validateFiles(files, maxFiles, isPremiumAccess) {
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    const ALLOWED_EXTENSIONS = ["png", "jpg", "jpeg", "gif"];



    if (!files || files.length === 0) {
      return { valid: true, message: "" }; // Images are optional
    }

    // Check file count limit
    if (files.length > maxFiles) {
      const message = isPremiumAccess 
        ? `You can only upload a maximum of ${maxFiles} images.`
        : "Free users can upload only one image.";
      

      return {
        valid: false,
        message: message
      };
    }

    // Check each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];


      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        const fileSizeMB = (file.size / 1024 / 1024).toFixed(2);
        const message = `File "${file.name}" (${fileSizeMB}MB) exceeds the maximum size of 5MB.`;

        return {
          valid: false,
          message: message
        };
      }

      // Check file extension
      const fileExt = file.name.split(".").pop().toLowerCase();
      if (!ALLOWED_EXTENSIONS.includes(fileExt)) {
        const message = `File "${file.name}" has an invalid extension. Allowed: PNG, JPG, JPEG, GIF.`;

        return {
          valid: false,
          message: message
        };
      }
    }


    return { valid: true, message: "" };
  }

  /**
   * Confirm and delete event
   */
  confirmDelete(eventId) {
    if (typeof showModal === "function") {
      showModal(
        "Delete Event",
        "Are you sure you want to delete this event? This action cannot be undone.",
        {
          actionText: "Delete",
          onAction: () => {
            this.deleteEvent(eventId);
          },
        }
      );
    } else {
      // Fallback to browser confirm
      if (
        confirm(
          "Are you sure you want to delete this event? This action cannot be undone."
        )
      ) {
        this.deleteEvent(eventId);
      }
    }
  }

  /**
   * Delete event
   */
  async deleteEvent(eventId) {
    try {
      const deleteForm = document.getElementById(`deleteEventForm_${eventId}`);

      if (deleteForm) {
        deleteForm.submit();
      } else {
        console.error("Delete form not found");
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(
            "Delete form not found. Please refresh and try again.",
            "danger"
          );
        }
      }
    } catch (error) {
      console.error("Error deleting event:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "An error occurred. Please try again.",
          "danger"
        );
      }
    }
  }

  /**
   * Update like button appearance
   */
  updateLikeButton(button, isLiked, likesCount) {
    const icon = button.querySelector("i");
    const countSpan = button.querySelector("span");

    if (icon) {
      icon.className = isLiked ? "bi bi-heart-fill" : "bi bi-heart";
    }

    if (countSpan) {
      countSpan.textContent = likesCount || 0;
    }

    // Update button styling
    if (isLiked) {
      button.classList.add("liked");
    } else {
      button.classList.remove("liked");
    }
  }
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.eventOperations = new EventOperations();
});

// Export for module usage
if (typeof module !== "undefined" && module.exports) {
  module.exports = EventOperations;
}
