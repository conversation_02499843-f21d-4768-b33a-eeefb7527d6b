/**
 * Centralized file validation utilities
 * Fetches configuration from backend to ensure consistency
 */

let fileConfig = null;

/**
 * Initialize file validation by fetching configuration from backend
 */
async function initFileValidation() {
    if (fileConfig) {
        return fileConfig; // Already initialized
    }
    
    try {
        const response = await fetch('/api/file-config/');
        fileConfig = await response.json();
        return fileConfig;
    } catch (error) {
        console.error('Failed to load file configuration:', error);
        // Fallback configuration
        fileConfig = {
            allowedExtensions: ['png', 'jpg', 'jpeg', 'gif'],
            allowedExtensionsHtml: '.png,.jpg,.jpeg,.gif',
            allowedMimeTypes: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif'],
            maxFileSize: 5242880, // 5MB
            maxFileSizeMB: 5,
            allowedFormatsText: 'PNG, JPG, JPEG, GIF'
        };
        return fileConfig;
    }
}

/**
 * Validate a single file
 * @param {File} file - The file to validate
 * @returns {Object} - {valid: boolean, message: string}
 */
function validateFile(file) {
    if (!fileConfig) {
        console.error('File validation not initialized. Call initFileValidation() first.');
        return { valid: false, message: 'File validation not available' };
    }
    
    if (!file) {
        return { valid: false, message: 'No file provided' };
    }
    
    // Check file size
    if (file.size > fileConfig.maxFileSize) {
        return { 
            valid: false, 
            message: `File size exceeds maximum limit of ${fileConfig.maxFileSizeMB}MB.` 
        };
    }
    
    // Check file extension
    const fileExt = file.name.split('.').pop().toLowerCase();
    if (!fileConfig.allowedExtensions.includes(fileExt)) {
        return { 
            valid: false, 
            message: `File type not allowed. Only ${fileConfig.allowedFormatsText} are accepted.` 
        };
    }
    
    // Check MIME type if available
    if (file.type && !fileConfig.allowedMimeTypes.includes(file.type)) {
        return { 
            valid: false, 
            message: `Invalid file type. Only ${fileConfig.allowedFormatsText} are accepted.` 
        };
    }
    
    return { valid: true, message: '' };
}

/**
 * Validate multiple files
 * @param {FileList|Array} files - The files to validate
 * @param {number} maxFiles - Maximum number of files allowed (optional)
 * @returns {Object} - {valid: boolean, message: string, validFiles: Array}
 */
function validateFiles(files, maxFiles = null) {
    if (!fileConfig) {
        console.error('File validation not initialized. Call initFileValidation() first.');
        return { valid: false, message: 'File validation not available', validFiles: [] };
    }
    
    if (!files || files.length === 0) {
        return { valid: false, message: 'Please select at least one file.', validFiles: [] };
    }
    
    // Check file count limit
    if (maxFiles && files.length > maxFiles) {
        return { 
            valid: false, 
            message: `You can only upload a maximum of ${maxFiles} files.`,
            validFiles: []
        };
    }
    
    const validFiles = [];
    const errors = [];
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const validation = validateFile(file);
        
        if (validation.valid) {
            validFiles.push(file);
        } else {
            errors.push(`${file.name}: ${validation.message}`);
        }
    }
    
    if (errors.length > 0) {
        return {
            valid: false,
            message: errors.join('\n'),
            validFiles: validFiles
        };
    }
    
    return {
        valid: true,
        message: '',
        validFiles: validFiles
    };
}

/**
 * Get the HTML accept attribute value
 * @returns {string} - Accept attribute value for file inputs
 */
function getAcceptAttribute() {
    if (!fileConfig) {
        console.error('File validation not initialized. Call initFileValidation() first.');
        return '.png,.jpg,.jpeg,.gif'; // Fallback
    }
    return fileConfig.allowedExtensionsHtml;
}

/**
 * Get human-readable format text
 * @returns {string} - Human-readable list of allowed formats
 */
function getAllowedFormatsText() {
    if (!fileConfig) {
        console.error('File validation not initialized. Call initFileValidation() first.');
        return 'PNG, JPG, JPEG, GIF'; // Fallback
    }
    return fileConfig.allowedFormatsText;
}

/**
 * Get maximum file size in MB
 * @returns {number} - Maximum file size in MB
 */
function getMaxFileSizeMB() {
    if (!fileConfig) {
        console.error('File validation not initialized. Call initFileValidation() first.');
        return 5; // Fallback
    }
    return fileConfig.maxFileSizeMB;
}

/**
 * Set up a file input with proper validation
 * @param {HTMLInputElement} input - The file input element
 * @param {Object} options - Configuration options
 * @param {number} options.maxFiles - Maximum number of files
 * @param {Function} options.onValidation - Callback for validation results
 * @param {Function} options.onChange - Callback for file changes
 */
function setupFileInput(input, options = {}) {
    if (!input || input.type !== 'file') {
        console.error('Invalid file input element provided');
        return;
    }
    
    // Set accept attribute
    input.setAttribute('accept', getAcceptAttribute());
    
    // Add change event listener
    input.addEventListener('change', function(event) {
        const files = event.target.files;
        const validation = validateFiles(files, options.maxFiles);
        
        // Call validation callback if provided
        if (options.onValidation) {
            options.onValidation(validation, files);
        }
        
        // Call change callback if provided
        if (options.onChange) {
            options.onChange(files, validation);
        }
        
        // Add/remove validation classes
        if (validation.valid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            
            // Show error message if there's a feedback element
            const feedback = input.parentElement.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = validation.message;
            }
        }
    });
}

// Auto-initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initFileValidation().then(() => {
        console.log('File validation initialized');
    });
});

// Export functions for use in other scripts
window.FileValidation = {
    init: initFileValidation,
    validateFile: validateFile,
    validateFiles: validateFiles,
    getAcceptAttribute: getAcceptAttribute,
    getAllowedFormatsText: getAllowedFormatsText,
    getMaxFileSizeMB: getMaxFileSizeMB,
    setupFileInput: setupFileInput
};
