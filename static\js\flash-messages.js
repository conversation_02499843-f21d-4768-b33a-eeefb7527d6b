/**
 * Flash Messages Utility
 *
 * A unified approach to display flash messages throughout the application using Bootstrap toasts.
 * This utility provides functions to show custom flash messages from JavaScript, as well as
 * display server-side flash messages.
 */

/**
 * Toast container ID - this element will contain all toast messages
 */
const TOAST_CONTAINER_ID = "universalToastContainer";

/**
 * Create and get the toast container
 * @returns {HTMLElement} The toast container element
 */
function getToastContainer() {
  let container = document.getElementById(TOAST_CONTAINER_ID);

  // Create container if it doesn't exist
  if (!container) {
    container = document.createElement("div");
    container.id = TOAST_CONTAINER_ID;
    container.className =
      "toast-container position-fixed top-0 start-50 translate-middle-x mt-4";
    container.style.zIndex = "1090"; // Higher than modal backdrop (1050)
    document.body.appendChild(container);
  }

  return container;
}

/**
 * Show a flash message using Bootstrap toast
 *
 * @param {string} message - The message to display
 * @param {string} type - The message type ('success', 'danger', 'info', 'warning')
 * @param {object|string} optionsOrId - Additional options (autohide, delay) or toast ID string
 * @returns {bootstrap.Toast} The created toast instance
 */
function showFlashMessage(message, type = "success", optionsOrId = {}) {
  // Handle the case where third parameter is a string (ID) for backward compatibility
  let options = {};
  let toastId = null;

  if (typeof optionsOrId === 'string') {
    toastId = optionsOrId;
  } else {
    options = optionsOrId;
    toastId = options.id;
  }

  // Default options
  const defaultOptions = {
    autohide: true,
    delay: 5000,
    animation: true,
  };

  // Merge with user options
  const finalOptions = { ...defaultOptions, ...options };

  // Get toast container
  const container = getToastContainer();

  // Create toast element
  const toastElement = document.createElement("div");
  toastElement.className = `toast border-0 shadow-lg`;
  toastElement.setAttribute("role", "alert");
  toastElement.setAttribute("aria-live", "assertive");
  toastElement.setAttribute("aria-atomic", "true");

  // Set ID if provided
  if (toastId) {
    toastElement.id = toastId;
  }

  // Style the toast
  toastElement.style.borderRadius = "15px";
  toastElement.style.backdropFilter = "blur(10px)";
  toastElement.style.background = "rgba(255, 255, 255, 0.95)";
  toastElement.style.minWidth = "300px";
  toastElement.style.marginBottom = "10px";

  // Get appropriate icon based on type
  let iconClass = "bi-info-circle-fill text-primary";
  let headerText = "Info";

  if (type === "success") {
    iconClass = "bi-check-circle-fill text-success";
    headerText = "Success";
  } else if (type === "danger" || type === "error") {
    iconClass = "bi-exclamation-circle-fill text-danger";
    headerText = "Error";
    type = "danger";
  } else if (type === "warning") {
    iconClass = "bi-exclamation-triangle-fill text-warning";
    headerText = "Warning";
  }

  // Create toast content
  toastElement.innerHTML = `
    <div class="toast-header border-0" style="background: transparent; padding: 1rem 1rem 0.5rem 1rem;">
      <div class="me-auto d-flex align-items-center">
        <i class="bi ${iconClass} me-2" style="font-size: 1.2rem;"></i>
        <strong class="text-${type}">${headerText}</strong>
      </div>
      <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body" style="padding: 0.5rem 1rem 1rem 1rem;">
      <span style="color: #4a5568;">${message}</span>
    </div>
  `;

  // Add toast to container
  container.appendChild(toastElement);

  // Create and show Bootstrap toast
  const toast = new bootstrap.Toast(toastElement, finalOptions);
  toast.show();

  // Add hover effect
  toastElement.addEventListener("mouseenter", function () {
    this.style.transform = "translateY(-3px)";
    this.style.transition = "transform 0.3s ease";

    // Pause auto-hide on hover
    if (toast._timeout) {
      clearTimeout(toast._timeout);
    }
  });

  toastElement.addEventListener("mouseleave", function () {
    this.style.transform = "translateY(0)";

    // Resume auto-hide on mouse leave
    if (finalOptions.autohide) {
      toast._timeout = setTimeout(() => toast.hide(), finalOptions.delay / 2);
    }
  });

  // Clean up when the toast is hidden
  toastElement.addEventListener("hidden.bs.toast", function () {
    toastElement.remove();
  });

  // Return the toast instance
  return toast;
}

/**
 * Store a flash message in sessionStorage to display after page reload
 *
 * @param {string} message - The message to store
 * @param {string} type - The message type
 */
function storeFlashMessage(message, type = "success") {
  // Store in session storage
  const flashData = {
    message,
    type,
    timestamp: Date.now(),
  };

  sessionStorage.setItem("storedFlashMessage", JSON.stringify(flashData));
}

/**
 * Process any stored flash messages from sessionStorage
 */
function processStoredFlashMessages() {
  const storedMessage = sessionStorage.getItem("storedFlashMessage");

  if (storedMessage) {
    try {
      const flashData = JSON.parse(storedMessage);

      // Check if the message is not too old (within 10 seconds)
      const now = Date.now();
      if (now - flashData.timestamp < 10000) {
        // Show the message
        showFlashMessage(flashData.message, flashData.type);
      }

      // Remove the stored message
      sessionStorage.removeItem("storedFlashMessage");
    } catch (e) {
      console.error("Error processing stored flash message:", e);
      sessionStorage.removeItem("storedFlashMessage");
    }
  }
}

/**
 * Initialize the flash message system
 *
 * This should be called when the document is loaded.
 */
function initFlashMessages() {
  // Process any stored flash messages
  processStoredFlashMessages();

  // Process any server-side flash messages (already in the DOM)
  // We'll add this integration in the base template
}

// Expose the functions to the global scope
window.showFlashMessage = showFlashMessage;
window.storeFlashMessage = storeFlashMessage;

// Initialize when document is loaded
document.addEventListener("DOMContentLoaded", initFlashMessages);

// Compatibility with existing code - map old function names to new ones
window.showEventDetailFlashMessage = function (message, type = "success") {
  console.log(
    "showEventDetailFlashMessage is deprecated, use showFlashMessage instead"
  );
  showFlashMessage(message, type);
};
