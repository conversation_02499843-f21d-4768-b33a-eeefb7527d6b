// Form validation for Bootstrap
(() => {
  "use strict";

  // Initialize when document is loaded
  document.addEventListener("DOMContentLoaded", () => {
    const forms = document.querySelectorAll(".needs-validation");

    // Initialize form validation
    Array.from(forms).forEach(initializeFormValidation);

    // Add styling to prevent validation icons on optional fields
    addOptionalFieldStyles();
  });

  /**
   * Initialize form validation for a form
   * @param {HTMLFormElement} form - The form to initialize
   */
  function initializeFormValidation(form) {
    // Handle form submission
    form.addEventListener(
      "submit",
      (event) => {
        if (!form.checkValidity()) {
          event.preventDefault();
          event.stopPropagation();

          // After validation fails, scroll to the first invalid field
          setTimeout(() => {
            scrollToFirstInvalidField(form);
          }, 100);
        }

        form.classList.add("was-validated");
        handleOptionalFields(form);
      },
      false
    );

    // Set up input handlers
    setupInputHandlers(form);
  }

  /**
   * Scroll to the first invalid field in the form
   * @param {HTMLFormElement} form - The form containing invalid fields
   */
  function scrollToFirstInvalidField(form) {
    const invalidField = form.querySelector(":invalid");
    if (invalidField) {
      // Find the closest parent with some vertical padding/margin for better positioning
      const scrollTarget =
        invalidField.closest(".mb-3") ||
        invalidField.closest(".form-group") ||
        invalidField;

      // Scroll the element into view with some offset
      const yOffset = -80; // Adjust this value for the desired amount of space at the top
      const y =
        scrollTarget.getBoundingClientRect().top + window.pageYOffset + yOffset;

      window.scrollTo({
        top: y,
        behavior: "smooth",
      });

      // Try to focus the invalid field
      try {
        invalidField.focus();
      } catch (e) {
        // Some elements might not be focusable
      }
    }
  }

  /**
   * Add CSS styles to handle optional fields
   */
  function addOptionalFieldStyles() {
    const style = document.createElement("style");
    style.textContent = `
      .optional-field:not([required]):valid,
      .was-validated .optional-field:not([required]):valid {
        background-image: none !important;
        border-color: #ced4da !important;
      }
    `;
    document.head.appendChild(style);
  }
})();

/**
 * Handle optional fields in a form by removing validation styling when they're empty
 * @param {HTMLFormElement} form - The form to process
 */
function handleOptionalFields(form) {
  const optionalFields = form.querySelectorAll(
    ".optional-field:not([required])"
  );
  optionalFields.forEach((field) => {
    if (!field.value) {
      field.classList.remove("is-invalid", "is-valid");
      field.setCustomValidity("");
    }
  });
}

/**
 * Set up input handlers for form fields
 * @param {HTMLFormElement} form - The form containing fields to set up
 */
function setupInputHandlers(form) {
  // Set up handlers for optional fields
  form.querySelectorAll(".optional-field:not([required])").forEach((field) => {
    field.addEventListener("input", function () {
      if (!this.value) {
        this.classList.remove("is-invalid", "is-valid");
        this.setCustomValidity("");
      }
    });
  });

  // Set up special field validation handlers
  const fieldValidators = {
    "#username": validateUsername,
    "#email": validateEmail,
    "#password": validatePassword,
    "#first_name, #last_name": validateName,
  };

  // Apply validation handlers to fields
  for (const [selector, validator] of Object.entries(fieldValidators)) {
    form.querySelectorAll(selector).forEach((field) => {
      field.addEventListener("input", () => validator(field));
    });
  }
}

/**
 * Validate username field
 * @param {HTMLInputElement} input - The username input field
 */
function validateUsername(input) {
  const username = input.value;

  // For login form, just check if username is entered
  if (
    document.querySelector("form").getAttribute("action") &&
    document.querySelector("form").getAttribute("action").includes("login")
  ) {
    if (!username) {
      setValidationState(input, ["Please enter your username"]);
    } else {
      setValidationState(input, []);
    }
    return;
  }

  // For registration forms
  const minLength = 3;
  const maxLength = 20;
  const validPattern = /^[a-zA-Z0-9]+$/;

  let validationMessage = [];

  if (username.length < minLength)
    validationMessage.push(`Minimum ${minLength} characters`);
  if (username.length > maxLength)
    validationMessage.push(`Maximum ${maxLength} characters`);
  if (!validPattern.test(username))
    validationMessage.push("Only letters and numbers allowed");

  setValidationState(input, validationMessage);
}

/**
 * Validate email field
 * @param {HTMLInputElement} input - The email input field
 */
function validateEmail(input) {
  const email = input.value;
  const maxLength = 254;
  const validPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  let validationMessage = [];

  if (email.length > maxLength)
    validationMessage.push("Email address is too long");
  if (!validPattern.test(email))
    validationMessage.push("Please enter a valid email address");

  setValidationState(input, validationMessage);
}

/**
 * Validate password field
 * @param {HTMLInputElement} input - The password input field
 */
function validatePassword(input) {
  const password = input.value;
  const allowedIds = window.PASSWORD_REQUIREMENT_IDS || [
    "password",
    "new_password",
  ];
  if (!allowedIds.includes(input.id)) {
    if (!password) {
      setValidationState(input, ["Please enter your password"]);
    } else {
      setValidationState(input, []);
    }
    return;
  }

  // For login form, just check if password is entered
  if (
    document.querySelector("form").getAttribute("action") &&
    document.querySelector("form").getAttribute("action").includes("login")
  ) {
    if (!password) {
      setValidationState(input, ["Please enter your password"]);
    } else {
      setValidationState(input, []);
    }
    return;
  }

  // For registration and change password forms
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  // Update requirement indicators
  updateRequirement("length", password.length >= minLength);
  updateRequirement("uppercase", hasUpperCase);
  updateRequirement("lowercase", hasLowerCase);
  updateRequirement("number", hasNumbers);
  updateRequirement("special", hasSpecialChar);

  const isValid =
    password.length >= minLength &&
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasSpecialChar;

  const validationMessage = isValid
    ? []
    : ["Please meet all password requirements"];

  setValidationState(input, validationMessage);
}

/**
 * Update password requirement status
 * @param {string} requirement - The requirement identifier
 * @param {boolean} isValid - Whether the requirement is met
 */
function updateRequirement(requirement, isValid) {
  const reqElement = document.getElementById(`req-${requirement}`);
  if (reqElement) {
    const icon = reqElement.querySelector("i");
    if (isValid) {
      reqElement.classList.add("valid");
      icon.classList.remove("bi-x-circle", "text-danger");
      icon.classList.add("bi-check-circle", "text-success");
    } else {
      reqElement.classList.remove("valid");
      icon.classList.remove("bi-check-circle", "text-success");
      icon.classList.add("bi-x-circle", "text-danger");
    }
  }
}

/**
 * Validate name fields
 * @param {HTMLInputElement} input - The name input field
 */
function validateName(input) {
  const name = input.value;

  // If field is empty and optional, skip validation
  if (!input.hasAttribute("required") && !name) {
    input.setCustomValidity("");
    input.classList.remove("is-invalid", "is-valid");
    return;
  }

  const minLength = 2;
  const maxLength = 50;
  const validPattern = /^[a-zA-Z\s-']+$/;

  let validationMessage = [];

  if (name && name.length < minLength)
    validationMessage.push(`Minimum ${minLength} characters`);
  if (name && name.length > maxLength)
    validationMessage.push(`Maximum ${maxLength} characters`);
  if (name && !validPattern.test(name))
    validationMessage.push(
      "Only letters, spaces, hyphens and apostrophes allowed"
    );

  setValidationState(input, validationMessage);
}

/**
 * Set validation state for an input field
 * @param {HTMLInputElement} input - The input field
 * @param {string[]} validationMessage - Array of validation messages (empty if valid)
 */
function setValidationState(input, validationMessage) {
  // Set custom validity
  input.setCustomValidity(validationMessage.length > 0 ? "Invalid" : "");

  // Update feedback message
  const feedbackElement =
    input.parentElement.querySelector(".invalid-feedback");
  if (feedbackElement) {
    feedbackElement.textContent =
      validationMessage.length > 0 ? validationMessage.join(", ") : "";
  }
}

/**
 * Enhanced Form Validation for Create/Edit Forms
 */
window.EnhancedFormValidation = {
  /**
   * Initialize enhanced validation for modern forms
   */
  initializeModernForm: function (form, options = {}) {
    if (!form) return;

    const defaults = {
      validateOnInput: true,
      validateOnBlur: true,
      showSuccessStates: true,
      customValidators: {},
    };

    const config = Object.assign(defaults, options);
    form._enhancedConfig = config;

    this.setupModernEventListeners(form, config);
    this.setupDateTimeValidation(form);
    this.setupLocationValidation(form);

    console.log("Enhanced form validation initialized");
  },

  /**
   * Setup event listeners for modern forms
   */
  setupModernEventListeners: function (form, config) {
    const inputs = form.querySelectorAll(
      ".modern-input, .modern-textarea, .modern-select"
    );

    inputs.forEach((input) => {
      if (config.validateOnInput) {
        input.addEventListener("input", () =>
          this.validateModernField(input, config)
        );
      }

      if (config.validateOnBlur) {
        input.addEventListener("blur", () =>
          this.validateModernField(input, config)
        );
      }

      input.addEventListener("focus", () => this.clearModernValidation(input));
    });

    form.addEventListener("submit", (e) =>
      this.handleModernFormSubmit(e, config)
    );
  },

  /**
   * Validate modern form field
   */
  validateModernField: function (field, config) {
    const isValid = this.checkModernFieldValidity(field, config);
    this.updateModernFieldState(field, isValid);
    return isValid;
  },

  /**
   * Check modern field validity
   */
  checkModernFieldValidity: function (field, config) {
    if (!field.checkValidity()) return false;

    // Custom validation rules
    if (field.type === "datetime-local") {
      return this.validateDateTime(field);
    }

    if (field.name === "title" || field.id === "title") {
      return this.validateTitle(field.value);
    }

    if (field.name === "newLocationName" || field.id === "newLocationName") {
      return this.validateLocationName(field.value);
    }

    return true;
  },

  /**
   * Validate date/time fields
   */
  validateDateTime: function (field) {
    const value = field.value;
    if (!value) return !field.required;

    const date = new Date(value);
    if (isNaN(date.getTime())) return false;

    // Check if end date is after start date
    if (field.id === "endDatetime" || field.name === "end_datetime") {
      const startField = field.form.querySelector(
        '#startDatetime, [name="start_datetime"]'
      );
      if (startField && startField.value) {
        const startDate = new Date(startField.value);
        if (date <= startDate) {
          field.setCustomValidity("End date must be after start date");
          return false;
        }
      }
    }

    field.setCustomValidity("");
    return true;
  },

  /**
   * Validate title
   */
  validateTitle: function (value) {
    if (!value) return false;
    const trimmed = value.trim();
    return trimmed.length >= 5 && trimmed.length <= 50;
  },

  /**
   * Validate location name
   */
  validateLocationName: function (value) {
    if (!value) return false;
    const trimmed = value.trim();
    return trimmed.length >= 2 && trimmed.length <= 100;
  },

  /**
   * Update modern field validation state
   */
  updateModernFieldState: function (field, isValid) {
    field.classList.remove("is-valid", "is-invalid");

    if (isValid) {
      if (field.value.trim()) {
        field.classList.add("is-valid");
      }
    } else {
      field.classList.add("is-invalid");
    }
  },

  /**
   * Clear modern field validation
   */
  clearModernValidation: function (field) {
    field.classList.remove("is-valid", "is-invalid");
    field.setCustomValidity("");
  },

  /**
   * Handle modern form submission
   */
  handleModernFormSubmit: function (event, config) {
    const form = event.target;
    let isFormValid = true;

    const fields = form.querySelectorAll(
      ".modern-input, .modern-textarea, .modern-select"
    );
    fields.forEach((field) => {
      if (!this.validateModernField(field, config)) {
        isFormValid = false;
      }
    });

    form.classList.add("was-validated");

    if (!isFormValid) {
      event.preventDefault();
      event.stopPropagation();

      const firstInvalid = form.querySelector(".is-invalid");
      if (firstInvalid) {
        firstInvalid.focus();
        firstInvalid.scrollIntoView({ behavior: "smooth", block: "center" });
      }

      return false;
    }

    return true;
  },

  /**
   * Setup date/time validation
   */
  setupDateTimeValidation: function (form) {
    const startField = form.querySelector(
      '#startDatetime, [name="start_datetime"]'
    );
    const endField = form.querySelector('#endDatetime, [name="end_datetime"]');

    if (startField && endField) {
      [startField, endField].forEach((field) => {
        field.addEventListener("change", () => {
          this.validateDateTime(startField);
          this.validateDateTime(endField);
        });
      });
    }
  },

  /**
   * Setup location validation
   */
  setupLocationValidation: function (form) {
    const locationField = form.querySelector("#newLocationName");
    if (locationField) {
      locationField.addEventListener("input", () => {
        this.validateModernField(locationField, form._enhancedConfig || {});
      });
    }
  },
};
