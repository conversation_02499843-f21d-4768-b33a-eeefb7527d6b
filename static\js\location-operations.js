/**
 * Location Operations Utility
 *
 * Complete location search and map handling system
 * Based on the working LocationSelector from the old implementation
 *
 * Dependencies: Leaflet.js, flash-messages.js
 *
 */

class LocationOperations {
  constructor() {
    this.state = "INITIAL";
    this.selectedLocation = null;
    this.map = null;
    this.marker = null;
    this.tempCoordinates = null;
    this.lastSuggestedName = "";
    this.searchTimeout = null;
    this.nameValidationTimeout = null;
    this.mapSearchTimeout = null;
    this.elements = {};

    this.init();
  }

  init() {
    try {
      this.initializeElements();
      this.bindEventListeners();
      this.setupMapIcons();
      console.log("LocationOperations initialized successfully");
    } catch (error) {
      console.error("LocationOperations initialization failed:", error);
    }
  }

  /**
   * Initialize all required DOM elements
   */
  initializeElements() {
    this.elements = {
      locationSearch:
        document.getElementById("locationSearch") ||
        document.querySelector("[data-location-search]"),
      searchBtn: document.getElementById("searchLocationBtn"),
      searchResults: document.getElementById("searchResults"),
      resultsList: document.getElementById("resultsList"),
      selectedSection: document.getElementById("selectedLocationSection"),
      locationInput: document.getElementById("location"),
      changeLocationBtn: document.getElementById("changeLocationBtn"),
      editMapLocationBtn: document.getElementById("editMapLocationBtn"),
      mapSection: document.getElementById("mapSection"),
      mapSearchGroup: document.getElementById("mapSearchGroup"),
      mapSearch: document.getElementById("mapSearch"),
      mapSuggestions: document.getElementById("mapSuggestions"),
      coordinatesStatus: document.getElementById("coordinatesStatus"),
      coordinatesText: document.getElementById("coordinatesText"),
      newLocationNameGroup: document.getElementById("newLocationNameGroup"),
      newLocationName: document.getElementById("newLocationName"),
      latitudeInput: document.getElementById("latitude"),
      longitudeInput: document.getElementById("longitude"),
    };

    // Verify critical elements are found
    if (!this.elements.searchBtn || !this.elements.locationSearch) {
      console.warn("Some critical location search elements not found");
    }
  }

  /**
   * Setup map icons
   */
  setupMapIcons() {
    if (typeof L !== "undefined") {
      this.redIcon = L.icon({
        iconUrl:
          "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png",
        shadowUrl:
          "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41],
      });
    }
  }

  /**
   * Bind event listeners for location operations
   */
  bindEventListeners() {
    // Search functionality
    if (this.elements.searchBtn) {
      this.elements.searchBtn.addEventListener("click", () => {
        this.performSearch();
      });
    }

    if (this.elements.locationSearch) {
      this.elements.locationSearch.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
          e.preventDefault();
          e.stopPropagation();
          this.performSearch();
        }
      });
    }

    // Location management
    if (this.elements.changeLocationBtn) {
      this.elements.changeLocationBtn.addEventListener("click", () =>
        this.changeLocation()
      );
    }

    if (this.elements.editMapLocationBtn) {
      this.elements.editMapLocationBtn.addEventListener("click", () =>
        this.editMapLocation()
      );
    }

    // New location creation
    if (this.elements.newLocationName) {
      this.elements.newLocationName.addEventListener("input", () =>
        this.debounceNameValidation()
      );
    }

    // Map search
    if (this.elements.mapSearch) {
      this.elements.mapSearch.addEventListener("input", () =>
        this.debounceMapSearch()
      );
    }

    // Map search button
    const mapSearchBtn = document.getElementById("mapSearchBtn");
    if (mapSearchBtn) {
      mapSearchBtn.addEventListener("click", () => this.performMapSearch());
    }

    // Click outside to hide suggestions
    document.addEventListener("click", (e) => this.handleClickOutside(e));
  }

  /**
   * Perform location search (triggered by search button click)
   */
  async performSearch() {
    const query = this.elements.locationSearch?.value.trim();

    if (!query) {
      this.showError("Please enter a location name or address to search.");
      return;
    }

    if (query.length < 3) {
      this.showError("Please enter at least 3 characters to search for locations.");
      return;
    }

    // Show loading state
    this.showSearchLoading(true);

    try {
      // Search both database and map
      const [dbResults, mapResults] = await Promise.all([
        this.searchDatabase(query),
        this.searchMap(query),
      ]);

      this.displaySearchResults(dbResults, mapResults);
      this.setState("SEARCHING");
    } catch (error) {
      console.error("Search error:", error);
      this.showError(
        "Search failed. Please check your connection and try again."
      );
    } finally {
      this.showSearchLoading(false);
    }
  }

  /**
   * Search database for locations
   */
  async searchDatabase(query) {
    const response = await fetch(
      `/location/search?query=${encodeURIComponent(query)}`
    );
    if (!response.ok) throw new Error("Database search failed");
    return await response.json();
  }

  /**
   * Search map for locations
   */
  async searchMap(query) {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
        query
      )}&limit=1&addressdetails=1`
    );
    if (!response.ok) throw new Error("Map search failed");
    return await response.json();
  }

  /**
   * Show/hide search loading state
   */
  showSearchLoading(isLoading) {
    const searchBtn = this.elements.searchBtn;
    const searchInput = this.elements.locationSearch;

    if (isLoading) {
      // Show loading state
      if (searchBtn) {
        searchBtn.disabled = true;
        searchBtn.innerHTML =
          '<i class="bi bi-hourglass-split me-2"></i>Searching...';
        searchBtn.classList.add("loading");
      }
      if (searchInput) {
        searchInput.disabled = true;
      }

      // Hide any previous results
      if (this.elements.searchResults) {
        this.elements.searchResults.style.display = "none";
      }
    } else {
      // Reset to normal state
      if (searchBtn) {
        searchBtn.disabled = false;
        searchBtn.innerHTML = '<i class="bi bi-search"></i>';
        searchBtn.classList.remove("loading");
      }
      if (searchInput) {
        searchInput.disabled = false;
      }
    }
  }

  /**
   * Debounced map search
   */
  debounceMapSearch() {
    clearTimeout(this.mapSearchTimeout);
    this.mapSearchTimeout = setTimeout(() => this.performMapSearch(), 300);
  }

  /**
   * Perform map search for suggestions
   */
  async performMapSearch() {
    const query = this.elements.mapSearch?.value.trim();
    if (query.length < 3) {
      if (this.elements.mapSuggestions) {
        this.elements.mapSuggestions.style.display = "none";
      }
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          query
        )}&limit=5`
      );
      const results = await response.json();

      this.displayMapSuggestions(results);
    } catch (error) {
      console.error("Map search error:", error);
    }
  }

  /**
   * Display map search suggestions
   */
  displayMapSuggestions(results) {
    if (!this.elements.mapSuggestions) return;

    this.elements.mapSuggestions.innerHTML = "";

    if (results.length === 0) {
      this.elements.mapSuggestions.style.display = "none";
      return;
    }

    results.forEach((place) => {
      const item = document.createElement("div");
      item.className = "suggestion-item";
      item.textContent = place.display_name;
      item.addEventListener("click", () => this.selectMapSuggestion(place));
      this.elements.mapSuggestions.appendChild(item);
    });

    // Show the suggestions dropdown
    this.elements.mapSuggestions.style.display = "block";
  }

  /**
   * Select a map suggestion
   */
  selectMapSuggestion(place) {
    const lat = parseFloat(place.lat);
    const lng = parseFloat(place.lon);

    // Update map view and marker
    if (this.map && this.marker) {
      this.map.setView([lat, lng], 13);
      this.marker.setLatLng([lat, lng]);
    }

    // Update coordinates
    this.tempCoordinates = { lat, lng };

    // Update search field and hide suggestions
    if (this.elements.mapSearch) {
      this.elements.mapSearch.value = place.display_name;
    }
    if (this.elements.mapSuggestions) {
      this.elements.mapSuggestions.style.display = "none";
    }

    // Update coordinates status (hidden from users)
    if (this.elements.coordinatesText) {
      this.elements.coordinatesText.textContent = "Location coordinates set";
    }

    // Enable save button since coordinates are now selected
    this.validateNewLocationForm();
  }

  /**
   * Display search results in the search results section
   */
  displaySearchResults(dbResults, mapResults) {
    if (!this.elements.resultsList || !this.elements.searchResults) {
      console.error("Search results elements not found");
      return;
    }

    this.elements.resultsList.innerHTML = "";

    // Check if both result arrays are empty
    const hasDbResults = dbResults && dbResults.length > 0;
    const hasMapResults = mapResults && mapResults.length > 0;

    if (!hasDbResults && !hasMapResults) {
      // Show "no results found" message
      const noResultsItem = document.createElement("div");
      noResultsItem.className = "no-results-message p-3 text-center text-muted";
      noResultsItem.innerHTML = `
        <div class="mb-2">
          <i class="bi bi-search fs-4"></i>
        </div>
        <p class="mb-1 fw-medium">No locations found</p>
        <small>Try a different search <br> term or check your spelling</small>
      `;
      this.elements.resultsList.appendChild(noResultsItem);
      this.elements.searchResults.style.display = "block";
      return;
    }

    // Add database results
    dbResults.forEach((location) => {
      const item = this.createResultItem(
        location.name,
        "Existing Location",
        "bi-database",
        "existing",
        () => this.selectExistingLocation(location.name)
      );
      this.elements.resultsList.appendChild(item);
    });

    // Add map results (limited to most relevant result for better UX)
    mapResults.forEach((place, index) => {
      const cleanName = this.extractLocationName(place.display_name);
      const item = this.createResultItem(
        cleanName,
        index === 0 ? "New Location (Most Relevant)" : "New Location",
        "bi-geo-alt",
        "new",
        () => this.selectNewLocation(place, cleanName)
      );
      this.elements.resultsList.appendChild(item);
    });

    this.elements.searchResults.style.display = "block";
  }

  /**
   * Create result item for search results
   */
  createResultItem(name, type, icon, category, onClick) {
    const item = document.createElement("div");
    item.className = "result-item";
    item.innerHTML = `
            <div class="result-info">
                <div class="result-icon">
                    <i class="bi ${icon}"></i>
                </div>
                <div class="result-details">
                    <h6>${name}</h6>
                    <small>${type}</small>
                </div>
            </div>
            <span class="result-type">${category}</span>
        `;
    item.addEventListener("click", onClick);
    return item;
  }

  /**
   * Extract clean location name from display name
   */
  extractLocationName(displayName) {
    // Extract the first part before the first comma for cleaner names
    return displayName.split(",")[0].trim();
  }

  /**
   * Select existing location from database
   */
  async selectExistingLocation(locationName) {
    try {
      this.showLoading(this.elements.selectedSection);

      // Get coordinates for existing location
      const response = await fetch(
        `/api/location-coords?name=${encodeURIComponent(locationName)}`
      );
      const data = await response.json();

      this.selectedLocation = {
        name: locationName,
        type: "existing",
        lat: data.lat,
        lng: data.lng,
      };

      this.updateLocationDisplay();
      this.initializeMap();
      this.hideSearchSection();
      this.setState("SELECTED_EXISTING");
    } catch (error) {
      console.error("Error selecting existing location:", error);
      this.showError("Failed to load location details.");
    } finally {
      this.hideLoading(this.elements.selectedSection);
    }
  }

  /**
   * Select new location from map search
   */
  selectNewLocation(place, suggestedName) {
    this.tempCoordinates = {
      lat: parseFloat(place.lat),
      lng: parseFloat(place.lon),
      mapDisplayName: place.display_name,
    };

    // Show map for new location creation
    if (this.elements.newLocationName) {
      this.elements.newLocationName.value = suggestedName;
    }

    this.initializeMapForNewLocation();
    this.hideSearchSection();
    this.setState("CREATING_NEW");

    // Enhanced UX: Auto-focus on map and prepopulate map search field
    setTimeout(() => {
      // Prepopulate the map search field with the selected location
      if (this.elements.mapSearch) {
        this.elements.mapSearch.value = place.display_name;
        console.log(
          `✨ Auto-populated map search field: ${place.display_name}`
        );
      }

      // Auto-focus on the map section by scrolling to it
      if (this.elements.mapSection) {
        this.elements.mapSection.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        console.log("Auto-focused on map section for better UX");
      }

      // Validate the form since we now have coordinates
      this.validateNewLocationForm();
    }, 200); // Small delay to ensure map is initialized
  }

  /**
   * Update location display after selection
   */
  updateLocationDisplay() {
    if (this.elements.locationInput) {
      this.elements.locationInput.value = this.selectedLocation.name;
    }
    if (this.elements.latitudeInput) {
      this.elements.latitudeInput.value = this.selectedLocation.lat;
    }
    if (this.elements.longitudeInput) {
      this.elements.longitudeInput.value = this.selectedLocation.lng;
    }

    if (this.elements.selectedSection) {
      this.elements.selectedSection.style.display = "block";
    }
    if (this.elements.searchResults) {
      this.elements.searchResults.style.display = "none";
    }

    // Show edit map button for existing locations
    if (
      this.selectedLocation.type === "existing" &&
      this.elements.editMapLocationBtn
    ) {
      this.elements.editMapLocationBtn.style.display = "inline-block";
    }
  }

  /**
   * Initialize map for existing location (preview only)
   */
  initializeMap() {
    if (!this.selectedLocation?.lat || !this.selectedLocation?.lng) return;

    // Show only the map section for existing locations (no search interface)
    if (this.elements.mapSection) {
      this.elements.mapSection.style.display = "block";
    }

    // Hide map search and new location creation fields for existing locations
    if (this.elements.mapSearchGroup) {
      this.elements.mapSearchGroup.style.display = "none";
    }
    if (this.elements.newLocationNameGroup) {
      this.elements.newLocationNameGroup.style.display = "none";
    }
    if (this.elements.coordinatesStatus) {
      this.elements.coordinatesStatus.style.display = "none";
    }

    // Remove existing map
    if (this.map) {
      this.map.remove();
    }

    // Create new map for preview only (non-interactive)
    this.map = L.map("map").setView(
      [this.selectedLocation.lat, this.selectedLocation.lng],
      13
    );

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(this.map);

    // Add non-draggable marker for existing locations (preview only)
    this.marker = L.marker([
      this.selectedLocation.lat,
      this.selectedLocation.lng,
    ])
      .addTo(this.map)
      .bindPopup(this.selectedLocation.name);

    // Invalidate size after modal is shown
    setTimeout(() => this.map.invalidateSize(), 100);
  }

  /**
   * Initialize map for new location creation (interactive)
   */
  initializeMapForNewLocation() {
    if (this.elements.mapSection) {
      this.elements.mapSection.style.display = "block";
    }
    if (this.elements.mapSearchGroup) {
      this.elements.mapSearchGroup.style.display = "block";
    }
    if (this.elements.newLocationNameGroup) {
      this.elements.newLocationNameGroup.style.display = "block";
    }
    if (this.elements.selectedSection) {
      this.elements.selectedSection.style.display = "none";
    }
    if (this.elements.searchResults) {
      this.elements.searchResults.style.display = "none";
    }

    // Remove existing map
    if (this.map) {
      this.map.remove();
    }

    // Create new map
    const lat = this.tempCoordinates.lat;
    const lng = this.tempCoordinates.lng;

    this.map = L.map("map").setView([lat, lng], 13);

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(this.map);

    // Add draggable marker
    this.marker = L.marker([lat, lng], { draggable: true })
      .addTo(this.map)
      .bindPopup("New Location");

    // Update coordinates when marker is moved
    this.marker.on("dragend", (e) => {
      const position = e.target.getLatLng();
      this.tempCoordinates.lat = position.lat;
      this.tempCoordinates.lng = position.lng;

      // Coordinates are set but hidden from users (for internal use only)
      if (this.elements.coordinatesText) {
        this.elements.coordinatesText.textContent = "Location coordinates set";
      }

      // Perform reverse geocoding to update search field
      this.reverseGeocode(position.lat, position.lng);
      this.validateNewLocationForm();
    });

    // Update marker when map is clicked
    this.map.on("click", (e) => {
      this.marker.setLatLng(e.latlng);
      this.tempCoordinates.lat = e.latlng.lat;
      this.tempCoordinates.lng = e.latlng.lng;

      // Coordinates are set but hidden from users (for internal use only)
      if (this.elements.coordinatesText) {
        this.elements.coordinatesText.textContent = "Location coordinates set";
      }

      // Perform reverse geocoding to update search field
      this.reverseGeocode(e.latlng.lat, e.latlng.lng);
      this.validateNewLocationForm();
    });

    setTimeout(() => this.map.invalidateSize(), 100);
  }

  /**
   * Validate new location form
   */
  validateNewLocationForm() {
    const name = this.elements.newLocationName?.value.trim();
    const hasCoordinates =
      this.tempCoordinates &&
      this.tempCoordinates.lat &&
      this.tempCoordinates.lng;

    // Update form fields when both name and coordinates are available
    if (name && hasCoordinates) {
      // Check name uniqueness before populating form fields
      this.checkLocationNameUniqueness(name);
    } else if (!hasCoordinates) {
      // Clear form fields if coordinates missing
      if (this.elements.locationInput) this.elements.locationInput.value = "";
      if (this.elements.latitudeInput) this.elements.latitudeInput.value = "";
      if (this.elements.longitudeInput) this.elements.longitudeInput.value = "";

      const helpText =
        this.elements.newLocationNameGroup?.querySelector(".input-help");
      if (helpText) {
        helpText.innerHTML =
          '<i class="bi bi-exclamation-triangle text-warning"></i> Please search for an address and select coordinates from the map';
        helpText.className = "input-help text-warning";
      }
    } else {
      // Clear form fields if name missing
      if (this.elements.locationInput) this.elements.locationInput.value = "";
      if (this.elements.latitudeInput) this.elements.latitudeInput.value = "";
      if (this.elements.longitudeInput) this.elements.longitudeInput.value = "";

      const helpText =
        this.elements.newLocationNameGroup?.querySelector(".input-help");
      if (helpText) {
        helpText.innerHTML =
          '<i class="bi bi-info-circle"></i> This name will be validated and created when you submit the event';
        helpText.className = "input-help";
      }
    }
  }

  /**
   * Check if location name is unique
   */
  async checkLocationNameUniqueness(name) {
    if (!name || name.length < 2) return;

    try {
      // Add loading indicator
      const helpText =
        this.elements.newLocationNameGroup?.querySelector(".input-help");
      if (helpText) {
        helpText.innerHTML =
          '<i class="bi bi-hourglass-split text-info"></i> Checking name availability...';
        helpText.className = "input-help text-info";
      }

      // Check if location name already exists
      const response = await fetch(
        `/location/search?query=${encodeURIComponent(name)}`
      );
      if (!response.ok) throw new Error("Failed to check location names");

      const existingLocations = await response.json();
      const nameExists = existingLocations.some(
        (loc) => loc.name.toLowerCase() === name.toLowerCase()
      );

      if (nameExists) {
        // Name already exists
        if (this.elements.locationInput) this.elements.locationInput.value = "";
        if (this.elements.latitudeInput) this.elements.latitudeInput.value = "";
        if (this.elements.longitudeInput)
          this.elements.longitudeInput.value = "";

        if (helpText) {
          helpText.innerHTML =
            '<i class="bi bi-exclamation-triangle text-danger"></i> This location name already exists. Please choose a different name.';
          helpText.className = "input-help text-danger";
        }

        // Add visual feedback to input
        if (this.elements.newLocationName) {
          this.elements.newLocationName.classList.add("is-invalid");
        }
      } else {
        // Name is available - populate form fields
        if (this.elements.locationInput)
          this.elements.locationInput.value = name;
        if (this.elements.latitudeInput)
          this.elements.latitudeInput.value = this.tempCoordinates.lat;
        if (this.elements.longitudeInput)
          this.elements.longitudeInput.value = this.tempCoordinates.lng;

        if (helpText) {
          helpText.innerHTML =
            '<i class="bi bi-check-circle text-success"></i> Location name is available! Ready to create event.';
          helpText.className = "input-help text-success";
        }

        // Remove invalid styling
        if (this.elements.newLocationName) {
          this.elements.newLocationName.classList.remove("is-invalid");
          this.elements.newLocationName.classList.add("is-valid");
        }
      }
    } catch (error) {
      console.error("Error checking location name:", error);
      const helpText =
        this.elements.newLocationNameGroup?.querySelector(".input-help");
      if (helpText) {
        helpText.innerHTML =
          '<i class="bi bi-exclamation-triangle text-warning"></i> Could not verify name availability. Will check when creating event.';
        helpText.className = "input-help text-warning";
      }
    }
  }

  /**
   * Reverse geocode coordinates to address
   */
  async reverseGeocode(lat, lng) {
    try {
      // Show loading indicator in search field
      if (this.elements.mapSearch) {
        this.elements.mapSearch.placeholder = "Loading address...";
        this.elements.mapSearch.disabled = true;
      }

      // Call Nominatim reverse geocoding API
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`
      );

      if (!response.ok) {
        throw new Error("Reverse geocoding failed");
      }

      const data = await response.json();

      if (data && data.display_name) {
        // Update the search field with the new address
        if (this.elements.mapSearch) {
          this.elements.mapSearch.value = data.display_name;
        }

        // Extract a clean name for the location
        const cleanName = this.extractLocationName(data.display_name);

        // Update the new location name field if it's empty or contains the old suggested name
        const currentName = this.elements.newLocationName?.value.trim();
        if (
          !currentName ||
          currentName.includes("(Custom)") ||
          currentName === this.lastSuggestedName
        ) {
          if (this.elements.newLocationName) {
            this.elements.newLocationName.value = cleanName;
            this.lastSuggestedName = cleanName;

            // Trigger validation for the new name
            this.validateNewLocationForm();
          }
        }
      }
    } catch (error) {
      console.error("Reverse geocoding error:", error);
    } finally {
      // Reset search field state
      if (this.elements.mapSearch) {
        this.elements.mapSearch.disabled = false;
        this.elements.mapSearch.placeholder = "Search for address...";
      }
    }
  }

  /**
   * Change location (reset to search)
   */
  changeLocation() {
    // Reset all location-related state
    this.selectedLocation = null;
    this.tempCoordinates = null;

    // Hide all location display sections
    if (this.elements.selectedSection)
      this.elements.selectedSection.style.display = "none";
    if (this.elements.mapSection)
      this.elements.mapSection.style.display = "none";
    if (this.elements.mapSearchGroup)
      this.elements.mapSearchGroup.style.display = "none";
    if (this.elements.coordinatesStatus)
      this.elements.coordinatesStatus.style.display = "none";
    if (this.elements.newLocationNameGroup)
      this.elements.newLocationNameGroup.style.display = "none";

    // Clear form inputs
    if (this.elements.locationInput) this.elements.locationInput.value = "";
    if (this.elements.latitudeInput) this.elements.latitudeInput.value = "";
    if (this.elements.longitudeInput) this.elements.longitudeInput.value = "";

    // Reset map search placeholder
    if (this.elements.mapSearch) {
      this.elements.mapSearch.placeholder = "Search for address...";
      this.elements.mapSearch.value = "";
    }

    // Clean up map if it exists
    if (this.map) {
      this.map.remove();
      this.map = null;
    }

    // Show search section and focus on input
    this.showSearchSection();

    // Return to initial state
    this.setState("INITIAL");
  }

  /**
   * Edit map location for existing location
   */
  editMapLocation() {
    if (this.selectedLocation?.type === "existing") {
      // Initialize temp coordinates with current location as starting point
      this.tempCoordinates = {
        lat: this.selectedLocation.lat,
        lng: this.selectedLocation.lng,
        mapDisplayName: `New location based on ${this.selectedLocation.name}`,
      };

      // Suggest a name based on the current location
      const suggestedName = `${this.selectedLocation.name} (Custom)`;

      // Show new location creation interface
      if (this.elements.newLocationName) {
        this.elements.newLocationName.value = suggestedName;
      }

      this.initializeMapForNewLocation();
      this.hideSearchSection();
      this.setState("CREATING_NEW");

      // Enhanced UX: Auto-focus on map search field and prepopulate with current location
      setTimeout(() => {
        // Prepopulate the map search field with the current location name
        if (this.elements.mapSearch) {
          this.elements.mapSearch.value = this.selectedLocation.name;
          console.log(
            `✨ Auto-populated map search field with current location: ${this.selectedLocation.name}`
          );

          // Auto-focus on the map search field for immediate editing
          this.elements.mapSearch.focus();
          this.elements.mapSearch.select(); // Select all text for easy replacement
          console.log("Auto-focused on map search field for better UX");
        }

        // Auto-scroll to the map search section
        if (this.elements.mapSearchGroup) {
          this.elements.mapSearchGroup.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          console.log("Auto-scrolled to map search section");
        }

        // Validate the form since we now have coordinates
        this.validateNewLocationForm();
      }, 300); // Small delay to ensure map and UI are initialized
    }
  }

  /**
   * Debounce name validation
   */
  debounceNameValidation() {
    clearTimeout(this.nameValidationTimeout);
    this.nameValidationTimeout = setTimeout(
      () => this.validateNewLocationForm(),
      500
    );
  }

  /**
   * Handle click outside to hide suggestions
   */
  handleClickOutside(e) {
    // Hide map suggestions if clicking outside
    if (
      this.elements.mapSearchGroup &&
      !this.elements.mapSearchGroup.contains(e.target)
    ) {
      if (this.elements.mapSuggestions) {
        this.elements.mapSuggestions.style.display = "none";
      }
    }
  }

  /**
   * Hide search section
   */
  hideSearchSection() {
    // Hide the search section and results
    if (this.elements.searchResults) {
      this.elements.searchResults.style.display = "none";
    }

    // Clear search input for next use
    if (this.elements.locationSearch) {
      this.elements.locationSearch.value = "";
    }

    // Reset search button state
    this.showSearchLoading(false);
  }

  /**
   * Show search section
   */
  showSearchSection() {
    // Show search section and focus on input
    if (this.elements.locationSearch) {
      this.elements.locationSearch.focus();
    }

    // Clear any existing search results
    if (this.elements.searchResults) {
      this.elements.searchResults.style.display = "none";
    }
    if (this.elements.resultsList) {
      this.elements.resultsList.innerHTML = "";
    }

    // Ensure search button is ready
    this.showSearchLoading(false);
  }

  /**
   * Helper methods for UI state management
   */
  showLoading(element) {
    if (element) {
      element.classList.add("loading");
    }
  }

  hideLoading(element) {
    if (element) {
      element.classList.remove("loading");
    }
  }

  showError(message) {
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(message, "danger");
    } else {
      console.error(message);
      alert(message);
    }
  }

  setState(state) {
    console.log(`LocationOperations: ${this.state} -> ${state}`);
    this.state = state;
  }
  /**
   * Static method to initialize location operations
   * Can be called from other modules
   */
  static initialize() {
    if (!window.locationOperations) {
      window.locationOperations = new LocationOperations();
    }
    return window.locationOperations;
  }
}

// Don't initialize automatically on DOM load - wait for modal to be shown
// document.addEventListener("DOMContentLoaded", () => {
//   LocationOperations.initialize();
// });

// Global initialization function for backward compatibility
window.initializeLocationSelector = function () {
  try {
    const instance = LocationOperations.initialize();
    return instance;
  } catch (error) {
    console.error("Error initializing location selector:", error);
    throw error;
  }
};

// Export for module usage
if (typeof module !== "undefined" && module.exports) {
  module.exports = LocationOperations;
}
