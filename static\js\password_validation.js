/**
 * Set up password confirmation validation by comparing password fields
 * @param {string} passwordId - The ID of the primary password field
 * @param {string} confirmPasswordId - The ID of the confirmation password field
 */
function setupPasswordConfirmation(passwordId, confirmPasswordId) {
  const confirmPasswordInput = document.getElementById(confirmPasswordId);
  const passwordInput = document.getElementById(passwordId);

  if (confirmPasswordInput && passwordInput) {
    confirmPasswordInput.addEventListener("input", function () {
      const password = passwordInput.value;
      const confirmPassword = this.value;

      if (confirmPassword !== password) {
        this.setCustomValidity("Passwords do not match");
        const feedbackElement =
          this.parentElement.querySelector(".invalid-feedback");
        if (feedbackElement) {
          feedbackElement.textContent = "Passwords must match";
        }
      } else {
        this.setCustomValidity("");
      }
    });

    // Also check when the primary password changes
    passwordInput.addEventListener("input", function () {
      const confirmPassword = confirmPasswordInput.value;
      // Only validate if confirmation field has a value
      if (confirmPassword) {
        if (this.value !== confirmPassword) {
          confirmPasswordInput.setCustomValidity("Passwords do not match");
          const feedbackElement =
            confirmPasswordInput.parentElement.querySelector(
              ".invalid-feedback"
            );
          if (feedbackElement) {
            feedbackElement.textContent = "Passwords must match";
          }
        } else {
          confirmPasswordInput.setCustomValidity("");
        }
      }
    });
  }
}

/**
 * Set up validation for new password to ensure it differs from current password
 * @param {string} currentPasswordId - The ID of the current password field
 * @param {string} newPasswordId - The ID of the new password field
 */
function setupNewPasswordValidation(currentPasswordId, newPasswordId) {
  const newPasswordInput = document.getElementById(newPasswordId);
  const currentPasswordInput = document.getElementById(currentPasswordId);

  if (newPasswordInput && currentPasswordInput) {
    // Set initial error messages
    const currentPasswordFeedback =
      currentPasswordInput.parentElement.querySelector(".invalid-feedback");
    if (currentPasswordFeedback) {
      currentPasswordFeedback.textContent =
        "Please enter your current password";
    }

    currentPasswordInput.addEventListener("input", function () {
      if (!this.value) {
        const feedbackElement =
          this.parentElement.querySelector(".invalid-feedback");
        if (feedbackElement) {
          feedbackElement.textContent = "Please enter your current password";
        }
      }
    });

    newPasswordInput.addEventListener("input", function () {
      const currentPassword = currentPasswordInput.value;
      const newPassword = this.value;

      if (newPassword === currentPassword && newPassword) {
        this.setCustomValidity(
          "New password must be different from current password"
        );
        const feedbackElement =
          this.parentElement.querySelector(".invalid-feedback");
        if (feedbackElement) {
          feedbackElement.textContent =
            "New password must be different from current password";
        }
      } else {
        // Use the standard password validation
        validatePassword(this);
      }
    });
  }
}
