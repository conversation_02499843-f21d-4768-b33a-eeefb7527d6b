{% set activities_tab = activities_tab or request.args.get('activities_tab',
'journeys') %}
<style>
  .activities-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  .activities-tabs .btn {
    border: none !important;
    border-radius: 999px !important;
    background: #f4f6fb;
    color: #4e6bff;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.7rem 2.2rem;
    font-size: 1.08rem;
    min-width: 180px;
    max-width: 240px;
    text-align: center;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.15s;
    box-shadow: 0 1px 4px rgba(78, 107, 255, 0.06);
    margin: 0 0.25rem 0.5rem 0.25rem;
  }

  .activities-tabs .btn span,
  .activities-tabs .btn i {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .activities-tabs .btn.active,
  .activities-tabs .btn:focus {
    background: #4e6bff;
    color: #fff !important;
  }

  .activities-tabs .btn:active {
    background: #4e6bff;
  }

  @media (max-width: 900px) {
    .activities-tabs {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .activities-tabs .btn {
      min-width: 45%;
      max-width: 100%;
      font-size: 0.98rem;
      padding: 0.6rem 0.5rem;
      margin: 0.25rem 0;
    }
  }

  @media (max-width: 600px) {
    .activities-tabs .btn {
      min-width: 100%;
      font-size: 0.92rem;
      padding: 0.5rem 0.2rem;
    }
  }

  /* Discovery journey card style */
  .journey-card {
    height: 380px;
    border-radius: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    border: 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    background: #fff;
  }

  .journey-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .journey-image {
    height: 180px;
    border-radius: 15px 15px 0 0;
    overflow: hidden;
  }

  .journey-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .journey-dates {
    margin-bottom: 8px;
  }

  .journey-title {
    font-weight: bold;
    font-size: 1.1rem;
    line-height: 1.3;
    margin-bottom: 6px;
    max-height: 2.7rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .journey-description {
    color: #6c757d;
    font-size: 0.8rem;
    line-height: 1.5;
    max-height: 3.8rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  .journey-author {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  .author-avatar {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }

  .author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .author-info {
    margin-left: 8px;
  }

  .author-label {
    color: #6c757d;
    line-height: 1;
  }

  .author-name {
    font-weight: 500;
  }
</style>

<div class="container">
  <div class="activities-tabs mb-4">
    <a
      href="{{ url_for('account.get_profile', active_tab='activities', activities_tab='journeys') }}"
      class="btn{% if activities_tab == 'journeys' %} active{% endif %}"
    >
      <i class="bi bi-map"></i> <span>Public Journeys</span>
    </a>
    <a
      href="{{ url_for('account.get_profile', active_tab='activities', activities_tab='places') }}"
      class="btn{% if activities_tab == 'places' %} active{% endif %}"
    >
      <i class="bi bi-geo-alt"></i> <span>Visited Places</span>
    </a>
    <a
      href="{{ url_for('account.get_profile', active_tab='activities', activities_tab='likes') }}"
      class="btn{% if activities_tab == 'likes' %} active{% endif %}"
    >
      <i class="bi bi-heart"></i> <span>Recent Likes</span>
    </a>
    <a
      href="{{ url_for('account.get_profile', active_tab='activities', activities_tab='comments') }}"
      class="btn{% if activities_tab == 'comments' %} active{% endif %}"
    >
      <i class="bi bi-chat"></i> <span>Recent Comments</span>
    </a>
  </div>

  <div class="tab-content p-3" id="activitiesTabContent">
    {% if activities_tab == 'journeys' %} {% if account.public_journeys %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-5">
      {% for journey in account.public_journeys %}
      <div class="col">
        <div class="card h-100 journey-card border-0 shadow-sm">
          <!-- Card Image -->
          <div class="position-relative journey-image">
            {% if journey.cover_image %}
            <img
              src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
              alt="{{ journey.title }}"
              class="card-img-top"
              style="object-fit: cover; height: 100%; width: 100%"
            />
            {% elif journey.event_image %}
            <img
              src="{{ url_for('static', filename='uploads/event_images/' + journey.event_image) }}"
              alt="{{ journey.title }}"
              class="card-img-top"
              style="object-fit: cover; height: 100%; width: 100%"
              onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'"
            />
            {% elif journey.image_url %}
            <img
              src="{{ journey.image_url }}"
              alt="{{ journey.title }}"
              class="card-img-top"
              style="object-fit: cover; height: 100%; width: 100%"
              onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'"
            />
            {% else %}
            <img
              src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
              alt="Journey placeholder"
              class="card-img-top"
              style="object-fit: cover; height: 100%; width: 100%"
            />
            {% endif %}
          </div>

          <!-- Card Body -->
          <div class="card-body d-flex flex-column">
            <!-- Date information -->
            <div class="d-flex journey-dates">
              <div class="small text-muted me-auto">
                <i class="bi bi-calendar3 me-1"></i>
                <span>{{ journey.start_date|date }}</span>
              </div>
            </div>

            <!-- Title and Description -->
            <h4 class="card-title journey-title">{{ journey.title }}</h4>
            <p class="card-text journey-description">
              {{ journey.description|truncate(80) }}
            </p>

            <!-- Author Info -->
            <div class="mt-auto journey-author">
              <div class="d-flex align-items-center">
                <div class="rounded-circle overflow-hidden author-avatar">
                  {% if journey.profile_image %}
                  <img
                    src="{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}"
                    alt="{{ journey.username }}"
                    class="img-fluid"
                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'"
                  />
                  {% else %}
                  <img
                    src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                    alt="{{ journey.username }}"
                    class="img-fluid"
                  />
                  {% endif %}
                </div>
                <div class="author-info">
                  <span class="text-muted small d-block author-label"
                    >Written by</span
                  >
                  <p class="mb-0 fw-medium small author-name">
                    {{ journey.username }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Clickable link over the whole card -->
            <a
              href="{{ url_for('journey.get_private_journey', journey_id=journey.id) }}"
              class="stretched-link"
              aria-label="View {{ journey.title }}"
            ></a>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div
      class="alert"
      style="
        background-color: rgba(78, 107, 255, 0.1);
        color: #4e6bff;
        border: 1px solid rgba(78, 107, 255, 0.2);
        border-radius: 8px;
      "
    >
      <i class="bi bi-info-circle me-2"></i> You don't have any public journeys
      yet.
    </div>
    {% endif %} {% elif activities_tab == 'places' %} {% if
    account.visited_places %}
    <div
      class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4 location-container"
    >
      <div
        id="map"
        style="height: 500px; width: 100%; border-radius: 8px"
      ></div>
    </div>
    {% else %}
    <div
      class="alert"
      style="
        background-color: rgba(78, 107, 255, 0.1);
        color: #4e6bff;
        border: 1px solid rgba(78, 107, 255, 0.2);
        border-radius: 8px;
      "
    >
      <i class="bi bi-info-circle me-2"></i> No visited places found.
      <p class="small text-muted mt-2 mb-0">
        Create journeys with events to see your visited places here.
      </p>
    </div>
    {% endif %} {% elif activities_tab == 'likes' %} {% if account.recent_likes
    %}
    <div class="list-group">
      {% for like in account.recent_likes %}
      <a
        href="{{ url_for('event.get_event_details', event_id=like.event_id) }}"
        class="list-group-item list-group-item-action border-0 rounded-3 mb-2 shadow-sm p-4"
      >
        <div class="d-flex w-100 justify-content-between align-items-center">
          <div class="d-flex flex-column align-items-start gap-2">
            {% if like.like_type == 'comment' %}
            <span class="badge bg-info-subtle text-dark px-3 py-2 rounded-pill"
              ><i class="bi bi-chat-quote"></i> Liked Comment</span
            >
            {% else %}
            <span
              class="badge bg-danger-subtle text-dark px-3 py-2 rounded-pill"
              ><i class="bi bi-heart-fill"></i> Liked Event</span
            >
            {% endif %}
            <h5 class="mb-0">{{ like.title }}</h5>
          </div>
          <small class="d-none d-sm-inline">{{ like.liked_at|datetime }}</small>
        </div>
        {% if like.like_type == 'comment' %}
        <div class="mt-2 p-2 rounded bg-light border">
          <span class="text-muted small"
            >Comment by <b>{{ like.comment_username }}</b>:</span
          >
          <div class="fw-medium">{{ like.comment_content }}</div>
        </div>
        {% endif %}
        <div class="mt-2 d-flex flex-column align-items-start">
          <span class="text-muted small">{{ like.journey_title }}</span>
          <span class="d-flex align-items-center gap-2 mt-2">
            <span
              class="rounded-circle overflow-hidden align-self-center"
              style="width: 24px; height: 24px"
            >
              {% if like.journey_profile_image %}
              <img
                src="{{ url_for('static', filename='uploads/profile_images/' + like.journey_profile_image) }}"
                alt="{{ like.journey_username }}"
                class="img-fluid"
                style="
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  display: block;
                "
                onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'"
              />
              {% else %}
              <img
                src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                alt="{{ like.journey_username }}"
                class="img-fluid"
                style="
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  display: block;
                "
              />
              {% endif %}
            </span>
            <span class="align-self-center">{{ like.journey_username }}</span>
          </span>
        </div>
      </a>
      {% endfor %}
    </div>
    {% else %}
    <div
      class="alert"
      style="
        background-color: rgba(78, 107, 255, 0.1);
        color: #4e6bff;
        border: 1px solid rgba(78, 107, 255, 0.2);
        border-radius: 8px;
      "
    >
      <i class="bi bi-info-circle me-2"></i> You haven't liked any events or
      comments yet.
    </div>
    {% endif %} {% elif activities_tab == 'comments' %} {% if
    account.recent_comments %}
    <div class="list-group">
      {% for comment in account.recent_comments %}
      <a
        href="{{ url_for('event.get_event_details', event_id=comment.event_id) }}"
        class="list-group-item list-group-item-action border-0 rounded-3 mb-2 shadow-sm p-4"
      >
        <div class="d-flex w-100 justify-content-between">
          <h5 class="mb-1">{{ comment.event_title }}</h5>
          <small class="d-none d-sm-inline"
            >{{ comment.created_at|datetime }}</small
          >
        </div>
        <p class="mb-1">{{ comment.content }}</p>
        <small
          ><i class="bi bi-chat-fill me-1"></i>{{ comment.like_count }} like{%
          if comment.like_count != 1 %}s{% endif %}</small
        >
      </a>
      {% endfor %}
    </div>
    {% else %}
    <div
      class="alert"
      style="
        background-color: rgba(78, 107, 255, 0.1);
        color: #4e6bff;
        border: 1px solid rgba(78, 107, 255, 0.2);
        border-radius: 8px;
      "
    >
      <i class="bi bi-info-circle me-2"></i> You haven't commented on any events
      yet.
    </div>
    {% endif %} {% endif %}
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const unsortedLocations = {{ account['events'] | tojson | safe }};

    if (!unsortedLocations || unsortedLocations.length === 0) {
      console.warn("No valid locations provided.");
      return;
    }

    if (typeof L === 'undefined') {
      console.error("Leaflet library (L) is not loaded!");
      return;
    }

    const locations = unsortedLocations.sort((a, b) => {
      return new Date(a.event_start_datetime) - new Date(b.event_start_datetime);
    });

    const firstLat = parseFloat(locations[0].latitude);
    const firstLng = parseFloat(locations[0].longitude);

    const mymap = L.map('map').setView([firstLat, firstLng], 10);
    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
      maxZoom: 19,
      attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
    }).addTo(mymap);

    // Custom icon example (can be removed if unused)
    const redIcon = L.icon({
      iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    });

    let pathPoints = [];

    // Group locations by lat/lng
    const locationMap = locations.reduce((acc, loc) => {
      const key = `${loc.latitude},${loc.longitude}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(loc);
      return acc;
    }, {});

    Object.entries(locationMap).forEach(([coordKey, locsAtSamePlace]) => {
  const [lat, lng] = coordKey.split(',').map(parseFloat);
  pathPoints.push([lat, lng]);

  // Add red marker
  const marker = L.marker([lat, lng], {
    icon: redIcon
  }).addTo(mymap);

  const popupContent = `
      <div class="location-label-bubble">
        <strong>${locsAtSamePlace[0].location_name}</strong><br>
        Visits: ${locsAtSamePlace.length}
      </div>
    `;

    marker.bindPopup(popupContent, {
      closeButton: true,
      autoClose: false,
      closeOnClick: false,
      maxWidth: 200
    }).openPopup();
  });


    if (pathPoints.length > 1) {
      const bounds = L.latLngBounds(pathPoints);
      mymap.fitBounds(bounds, {
        padding: [30, 30]
      });
    }
  });
</script>
