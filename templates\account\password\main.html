{% from "components/password_input.html" import password_input %}

{% block content %}
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-7 col-lg-6">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-body p-4">
          <form method="post" action="{{ url_for('account.change_password') }}" class="needs-validation" novalidate
            id="passwordForm">
            {{ password_input(
            id="current_password",
            name="current_password",
            label="Current Password",
            required=true
            ) }}
            {{ password_input(
            id="new_password",
            name="new_password",
            label="New Password",
            required=true,
            form_text="Your new password should be strong and unique to keep your account secure."
            ) }}

            <!-- Password Strength Bar & Requirements-->
            <div class="password-strength-container mb-3">
              <div class="password-strength-bar">
                <div class="password-strength-fill" id="strengthFill"></div>
              </div>
              <div class="password-strength-label">
                <span class="strength-text" id="strengthText">Enter Password</span>
                <span class="strength-percentage" id="strengthPercentage">0%</span>
              </div>
            </div>
            <div class="requirements-container mb-4">
              <div class="requirements-header">
                <div class="icon-wrapper">
                  <i class="bi bi-shield-check"></i>
                </div>
                <h3 class="title">Password Requirements</h3>
              </div>
              <div class="requirements-grid">
                <div class="requirement-item" id="req-length">
                  <div class="requirement-icon">
                    <i class="bi bi-x"></i>
                  </div>
                  <span>At least 8 characters</span>
                </div>
                <div class="requirement-item" id="req-uppercase">
                  <div class="requirement-icon">
                    <i class="bi bi-x"></i>
                  </div>
                  <span>One uppercase letter (A-Z)</span>
                </div>
                <div class="requirement-item" id="req-lowercase">
                  <div class="requirement-icon">
                    <i class="bi bi-x"></i>
                  </div>
                  <span>One lowercase letter (a-z)</span>
                </div>
                <div class="requirement-item" id="req-number">
                  <div class="requirement-icon">
                    <i class="bi bi-x"></i>
                  </div>
                  <span>One number (0-9)</span>
                </div>
                <div class="requirement-item" id="req-special">
                  <div class="requirement-icon">
                    <i class="bi bi-x"></i>
                  </div>
                  <span>One special character (!@#$%)</span>
                </div>
              </div>
            </div>

            {{ password_input(
            id="confirm_password",
            name="confirm_password",
            label="Confirm New Password",
            required=true
            ) }}

            <div class="d-grid mt-4">
              <button type="submit" class="btn btn-primary py-2">Confirm</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .password-strength-container {
    margin-bottom: 1.5rem;
  }

  .password-strength-bar {
    height: 6px;
    background-color: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
    margin-bottom: 0.75rem;
  }

  .password-strength-fill {
    height: 100%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 3px;
  }

  .strength-very-weak .password-strength-fill {
    width: 20%;
    background: linear-gradient(90deg, #ef4444, #f97316);
  }

  .strength-weak .password-strength-fill {
    width: 40%;
    background: linear-gradient(90deg, #f97316, #eab308);
  }

  .strength-fair .password-strength-fill {
    width: 60%;
    background: linear-gradient(90deg, #eab308, #3b82f6);
  }

  .strength-good .password-strength-fill {
    width: 80%;
    background: linear-gradient(90deg, #3b82f6, #10b981);
  }

  .strength-strong .password-strength-fill {
    width: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
  }

  .password-strength-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 600;
  }

  .strength-text {
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.8rem;
  }

  .strength-percentage {
    color: #718096;
    font-weight: 500;
  }

  .requirements-container {
    background: linear-gradient(135deg, #f8faff 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    position: relative;
  }

  .requirements-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4e6bff, #3b82f6);
    border-radius: 12px 12px 0 0;
  }

  .requirements-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.25rem;
    gap: 0.75rem;
  }

  .requirements-header .icon-wrapper {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #4e6bff, #3b82f6);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
  }

  .requirements-header .title {
    font-weight: 700;
    color: #1a202c;
    font-size: 1rem;
    margin: 0;
  }

  .requirements-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.875rem;
  }

  .requirement-item {
    display: flex;
    align-items: center;
    gap: 0.625rem;
    padding: 0.75rem 1rem;
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    transition: all 0.3s ease;
  }

  .requirement-item.valid {
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
    transform: scale(1.02);
  }

  .requirement-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
  }

  .requirement-item:not(.valid) .requirement-icon {
    background-color: #fed7d7;
    color: #c53030;
  }

  .requirement-item.valid .requirement-icon {
    background-color: #c6f6d5;
    color: #25543e;
  }

  @media (max-width: 576px) {
    .requirements-grid {
      grid-template-columns: 1fr !important;
    }
  }

  .password-strength-container:not(.strength-very-weak):not(.strength-weak):not(.strength-fair):not(.strength-good):not(.strength-strong) .password-strength-fill {
    width: 0% !important;
    background: none !important;
  }
</style>
{% endblock %}

{% block scripts %}
<script>
  window.PASSWORD_REQUIREMENT_IDS = ["new_password"];
</script>
<script src="{{ url_for('static', filename='js/password_toggle.js') }}"></script>
<script src="{{ url_for('static', filename='js/form-validation.js') }}?v=123"></script>
<script src="{{ url_for('static', filename='js/password_validation.js') }}"></script>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    setupPasswordConfirmation('new_password', 'confirm_password');
    setupNewPasswordValidation('current_password', 'new_password');

    // Password strength/requirements UI - NEW PASSWORD ONLY
    function calculatePasswordStrength(password) {
      if (!password) {
        updateRequirementIndicator('req-length', false);
        updateRequirementIndicator('req-uppercase', false);
        updateRequirementIndicator('req-lowercase', false);
        updateRequirementIndicator('req-number', false);
        updateRequirementIndicator('req-special', false);
        updateStrengthIndicator(0, 0);
        return { score: 0, percentage: 0, allRequirementsMet: false };
      }

      let score = 0;
      const checks = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>[\]\\/~`_+=\-]/.test(password)
      };

      // Count basic requirements
      Object.values(checks).forEach(passed => {
        if (passed) score++;
      });

      // Calculate percentage: 5 requirements = 100%
      const percentage = Math.round((score / 5) * 100);

      updateRequirementIndicator('req-length', checks.length);
      updateRequirementIndicator('req-uppercase', checks.uppercase);
      updateRequirementIndicator('req-lowercase', checks.lowercase);
      updateRequirementIndicator('req-number', checks.number);
      updateRequirementIndicator('req-special', checks.special);
      updateStrengthIndicator(score, percentage);

      return { score, percentage, allRequirementsMet: Object.values(checks).every(Boolean) };
    }

    function updateRequirementIndicator(id, isValid) {
      const element = document.getElementById(id);
      const icon = element.querySelector('.requirement-icon i');
      if (isValid) {
        element.classList.add('valid');
        icon.className = 'bi bi-check';
      } else {
        element.classList.remove('valid');
        icon.className = 'bi bi-x';
      }
    }

    function updateStrengthIndicator(score, percentage) {
      const container = document.querySelector('.password-strength-container');
      const textEl = document.getElementById('strengthText');
      const percentageEl = document.getElementById('strengthPercentage');
      const fillEl = document.getElementById('strengthFill');

      container.classList.remove('strength-very-weak', 'strength-weak', 'strength-fair', 'strength-good', 'strength-strong');
      percentageEl.textContent = `${percentage}%`;

      if (score === 0) {
        textEl.textContent = 'Enter Password';
        textEl.style.color = '#718096';
        if (fillEl) {
          fillEl.style.width = '0%';
          fillEl.style.background = 'none';
        }
      } else if (score === 1) {
        container.classList.add('strength-very-weak');
        textEl.textContent = 'Very Weak';
        textEl.style.color = '#ef4444';
        if (fillEl) {
          fillEl.style.width = '';
          fillEl.style.background = '';
        }
      } else if (score === 2) {
        container.classList.add('strength-weak');
        textEl.textContent = 'Weak';
        textEl.style.color = '#f97316';
        if (fillEl) {
          fillEl.style.width = '';
          fillEl.style.background = '';
        }
      } else if (score === 3) {
        container.classList.add('strength-fair');
        textEl.textContent = 'Fair';
        textEl.style.color = '#eab308';
        if (fillEl) {
          fillEl.style.width = '';
          fillEl.style.background = '';
        }
      } else if (score === 4) {
        container.classList.add('strength-good');
        textEl.textContent = 'Good';
        textEl.style.color = '#3b82f6';
        if (fillEl) {
          fillEl.style.width = '';
          fillEl.style.background = '';
        }
      } else if (score === 5) {
        container.classList.add('strength-strong');
        textEl.textContent = 'Strong';
        textEl.style.color = '#10b981';
        if (fillEl) {
          fillEl.style.width = '';
          fillEl.style.background = '';
        }
      }
    }

    // ONLY attach events to new password input
    const newPasswordInput = document.getElementById('new_password');
    if (newPasswordInput) {
      newPasswordInput.addEventListener('input', function () {
        calculatePasswordStrength(this.value);
      });
    }

    // Set initial validation messages
    const currentPasswordFeedback = document.querySelector('#current_password')
      ?.closest('.input-group')?.querySelector('.invalid-feedback');
    if (currentPasswordFeedback) {
      currentPasswordFeedback.textContent = 'Please enter your current password';
    }

    const newPasswordFeedback = document.querySelector('#new_password')
      ?.closest('.input-group')?.querySelector('.invalid-feedback');
    if (newPasswordFeedback) {
      newPasswordFeedback.textContent = 'Please enter your new password';
    }

    const confirmPasswordFeedback = document.querySelector('#confirm_password')
      ?.closest('.input-group')?.querySelector('.invalid-feedback');
    if (confirmPasswordFeedback) {
      confirmPasswordFeedback.textContent = 'Passwords do not match';
    }

    // Initialize with default state
    updateStrengthIndicator(0, 0);
  });
</script>
{% endblock %}