{% from 'components/pagination.html' import render_pagination %}

{% block content %}
<div class="container-fluid px-4">
  <div class="row g-4" id="subscriptionContainer">

    {# Staff Member Alert #}
    {% if is_staff() %}
    <div class="col-12">
      <div class="d-flex alert"
        style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;"
        role="alert">
        <i class="bi bi-shield-lock-fill me-3 fs-4"></i>
        <div>
          <div class="fw-semibold">Staff Access Granted</div>
          <div class="small">As a {{ user_role|capitalize }}, you have access to all premium features without a
            subscription.</div>
        </div>
      </div>
    </div>
    {% endif %}

    {# Current Plan Card #}
    <div class="col-lg-6">
      <div class="card shadow-sm border-0 rounded-3 h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                {% if active_subscription %}
                <div class="rounded-circle bg-warning d-flex align-items-center justify-content-center text-white"
                  style="width: 64px; height: 64px;">
                  <i class="bi bi-trophy fs-3"></i>
                </div>
                {% else %}
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center text-muted"
                  style="width: 64px; height: 64px;">
                  <i class="bi bi-flower1 fs-3"></i>
                </div>
                {% endif %}
              </div>
              <div>
                <h2 class="fs-3 fw-bold mb-1">
                  {% if user_role != 'traveller' %}
                  Premium
                  {% else %}
                  {% if active_subscription and active_subscription.plan_code == 'admin_gift' %}
                  Premium
                  {% elif active_subscription and active_subscription.plan_code == 'free_trial' %}
                  Premium
                  {% elif not active_subscription %}
                  Free Plan
                  {% else %}
                  {{ active_subscription.plan_name or 'Premium' }}
                  {% endif %}
                  {% endif %}
                </h2>
                <div>
                  {% if user_role != 'traveller' %}
                  <span class="badge bg-primary rounded-pill py-1 px-3">{{ user_role|capitalize }}</span>
                  {% else %}
                  {% if active_subscription and active_subscription.plan_code == 'admin_gift' %}
                  <span class="badge bg-dark rounded-pill py-1 px-3">Admin Gift</span>
                  {% elif active_subscription and active_subscription.plan_code == 'free_trial' %}
                  <span class="badge bg-info rounded-pill py-1 px-3">Free Trial</span>
                  {% elif active_subscription %}
                  <span class="badge bg-warning rounded-pill py-1 px-3">Active</span>
                  {% else %}
                  <span class="badge bg-secondary rounded-pill py-1 px-3">Free</span>
                  {% endif %}
                  {% endif %}
                </div>
              </div>
            </div>

            {# Top Up Button #}
            {% if user_role == 'traveller' %}
            <div class="d-flex align-items-center gap-2">
              {% if not active_subscription %}
              {% if not has_had_free_trial and not has_had_premium %}
              <button class="btn btn-primary rounded-pill px-4 py-2 text-white fw-bold shadow-sm" id="adjustPlanBtn"
                style="background:#4e6bff; border: none;">
                Get Free Trial
              </button>
              {% elif has_had_premium %}
              <button class="btn btn-primary rounded-pill px-4 py-2 text-white fw-bold shadow-sm" id="adjustPlanBtn"
                style="background:#4e6bff; border: none;">
                Renew Premium
              </button>
              {% else %}
              <button class="btn btn-primary rounded-pill px-4 py-2 text-white fw-bold shadow-sm" id="adjustPlanBtn"
                style="background:#4e6bff; border: none;">
                Upgrade to Premium
              </button>
              {% endif %}
              {% else %}
              <button class="btn btn-primary rounded-pill px-4 py-2 text-white fw-bold shadow-sm" id="adjustPlanBtn"
                style="background:#4e6bff; border: none;">
                Extend Plan
              </button>
              {% endif %}
            </div>
            {% endif %}
          </div>

          <div class="subscription-details">
            {% if active_subscription %}
            <div class="mb-3">
              <h6 class="text-uppercase text-muted small fw-bold mb-2">Subscription Status</h6>
              <p class="mb-1">Your subscription expires on <strong>{{ active_subscription.end_date|formatdate
                  }}</strong></p>
              <p class="text-muted small mb-0">No commitment plan - manual renewal required</p>
            </div>
            {% if active_subscription.plan_code == 'admin_gift' %}
            <div class="mb-3">
              <h6 class="text-uppercase text-muted small fw-bold mb-2">Gift Reason</h6>
              <p class="mb-0">{{ active_subscription.reason if active_subscription.reason else '-' }}</p>
            </div>
            {% endif %}
            {% else %}
            <div class="mb-3">
              <h6 class="text-uppercase text-muted small fw-bold mb-2">Subscription Status</h6>
              {% if current_user.role != 'traveller' %}
              <p class="mb-0 text-muted">Your role is <strong>{{ current_user.role|capitalize }}</strong>, so you
                automatically have premium access.</p>
              {% else %}
              <p class="mb-0 text-muted">You do not have an active subscription. Upgrade to unlock premium features!</p>
              {% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    {# Payment Information Card #}
    <div class="col-lg-6">
      <div class="card shadow-sm border-0 rounded-3 h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h3 class="fs-4 fw-bold mb-0">Payment Method</h3>
          </div>

          <div class="payment-info">
            {% if active_subscription and latest_payment and latest_payment.card_last_four %}
            <div class="d-flex align-items-center">
              <div class="me-3">
                <div class="rounded bg-light p-2">
                  <i class="bi bi-credit-card fs-4 text-muted"></i>
                </div>
              </div>
              <div>
                <div class="fw-semibold">Card ending in {{ latest_payment.card_last_four }}</div>
                <div class="text-muted small">Primary payment method</div>
              </div>
            </div>
            {% else %}
            <div class="text-center py-4">
              <i class="bi bi-credit-card text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Payment Method</h5>
              <p class="text-muted mb-0">Add a payment method to enable premium features</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    {# Subscription History Card #}
    <div class="col-12">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-4 fw-bold mb-0">Subscription History</h3>
          </div>

          {% if all_subscriptions %}
          <div class="table-responsive">
            <table class="table align-middle table-hover">
              <thead>
                <tr>
                  <th>Period</th>
                  <th class="d-none d-sm-table-cell">Plan</th>
                  <th class="d-none d-lg-table-cell">Duration</th>
                  <th class="d-none d-sm-table-cell">Amount</th>
                  <th class="d-none d-sm-table-cell">Country</th>
                  <th class="d-none d-lg-table-cell">Note</th>
                  <th>Receipt</th>
                </tr>
              </thead>
              <tbody>
                {% for sub in all_subscriptions %}
                <tr>
                  <td class="py-3">
                    <div class="fw-semibold">{{ sub.start_date|formatdate }}</div>
                    <div class="text-muted small">to {{ sub.end_date|formatdate }}</div>
                  </td>
                  <td class="py-3 d-none d-sm-table-cell">
                    {% if user_role != 'traveller' %}
                    <span class="badge bg-primary rounded-pill py-2 px-3">Premium ({{ user_role }})</span>
                    {% else %}
                    {% if sub.plan_code == 'admin_gift' %}
                    <span class="badge bg-dark rounded-pill py-2 px-3">Admin Gift</span>
                    {% elif sub.plan_code == 'free_trial' %}
                    <span class="badge bg-secondary rounded-pill py-2 px-3">Free Trial</span>
                    {% elif sub.plan_name|lower == 'free' %}
                    <span class="badge bg-light text-dark rounded-pill py-2 px-3">Free</span>
                    {% else %}
                    <span class="badge rounded-pill py-2 px-3"
                      style="background:rgba(78,107,255,0.15); color:#4e6bff; font-weight:600;">
                      {{ sub.plan_name }}
                    </span>
                    {% endif %}
                    {% endif %}
                  </td>
                  <td class="py-3 d-none d-lg-table-cell">
                    <span class="fw-semibold">{{ sub.period_months }}</span>
                    <span class="text-muted">month{{ 's' if sub.period_months != 1 else '' }}</span>
                  </td>
                  <td class="py-3 d-none d-sm-table-cell">
                    <div class="fw-semibold">${{ sub.discounted_amount|round(2) }}</div>
                    {% if sub.discount_percentage %}
                    <div class="text-success small">{{ sub.discount_percentage }}% discount</div>
                    {% endif %}
                  </td>
                  <td class="py-3 d-none d-sm-table-cell">
                    {% if sub.country_code %}
                    <span class="badge bg-light text-dark rounded-pill">{{ sub.country_code }}</span>
                    {% else %}
                    <span class="text-muted">—</span>
                    {% endif %}
                  </td>
                  <td class="py-3 d-none d-lg-table-cell">
                    {% if sub.plan_code == "admin_gift" %}
                    <span class="text-muted small">{{ sub.reason }}</span>
                    {% else %}
                    <span class="text-muted">—</span>
                    {% endif %}
                  </td>
                  <td class="py-3">
                    {% if sub.plan_code == 'admin_gift' or sub.plan_code == 'free_trial' %}
                    <span class="text-muted">—</span>
                    {% else %}
                    <a href="{{ url_for('subscription.get_receipt', subscription_id=sub.id, payment_id=sub.payment_id or sub.id, user_id=sub.user_id) }}"
                      target="_blank" class="btn btn-outline-dark btn-sm">
                      View
                    </a>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>

          {% if total_pages > 1 %}
          <div class="mt-4">
            {{ render_pagination(page, total_pages, 'account.get_profile', {'active_tab': 'subscription'}) }}
          </div>
          {% endif %}

          {% else %}
          <div class="text-center py-5">
            <i class="bi bi-clock-history text-muted" style="font-size: 4rem;"></i>
            <h4 class="mt-3">No History Yet</h4>
            <p class="text-muted mb-0">Your subscription history will appear here once you make your first purchase.</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  #subscriptionContainer {
    min-height: calc(100vh - 200px);
  }

  .subscription-details .text-uppercase {
    letter-spacing: 0.5px;
    font-size: 0.7rem;
  }

  .payment-info .rounded {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table th {
    font-weight: 600;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .table td {
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, .05);
  }

  .table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, .02);
  }

  .badge {
    font-weight: 500;
  }

  .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .btn,
  button.btn,
  a.btn {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    font-weight: 500;
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, .3);
  }

  @media (max-width: 767.98px) {
    .container-fluid {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .card-body {
      padding: 1.5rem !important;
    }

    .table-responsive {
      font-size: 0.875rem;
    }

    .d-flex.align-items-center.justify-content-between {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .d-flex.align-items-center.justify-content-between .btn {
      align-self: stretch;
    }
  }

  /* Custom scrollbar for better UX */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d1d1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #b1b1b1;
  }

  @media (max-width: 576px) {
    .d-none-mobile {
      display: none !important;
    }
  }
</style>

<script>
  // All existing JavaScript functionality
  function hideModal() {
    const modal = document.querySelector('.modal.show');
    if (modal) {
      const modalInstance = bootstrap.Modal.getInstance(modal);
      if (modalInstance) {
        modalInstance.hide();
      }
    }
  }

  document.getElementById('updatePaymentBtn')?.addEventListener('click', async function (event) {
    event.preventDefault();
    const editUrl = "{{ url_for('subscription.create_subscription') }}";

    const response = await fetch(editUrl);
    const formHtml = await response.text();

    showModal('Add Payment', formHtml, {
      actionText: 'Save',
      onAction: function () {
        const form = document.getElementById('paymentForm');
        form.classList.add('was-validated');
        if (form.checkValidity()) {
          form.submit();
        }
      }
    });
  });

  document.getElementById('adjustPlanBtn')?.addEventListener('click', async function (event) {
    event.preventDefault();

    // Fetch plan data
    const response = await fetch("{{ url_for('subscription.get_selectable_plans') }}");
    const data = await response.json();
    const plans = data.plans || [];
    const hasHadFreeTrial = data.has_had_free_trial;

    // Store plan data on window object
    window.subscriptionPlans = plans;

    // Generate option HTML
    let planOptions = '';
    plans.forEach(plan => {
      planOptions += `<option value="${plan.plan_code}">${plan.name} / $${plan.discounted_amount} ${plan.discount_percentage ? ' (' + plan.discount_percentage + '% OFF)' : ''}</option>`;
    });

    // Premium features info box
    const isMobile = window.innerWidth <= 576;
    const premiumFeatureList = isMobile
      ? `<div class="alert alert-light border-0 rounded-3 mb-3 mt-3 text-center" style="font-size:0.95rem;">
            <i class="bi bi-trophy text-warning me-1"></i>
            Premium features: published journeys, multiple images, cover images, and more!
         </div>`
      : `<div class="card border-0 shadow-sm rounded-4 mb-4 mt-3">
        <div class="card-body bg-light">
          <h6 class="fw-bold mb-3 d-flex align-items-center" style="font-size:1.05rem;"><i class="bi bi-trophy text-warning me-2"></i>Premium Features</h6>
          <ul class="list-unstyled mb-0" style="font-size:0.86rem;">
            <li class="mb-2 d-flex">
              <i class="bi bi-globe me-2 text-primary" style="font-size:1rem;"></i>
              <span><b>Published Journeys</b> <br> <span class="text-muted small">Set journeys as Published to appear on homepage and be visible to everyone</span>
            </li>
            <li class="mb-2 d-flex">
              <i class="bi bi-images me-2 text-primary" style="font-size:1rem;"></i>
              <span><b>Multiple Images per Event</b> <br> <span class="text-muted small">Add multiple images to each event in your journeys</span></span>
            </li>
            <li class="d-flex">
              <i class="bi bi-image me-2 text-primary" style="font-size:1rem;"></i>
              <span><b>Journey Cover Images</b> <br> <span class="text-muted small">Choose cover images for journeys to display as headers and thumbnails</span></span>
            </li>
          </ul>
          <div class="alert alert-light border-0 mt-3 mb-0 rounded-3" style="font-size:0.93rem;">
            <small class="text-muted">
              <i class="bi bi-info-circle me-1"></i>
              If your subscription expires, existing images and covers remain visible, but you won't be able to add new ones until renewal.
            </small>
          </div>
        </div>
      </div>`;

    // Free trial section
    let freeTrialSection = '';
    if (!hasHadFreeTrial) {
      freeTrialSection = `
        <div class="card border-success rounded-4 shadow-sm mb-4">
          <div class="card-body p-3 d-flex align-items-center gap-3">
            <input class="form-check-input flex-shrink-0 mt-0" type="radio" name="plan_group" id="freeTrial" value="free_trial">
            <label class="form-check-label flex-grow-1" for="freeTrial">
              <div class="d-flex align-items-center mb-1 gap-2">
                <strong>Free Trial</strong>
                <span class="badge bg-success">No payment required</span>
              </div>
              <div class="text-muted small">
                Enjoy <strong>all premium features</strong> for <strong>1 month</strong> free.<br>
                <em>Limited to one trial per account.</em>
              </div>
            </label>
          </div>
        </div>
      `;
    }

    // Create select plan form
    const adjustPlanHtml = `
      <form id="adjustPlanForm" class="p-0">
        ${freeTrialSection}
        <div class="card border-primary rounded-4 shadow-sm mb-2">
          <div class="card-body">
            <div class="form-check mb-3">
              <input class="form-check-input" type="radio" name="plan_group" id="premiumPlan" value="premium" checked>
              <label class="form-check-label fw-semibold" for="premiumPlan">
                Premium Plans
              </label>
            </div>
            <div class="mb-3">
              <label class="form-label small text-muted fw-bold text-uppercase">Choose Duration:</label>
              <select class="form-select" id="premiumOption" name="premium_option">
                ${planOptions}
              </select>
            </div>
          </div>
        </div>
          ${premiumFeatureList}
      </form>
    `;

    // Show modal
    showModal('Choose Your Plan', adjustPlanHtml, {
      actionText: 'Continue',
      actionClass: 'btn-primary',
      onAction: handlePlanSelectionNext
    });

    setTimeout(() => {
      const modalActionBtn = document.querySelector('#modalActionBtn');
      if (modalActionBtn) {
        modalActionBtn.textContent = 'Continue';
        const newActionBtn = modalActionBtn.cloneNode(true);
        modalActionBtn.parentNode.replaceChild(newActionBtn, modalActionBtn);
        newActionBtn.addEventListener('click', handlePlanSelectionNext);
      }
    }, 50);

    if (this.textContent.trim() === 'Get Free Trial') {
      setTimeout(() => {
        const freeTrialRadio = document.getElementById('freeTrial');
        if (freeTrialRadio) {
          freeTrialRadio.checked = true;
        }
      }, 100);
    }

    // Next button handler
    async function handlePlanSelectionNext() {
      const form = document.getElementById('adjustPlanForm');
      const selected = form.querySelector('input[name="plan_group"]:checked');

      if (!selected) {
        alert('Please select a plan');
        return;
      }

      // If free trial is selected
      if (selected.value === 'free_trial') {
        function addOneMonth(date) {
          const d = new Date(date);
          const day = d.getDate();
          d.setMonth(d.getMonth() + 1);
          if (d.getDate() < day) {
            d.setDate(0);
          }
          return d;
        }
        const today = new Date();
        const expiryDate = addOneMonth(today);

        const formattedDate = expiryDate.toLocaleDateString('en-GB', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        }).replace(/\//g, '/');

        const modalTitle = document.querySelector('.modal-title');
        const modalBody = document.querySelector('.modal-body');
        const modalActionBtn = document.querySelector('#modalActionBtn');
        const closeBtn = document.querySelector('.close-button');

        modalTitle.textContent = 'Welcome to Premium!';
        modalBody.innerHTML = `
      <div class="text-center py-4">
        <div class="mb-4">
          <i class="bi bi-trophy text-warning" style="font-size: 4rem;"></i>
        </div>
        <h4 class="fw-bold mb-3">Welcome to Premium!</h4>
        <div class="alert alert-success border-0 rounded-3 p-4">
          <p class="mb-0 fs-5">You now have access to all premium features until <strong>${formattedDate}</strong>.</p>
        </div>
        <p class="text-muted">Start exploring all the enhanced features available to you!</p>
      </div>
    `;

        closeBtn.textContent = 'Go Back';

        modalActionBtn.textContent = 'Activate Trial';

        const newActionBtn = modalActionBtn.cloneNode(true);
        modalActionBtn.parentNode.replaceChild(newActionBtn, modalActionBtn);

        newActionBtn.addEventListener('click', async function () {
          try {
            const response = await fetch("{{ url_for('subscription.update_plan') }}", {
              method: 'POST',
              headers: { 'Content-Type': 'application/x-www-form-urlencoded', 'X-Requested-With': 'XMLHttpRequest' },
              body: 'plan=free_trial'
            });

            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                hideModal();
                window.location.reload();
              } else {
                alert(data.message || "Failed to activate free trial.");
              }
            } else {
              const data = await response.json();
              alert(data.message || "Failed to activate free trial.");
            }
          } catch (error) {
            alert('Error activating free trial. Please try again.');
          }
        });

        return;
      }

      // If premium plan is selected
      const selectedPremium = form.querySelector('#premiumOption').value;

      try {
        const response = await fetch("{{ url_for('subscription.get_countries') }}");
        const data = await response.json();
        const countries = data.countries || [];

        let countryOptions = '';
        countries.forEach(c => {
          countryOptions += `<option value="${c.id}">${c.name}</option>`;
        });

        const selectedPlan = window.subscriptionPlans.find(p => p.plan_code === selectedPremium) || {};
        const planPrice = selectedPlan.discounted_amount || 0;
        const planCurrency = selectedPlan.currency_code || 'NZD';

        const paymentFormHtml = `
      <form id="paymentForm" action="{{ url_for('subscription.create_subscription') }}" method="POST">
        <input type="hidden" name="plan_code" value="${selectedPremium}">

        <div class="row g-2">
          <div class="col-12">
            <label for="name" class="form-label small fw-bold text-uppercase text-muted">Cardholder Name</label>
            <input type="text" class="form-control" id="name" name="name" placeholder="Enter full name" required>
          </div>

          <div class="col-12">
            <label for="cardNumber" class="form-label small fw-bold text-uppercase text-muted">Card Number</label>
            <input type="text" class="form-control" id="cardNumber" name="card_number"
              placeholder="1234 5678 9012 3456" required pattern="[0-9]{16}" maxlength="16">
            <div class="invalid-feedback">Please enter a valid 16-digit card number.</div>
          </div>

          <div class="col-md-6">
            <label for="expirationDate" class="form-label small fw-bold text-uppercase text-muted">Expiry Date</label>
            <input type="text" class="form-control" id="expirationDate" name="expiration_date"
              placeholder="MM/YY" required pattern="(0[1-9]|1[0-2])\\/([0-9]{2})" maxlength="5">
            <div class="invalid-feedback">Please enter a valid expiration date.</div>
          </div>
          <div class="col-md-6">
            <label for="securityCode" class="form-label small fw-bold text-uppercase text-muted">CVV</label>
            <input type="text" class="form-control" id="securityCode" name="security_code"
              placeholder="123" required pattern="[0-9]{3,4}" maxlength="3">
            <div class="invalid-feedback">Please enter a valid security code.</div>
          </div>

          <div class="col-12">
            <label for="country" class="form-label small fw-bold text-uppercase text-muted">Country</label>
            <select class="form-select" id="country" name="country" required>
              <option value="">Select Country</option>
              ${countryOptions}
            </select>
            <div class="invalid-feedback">Please select a country.</div>
          </div>

          <div class="col-12">
            <label for="address" class="form-label small fw-bold text-uppercase text-muted">Billing Address</label>
            <input type="text" class="form-control" id="address" name="address"
              placeholder="Enter your billing address" required>
            <div class="invalid-feedback">Please enter your billing address.</div>
          </div>

          <div class="col-12">
            <label for="email" class="form-label small fw-bold text-uppercase text-muted">Email Address</label>
            <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
          </div>
        </div>

        <div class="alert alert-light border-0 rounded-3 mt-3 mb-3" id="gstNotice">
          <small class="text-muted">
            <i class="bi bi-info-circle me-1"></i>
            New Zealand billing addresses will be charged 15% GST
          </small>
        </div>

        <div class="card bg-light border-0 rounded-3 mt-2">
          <div class="card-body p-3">
            <h6 class="fw-bold mb-3">Order Summary</h6>
            <div class="d-flex justify-content-between mb-2">
              <div>${selectedPlan.name || 'Premium Plan'}</div>
              <div class="fw-semibold">${planPrice} ${planCurrency}</div>
            </div>
            <hr class="my-2">
            <div class="d-flex justify-content-between mb-1 small text-muted">
              <div>Subtotal</div>
              <div id="subtotal">${planPrice} ${planCurrency}</div>
            </div>
            <div class="d-flex justify-content-between mb-2 small text-muted" id="taxRow" style="display:none;">
              <div>GST (15%)</div>
              <div id="taxAmount">0.00 ${planCurrency}</div>
            </div>
            <div class="d-flex justify-content-between fw-bold fs-5">
              <div>Total</div>
              <div id="totalAmount">${planPrice} ${planCurrency}</div>
            </div>
          </div>
        </div>
      </form>
    `;

        const modalTitle = document.querySelector('.modal-title');
        const modalBody = document.querySelector('.modal-body');
        const modalActionBtn = document.querySelector('#modalActionBtn');

        modalTitle.textContent = 'Complete Payment';
        modalBody.innerHTML = paymentFormHtml;

        modalActionBtn.textContent = 'Complete Purchase';
        modalActionBtn.className = 'btn btn-dark rounded-pill px-4';

        const newActionBtn = modalActionBtn.cloneNode(true);
        modalActionBtn.parentNode.replaceChild(newActionBtn, modalActionBtn);

        newActionBtn.addEventListener('click', function () {
          const form = document.getElementById('paymentForm');
          form.classList.add('was-validated');

          if (form.checkValidity()) {
            form.submit();
          }
        });

        // GST calculation logic
        const countrySelect = document.getElementById('country');
        const taxRow = document.getElementById('taxRow');
        const taxAmount = document.getElementById('taxAmount');
        const totalAmount = document.getElementById('totalAmount');

        countrySelect.addEventListener('change', function () {
          const isNZ = this.options[this.selectedIndex].text === 'New Zealand';
          taxRow.style.display = isNZ ? 'flex' : 'none';

          const subtotal = parseFloat(planPrice);
          let tax = 0;
          let total = subtotal;

          if (isNZ) {
            tax = subtotal * 0.15;
            total = subtotal + tax;
          }

          taxAmount.textContent = tax.toFixed(2) + ' ' + planCurrency;
          totalAmount.textContent = total.toFixed(2) + ' ' + planCurrency;
        });
      } catch (error) {
        console.error('Error:', error);
        alert('An error occurred while loading the payment form. Please try again later.');
      }
    }
  });
</script>
{% endblock %}