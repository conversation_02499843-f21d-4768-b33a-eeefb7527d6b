{% extends "base.html" %}
{% block title %}Receipt - Footprints{% endblock %}

{% block head %}
<!-- Additional styles to override base CSS -->
<style>
  /* Hide header and footer */
  .navbar-wrapper,
  footer {
    display: none !important;
  }

  body {
    padding-top: 0 !important;
    background-color: #f5f5f5 !important;
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    font-family: 'Inter', 'Pretendard', 'Apple SD Gothic Neo', Arial, sans-serif;
  }

  .container {
    width: 100%;
    padding: 20px;
  }

  .receipt-container {
    max-width: 600px;
    width: 100%;
    margin: 0 auto;
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 4px 32px rgba(78, 107, 255, 0.08), 0 1.5px 8px rgba(0, 0, 0, 0.04);
    padding: 48px 36px 36px 36px;
    border: 1.5px solid #e9ecef;
  }

  .receipt-header {
    text-align: center;
    margin-bottom: 36px;
  }

  .receipt-header h2 {
    font-size: 2rem;
    font-weight: 800;
    color: #222;
    letter-spacing: 0.01em;
  }

  .receipt-section {
    margin-bottom: 32px;
  }

  .receipt-section-title {
    font-weight: 700;
    margin-bottom: 18px;
    color: #4e6bff;
    letter-spacing: 0.04em;
    font-size: 1.1rem;
  }

  .receipt-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 1rem;
  }

  .receipt-divider {
    height: 1.5px;
    background-color: #e9ecef;
    margin: 28px 0 24px 0;
    border-radius: 2px;
  }

  .receipt-total {
    border-top: 2.5px solid #4e6bff;
    padding-top: 18px;
    margin-top: 18px;
    font-size: 1.2rem;
    font-weight: 700;
    letter-spacing: 0.02em;
    align-items: flex-end;
  }

  .total-amount {
    font-size: 1.5rem;
    font-weight: 800;
    color: #4e6bff;
    letter-spacing: 0.5px;
    margin-left: 12px;
  }

  .btn-download {
    display: inline-block;
    background: linear-gradient(135deg, #4e6bff 0%, #3b82f6 100%);
    color: white;
    border-radius: 25px;
    padding: 14px 36px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    border: none !important;
    font-size: 1.1rem;
  }

  .btn-download:focus {
    outline: none !important;
  }

  .premium-badge {
    display: inline-block;
    background: rgba(78, 107, 255, 0.12);
    color: #4e6bff;
    font-weight: 700;
    border-radius: 20px;
    padding: 7px 18px;
    font-size: 1.05rem;
    letter-spacing: 0.04em;
    margin-bottom: 10px;
    margin-right: 6px;
  }

  .text-muted {
    color: #6c757d;
  }

  .text-right {
    text-align: right;
  }

  .buttons-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 36px;
  }

  .loading-indicator {
    display: none;
    text-align: center;
    margin-top: 20px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4e6bff;
    border-radius: 50%;
    animation: spin 2s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @media print {
    body {
      display: block !important;
      height: auto !important;
    }

    .container {
      width: 100%;
      max-width: 100%;
      padding: 0;
    }

    .receipt-container {
      width: 100%;
      max-width: 100%;
      margin: 0;
      padding: 20px;
      box-shadow: none;
      border-radius: 0;
    }

    .buttons-container,
    .btn-download,
    .loading-indicator {
      display: none !important;
    }
  }

  @media (max-height: 800px) {
    body {
      justify-content: flex-start !important;
      padding: 20px 0;
    }
  }

  @media (max-width: 650px) {
    .receipt-container {
      padding: 30px 12px;
    }

    .btn-download {
      padding: 12px 18px;
      font-size: 1rem;
    }

    .total-amount {
      font-size: 1.3rem;
    }
  }
</style>

<!-- Include html2pdf library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"
  integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg=="
  crossorigin="anonymous" referrerpolicy="no-referrer"></script>
{% endblock %}

{% block content %}
<div class="container">
  <div class="receipt-container" id="receipt">
    <div class="receipt-header">
      <h2 class="fw-bold">Paid on {{ payment.payment_date|formatdate if payment.payment_date else '-' }}</h2>
    </div>

    <div class="receipt-section">
      <h3 class="receipt-section-title">SUMMARY</h3>
      <div class="receipt-row">
        <div>To</div>
        <div>{{ payment.recipient or 'Footprints' }}</div>
      </div>
      <div class="receipt-row">
        <div>From</div>
        <div>{{ user.username }}</div>
      </div>
      <div class="receipt-row">
        <div>Country</div>
        <div>{{ payment.country_name }}{% if payment.country_code %} ({{ payment.country_code }}){% endif %}</div>
      </div>
    </div>

    <div class="receipt-section">
      <h3 class="receipt-section-title">ITEMS</h3>
      <div class="receipt-row">
        <div>{{ payment.subscription_start|formatdate if payment.subscription_start else '-' }} - {{
          payment.subscription_end|formatdate if
          payment.subscription_end else '-' }}</div>
      </div>
      <div class="receipt-row">
        <div>
          {% if payment.plan_code == 'admin_gift' %}
          <div class="premium-badge">-</div>
          {% else %}
          <div class="premium-badge">{{ payment.plan_code|upper }}</div>
          {% endif %}
        </div>
        <div class="text-right">
          {% if payment.plan_code == 'admin_gift' %}-{% else %}${{ '%.2f'|format(payment.amount or 0) }}{% endif %}
        </div>
      </div>
    </div>

    <div class="receipt-divider"></div>

    <div class="receipt-section">
      <div class="receipt-row">
        <div>Subtotal</div>
        <div>{% if payment.plan_code == 'admin_gift' %}-{% else %}${{ '%.2f'|format(payment.amount or 0) }}{% endif %}
        </div>
      </div>
      <div class="receipt-row">
        <div>Total Excluding Tax</div>
        <div>{% if payment.plan_code == 'admin_gift' %}-{% else %}${{ '%.2f'|format(payment.amount or 0) }}{% endif %}
        </div>
      </div>
      <div class="receipt-row">
        <div class="text-muted">Tax ({% if payment.country_code == 'NZ' %}15.00{% else %}0.00{% endif %}%)</div>
        <div class="text-muted">{% if payment.plan_code == 'admin_gift' %}-{% else %}${{ '%.2f'|format(payment.tax) }}{%
          endif %}</div>
      </div>
      <div class="receipt-row receipt-total">
        <div>Total</div>
        <div class="total-amount">{% if payment.plan_code == 'admin_gift' %}-{% else %}${{ '%.2f'|format(payment.total)
          }}{% endif %}</div>
      </div>
    </div>

    <div class="text-center mt-5 mb-2">
      <img src="{{ url_for('static', filename='images/footprints_logo.png') }}" alt="Footprints" height="30">
      <p class="text-muted small mt-2">© {{ payment.paid_at|formatdate if payment.paid_at else now()|formatdate }}
        Footprints. All rights reserved.</p>
    </div>

    <!-- Download button inside receipt container -->
    <div class="buttons-container">
      <button id="downloadPdf" class="btn-download">
        <i class="bi bi-download me-1"></i> Download Receipt as PDF
      </button>
    </div>

    <!-- Loading indicator -->
    <div class="loading-indicator" id="loadingIndicator">
      <div class="spinner"></div>
      <p class="mt-2">Generating PDF...</p>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    document.getElementById('downloadPdf').addEventListener('click', function () {
      // Hide download button and loading indicator for clean PDF
      var downloadBtn = document.getElementById('downloadPdf');
      var loadingIndicator = document.getElementById('loadingIndicator');
      var buttonsContainer = document.querySelector('.buttons-container');

      // Hide all UI elements that shouldn't appear in PDF
      if (downloadBtn) downloadBtn.style.display = 'none';
      if (buttonsContainer) buttonsContainer.style.display = 'none';
      if (loadingIndicator) loadingIndicator.style.display = 'none';

      // Get the element to convert to PDF
      var element = document.getElementById('receipt');

      // Set the PDF filename
      var paymentId = '{{ payment.id }}';
      var paymentDate = '{{ payment.paid_at.strftime("%Y-%m-%d") if payment.paid_at else now().strftime("%Y-%m-%d") }}';
      var filename = 'receipt_' + paymentId + '_' + paymentDate + '.pdf';

      // Configure PDF options
      var opt = {
        margin: [10, 10, 10, 10],
        filename: filename,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          logging: false,
          letterRendering: true,
          allowTaint: true
        },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };

      // Generate and download PDF
      html2pdf().set(opt).from(element).save().then(function () {
        // Restore UI elements after PDF is generated
        if (downloadBtn) downloadBtn.style.display = 'inline-block';
        if (buttonsContainer) buttonsContainer.style.display = 'flex';
        if (loadingIndicator) loadingIndicator.style.display = 'none';
      });
    });

    // Set focus on download button after page load
    setTimeout(function () {
      document.getElementById('downloadPdf').focus();
    }, 500);
  });
</script>
{% endblock %}