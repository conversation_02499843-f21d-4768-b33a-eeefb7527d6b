{% extends "base.html" %}
{% from "components/pagination.html" import render_pagination %}

{% block title %}Hidden Journeys - Footprints{% endblock %}

{% block content %}
<div class="container-fluid py-4">
  <div class="d-flex align-items-center mb-4">
    <h1 class="display-6 fw-bold mb-0">
      <span class="position-relative">
        Hidden Journeys
        <span class="position-absolute start-0 bottom-0"
          style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
      </span>
    </h1>
  </div>

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold mb-0">Total ({{ total_count|default(0) }})</h5>
  </div>

  <div class="card shadow-sm border-0 rounded-3 mb-4">
    <div class="card-body">
      <form action="{{ url_for('journey.admin_hidden_journeys') }}" method="get" class="mb-0">
        <div class="d-flex">
          <div class="input-group flex-grow-1">
            <input type="text" name="q" id="searchInput" class="form-control" placeholder="Search hidden journeys..."
              value="{{ search_term }}">
            {% if search_term %}
            <a href="{{ url_for('journey.admin_hidden_journeys') }}" class="btn btn-outline-secondary border-start-0">
              <i class="bi bi-x"></i>
            </a>
            {% endif %}
          </div>
          <button type="submit" class="btn btn-outline-secondary ms-2">Search</button>
        </div>
        <div class="form-text text-muted mt-2">
          <i class="bi bi-info-circle"></i> Search for hidden journeys by title or description.
        </div>
      </form>
    </div>
  </div>

  <div class="card-body">
    {% if journeys_by_user %}
    <div class="accordion" id="hiddenJourneysAccordion">
      {% for user_id, user_info in journeys_by_user.items() %}
      <div class="accordion-item border mb-3 rounded-3 shadow-sm">
        <h2 class="accordion-header" id="heading{{ user_id }}">
          <button class="accordion-button collapsed rounded-3" type="button" data-bs-toggle="collapse"
            data-bs-target="#collapse{{ user_id }}" aria-expanded="false" aria-controls="collapse{{ user_id }}">
            <div class="d-flex justify-content-between align-items-center w-100 me-3">
              <div class="d-flex align-items-center">
                <div class="position-relative me-3">
                  <div class="rounded-circle overflow-hidden" style="width: 40px; height: 40px;">
                    {% if user_info.profile_image %}
                    <img src="{{ url_for('static', filename='uploads/profile_images/' + user_info.profile_image) }}"
                      alt="{{ user_info.username }}" style="width: 100%; height: 100%; object-fit: cover;"
                      onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                    {% else %}
                    <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                      alt="{{ user_info.username }}" style="width: 100%; height: 100%; object-fit: cover;">
                    {% endif %}
                  </div>
                </div>
                <span>
                  <strong class="fw-semibold">{{ user_info.username }}</strong>
                  {% if user_info.first_name or user_info.last_name %}
                  <span class="text-muted small ms-2 d-none d-sm-inline">({{ user_info.first_name or '' }} {{
                    user_info.last_name or '' }})</span>
                  {% endif %}
                </span>
              </div>
              <span class="badge bg-white border text-dark rounded-pill py-2 px-3 d-none d-sm-inline">{{
                user_info.journeys|length }} hidden {{ 'journey' if user_info.journeys|length == 1 else 'journeys'
                }}</span>
            </div>
          </button>
        </h2>
        <div id="collapse{{ user_id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ user_id }}"
          data-bs-parent="#hiddenJourneysAccordion">
          <div class="accordion-body">
            <div class="list-group">
              {% for journey in user_info.journeys %}
              <div class="list-group-item border mb-2 rounded-3 shadow-sm">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h5 class="fw-semibold mb-0">{{ journey.title }}</h5>
                  <div class="d-flex align-items-center">
                    {% if journey.visibility == 'private' %}
                    <span class="badge bg-secondary text-white rounded-pill me-2">
                      <i class="bi bi-lock-fill me-1"></i>Private
                    </span>
                    {% endif %}
                    <small class="text-muted d-none d-sm-inline">{{ journey.start_date|date }}</small>
                  </div>
                </div>
                <p class="mb-3 text-muted">{{ journey.description|truncate(150) }}</p>

                {% if journey.visibility == 'private' %}
                <div class="py-2 px-3 mb-3 small bg-secondary-subtle text-dark rounded-3">
                  <i class="bi bi-exclamation-triangle-fill me-2"></i>
                  <strong>Access Restricted:</strong> This journey has been set to private by the author and cannot be
                  accessed by staff members.
                </div>
                {% endif %}

                <div class="d-flex justify-content-end mt-2">
                  {% if journey.visibility == 'private' %}
                  <button class="btn btn-sm btn-secondary rounded-pill me-2 py-2 px-3" disabled
                    title="Cannot view private journeys">
                    View (Restricted)
                  </button>
                  {% else %}
                  <a href="{{ url_for('journey.get_admin_journey', journey_id=journey.id) }}"
                    class="btn btn-sm btn-primary rounded-pill me-2 py-2 px-3">
                    View
                  </a>
                  {% endif %}

                  <form method="post" class="unhide-form"
                    action="{{ url_for('journey.toggle_hidden', journey_id=journey.id) }}">
                    <button type="submit" class="btn btn-sm btn-warning rounded-pill unhide-btn py-2 px-3"
                      data-journey-title="{{ journey.title }}">
                      Unhide
                    </button>
                  </form>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
    {{ render_pagination(page, total_pages, 'journey.admin_hidden_journeys', {'q': search_term}, None) }}
    {% else %}
    <div class="d-flex align-items-center alert"
      style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
      {% if search_term %}
      <p class="mb-0"><i class="bi bi-info-circle me-2"></i>No hidden journeys found matching "{{ search_term }}". Try
        a different search term or view all hidden journeys.</p>
      {% else %}
      <p class="mb-0"><i class="bi bi-info-circle me-2"></i>No hidden journeys found.</p>
      {% endif %}
    </div>
    {% endif %}
  </div>
</div>

<!-- Confirmation Modal -->

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const unhideButtons = document.querySelectorAll('.unhide-btn');
    let currentForm = null;

    unhideButtons.forEach(button => {
      button.addEventListener('click', function (e) {
        e.preventDefault();

        const journeyTitle = this.getAttribute('data-journey-title');
        const form = this.closest('.unhide-form');
        currentForm = form;

        showModal(
          'Unhide Journey',
          `<p>Are you sure you want to unhide the journey <strong>${journeyTitle}</strong>?</p>
           <p class="text-muted small mb-0">
             <i class="bi bi-info-circle me-1"></i>
             This will make the journey visible to all users again in the public listings if this journey is set to public.
           </p>`,
          {
            actionText: 'Confirm',
            onAction: () => {
              if (currentForm) currentForm.submit();
            }
          }
        );
      });
    });
  });
</script>
{% endblock %}