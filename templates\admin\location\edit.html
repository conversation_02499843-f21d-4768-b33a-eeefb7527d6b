{% block content %}
<div class="container py-2">
  <div class="row justify-content-center">
    <div class="col-12">
      <form method="post" action="{{ url_for('location.update_location', location_id=location.id) }}"
        id="edit-location-form">
        <div class="mb-3 position-relative">
          <label for="name" class="form-label">Location Name</label>
          <input type="text" class="form-control" id="name" name="name" required value="{{ location.name }}"
            autocomplete="off" oninput="searchLocations()" onfocus="searchLocations()" />
          <div id="locationSuggestions" class="location-suggestions border rounded shadow-sm w-100 bg-white"
            style="display: none; position: absolute; z-index: 1000; max-height: 150px; overflow-y: auto;">
            <!-- Suggestions will be populated here -->
          </div>
          <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            If you select an existing location, this location will be merged with the selected one.
          </div>
        </div>

        <input type="hidden" id="target_location_id" name="target_location_id" value="">
        <div id="mergeWarning" class="alert alert-warning d-none mb-3">
          <i class="bi bi-exclamation-triangle me-2"></i>
          <span id="warningText">Location will be merged with an existing location</span>
        </div>
      </form>
    </div>
  </div>
</div>

<style>
  .location-suggestions {
    position: absolute;
    width: 100%;
    max-height: 250px;
    overflow-y: auto;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
  }

  .location-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
  }

  .location-item:last-child {
    border-bottom: none;
  }

  .location-item:hover {
    background-color: #f8f9fa;
  }
</style>

<script>
  async function searchLocations() {
    const query = document.getElementById('name').value.trim();
    const currentLocationId = '{{ location.id }}';
    const suggestionBox = document.getElementById('locationSuggestions');
    const mergeWarning = document.getElementById('mergeWarning');
    const warningText = document.getElementById('warningText');
    const targetLocationId = document.getElementById('target_location_id');

    // Reset the merge warning and target ID
    mergeWarning.classList.add('d-none');
    targetLocationId.value = '';

    // If query is empty, hide the suggestion box
    if (query.length === 0) {
      suggestionBox.style.display = 'none';
      return;
    }

    try {
      const response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
      const data = await response.json();

      suggestionBox.innerHTML = '';

      if (data.length === 0) {
        suggestionBox.style.display = 'none';
        return;
      }

      // Create suggestion items
      data.forEach((location) => {
        const item = document.createElement('div');
        item.className = 'location-item';
        item.textContent = location.name;

        item.addEventListener('click', () => {
          document.getElementById('name').value = location.name;
          targetLocationId.value = location.id || '';

          // Only show merge warning if selecting a different location
          if (location.name !== '{{ location.name }}') {
            warningText.textContent = `This location will be merged with "${location.name}" and all events will be updated.`;
            mergeWarning.classList.remove('d-none');
          }

          suggestionBox.style.display = 'none';
        });

        suggestionBox.appendChild(item);
      });

      // Show suggestion box
      suggestionBox.style.display = 'block';
    } catch (error) {
      console.error('Error searching locations:', error);
    }
  }

  // Handle clicks outside the suggestion box
  document.addEventListener('click', function (event) {
    const nameInput = document.getElementById('name');
    const suggestionBox = document.getElementById('locationSuggestions');

    if (event.target !== nameInput && !suggestionBox.contains(event.target)) {
      suggestionBox.style.display = 'none';
    }
  });

  // Show suggestions when input is focused
  document.getElementById('name').addEventListener('focus', searchLocations);

  // Clear the warning if the user changes the text after selecting a location
  document.getElementById('name').addEventListener('input', function () {
    const initialValue = this.value;
    setTimeout(() => {
      if (this.value !== initialValue) {
        document.getElementById('mergeWarning').classList.add('d-none');
        document.getElementById('target_location_id').value = '';
      }
    }, 100);
  });
</script>
{% endblock %}