{% extends "base.html" %}
{% from "components/pagination.html" import render_pagination %}
{% block title %}Location Management - Footprints{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-6 fw-bold">
            <span class="position-relative">
                Location Management
                <span class="position-absolute start-0 bottom-0 title-underline"></span>
            </span>
        </h1>
    </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h5 class="fw-bold mb-0">Total ({{ total_count|default(0) }})</h5>
    </div>

    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form action="{{ url_for('location.get_locations') }}" method="get" class="mb-0">
                <div class="d-flex">
                    <div class="input-group flex-grow-1">
                        <input type="text" name="q" id="searchInput" class="form-control"
                            placeholder="Search locations..." value="{{ search_term }}">
                        {% if search_term %}
                        <a href="{{ url_for('location.get_locations') }}"
                            class="btn btn-outline-secondary border-start-0">
                            <i class="bi bi-x"></i>
                        </a>
                        {% endif %}
                    </div>
                    <button type="submit" class="btn btn-outline-secondary ms-2">Search</button>
                </div>
                <div class="form-text text-muted mt-2">
                    <i class="bi bi-info-circle"></i> Search for locations to edit, delete, or merge them with other
                    locations.
                </div>
            </form>
        </div>
    </div>

    {% if locations %}
    <div class="table-responsive">
        <table class="table align-middle table-hover">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Location Name</th>
                    <th>Usage Count</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for location in locations %}
                <tr class="location-row" data-location-id="{{ location.id }}">
                    <td>{{ (page - 1) * per_page + loop.index }}</td>
                    <td>{{ location.name }}</td>
                    <td>
                        <span class="badge rounded-pill bg-light text-dark px-3 py-2">
                            {{ location.event_count|default(0) }}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button type="button" class="btn btn-sm btn-outline-dark edit-btn"
                                data-location-id="{{ location.id }}" onclick="event.stopPropagation();">
                                Edit
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {{ render_pagination(page, total_pages, 'location.get_locations', {'q': search_term}, None) }}
    {% else %}
    <div class="alert not-found-alert">
        {% if search_term %}
        <p class="mb-0"><i class="bi bi-info-circle me-2"></i>No locations found matching "{{ search_term }}". Try a
            different search term or view all locations.</p>
        {% else %}
        <p class="mb-0"><i class="bi bi-info-circle me-2"></i>No locations found.</p>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Modal for location details -->
<div class="modal fade" id="commonModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded-4 border-0">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title fw-bold"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body pt-2">
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-outline-dark rounded-pill px-4"
                    data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-dark rounded-pill px-4" id="modalActionBtn"
                    style="display: none;">Action</button>
            </div>
        </div>
    </div>
</div>

<style>
    .location-suggestions {
        position: absolute;
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        z-index: 1100;
    }

    .location-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
        cursor: pointer;
    }

    .location-item:last-child {
        border-bottom: none;
    }

    .location-item:hover {
        background-color: #f8f9fa;
    }

    .title-underline {
        height: 6px;
        width: 60%;
        background-color: #4e6bff;
        opacity: 0.2;
        border-radius: 3px;
    }

    .not-found-alert {
        background-color: rgba(78, 107, 255, 0.1);
        color: #4e6bff;
        border: 1px solid rgba(78, 107, 255, 0.2);
        border-radius: 8px;
    }
</style>

<script>
    async function searchLocations() {
        const nameInput = document.getElementById('name') || document.getElementById('location');
        const query = nameInput ? nameInput.value.trim() : '';
        const suggestionBox = document.getElementById('locationSuggestions');

        if (!suggestionBox) return;

        try {
            const response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
            const data = await response.json();

            suggestionBox.innerHTML = '';

            if (data.length === 0) {
                suggestionBox.style.display = 'none';
                return;
            }

            // Create suggestion items
            data.forEach((location) => {
                const item = document.createElement('div');
                item.className = 'location-item';
                item.textContent = location.name;

                item.addEventListener('click', () => {
                    nameInput.value = location.name;

                    const targetLocationId = document.getElementById('target_location_id');
                    if (targetLocationId) {
                        targetLocationId.value = location.id || '';

                        const mergeWarning = document.getElementById('mergeWarning');
                        const defaultValue = nameInput.defaultValue;

                        if (mergeWarning && location.name !== defaultValue) {
                            document.getElementById('warningText').textContent =
                                `This location will be merged with "${location.name}" and all events will be updated.`;
                            mergeWarning.classList.remove('d-none');
                        }
                    }

                    suggestionBox.style.display = 'none';
                });

                suggestionBox.appendChild(item);
            });

            // Show suggestion box
            suggestionBox.style.display = 'block';
        } catch (error) {
            console.error('Error searching locations:', error);
        }
    }

    // Using global showModal function from components/modal.html

    async function searchLocations() {
        const nameInput = document.getElementById('name') || document.getElementById('location');
        const query = nameInput ? nameInput.value.trim() : '';
        const suggestionBox = document.getElementById('locationSuggestions');
        const mergeWarning = document.getElementById('mergeWarning');
        const targetLocationId = document.getElementById('target_location_id');

        if (!suggestionBox) return;

        if (query.length === 0 && mergeWarning) {
            mergeWarning.classList.add('d-none');
            if (targetLocationId) {
                targetLocationId.value = '';
            }
        }

        try {
            const response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
            const data = await response.json();

            suggestionBox.innerHTML = '';

            if (data.length === 0) {
                suggestionBox.style.display = 'none';
                return;
            }

            // Create suggestion items
            data.forEach((location) => {
                const item = document.createElement('div');
                item.className = 'location-item';
                item.textContent = location.name;

                item.addEventListener('click', () => {
                    nameInput.value = location.name;

                    if (targetLocationId) {
                        targetLocationId.value = location.id || '';

                        if (mergeWarning && location.name !== nameInput.defaultValue) {
                            document.getElementById('warningText').textContent =
                                `This location will be merged with "${location.name}" and all events will be updated.`;
                            mergeWarning.classList.remove('d-none');
                        }
                    }

                    suggestionBox.style.display = 'none';
                });

                suggestionBox.appendChild(item);
            });

            // Show suggestion box
            suggestionBox.style.display = 'block';
        } catch (error) {
            console.error('Error searching locations:', error);
        }
    }

    function setupNameInputEvents(nameInput) {
        if (!nameInput) return;

        nameInput.addEventListener('focus', searchLocations);
        nameInput.addEventListener('input', function () {
            searchLocations();

            const mergeWarning = document.getElementById('mergeWarning');
            const targetLocationId = document.getElementById('target_location_id');

            if (this.value.trim() === '') {
                if (mergeWarning) mergeWarning.classList.add('d-none');
                if (targetLocationId) targetLocationId.value = '';
            }
        });
    }

    // Using global showModal function from components/modal.html - duplicate removed

    document.addEventListener('DOMContentLoaded', function () {
        const editButtons = document.querySelectorAll('.edit-btn');

        editButtons.forEach(button => {
            button.addEventListener('click', async function (event) {
                event.preventDefault();
                event.stopPropagation();

                const locationId = this.getAttribute('data-location-id');

                try {
                    const response = await fetch(`/location/update/${locationId}`);
                    const formHtml = await response.text();

                    showModal('Edit Location', formHtml, {
                        actionText: 'Save',
                        onAction: function () {
                            const form = document.getElementById('edit-location-form');
                            if (form.checkValidity()) {
                                form.submit();
                            } else {
                                form.reportValidity();
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error loading edit form:', error);
                }
            });
        });
    });
</script>
{% endblock %}