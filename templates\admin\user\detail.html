{% extends "base.html" %}

{% block title %}User Details - Footprints{% endblock %}

{% block content %}
<!-- Back button positioned above tabs -->
<a href="javascript:void(0)" onclick="smartBack()"
    class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
    <i class="bi bi-arrow-left me-2"></i>
    <span id="backButtonText">Back</span>
</a>
<div class="container">

    <div class="mb-4">
        <h1 class="display-6 fw-bold">
            <span class="position-relative">
                User Management
                <span class="position-absolute start-0 bottom-0"
                    style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
            </span>
        </h1>

        <div class="border-bottom position-relative">
            <div class="d-flex" id="userTabNav">
                <div class="me-4 position-relative">
                    <a href="?active_tab=profile"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'profile' %}text-primary{% else %}text-secondary{% endif %} user-tab-link"
                        data-tab="profile-content">
                        Profile
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'profile' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
                <div class="me-4 position-relative">
                    <a href="?active_tab=activities"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'activities' %}text-primary{% else %}text-secondary{% endif %} user-tab-link"
                        data-tab="activities-content">
                        Activities
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'activities' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
                <div class="position-relative">
                    <a href="?active_tab=subscription"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'subscription' %}text-primary{% else %}text-secondary{% endif %} user-tab-link"
                        data-tab="subscription-content">
                        Subscription
                    </a>
                    <div class="position-absolute bottom-0 start-0 w-100 tab-underline{% if active_tab != 'subscription' %} d-none{% endif %}"
                        style="height: 3px; background-color: #6366f1;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="tab-content" id="userTabContent">
        <div class="tab-pane fade{% if active_tab == 'profile' %} show active{% endif %}" id="profile-content"
            role="tabpanel">
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="display-6 mb-0 d-flex align-items-center flex-wrap">
                                <span class="fw-bold">{{ user.username }}</span>
                                <div class="ms-3 d-flex gap-2 flex-wrap">
                                    {% if user.is_banned %}
                                    <span
                                        class="badge rounded-pill bg-danger-subtle text-danger border-0 fs-6 py-2 px-3">
                                        <i class="bi bi-person-x-fill me-1"></i>Banned
                                    </span>
                                    {% endif %}
                                    {% if user.is_blocked %}
                                    <span
                                        class="badge rounded-pill bg-warning-subtle text-warning border-0 fs-6 py-2 px-3">
                                        <i class="bi bi-lock-fill me-1"></i>Blocked
                                    </span>
                                    {% endif %}
                                    {% if not user.is_banned and not user.is_blocked %}
                                    <span
                                        class="badge rounded-pill bg-success-subtle text-success border-0 fs-6 py-2 px-3">
                                        <i class="bi bi-check-circle-fill me-1"></i>Active
                                    </span>
                                    {% endif %}
                                </div>
                            </h1>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-5">
                    <div class="card shadow-sm border-0 rounded-4 mb-3">
                        <div class="card-header bg-light py-3 rounded-top-4 border-0">
                            <h5 class="fw-bold mb-0">Profile</h5>
                        </div>
                        <div class="card-body d-flex flex-column align-items-center justify-content-center">
                            <div class="mb-3">
                                {% if user.profile_image %}
                                <img src="{{ url_for('static', filename='uploads/profile_images/' + user.profile_image) }}"
                                    class="img-fluid rounded-circle shadow-sm"
                                    style="width: 150px; height: 150px; object-fit: cover; border: 2px solid #000;"
                                    alt="{{ user.username }}'s profile"
                                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                                {% else %}
                                <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                                    class="img-fluid rounded-circle shadow-sm"
                                    style="width: 150px; height: 150px; object-fit: cover; border: 2px solid #000;"
                                    alt="Profile placeholder">
                                {% endif %}
                            </div>
                        </div>
                        <div class="w-100 text-center mb-3">
                            <div class="text-uppercase text-muted small fw-semibold mb-1" style="letter-spacing: 1px;">
                                ACCOUNT TYPE</div>
                            <span class="badge rounded-pill bg-light text-dark px-4 py-2 fs-6 mb-2"
                                style="font-weight:600;">
                                <i class="{{ get_role_icon(user.role) }} me-1"></i>{{ user.role|title }}
                            </span>
                        </div>
                    </div>

                    <!-- Role Management and Actions Card -->
                    <div class="card shadow-sm border-0 rounded-4 mt-3">
                        <div class="card-header bg-light py-3 rounded-top-4 border-0">
                            <h5 class="card-title fw-bold mb-0">Account Management</h5>
                        </div>
                        <div class="card-body p-3">
                            {% set is_current_user = current_user.id == user.id %}

                            <!-- Role Management -->
                            {% if can_change_user_roles() %}
                            <form method="post"
                                action="{{ url_for('user.update_user_role', user_id=user.id, referrer=request.args.get('referrer')) }}"
                                class="mb-3" id="roleForm">
                                <div class="text-muted small mb-1 fw-medium">Update User Role</div>
                                <div class="d-flex gap-2">
                                    <select class="form-select rounded-pill" id="role" name="role" {% if is_current_user
                                        %}disabled{% endif %} title="Select new role for user"
                                        data-current-role="{{ user.role }}">
                                        <option value="traveller" {% if user.role=='traveller' %}selected{% endif %}>
                                            Traveller
                                        </option>
                                        <option value="moderator" {% if user.role=='moderator' %}selected{% endif %}>
                                            Moderator
                                        </option>
                                        <option value="editor" {% if user.role=='editor' %}selected{% endif %}>Editor
                                        </option>
                                        <option value="support_tech" {% if user.role=='support_tech' %}selected{% endif
                                            %}>
                                            Support Tech
                                        </option>
                                        <option value="admin" {% if user.role=='admin' %}selected{% endif %}>Admin
                                        </option>
                                    </select>
                                    <button type="button" class="btn btn-primary rounded-pill" {% if is_current_user
                                        %}disabled{% endif %} title="Update user role" id="roleSubmitBtn">
                                        Update
                                    </button>
                                </div>
                                {% if is_current_user %}

                                {% else %}
                                <div id="roleUpdateMessage" class="text-muted small mt-1 fst-italic">
                                    Select a different role to enable update
                                </div>
                                {% endif %}
                            </form>
                            {% else %}
                            <div class="mb-3">
                                <div class="text-muted small mb-1 fw-medium">User Role</div>
                                <div class="d-flex align-items-center">
                                    <span
                                        class="badge rounded-pill {{ get_role_badge_class(user.role) }} border-0 px-3 py-2">
                                        <i class="{{ get_role_icon(user.role) }} me-1"></i>{{ user.role|title }}
                                    </span>
                                </div>
                                <div class="text-muted small mt-1 fst-italic">
                                    <i class="bi bi-info-circle me-1"></i>Only administrators can change user roles
                                </div>
                            </div>
                            {% endif %}

                            <!-- Account Actions -->
                            <div class="d-grid gap-2">
                                <button type="button"
                                    class="btn {% if user.is_blocked %}btn-success{% else %}btn-warning{% endif %} w-100 rounded-pill shadow-sm"
                                    {% if is_current_user %}disabled{% endif %} id="blockToggleBtn">
                                    <i
                                        class="bi {% if user.is_blocked %}bi-unlock{% else %}bi-lock{% endif %} me-2"></i>
                                    {% if user.is_blocked %}Allow Sharing{% else %}Block From Sharing{% endif %}
                                </button>

                                <button type="button"
                                    class="btn {% if user.is_banned %}btn-success{% else %}btn-danger{% endif %} w-100 rounded-pill shadow-sm"
                                    {% if is_current_user %}disabled{% endif %} id="banToggleBtn">
                                    <i
                                        class="bi {% if user.is_banned %}bi-person-check{% else %}bi-person-x{% endif %} me-2"></i>
                                    {% if user.is_banned %}Unban User{% else %}Ban User{% endif %}
                                </button>
                                <!-- Gift Subscription Button inside Account Management -->
                                <button class="btn btn-outline-primary rounded-pill w-100" id="giftSubscriptionBtn"
                                    type="button">
                                    <i class="bi bi-gift"></i> Gift Subscription
                                </button>
                            </div>



                            <!-- Hidden Forms for POST actions -->
                            <form id="blockForm" method="post"
                                action="{{ url_for('user.update_user_block_status', user_id=user.id, referrer=request.args.get('referrer')) }}"
                                class="d-none"></form>
                            <form id="banForm" method="post"
                                action="{{ url_for('user.update_user_ban_status', user_id=user.id, referrer=request.args.get('referrer')) }}"
                                class="d-none"></form>

                            {% if is_current_user %}
                            <div class="alert alert-info mt-2 mb-0 py-2 small rounded-3 border-0 shadow-sm">
                                <i class="bi bi-info-circle me-2"></i>You cannot modify your own account role & status
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-lg-8 col-md-7">
                    <div class="card shadow-sm border-0 rounded-4 h-100">
                        <div class="card-header bg-light py-3 rounded-top-4 border-0">
                            <h5 class="card-title fw-bold mb-0">Personal Information</h5>
                        </div>

                        <div class="card-body p-4">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label text-muted small text-uppercase fw-medium">First
                                        Name</label>
                                    <div class="profile-info fw-medium">{{ user.first_name or '—' }}</div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label text-muted small text-uppercase fw-medium">Last
                                        Name</label>
                                    <div class="profile-info fw-medium">{{ user.last_name or '—' }}</div>
                                </div>
                            </div>

                            <div class="mb-2 mt-2">
                                <label class="form-label text-muted small text-uppercase fw-medium">Username</label>
                                <div class="profile-info">
                                    <i class="bi bi-person-badge me-2 text-primary opacity-75"></i>
                                    <span class="fw-medium">{{ user.username }}</span>
                                </div>
                            </div>

                            <div class="mb-2 mt-2">
                                <label class="form-label text-muted small text-uppercase fw-medium">Email</label>
                                <div class="profile-info">
                                    <i class="bi bi-envelope me-2 text-primary opacity-75"></i>
                                    <span class="fw-medium">{{ user.email }}</span>
                                </div>
                            </div>

                            <div class="mb-2 mt-2">
                                <label class="form-label text-muted small text-uppercase fw-medium">Location</label>
                                <div class="profile-info">
                                    <i class="bi bi-geo-alt me-2 text-primary opacity-75"></i>
                                    <span class="fw-medium">{{ user.location or '—' }}</span>
                                </div>
                            </div>

                            <div class="mb-0 mt-2">
                                <label class="form-label text-muted small text-uppercase fw-medium">About</label>
                                <div class="profile-info description p-3 bg-light rounded-3 shadow-sm">
                                    <i class="bi bi-quote me-2 text-primary opacity-75"></i>
                                    {{ user.description or '—' }}
                                    <i class="bi bi-quote ms-2 text-primary opacity-75"
                                        style="display: inline-block; transform: rotate(180deg);"></i>
                                </div>
                            </div>
                            <div class="mb-0 mt-3">
                                <label class="form-label text-muted small text-uppercase fw-medium">Interests</label>
                                <div class="profile-info">
                                    <i class="bi bi-tags me-2 text-primary opacity-75"></i>
                                    <span class="fw-medium">{{ user.interests }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade{% if active_tab == 'activities' %} show active{% endif %}" id="activities-content"
            role="tabpanel">
            {% if active_tab == 'activities' %}
            {% include 'admin/user/activities.html' %}
            {% endif %}
        </div>
        <div class="tab-pane fade{% if active_tab == 'subscription' %} show active{% endif %}" id="subscription-content"
            role="tabpanel">
            {% if active_tab == 'subscription' %}
            {% include 'admin/user/subscription.html' %}
            {% endif %}
        </div>
    </div>
</div>

<!-- Include the common modal component -->
{% include 'components/modal.html' %}
{% endblock %}

{% block scripts %}
<script>
    // Simple and reliable back button function
    function smartBack() {
        const backButtonText = document.getElementById('backButtonText');
        if (backButtonText && backButtonText.textContent.trim() === 'Back to User Management') {
            window.location.href = '/user/manage';
            return;
        }

        // Method 1: Check for explicit back URL parameter (most reliable)
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');

        if (backUrl) {
            try {
                const decodedUrl = decodeURIComponent(backUrl);
                // Only allow internal URLs for security
                if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
                    window.location.href = decodedUrl;
                    return;
                }
            } catch (e) {
                // Invalid URL, continue to next method
            }
        }

        // Method 2: Simple history back (works most of the time)
        if (window.history.length > 1) {
            window.history.back();
            return;
        }

        // Method 3: Fallback based on context
        const isLoggedIn = "{{ 'true' if g.current_user else 'false' }}" === "true";
        const referrerParam = '{{ request.args.get("referrer") }}';

        if (referrerParam === 'hidden_journeys') {
            window.location.href = '{{ url_for("journey.admin_hidden_journeys") }}';
        } else if (isLoggedIn) {
            window.location.href = '{{ url_for("user.get_users") }}';
        } else {
            window.location.href = '{{ url_for("main.get_landing_page") }}';
        }
    }

    // Update back button text based on context
    function updateBackButtonText() {
        const backButtonText = document.getElementById('backButtonText');
        const referrer = document.referrer;
        const currentDomain = window.location.origin;
        const referrerParam = '{{ request.args.get("referrer") }}';

        if (referrer && referrer.startsWith(currentDomain)) {
            if (referrer.includes('/manage/hidden-journeys')) {
                backButtonText.textContent = 'Back to Hidden Journeys';
            } else if (referrer.includes('/user/manage') || referrer.includes('/user/list')) {
                backButtonText.textContent = 'Back to User Management';
            } else if (referrer.includes('/user/') && referrer.includes('activities')) {
                backButtonText.textContent = 'Back to User Activities';
            } else if (referrer.includes('/admin') || referrer.includes('/dashboard')) {
                backButtonText.textContent = 'Back to Dashboard';
            } else {
                backButtonText.textContent = 'Back';
            }
        } else {
            // No referrer or external referrer - use referrer parameter
            if (referrerParam === 'hidden_journeys') {
                backButtonText.textContent = 'Back to Hidden Journeys';
            } else {
                backButtonText.textContent = 'Back to User Management';
            }
        }
    }

    // Debug: User details template loaded
    document.addEventListener('DOMContentLoaded', function () {
        // Update back button text on page load
        updateBackButtonText();
        // Gift Subscription button event listener
        const giftBtn = document.getElementById('giftSubscriptionBtn');
        if (giftBtn) {
            giftBtn.addEventListener('click', function () {
                const modalContent = `
          <form id="giftSubscriptionForm" method="POST" action="{{ url_for('user.gift_subscription', user_id=user.id) }}">
            <div class="mb-3">
              <label for="giftMonths" class="form-label">Months</label>
              <input type="number" class="form-control" id="giftMonths" name="months" min="1" max="12" value="1" required>
            </div>
            <div class="mb-3">
              <label for="giftReason" class="form-label">Reason (optional)</label>
              <textarea class="form-control" id="giftReason" name="reason" rows="2"></textarea>
            </div>
          </form>
        `;

                // Check if showModal function exists
                if (typeof showModal === 'function') {
                    showModal(
                        'Gift Subscription',
                        modalContent,
                        {
                            actionText: 'Confirm',
                            onAction: function () {
                                const form = document.getElementById('giftSubscriptionForm');
                                const monthsInput = document.getElementById('giftMonths');

                                // Ensure months is at least 1
                                if (monthsInput && (!monthsInput.value || parseInt(monthsInput.value) < 1)) {
                                    monthsInput.value = 1;
                                }

                                form.classList.add('was-validated');

                                if (!form.checkValidity()) {
                                    // Focus the first invalid field instead of showing browser popup
                                    const firstInvalidField = form.querySelector(':invalid');
                                    if (firstInvalidField) {
                                        firstInvalidField.focus();
                                    }
                                    return false; // Prevent modal from closing
                                }

                                form.submit();
                                return true; // Allow modal to close
                            }
                        }
                    );
                } else {
                    console.error('showModal function is not defined!');
                    alert('A system error has occurred. Please contact the administrator.');
                }
            });
        }

        // Role update validation
        const roleForm = document.getElementById('roleForm');
        const roleSelect = document.getElementById('role');
        const roleSubmitBtn = document.getElementById('roleSubmitBtn');
        const roleUpdateMessage = document.getElementById('roleUpdateMessage');

        if (roleSelect && roleSubmitBtn && roleUpdateMessage) {
            const currentRole = roleSelect.getAttribute('data-current-role');

            function validateRoleSelection() {
                const selectedRole = roleSelect.value;
                if (selectedRole === currentRole) {
                    roleSubmitBtn.disabled = true;
                    roleUpdateMessage.classList.remove('d-none', 'text-success', 'text-danger');
                    roleUpdateMessage.classList.add('text-muted');
                    roleUpdateMessage.textContent = 'Select a different role to enable update';
                    return false;
                } else {
                    roleSubmitBtn.disabled = false;
                    roleUpdateMessage.classList.remove('d-none', 'text-muted', 'text-danger');
                    roleUpdateMessage.classList.add('text-success');
                    roleUpdateMessage.innerHTML = `<i class="bi bi-check-circle me-1"></i>Click update button to change role to ${selectedRole.charAt(0).toUpperCase() + selectedRole.slice(1)}`;
                    return true;
                }
            }

            roleSelect.addEventListener('change', validateRoleSelection);
            validateRoleSelection();

            // Role update confirmation
            roleSubmitBtn.addEventListener('click', function (e) {
                if (!validateRoleSelection()) {
                    e.preventDefault();
                    return;
                }

                const newRole = roleSelect.value;

                if (typeof showModal === 'function') {
                    showModal(
                        'Confirm Role Update',
                        `<p>Are you sure you want to change this user\'s role to <strong>${newRole.charAt(0).toUpperCase() + newRole.slice(1)}</strong>?</p>
            <p class="text-muted small mb-0">This will modify the user\'s permissions and access levels.</p>`,
                        {
                            actionText: 'Update Role',
                            onAction: function () {
                                roleForm.submit();
                            }
                        }
                    );
                } else {
                    console.error('showModal function is not defined!');
                    if (confirm(`Are you sure you want to change this user\'s role to ${newRole}?`)) {
                        roleForm.submit();
                    }
                }
            });
        }

        // Block and Ban forms
        const blockForm = document.getElementById('blockForm');
        const banForm = document.getElementById('banForm');
        const blockToggleBtn = document.getElementById('blockToggleBtn');
        const banToggleBtn = document.getElementById('banToggleBtn');

        if (blockToggleBtn && blockForm) {
            blockToggleBtn.addEventListener('click', function () {
                const buttonText = this.textContent.trim();
                const isAllowAction = buttonText.includes('Allow');
                const action = isAllowAction ? 'allow sharing for' : 'block sharing for';
                const consequence = isAllowAction
                    ? 'will be able to share content again'
                    : 'will not be able to share any content';

                if (typeof showModal === 'function') {
                    showModal(
                        `Confirm ${buttonText}`,
                        `<p>Are you sure you want to ${action} <strong>{{ user.username }}</strong>?</p>
            <p class="text-muted small mb-0">User ${consequence}.</p>`,
                        {
                            actionText: buttonText,
                            onAction: function () {
                                blockForm.submit();
                            }
                        }
                    );
                } else {
                    console.error('showModal function is not defined!');
                    if (confirm(`Are you sure you want to ${action} {{ user.username }}?`)) {
                        blockForm.submit();
                    }
                }
            });
        }

        if (banToggleBtn && banForm) {
            banToggleBtn.addEventListener('click', function () {
                const buttonText = this.textContent.trim();
                const isUnbanAction = buttonText.includes('Unban');
                const action = isUnbanAction ? 'unban' : 'ban';
                const consequence = isUnbanAction
                    ? 'will regain access to their account'
                    : 'will be completely locked out of their account';

                if (typeof showModal === 'function') {
                    showModal(
                        `Confirm ${buttonText}`,
                        `<p>Are you sure you want to ${action} <strong>{{ user.username }}</strong>?</p>
            <p class="text-muted small mb-0">User ${consequence}.</p>`,
                        {
                            actionText: buttonText,
                            onAction: function () {
                                banForm.submit();
                            }
                        }
                    );
                } else {
                    console.error('showModal function is not defined!');
                    if (confirm(`Are you sure you want to ${action} {{ user.username }}?`)) {
                        banForm.submit();
                    }
                }
            });
        }
    });
</script>
{% endblock %}