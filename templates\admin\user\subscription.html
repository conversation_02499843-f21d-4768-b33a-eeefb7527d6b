{% from 'components/pagination.html' import render_pagination %}

{% block content %}
<div class="container-fluid px-4">
  <div class="row g-4" id="subscriptionContainer">
    <!-- Current Plan Card -->
    <div class="col-lg-6">
      <div class="card shadow-sm border-0 rounded-3 h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                {% if active_subscription %}
                <div class="rounded-circle bg-warning d-flex align-items-center justify-content-center text-white"
                  style="width: 64px; height: 64px;">
                  <i class="bi bi-trophy fs-3"></i>
                </div>
                {% else %}
                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center text-muted"
                  style="width: 64px; height: 64px;">
                  <i class="bi bi-flower1 fs-3"></i>
                </div>
                {% endif %}
              </div>
              <div>
                <h2 class="fs-3 fw-bold mb-1">
                  {% if user_role != 'traveller' %}
                  Premium
                  {% else %}
                  {% if active_subscription and active_subscription.plan_code == 'admin_gift' %}
                  Premium
                  {% elif active_subscription and active_subscription.plan_code == 'free_trial' %}
                  Premium
                  {% elif not active_subscription %}
                  Free Plan
                  {% else %}
                  {{ active_subscription.plan_name or 'Premium' }}
                  {% endif %}
                  {% endif %}
                </h2>
                <div>
                  {% if user_role != 'traveller' %}
                  <span class="badge bg-primary rounded-pill py-1 px-3">{{ user_role|capitalize }}</span>
                  {% else %}
                  {% if active_subscription and active_subscription.plan_code == 'admin_gift' %}
                  <span class="badge bg-dark rounded-pill py-1 px-3">Admin Gift</span>
                  {% elif active_subscription and active_subscription.plan_code == 'free_trial' %}
                  <span class="badge bg-info rounded-pill py-1 px-3">Free Trial</span>
                  {% elif active_subscription %}
                  <span class="badge bg-warning rounded-pill py-1 px-3">Active</span>
                  {% else %}
                  <span class="badge bg-secondary rounded-pill py-1 px-3">Free</span>
                  {% endif %}
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
          <div class="subscription-details">
            {% if active_subscription %}
            <div class="mb-3">
              <h6 class="text-uppercase text-muted small fw-bold mb-2">Subscription Status</h6>
              <p class="mb-1">User's subscription expires on <strong>{{ active_subscription.end_date|formatdate
                  }}</strong></p>
              <p class="text-muted small mb-0">No commitment plan - manual renewal required</p>
            </div>
            {% if active_subscription.plan_code == 'admin_gift' %}
            <div class="mb-3">
              <h6 class="text-uppercase text-muted small fw-bold mb-2">Gift Reason</h6>
              <p class="mb-0">{{ active_subscription.reason if active_subscription.reason else '-' }}</p>
            </div>
            {% endif %}
            {% else %}
            <div class="mb-3">
              <h6 class="text-uppercase text-muted small fw-bold mb-2">Subscription Status</h6>
              {% if user_role != 'traveller' %}
              <p class="mb-0 text-muted">This user is a <strong>{{ user_role|capitalize }}</strong>, so they
                automatically have premium access.</p>
              {% else %}
              <p class="mb-0 text-muted">This user does not have an active subscription. Upgrade to unlock premium
                features!</p>
              {% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    <!-- Payment Card -->
    <div class="col-lg-6">
      <div class="card shadow-sm border-0 rounded-3 h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center justify-content-between mb-4">
            <h3 class="fs-4 fw-bold mb-0">Payment Method</h3>
          </div>
          <div class="payment-info">
            {% if active_subscription and latest_payment and latest_payment.card_last_four %}
            <div class="d-flex align-items-center">
              <div class="me-3">
                <div class="rounded bg-light p-2">
                  <i class="bi bi-credit-card fs-4 text-muted"></i>
                </div>
              </div>
              <div>
                <div class="fw-semibold">Card ending in {{ latest_payment.card_last_four }}</div>
                <div class="text-muted small">Primary payment method</div>
              </div>
            </div>
            {% else %}
            <div class="text-center py-4">
              <i class="bi bi-credit-card text-muted" style="font-size: 3rem;"></i>
              <h5 class="mt-3">No Payment Method</h5>
              <p class="text-muted mb-0">No payment information available.</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    <!-- Subscription History Card -->
    <div class="col-12">
      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-body p-4">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="fs-4 fw-bold mb-0">Subscription History</h3>
          </div>
          {% if all_subscriptions %}
          <div class="table-responsive">
            <table class="table align-middle table-hover">
              <thead>
                <tr>
                  <th>Period</th>
                  <th class="d-none d-sm-table-cell">Plan</th>
                  <th class="d-none d-lg-table-cell">Duration</th>
                  <th class="d-none d-sm-table-cell">Amount</th>
                  <th class="d-none d-sm-table-cell">Country</th>
                  <th class="d-none d-lg-table-cell">Note</th>
                  <th>Receipt</th>
                </tr>
              </thead>
              <tbody>
                {% for sub in all_subscriptions %}
                <tr>
                  <td class="py-3">
                    <div class="fw-semibold">{{ sub.start_date|formatdate }}</div>
                    <div class="text-muted small">to {{ sub.end_date|formatdate }}</div>
                  </td>
                  <td class="py-3 d-none d-sm-table-cell">
                    {% if user_role != 'traveller' %}
                    <span class="badge bg-primary rounded-pill">Premium ({{ user_role }})</span>
                    {% else %}
                    {% if sub.plan_code == 'admin_gift' %}
                    <span class="badge bg-dark rounded-pill">Admin Gift</span>
                    {% elif sub.plan_code == 'free_trial' %}
                    <span class="badge bg-secondary rounded-pill">Free Trial</span>
                    {% elif sub.plan_name|lower == 'free' %}
                    <span class="badge bg-light text-dark rounded-pill">Free</span>
                    {% else %}
                    <span class="badge rounded-pill"
                      style="background:rgba(78,107,255,0.15); color:#4e6bff; font-weight:600;">
                      {{ sub.plan_name }}
                    </span>
                    {% endif %}
                    {% endif %}
                  </td>
                  <td class="py-3 d-none d-lg-table-cell">
                    <span class="fw-semibold">{{ sub.period_months }}</span>
                    <span class="text-muted">month{{ 's' if sub.period_months != 1 else '' }}</span>
                  </td>
                  <td class="py-3 d-none d-sm-table-cell">
                    <div class="fw-semibold">${{ sub.discounted_amount|round(2) }}</div>
                    {% if sub.discount_percentage %}
                    <div class="text-success small">{{ sub.discount_percentage }}% discount</div>
                    {% endif %}
                  </td>
                  <td class="py-3 d-none d-sm-table-cell">
                    {% if sub.country_code %}
                    <span class="badge bg-light text-dark rounded-pill">{{ sub.country_code }}</span>
                    {% else %}
                    <span class="text-muted">—</span>
                    {% endif %}
                  </td>
                  <td class="py-3 d-none d-lg-table-cell">
                    {% if sub.plan_code == "admin_gift" %}
                    <span class="text-muted small">{{ sub.reason }}</span>
                    {% else %}
                    <span class="text-muted">—</span>
                    {% endif %}
                  </td>
                  <td class="py-3">
                    {% if sub.plan_code == 'admin_gift' or sub.plan_code == 'free_trial' %}
                    <span class="text-muted">—</span>
                    {% else %}
                    <a href="{{ url_for('subscription.get_receipt', payment_id=sub.payment_id or sub.id, subscription_id=sub.id, user_id=sub.user_id) }}"
                      target="_blank" class="btn btn-outline-dark btn-sm">View</a>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          {% else %}
          <div class="text-center py-5">
            <i class="bi bi-clock-history text-muted" style="font-size: 4rem;"></i>
            <h4 class="mt-3">No History Yet</h4>
            <p class="text-muted mb-0">No subscription history found.</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  #subscriptionContainer {
    min-height: calc(100vh - 200px);
  }

  .subscription-details .text-uppercase {
    letter-spacing: 0.5px;
    font-size: 0.7rem;
  }

  .payment-info .rounded {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .table th {
    font-weight: 600;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .table td {
    vertical-align: middle;
    border-top: 1px solid rgba(0, 0, 0, .05);
  }

  .table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, .02);
  }

  .badge {
    font-weight: 500;
  }

  .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .btn,
  button.btn,
  a.btn {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    font-weight: 500;
  }

  .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, .3);
  }

  @media (max-width: 767.98px) {
    .container-fluid {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .card-body {
      padding: 1.5rem !important;
    }

    .table-responsive {
      font-size: 0.875rem;
    }

    .d-flex.align-items-center.justify-content-between {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .d-flex.align-items-center.justify-content-between .btn {
      align-self: stretch;
    }
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d1d1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #b1b1b1;
  }
</style>
{% endblock %}