{% extends "base.html" %}

{% block title %}Create Announcement - Travel Journal{% endblock %}

{% block content %}
<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card border-0">
      <div class="card-header  border-0">
        <h1 class="card-title">Create New Announcement</h1>
      </div>
      <div class="card-body  border-0">
        <form method="post" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" required>
            <div class="invalid-feedback">Please provide a title.</div>
          </div>
          <div class="mb-3">
            <label for="content" class="form-label">Content</label>
            <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
            <div class="invalid-feedback">Please provide content for the announcement.</div>
          </div>
          <div class="d-flex justify-content-center">
            <button type="submit" class="btn btn-dark ms-2">Create Announcement</button>
            <a href="{{ url_for('announcement.get_all_announcements') }}" class="btn btn-secondary ms-2">Cancel</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
{% endblock %}