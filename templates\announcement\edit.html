{% extends "base.html" %}

{% block title %}Edit Announcement - Travel Journal{% endblock %}

{% block content %}
<div class="row justify-content-center">
  <div class="col-md-8">
    <div class="card border-0">
      <div class="card-header border-0">
        {% if can_manage_content() %}
        <h1 class="card-title">Edit Announcement</h1>
        {% else %}
        <h1 class="card-title">View Announcement</h1>
        {% endif %}
      </div>
      <div class="card-body border-0">
        <form method="post" action="{{ url_for('announcement.update_announcement', announcement_id=announcement.id) }}" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="title" class="form-label">Title</label>
            <input type="text" class="form-control" id="title" name="title" value="{{ announcement.title }}" required>
            <div class="invalid-feedback">Please provide a title.</div>
          </div>
          <div class="mb-3">
            <label for="content" class="form-label">Content</label>
            <textarea class="form-control" id="content" name="content" rows="5"
              required>{{ announcement.content }}</textarea>
            <div class="invalid-feedback">Please provide content for the announcement.</div>
          </div>

          {% if can_manage_content() %}
          <div class="d-flex justify-content-center">
            <button type="submit" class="btn btn-dark ms-2">Update Announcement</button>
            <a href="{{ url_for('announcement.get_all_announcements') }}" class="btn btn-secondary ms-2">Cancel</a>

          </div>
          {% else %}
          <div class="row mb-3 justify-content-center">
            <a href="{{ url_for('announcement.get_user_read_announcements') }}"
              class="btn btn-secondary col-md-4">Back</a>
          </div>
          {% endif %}
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
{% endblock %}