{% extends "base.html" %}

{% block title %}Announcements - Footprints{% endblock %}

{% block content %}
<div class="container">
    <div class="row align-items-center mb-4">
        <div class="col-md-6 mb-3 mb-md-0">
            <h1 class="display-6 fw-bold">
                <span class="position-relative">
                    Announcements
                    <span class="position-absolute start-0 bottom-0 bg-primary opacity-25 rounded"
                        style="height: 6px; width: 60%;"></span>
                </span>
            </h1>
        </div>
        <div class="col-md-6">
            <div class="d-flex justify-content-md-end">
                {% if is_staff() %}
                <a href="{{ url_for('announcement.get_all_announcements') }}"
                    class="btn btn-dark rounded-pill px-4 py-2 d-flex align-items-center">
                    <i class="bi bi-gear me-2"></i> Manage Announcements
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h5 class="fw-bold mb-0">Total ({{ total_count|default(0) }})</h5>
    </div>

    <!-- Tabs -->
    <div class="mb-4">
        <div class="border-bottom position-relative">
            <div class="d-flex">
                <div class="me-4 position-relative">
                    <a href="{{ url_for('announcement.get_announcements', active_tab='all') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'all' %}text-primary{% else %}text-secondary{% endif %}">
                        All
                    </a>
                    {% if active_tab == 'all' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('announcement.get_announcements', active_tab='unread') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'unread' %}text-primary{% else %}text-secondary{% endif %}">
                        Unread
                    </a>
                    {% if active_tab == 'unread' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>
                <div class="me-4 position-relative">
                    <a href="{{ url_for('announcement.get_announcements', active_tab='read') }}"
                        class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'read' %}text-primary{% else %}text-secondary{% endif %}">
                        Read
                    </a>
                    {% if active_tab == 'read' %}
                    <div class="position-absolute bottom-0 start-0 w-100"
                        style="height: 3px; background-color: #6366f1;"></div>
                    {% endif %}
                </div>

            </div>
        </div>
    </div>

    {% if announcements %}
    <div class="table-responsive">
        <table class="table align-middle table-hover">
            <thead>
                <tr>
                    <th>No.</th>
                    <th style="max-width: 150px;">Title</th>
                    <th class="d-none d-lg-table-cell">Content</th>
                    <th class="d-none d-md-table-cell">Author</th>
                    <th class="d-none d-lg-table-cell">Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for announcement in announcements %}
                <tr>
                    <td>{{ (page - 1) * 10 + loop.index }}</td>
                    <td style="max-width: 150px;" class="text-truncate">{{ announcement.title }}</td>
                    <td class="d-none d-lg-table-cell">
                        {% if announcement.content %}
                        <div class="small">{{ announcement.content|truncate(100) }}</div>
                        {% else %}
                        <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                    <td class="d-none d-md-table-cell">{{ announcement.username }}</td>
                    <td class="d-none d-lg-table-cell">{{ announcement.created_at|formatdate }}</td>
                    <td>
                        <button type="button" class="btn btn-outline-dark btn-sm view-announcement-btn"
                            data-id="{{ announcement.id }}" data-title="{{ announcement.title }}"
                            data-content="{{ announcement.content }}"
                            data-created="{{ announcement.created_at.strftime('%B %d, %Y') }}">
                            View
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center"
            style="--bs-pagination-active-bg: #000; --bs-pagination-active-border-color: #000; --bs-pagination-color: #000; --bs-pagination-hover-color: #000; --bs-pagination-focus-color: #000;">
            {% if page > 1 %}
            <li class="page-item">
                <a class="page-link border-0" href="{{ url_for(request.endpoint, page=page-1) }}">«</a>
            </li>
            {% endif %}

            {% for p in range(1, total_pages + 1) %}
            {% if p == page %}
            <li class="page-item active">
                <span class="page-link border-0">{{ p }}</span>
            </li>
            {% else %}
            <li class="page-item">
                <a class="page-link border-0" href="{{ url_for(request.endpoint, page=p) }}">{{ p }}</a>
            </li>
            {% endif %}
            {% endfor %}

            {% if page < total_pages %} <li class="page-item">
                <a class="page-link border-0" href="{{ url_for(request.endpoint, page=page+1) }}">»</a>
                </li>
                {% endif %}
        </ul>
    </nav>
    {% else %}
    <div class="d-flex align-items-center alert"
        style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
        <i class="bi bi-info-circle-fill me-3 fs-4"></i>
        <p class="mb-0">
            {% if active_tab == 'unread' %}
            No unread announcements at the moment.
            {% elif active_tab == 'read' %}
            No previously read announcements found.
            {% else %}
            No announcements found.
            {% endif %}
        </p>
    </div>
    {% endif %}
</div>

{% include 'components/modal.html' %}

<style>
    @media (max-width: 576px) {

        td.text-truncate[style*="max-width: 120px;"],
        th[style*="max-width: 120px;"] {
            max-width: 90px !important;
        }
    }
</style>

{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const viewButtons = document.querySelectorAll('.view-announcement-btn');
        const currentEndpoint = "{{ request.endpoint }}";

        viewButtons.forEach(button => {
            button.addEventListener('click', function () {
                const id = this.getAttribute('data-id');
                const title = this.getAttribute('data-title');
                const content = this.getAttribute('data-content');
                const created = this.getAttribute('data-created');

                const modalContent = `
                    <div class="announcement-detail">
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-calendar3 me-2"></i>
                            <small class="text-muted">${created}</small>
                        </div>
                        <p class="mb-3">${content}</p>
                    </div>
                `;

                const modalOptions = {};

                if (currentEndpoint === "announcement.get_user_unread_announcements") {
                    modalOptions.actionText = 'Mark as Read';
                    modalOptions.onAction = function () {
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = "{{ url_for('announcement.mark_announcement_as_read', announcement_id=0) }}".replace('0', id) +
                            `?next=${currentEndpoint}`;
                        document.body.appendChild(form);
                        form.submit();
                    };
                }

                showModal(title, modalContent, modalOptions);
            });
        });
    });
</script>
{% endblock %}