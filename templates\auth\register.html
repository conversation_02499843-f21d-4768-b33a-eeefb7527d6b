{% extends "base.html" %}
{% from "components/password_input.html" import password_input %}

{% block title %}Register - Footprints{% endblock %}

{% block content %}
<div class="container login-container">
    <div class="row justify-content-center align-items-center full-height">
        <div class="col-md-10">
            <div class="row g-0 login-wrapper register-responsive-wrapper">
                <!-- left-side: image -->
                <div class="col-md-6 d-none d-md-block p-0 d-flex align-items-center register-image-col">
                    <div class="register-image-wrapper w-100">
                        <img src="{{ url_for('static', filename='images/login_img.jpg') }}" alt="Register Image"
                            class="register-image">
                    </div>
                </div>
                <!-- right-side: form -->
                <div class="col-md-6 login-form-section">
                    <div class="px-4 py-0 d-flex flex-column justify-content-center h-100">
                        <h1 class="display-6 fw-bold mb-2">Sign up</h1>

                        <form method="post" action="{{ url_for('auth.register') }}" class="needs-validation" novalidate>
                            <!-- Required Fields -->
                            <div class="row g-2">
                                <div class="col-md-6 mb-2">
                                    <label for="username" class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="username" name="username"
                                        value="{{ username if username else '' }}" required
                                        oninput="validateUsername(this)">
                                    <div class="invalid-feedback"></div>
                                    <div class="form-text" style="font-size:x-small;">Choose a unique username.</div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                        value="{{ email if email else '' }}" required oninput="validateEmail(this)">
                                    <div class="invalid-feedback">Please enter a valid email address.</div>
                                </div>
                            </div>

                            <!-- Password Fields -->
                            <div class="row g-2">
                                <div class="col-md-6 mb-2">
                                    <div class="input-group-custom">
                                        {{ password_input(
                                        id="password",
                                        name="password",
                                        label="Password *",
                                        required=true
                                        ) }}
                                    </div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <div class="input-group-custom">
                                        {{ password_input(
                                        id="confirm_password",
                                        name="confirm_password",
                                        label="Confirm Password *",
                                        required=true
                                        ) }}
                                    </div>
                                </div>
                            </div>

                            <!-- Password Requirements Box -->
                            <div class="password-requirements mb-3 p-2 rounded"
                                style="background-color: rgba(0,0,0,0.02); border: 1px solid rgba(0,0,0,0.1);">
                                <small class="text-muted d-block mb-1" style="font-size: 0.8rem;">Password
                                    Requirements:</small>
                                <div class="requirements-grid">
                                    <div class="requirement" id="req-length" style="font-size: 0.8rem;">
                                        <i class="bi bi-x-circle text-danger" style="font-size: 0.8rem;"></i> 8+
                                        characters
                                    </div>
                                    <div class="requirement" id="req-uppercase" style="font-size: 0.8rem;">
                                        <i class="bi bi-x-circle text-danger" style="font-size: 0.8rem;"></i> One
                                        uppercase letter
                                    </div>
                                    <div class="requirement" id="req-lowercase" style="font-size: 0.8rem;">
                                        <i class="bi bi-x-circle text-danger" style="font-size: 0.8rem;"></i> One
                                        lowercase letter
                                    </div>
                                    <div class="requirement" id="req-number" style="font-size: 0.8rem;">
                                        <i class="bi bi-x-circle text-danger" style="font-size: 0.8rem;"></i> One
                                        number
                                    </div>
                                    <div class="requirement" id="req-special" style="font-size: 0.8rem;">
                                        <i class="bi bi-x-circle text-danger" style="font-size: 0.8rem;"></i> One
                                        special character
                                    </div>
                                </div>
                            </div>

                            <!-- Optional Fields -->
                            <div class="mt-2">
                                <div class="row g-2">
                                    <div class="col-md-6 mb-2">
                                        <label for="first_name" class="form-label">First Name</label>
                                        <input type="text" class="form-control optional-field" id="first_name"
                                            name="first_name" value="{{ first_name if first_name else '' }}"
                                            oninput="validateName(this)">
                                        <div class="invalid-feedback"></div>
                                        <div class="valid-feedback" style="display: none;"></div>
                                        <div class="form-text" style="font-size:x-small;">(optional)</div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <label for="last_name" class="form-label">Last Name</label>
                                        <input type="text" class="form-control optional-field" id="last_name"
                                            name="last_name" value="{{ last_name if last_name else '' }}"
                                            oninput="validateName(this)">
                                        <div class="invalid-feedback"></div>
                                        <div class="valid-feedback" style="display: none;"></div>
                                        <div class="form-text" style="font-size:x-small;">(optional)</div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="location" class="form-label">Location</label>
                                    <input type="text" class="form-control optional-field" id="location" name="location"
                                        value="{{ location if location else '' }}">
                                    <div class="invalid-feedback"></div>
                                    <div class="form-text" style="font-size:x-small;">Where are you from? (optional)
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <button type="submit" class="signup-button btn btn-outline-dark w-100">Sign
                                    up</button>
                            </div>
                        </form>

                        <p class="text-center mb-0">
                            Already have an account?
                            <a href="{{ url_for('auth.login') }}" class="fw-medium text-decoration-none">Login</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    @media (max-width: 1024px) {
        .register-responsive-wrapper {
            flex-direction: column !important;
            display: flex !important;
            align-items: center !important;
        }

        .register-image-col {
            display: none !important;
        }

        .login-form-section {
            width: 100% !important;
            max-width: 400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
    }
</style>
{% endblock %}

{% include 'components/chatbot.html' %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
{% endblock %}

{% block scripts %}
<script>
    window.PASSWORD_REQUIREMENT_IDS = ["password"];
</script>
<script src="{{ url_for('static', filename='js/password_toggle.js') }}"></script>
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/password_validation.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Setup password validation
        setupPasswordConfirmation('password', 'confirm_password');
        const form = document.querySelector('form');
        // Real-time validation for required fields
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.addEventListener('input', function () {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });
    });
</script>
{% endblock %}