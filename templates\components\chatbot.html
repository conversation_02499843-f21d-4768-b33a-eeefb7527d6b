<!-- Chatbot Assistant Component -->
<style>
  #chatbot-fab {
    position: fixed;
    right: 32px;
    bottom: 62px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #7b2ff2 0%, #4e6bff 100%);
    color: #fff;
    box-shadow: 0 6px 24px rgba(123, 47, 242, 0.18),
      0 1.5px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    cursor: pointer;
    border: none;
    outline: none;
    transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
  }

  #chatbot-fab:hover {
    background: linear-gradient(135deg, #4e6bff 0%, #7b2ff2 100%);
    box-shadow: 0 10px 32px rgba(123, 47, 242, 0.32),
      0 2px 8px rgba(0, 0, 0, 0.13);
    transform: translateY(-2px) scale(1.06);
  }

  #chatbot-fab i {
    color: #fff !important;
    font-size: 1.7rem;
  }

  #chatbot-window {
    position: fixed;
    right: 40px;
    bottom: 140px;
    width: 290px;
    max-width: 95vw;
    height: 450px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border: 1px solid #000;
    z-index: 10000;
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: chatbot-pop 0.2s cubic-bezier(0.4, 1.4, 0.6, 1) 1;
  }

  @keyframes chatbot-pop {
    0% {
      opacity: 0;
      transform: translateY(20px) scale(0.96);
    }

    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  #chatbot-header {
    background: #fff;
    color: #000;
    padding: 1rem 1.2rem;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #000;
    flex-shrink: 0;
  }

  #chatbot-close {
    background: none;
    border: none;
    color: #000;
    font-size: 1.4rem;
    cursor: pointer;
    opacity: 0.9;
    transition: opacity 0.15s;
    padding: 4px;
    line-height: 1;
  }

  #chatbot-close:hover {
    opacity: 0.7;
  }

  #chatbot-body {
    padding: 1.2rem;
    background: #f8f9fa;
    flex: 1;
    font-size: 0.95rem;
    color: #333;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    overflow-y: auto;
    min-height: 0;
  }

  #chatbot-messages {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    overflow-y: auto;
  }

  .chatbot-message {
    max-width: 85%;
    padding: 0.8em 1.1em;
    border-radius: 12px;
    font-size: 0.95rem;
    line-height: 1.4;
    word-break: break-word;
  }

  .chatbot-message.bot {
    background: #fff;
    color: #333;
    align-self: flex-start;
    border: 1px solid #000;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  }

  .chatbot-message.user {
    background: #000;
    color: #fff;
    align-self: flex-end;
  }

  #chatbot-input {
    flex: 1 1 auto;
    border-radius: 20px;
    border: 1px solid #e3eaff;
    padding: 0.7rem 1rem;
    font-size: 0.95rem;
    outline: none;
    transition: all 0.2s;
    background: #fff;
    margin: 0;
  }

  #chatbot-input:focus {
    border-color: #000;
    background: #fff;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  }

  #chatbot-send {
    background: #000;
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s;
    padding: 0;
    margin: 0;
  }

  #chatbot-send:hover {
    background: #333;
    transform: scale(1.05);
  }

  #chatbot-body::-webkit-scrollbar {
    width: 6px;
  }

  #chatbot-body::-webkit-scrollbar-track {
    background: transparent;
  }

  #chatbot-body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  #chatbot-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
  }
</style>

<button id="chatbot-fab" title="Chat Assistant" aria-label="Chat Assistant">
  <i class="bi bi-send"></i>
</button>
<div id="chatbot-window">
  <div
    id="chatbot-header"
    class="d-flex align-items-center justify-content-between border-bottom px-3 py-2 bg-white rounded-top"
  >
    <span class="fw-bold fs-5">Create a request</span>
    <button id="chatbot-close" aria-label="Close">
      <i class="bi bi-x"></i>
    </button>
  </div>
  <form
    id="chatbot-form"
    class="p-3 bg-light rounded-bottom"
    method="POST"
    action="/helpdesk/create"
  >
    <div class="mb-2">
      <label for="chatbot-subject" class="form-label fw-semibold text-primary"
        >Title</label
      >
      <input
        type="text"
        class="form-control rounded-3"
        id="chatbot-subject"
        name="title"
        placeholder="Short summary (e.g. can't login)"
        required
        maxlength="80"
      />
    </div>
    <div class="mb-2">
      <label
        for="chatbot-description"
        class="form-label fw-semibold text-primary"
        >Description</label
      >
      <textarea
        class="form-control rounded-3"
        id="chatbot-description"
        name="description"
        rows="4"
        placeholder="Please describe your request..."
        required
        maxlength="500"
      ></textarea>
    </div>
    <!-- Username field for ban appeals -->
    <div class="mb-3" id="usernameField" style="display: none">
      <label for="username" class="form-label fw-semibold text-primary"
        >Username</label
      >
      <input
        type="text"
        class="form-control"
        id="username"
        name="username"
        value="{{ request.form.get('username', '') }}"
        placeholder="Enter the username of the banned account"
      />
      <div class="invalid-feedback">Username is required for ban appeals.</div>
      <div class="form-text">
        <i class="bi bi-info-circle me-1"></i>
        Please enter the exact username of the banned account you want to appeal
        for.
      </div>
    </div>
    <div class="mb-4">
      <label for="chatbot-type" class="form-label fw-semibold text-primary"
        >Type</label
      >
      <select
        class="form-select rounded-3"
        id="chatbot-type"
        name="category"
        required
      >
        <option value="" disabled selected>Select type</option>
        {% for t in request_types %}
        <option value="{{ t }}">
          {% if t == 'help' %}❓ Help {% elif t == 'bug' %}🐛 Bug {% elif t ==
          'appeal' %}🛡️ Appeal {% elif t == 'other' %}🔖 Other {% else %}{{
          t|capitalize }}{% endif %}
        </option>
        {% endfor %}
      </select>
    </div>
    <button
      type="submit"
      class="btn btn-primary w-100 rounded-pill fw-semibold py-2 fs-6 mb-3"
    >
      Submit
    </button>
  </form>
</div>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const fab = document.getElementById("chatbot-fab");
    const windowEl = document.getElementById("chatbot-window");
    const closeBtn = document.getElementById("chatbot-close");
    const input = document.getElementById("chatbot-input");
    if (fab && windowEl && closeBtn) {
      fab.addEventListener("click", function () {
        if (windowEl.style.display === "none" || !windowEl.style.display) {
          windowEl.style.display = "flex";
          setTimeout(() => {
            input && input.focus();
          }, 200);
        } else {
          windowEl.style.display = "none";
        }
      });
      closeBtn.addEventListener("click", function () {
        windowEl.style.display = "none";
      });
    }

    const categorySelect = document.getElementById("chatbot-type");
    const usernameField = document.getElementById("usernameField");
    const usernameInput = document.getElementById("username");
    const chatbotWindow = document.getElementById("chatbot-window");

    function toggleUsernameField() {
      if (categorySelect.value === "appeal") {
        usernameField.style.display = "block";
        usernameInput.required = true;
        chatbotWindow.style.height = "600px";
      } else {
        usernameField.style.display = "none";
        usernameInput.required = false;
        chatbotWindow.style.height = "450px";
      }
    }

    // Initial check
    toggleUsernameField();

    // Listen for changes
    categorySelect.addEventListener("change", toggleUsernameField);
  });
</script>
