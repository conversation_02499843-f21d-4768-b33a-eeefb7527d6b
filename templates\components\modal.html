<div class="modal fade" id="commonModal" tabindex="-1">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content rounded-4 border-0">
      <div class="modal-header border-0 pb-0">
        <h5 class="modal-title fw-bold"></h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body pt-2"></div>
      <div class="modal-footer border-0">
        <button
          type="button"
          class="btn close-button btn-outline-dark rounded-pill px-4"
          data-bs-dismiss="modal"
        >
          Close
        </button>
        <button
          type="button"
          class="btn btn-dark rounded-pill px-4"
          id="modalActionBtn"
          style="display: none"
        >
          Action
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  const modal = document.getElementById("commonModal");
  const modalTitle = modal.querySelector(".modal-title");
  const modalBody = modal.querySelector(".modal-body");
  const modalActionBtn = document.getElementById("modalActionBtn");
  const modalDialog = modal.querySelector(".modal-dialog");

  function showModal(title, content, options = {}) {
    modalTitle.textContent = title;

    const closeButton = modal.querySelector(".close-button");
    if (options.hideCloseButton) {
      closeButton.style.display = "none";
    } else {
      closeButton.style.display = "block";
    }

    // Create a temporary parsing container
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, "text/html");

    // Fix forms with file inputs before inserting into modal
    const forms = doc.querySelectorAll("form");
    forms.forEach((form) => {
      const hasFileInput = form.querySelector('input[type="file"]');
      if (hasFileInput) {
        // Ensure the form has the proper enctype for file uploads
        form.setAttribute("enctype", "multipart/form-data");
      }
    });

    // Extract the content from our processed document
    modalBody.innerHTML = doc.body.innerHTML;

    if (typeof options.onShow === "function") {
      options.onShow();
    }

    // After adding to DOM, ensure forms have proper enctype and validation
    const modalForms = modalBody.querySelectorAll("form");
    modalForms.forEach((form) => {
      const hasFileInput = form.querySelector('input[type="file"]');
      if (hasFileInput) {
        form.setAttribute("enctype", "multipart/form-data");
        form.setAttribute("method", "post");
      }

      // Add validation classes if not already present
      if (!form.classList.contains("needs-validation")) {
        form.classList.add("needs-validation");
        form.setAttribute("novalidate", "");
      }
    });

    // Handle action button
    if (options.actionText && options.onAction) {
      modalActionBtn.textContent = options.actionText;
      modalActionBtn.style.display = "block";

      // Create a wrapped handler to handle both sync and async execution
      modalActionBtn.onclick = async function () {


        // Disable button to prevent double-clicks
        modalActionBtn.disabled = true;

        try {
          // Execute the provided action handler
          const result = options.onAction();


          // Check if result is a Promise (async function)
                      if (result && typeof result.then === "function") {
            const asyncResult = await result;
            

            // If async handler returns true or undefined, hide the modal
            if (asyncResult === true || asyncResult === undefined) {
              
              setTimeout(() => {
                try {
                  modalInstance.hide();
                } catch (e) {
                  console.error("Error hiding modal:", e);
                }
              }, 300);
            }
          } else {
            // Handle synchronous result
            if (result === true || result === undefined) {
              
              setTimeout(() => {
                try {
                  modalInstance.hide();
                } catch (e) {
                  console.error("Error hiding modal:", e);
                }
              }, 300);
            }
          }
        } catch (error) {
          console.error("Error in modal action handler:", error);
        } finally {
          // Re-enable button
          modalActionBtn.disabled = false;
        }
      };
    } else {
      modalActionBtn.style.display = "none";
    }

    // Adjust modal size and scrolling for different content types
    if (
      modalBody.querySelector("#editEventForm") ||
      modalBody.querySelector("#editJourneyForm") ||
      modalBody.querySelector("#createJourneyModal") ||
      modalBody.querySelector("#createEventModal")
    ) {
      // Form modals
      modalBody.style.maxHeight = "80vh";
      modalBody.style.overflowY = "auto";
      modalBody.style.padding = "1rem";
      modalDialog.classList.add("modal-lg");
    } else if (
      modalBody.querySelector(".edit-history-content") ||
      modalBody.querySelector(".edit-timeline") ||
      modalBody.querySelector(".edit-history-loading")
    ) {
      // Edit history modals
      modalBody.style.maxHeight = "70vh";
      modalBody.style.overflowY = "auto";
      modalBody.style.padding = "1.5rem 2rem 2rem 2rem";
      modalDialog.classList.add("modal-lg");
    } else {
      // Default modals
      modalDialog.classList.remove("modal-lg");
      modalBody.style.maxHeight = "";
      modalBody.style.overflowY = "";
      modalBody.style.padding = "";
    }

    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // Initialize validation for modal forms after modal is shown
    modal.addEventListener(
      "shown.bs.modal",
      function () {
        const modalForms = modalBody.querySelectorAll("form.needs-validation");
        modalForms.forEach((form) => {
          // Initialize form validation if the function exists
          if (typeof initializeFormValidation === "function") {
            initializeFormValidation(form);
          }
        });
      },
      { once: true }
    );

    // Handle modal hidden event to clean up
    modal.addEventListener("hidden.bs.modal", function onHidden() {
      modal.removeEventListener("hidden.bs.modal", onHidden);

      // Clear modal content to prevent memory leaks
      modalBody.innerHTML = "";
      modalTitle.textContent = "";
      modalActionBtn.style.display = "none";
      modalActionBtn.disabled = false;
      modalActionBtn.onclick = null;

      // Remove any modal size classes
      modalDialog.classList.remove("modal-lg", "modal-xl");

      // Reset modal body styles
      modalBody.style.maxHeight = "";
      modalBody.style.overflowY = "";
      modalBody.style.padding = "";

      // Clear any existing flash messages (but not other alert elements like appeal sections)
      const existingFlash = document.querySelector(".alert.alert-dismissible");
      if (existingFlash) {
        existingFlash.remove();
      }

      // Enhanced cleanup for backdrop issues - especially for edit history modal
      setTimeout(() => {
        // Remove any lingering backdrops (sometimes multiple can exist)
        const backdrops = document.querySelectorAll(".modal-backdrop");
        backdrops.forEach((backdrop) => {
          console.log("Removing lingering backdrop:", backdrop);
          backdrop.remove();
        });

        // Ensure body classes are removed
        document.body.classList.remove("modal-open");

        // Reset body styles that Bootstrap might have set
        document.body.style.overflow = "";
        document.body.style.paddingRight = "";

        // Force re-enable page interaction
        document.body.style.pointerEvents = "";


      }, 100);

      // For debugging - log when modal is hidden
      
    });

    // Make the modal instance globally available for debugging
    window.currentModalInstance = modalInstance;

    return { modalInstance, modalActionBtn };
  }

  // Utility function to properly close modal and prevent grey-out issues
  function closeModal() {
    const modal = document.getElementById("commonModal");
    if (modal) {
      const modalInstance = bootstrap.Modal.getInstance(modal);
      if (modalInstance) {
        modalInstance.hide();
      }

      // Force cleanup if modal doesn't close properly
      setTimeout(() => {
        // Remove any lingering backdrop
        const backdrop = document.querySelector(".modal-backdrop");
        if (backdrop) {
          backdrop.remove();
        }

        // Remove modal-open class from body
        document.body.classList.remove("modal-open");

        // Reset body styles
        document.body.style.overflow = "";
        document.body.style.paddingRight = "";

  
      }, 500);
    }
  }

  // Make closeModal globally available
  window.closeModal = closeModal;

  // Legacy showFlashMessage function removed to prevent conflicts
  // Flash messages are now handled by the unified system in journey-detail-bundle.js
  // or flash-messages.js depending on the page

  /* example:
  showModal('Title', 'Are you sure you want to submit?', {
    actionText: 'Submit',  // optional
    onAction: () => {     // optional
      // Submit form or perform action
      form.submit();
      modalInstance.hide();
    }
  });
  */
</script>

<style>
  @media (max-width: 576px) {
    #commonModal .modal-dialog {
      max-width: 90vw;
      margin: 1.75rem auto;
    }

    #commonModal .modal-content {
      border-radius: 1rem;
      padding: 0.5rem;
    }

    #commonModal .modal-body {
      padding: 0.5rem;
    }

    #commonModal .modal-footer {
      padding: 0.5rem;
    }
  }
</style>
