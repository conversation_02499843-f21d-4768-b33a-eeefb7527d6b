{% macro render_pagination(page, total_pages, endpoint, extra_params=None, q=None, filter=None, active_tab=None) %}
{% set params = extra_params.copy() if extra_params else {} %}
{% if q is not none and 'q' not in params %}{% set _ = params.update({'q': q}) %}{% endif %}
{% if filter is not none and 'filter' not in params %}{% set _ = params.update({'filter': filter}) %}{% endif %}
{% if active_tab is not none and 'active_tab' not in params %}{% set _ = params.update({'active_tab': active_tab}) %}{%
endif %}
<nav aria-label="Page navigation" class="mt-5">
  <ul class="pagination justify-content-center"
    style="--bs-pagination-active-bg: #000; --bs-pagination-active-border-color: #000; --bs-pagination-color: #000; --bs-pagination-hover-color: #000; --bs-pagination-focus-color: #000;">

    {% if page > 1 %}
    {% set prev_params = params.copy() %}
    {% set _ = prev_params.update({'page': page-1}) %}
    <li class="page-item">
      <a class="page-link border-0" href="{{ url_for(endpoint, **prev_params) }}">
        <i class="bi bi-chevron-left"></i>
      </a>
    </li>
    {% endif %}

    {% for p in range(1, total_pages + 1) %}
    {% set p_params = params.copy() %}
    {% set _ = p_params.update({'page': p}) %}
    {% if p == page %}
    <li class="page-item active">
      <span class="page-link border-0">{{ p }}</span>
    </li>
    {% else %}
    <li class="page-item">
      <a class="page-link border-0" href="{{ url_for(endpoint, **p_params) }}">{{ p }}</a>
    </li>
    {% endif %}
    {% endfor %}

    {% if page < total_pages %} {% set next_params=params.copy() %} {% set _=next_params.update({'page': page+1}) %} <li
      class="page-item">
      <a class="page-link border-0" href="{{ url_for(endpoint, **next_params) }}">
        <i class="bi bi-chevron-right"></i>
      </a>
      </li>
      {% endif %}
  </ul>
</nav>
{% endmacro %}