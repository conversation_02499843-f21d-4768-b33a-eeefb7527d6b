{% macro password_input(id, name, label, required=false, value='', placeholder='', form_text='', validate=true) %}
<div class="mb-3">
  <label for="{{ id }}" class="form-label">{{ label }}</label>
  <div class="input-group has-validation">
    <input type="password" class="form-control" id="{{ id }}" name="{{ name }}" {% if required %}required{% endif %} {%
      if value %}value="{{ value }}" {% endif %} {% if placeholder %}placeholder="{{ placeholder }}" {% endif %} {% if
      validate %}oninput="validatePassword(this)" {% endif %}>
    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('{{ id }}')">
      <i class="bi bi-eye"></i>
    </button>
    <div class="invalid-feedback"></div>
  </div>
  {% if form_text %}
  <div class="form-text">{{ form_text }}</div>
  {% endif %}
</div>
{% endmacro %}