{% extends "base.html" %}
{% from "components/pagination.html" import render_pagination %}

{% block title %}Departure Board - Footprints{% endblock %}

{% block content %}
<div class="container">
  <!-- Page Title -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="display-6 fw-bold">
      <span class="position-relative">
        Departure Board
        <span class="position-absolute start-0 bottom-0"
          style="height: 6px; width: 60%; background-color: #4e6bff; opacity: 0.2; border-radius: 3px;"></span>
      </span>
    </h1>
  </div>

  <!-- Tab Navigation -->
  <div class="mb-4">
    <div class="border-bottom position-relative ">
      <div class="d-flex">
        <div class="me-4 position-relative">
          <a href="{{ url_for('departure_board.get_departure_list') }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if not request.args.get('tab') or request.args.get('tab') == 'all' %}text-primary{% else %}text-secondary{% endif %}">
            All Events
          </a>
          {% if not request.args.get('tab') or request.args.get('tab') == 'all' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
        <div class="position-relative me-4">
          <a href="{{ url_for('departure_board.get_departure_list', tab='manage') }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if request.args.get('tab') == 'manage' %}text-primary{% else %}text-secondary{% endif %}">
            Following
          </a>
          {% if request.args.get('tab') == 'manage' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
        <div class="position-relative me-4">
          <a href="{{ url_for('departure_board.get_departure_list', tab='map') }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if request.args.get('tab') == 'map' %}text-primary{% else %}text-secondary{% endif %}">
            Map
          </a>
          {% if request.args.get('tab') == 'map' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1;"></div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>


  {% if request.args.get('tab') == 'manage' %}
  {% include "departure_board/manage_events.html" %}
  {% elif request.args.get('tab') == 'map' %}
  {% include "departure_board/map.html" %}
  {% elif request.args.get('tab') == 'all' or not request.args.get('tab') %}
  <!-- Counts and Search Form -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold">Total ({{ total_count|default(0) }})</h5>

    <div class="d-flex gap-2 align-items-center">
      <form action="{{ url_for('departure_board.get_departure_list')}}" method="get" class="d-flex gap-2">
        <div class="input-group">
          <input type="text" class="form-control" name="q" placeholder="Search events..." value="{{ search_term }}">
          {% if search_term %}
          <a href="{{ url_for('departure_board.get_departure_list') }}"
            class="btn btn-outline-secondary border-start-0">
            <i class="bi bi-x"></i>
          </a>
          {% endif %}
        </div>
        <button type="submit" class="btn" style="background-color: black; color: white; border: none;">Search</button>
      </form>
    </div>
  </div>
  {% if events %}
  <!-- Event Cards Grid -->
  <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-5">
    {% for event in events %}
    <div class="col">
      <a href="{{url_for('event.get_event_details', event_id=event.event_id) }}" class="text-decoration-none text-dark">
        <div class="card h-100 event-card border-0 shadow-sm">
          <!-- Card Image -->
          <div class="position-relative event-image">
            {% if event.event_image %}
            <img src="{{ url_for('static', filename='uploads/event_images/' + event.event_image) }}"
              alt="{{ event.event_title }}" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;"
              onerror="this.onerror=null; this.src='/static/uploads/event_images/event_image_placeholder.jpg'">
            {% else %}
            <img src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
              alt="Event placeholder" class="card-img-top" style="object-fit: cover; height: 100%; width: 100%;">
            {% endif %}

            <!-- Event Type Labels -->
            <div class="badge-container">
              {% for follow_type in event.follow_types %}
              {% if follow_type == 'journey' %}
              <span class="badge rounded-pill px-3 py-2 journey-badge">Journey</span>
              {% elif follow_type == 'location' %}
              <span class="badge rounded-pill px-3 py-2 location-badge">Location</span>
              {% elif follow_type == 'user' %}
              <span class="badge rounded-pill px-3 py-2 user-badge">User</span>
              {% endif %}
              {% endfor %}
            </div>

          </div>

          <!-- Card Body -->

          <div class="card-body d-flex flex-column">
            <!-- Location and Date information -->
            <div class="d-flex event-meta mb-2">
              <div class="small text-muted me-auto d-flex align-items-center">
                <i class="bi bi-geo-alt me-1"></i>
                <span>{{ event.location_name }}</span>
              </div>
              <div class="small text-muted d-flex align-items-center">
                <i class="bi bi-clock me-1"></i>
                <span>{{ event.updated_at }}</span>
              </div>
            </div>

            <!-- Title with Three Dot Menu -->
            <div class="d-flex justify-content-between align-items-start mb-2">
              <h4 class="card-title event-title mb-0">{{ event.event_title }}</h4>
              <div class="dropdown ms-2">
                <button class="btn btn-link text-dark p-0 three-dots-btn" type="button" data-bs-toggle="dropdown"
                  aria-expanded="false">
                  <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0">
                  {% if 'journey' in event.follow_types %}
                  <li>
                    <form method="POST"
                      action="{{ url_for('departure_board.unfollow_journey', journey_id=event.journey_id) }}"
                      class="d-inline">
                      <button class="dropdown-item journey-unfollow-button">
                        Unfollow Journey
                      </button>
                    </form>
                  </li>
                  {% else %}
                  <li>
                    <form method="POST"
                      action="{{ url_for('departure_board.follow_journey', journey_id=event.journey_id) }}"
                      class="d-inline">
                      <button class="dropdown-item journey-unfollow-button">
                        Follow Journey
                      </button>
                    </form>
                  </li>
                  {% endif %}

                  {% if 'user' in event.follow_types %}
                  <li>
                    <form method="POST"
                      action="{{ url_for('departure_board.unfollow_user', followed_id=event.user_id, journey_id=event.journey_id) }}"
                      class="d-inline">
                      <button class="dropdown-item user-unfollow-button">
                        Unfollow User
                      </button>
                    </form>
                  </li>
                  {% else %}
                  <li>
                    <form method="POST"
                      action="{{ url_for('departure_board.follow_user', followed_id=event.user_id, journey_id=event.journey_id) }}"
                      class="d-inline">
                      <button class="dropdown-item user-unfollow-button">
                        Follow User
                      </button>
                    </form>
                  </li>
                  {% endif %}

                  {% if 'location' in event.follow_types %}
                  <li>
                    <form method="POST"
                      action="{{ url_for('departure_board.unfollow_location', location_id=event.location_id, journey_id=event.journey_id)  }}"
                      class="d-inline">
                      <button class="dropdown-item location-unfollow-button">
                        Unfollow Location
                      </button>
                    </form>
                  </li>
                  {% else %}
                  <li>
                    <form method="POST"
                      action="{{ url_for('departure_board.follow_location', location_id=event.location_id, journey_id=event.journey_id)  }}"
                      class="d-inline">
                      <button class="dropdown-item location-unfollow-button">
                        Follow Location
                      </button>
                    </form>
                  </li>
                  {% endif %}
                </ul>
              </div>
            </div>

            <!-- Subtitle -->
            <p class="card-text event-subtitle mb-2">{{ event.journey_title }}</p>

            <!-- Author Info -->
            <div class="mt-auto event-author">
              <div class="d-flex align-items-center">
                <div class="rounded-circle overflow-hidden author-avatar">
                  {% if event.profile_image %}
                  <img src="{{ url_for('static', filename='uploads/profile_images/' + event.profile_image) }}"
                    alt="{{ event.username }}" class="img-fluid"
                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                  {% else %}
                  <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                    alt="{{ event.username }}" class="img-fluid">
                  {% endif %}
                </div>
                <div class="author-info">
                  <span class="text-muted small d-block author-label">Written By</span>
                  <p class="mb-0 fw-medium small author-name">{{ event.username }}</p>
                </div>
              </div>
            </div>
          </div>
      </a>
    </div>
  </div>
  {% endfor %}
</div>

{{ render_pagination(page, total_pages, 'departure_board.get_departure_list', q=search_term, filter=filter,
active_tab=active_tab) }}

{% else %}
<!-- No Results Alert -->
<div class="alert rounded-4"
  style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2);">
  <div class="d-flex align-items-center">
    <i class="bi bi-info-circle-fill me-3 fs-4"></i>
    <p class="mb-0">No events found</p>
  </div>
</div>
{% endif %}
{% endif %}
</div>

<style>
  .event-image .badge {
    position: relative;
    margin-right: 6px;
    display: inline-block;
    z-index: 10;
  }

  .badge-container {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    gap: 6px;
    z-index: 20;
  }

  .badge-container .badge {
    position: relative;
    font-size: 0.7rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    color: white;
    white-space: nowrap;
  }

  .journey-badge {
    background-color: #ff6b6b;
  }

  .location-badge {
    background-color: #4e6bff;
  }

  .user-badge {
    background-color: #ffc107;
  }

  .journey-unfollow-button {
    color: #ff6b6b;
  }

  .location-unfollow-button {
    color: #4e6bff;
  }

  .user-unfollow-button {
    color: #ffc107;
  }


  .event-card {
    height: 380px;
    border-radius: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
  }


  .event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    border-color: rgba(78, 107, 255, 0.3);
  }

  .event-image {
    height: 180px;
    border-radius: 15px 15px 0 0;
    overflow: hidden;
    position: relative;
  }

  .event-meta {
    margin-bottom: 8px;
  }

  .event-title {
    font-weight: bold;
    font-size: 1.1rem;
    line-height: 1.3;
    max-height: 2.7rem;
    overflow: hidden;
  }

  .three-dots-btn {
    line-height: 1;
    font-size: 1.2rem;
    margin-top: 2px;
  }

  .three-dots-btn:hover,
  .three-dots-btn:focus {
    color: #4e6bff;
  }

  .event-subtitle {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    max-height: 1.5rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .event-author {
    display: flex;
    align-items: center;
    margin-top: auto;
  }

  .author-avatar {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
  }

  .author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .author-info {
    margin-left: 8px;
  }

  .author-label {
    color: #6c757d;
    line-height: 1;
  }

  .author-name {
    font-weight: 500;
  }

  .badge {
    font-size: 0.7rem;
    font-weight: 500;
  }

  @media (max-width: 767.98px) {
    .row-cols-1>.col {
      margin-bottom: 1rem;
    }
  }
</style>
{% endblock %}