{% block content %}
<div class="container-fluid pb-4">
  <style>
    .departure-tabs {
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;
      margin-bottom: 2rem;
    }

    .departure-tabs .btn {
      border: none !important;
      border-radius: 999px !important;
      background: #f4f6fb;
      color: #4e6bff;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.7rem 2.2rem;
      font-size: 1.08rem;
      min-width: 180px;
      max-width: 240px;
      text-align: center;
      transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.15s;
      box-shadow: 0 1px 4px rgba(78, 107, 255, 0.06);
      margin: 0 0.25rem 0.5rem 0.25rem;
    }

    .departure-tabs .btn span,
    .departure-tabs .btn i {
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .departure-tabs .btn.active,
    .departure-tabs .btn:focus {
      background: #4e6bff;
      color: #fff !important;
    }

    .departure-tabs .btn:active {
      background: #4e6bff;
    }

    @media (max-width: 900px) {
      .departure-tabs {
        flex-wrap: wrap;
        gap: 0.5rem;
      }

      .departure-tabs .btn {
        min-width: 45%;
        max-width: 100%;
        font-size: 0.98rem;
        padding: 0.6rem 0.5rem;
        margin: 0.25rem 0;
      }
    }

    @media (max-width: 600px) {
      .departure-tabs .btn {
        min-width: 100%;
        font-size: 0.92rem;
        padding: 0.5rem 0.2rem;
      }
    }

    /* Responsive event card for mobile */
    @media (max-width: 600px) {
      .event-link .border {
        flex-direction: column !important;
        padding: 0.8rem 0.5rem !important;
      }

      .event-link .d-flex {
        flex-direction: column !important;
        align-items: stretch !important;
      }

      .event-link .me-4 {
        margin-right: 0 !important;
        margin-bottom: 0.7rem !important;
      }

      .event-link img {
        width: 100% !important;
        height: 160px !important;
        margin-bottom: 0.5rem;
        border-radius: 10px !important;
      }

      .event-link h5 {
        font-size: 1.05rem !important;
      }

      .event-link p,
      .event-link .text-muted.small {
        font-size: 0.95rem !important;
      }
    }
  </style>
  <div class="departure-tabs mb-4">
    <a
      class="btn{% if request.args.get('filter', 'user') == 'user' %} active{% endif %}"
      href="{{ url_for('departure_board.get_departure_list', tab='manage', filter='user') }}"
    >
      <i class="bi bi-person"></i> <span>Users</span>
    </a>
    <a
      class="btn{% if request.args.get('filter') == 'journey' %} active{% endif %}"
      href="{{ url_for('departure_board.get_departure_list', tab='manage', filter='journey') }}"
    >
      <i class="bi bi-map"></i> <span>Journeys</span>
    </a>
    <a
      class="btn{% if request.args.get('filter') == 'location' %} active{% endif %}"
      href="{{ url_for('departure_board.get_departure_list', tab='manage', filter='location') }}"
    >
      <i class="bi bi-geo-alt"></i> <span>Locations</span>
    </a>
  </div>
  <div class="card shadow-sm border-0 rounded-3">
    <div class="card-body">
      {% if grouped_events %} {% for group in grouped_events %}
      <div class="accordion mb-3" id="accordion{{ group.entity_id }}">
        <div class="accordion-item border rounded-3 shadow-sm">
          <h2 class="accordion-header" id="heading{{ group.entity_id }}">
            <div
              class="d-flex align-items-center justify-content-between px-3 py-2"
            >
              <div class="d-flex align-items-center flex-grow-1">
                <button
                  class="accordion-button collapsed w-100 text-start px-0"
                  type="button"
                  data-bs-toggle="collapse"
                  data-bs-target="#collapse{{ group.entity_id }}"
                  aria-expanded="false"
                  aria-controls="collapse{{ group.entity_id }}"
                >
                  <div class="d-flex align-items-center">
                    {% if group.entity_type == 'user' and group.profile_image %}
                    <img
                      src="{{ url_for('static', filename='uploads/profile_images/' + group.profile_image) }}"
                      alt="{{ group.username }}"
                      class="rounded-circle me-3"
                      style="width: 40px; height: 40px; object-fit: cover"
                    />
                    {% endif %}
                    <span class="fw-semibold">
                      {% if group.entity_type == 'user' %}{{ group.username }}
                      {% elif group.entity_type == 'journey' %}{{ group.title }}
                      {% elif group.entity_type == 'location' %}{{ group.name }}
                      {% else %}{{ group.title }}{% endif %}
                    </span>
                  </div>
                </button>
              </div>
              <form
                method="POST"
                action="{% if group.entity_type == 'user' %}
                                      {{ url_for('departure_board.unfollow_user', followed_id=group.entity_id) }}
                                    {% elif group.entity_type == 'journey' %}
                                      {{ url_for('departure_board.unfollow_journey', journey_id=group.entity_id) }}
                                    {% elif group.entity_type == 'location' %}
                                      {{ url_for('departure_board.unfollow_location', location_id=group.entity_id) }}
                                    {% endif %}"
                class="ms-3"
              >
                <button type="submit" class="btn btn-sm btn-dark rounded-pill">
                  Unfollow
                </button>
              </form>
            </div>
          </h2>
          <div
            id="collapse{{ group.entity_id }}"
            class="accordion-collapse collapse"
            aria-labelledby="heading{{ group.entity_id }}"
            data-bs-parent="#accordion{{ group.entity_id }}"
          >
            <div class="accordion-body">
              {% for event in group.events %}
              <a
                class="event-link"
                href="{{url_for('event.get_event_details', event_id=event.event_id)}}"
              >
                <div class="p-3 border mb-2 rounded">
                  <div class="d-flex">
                    <div class="me-4">
                      {% if event.event_image %}
                      <img
                        src="{{ url_for('static', filename='uploads/event_images/' + event.event_image) }}"
                        alt="{{ event.event_title }}"
                        class="rounded"
                        style="width: 100px; height: 100px; object-fit: cover"
                      />
                      {% else %}
                      <img
                        src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                        alt="Image placeholder"
                        class="rounded"
                        style="width: 100px; height: 80px; object-fit: cover"
                      />
                      {% endif %}
                    </div>
                    <div class="flex-grow-1">
                      <h5 class="mb-1 fw-semibold">{{ event.event_title }}</h5>
                      <p class="mb-1 text-muted">{{ event.journey_title }}</p>
                      <div class="text-muted small">
                        <i class="bi bi-geo-alt me-1"></i>{{ event.location_name
                        }}
                      </div>
                      {% if event.updated_at %}
                      <div class="text-muted small">
                        Updated at {{ event.updated_at.strftime('%d/%m/%Y') }}
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </a>
              {% endfor %} {% if group.events|length == 0 %}
              <div
                class="alert rounded-4"
                style="
                  background-color: rgba(78, 107, 255, 0.1);
                  color: #4e6bff;
                  border: 1px solid rgba(78, 107, 255, 0.2);
                "
              >
                <div class="d-flex align-items-center">
                  <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                  <p class="mb-0">No events found.</p>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      {% endfor %} {{ render_pagination(page, total_pages,
      'departure_board.get_departure_list', extra_params={'tab': 'manage'},
      q=search_term, filter=filter) }} {% else %}
      <div
        class="alert rounded-4"
        style="
          background-color: rgba(78, 107, 255, 0.1);
          color: #4e6bff;
          border: 1px solid rgba(78, 107, 255, 0.2);
        "
      >
        <div class="d-flex align-items-center">
          <i class="bi bi-info-circle-fill me-3 fs-4"></i>
          {% if request.args.get('filter') %}
          <p class="mb-0">
            You are not following any {{request.args.get('filter')}}s
          </p>
          {% else %}
          <p class="mb-0">You are not following any users</p>
          {% endif %}
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .accordion-body {
    max-height: 450px;
    overflow-y: auto;
    padding: 1rem;
  }

  .tab-container {
    display: block;
  }

  .tabs {
    display: flex;
    width: 400px;
    max-width: 100%;
    border: 1px solid gray;
    border-radius: 10px;
    overflow: hidden;
  }

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 10px 20px;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    background-color: #fff;
    border-right: 1px solid gray;
    transition: background-color 0.3s;
  }

  .tab-item:last-child {
    border-right: none;
  }

  .tab-item:hover {
    background-color: #e9e9e9;
    text-decoration: none;
    color: #333;
  }

  .tab-item.active {
    background-color: #f5f5f5;
    color: #000;
    font-weight: 600;
  }

  .event-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.2s ease;
  }

  .event-link:hover {
    text-decoration: none;
    color: inherit;
  }

  .event-link .border {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .event-link:hover .border {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    background-color: rgba(0, 0, 0, 0.02);
  }

  .event-link img {
    transition: transform 0.2s ease;
  }

  .event-link:hover img {
    transform: scale(1.05);
  }
</style>

{% endblock %}
