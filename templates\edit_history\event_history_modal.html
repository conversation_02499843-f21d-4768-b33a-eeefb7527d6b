<!-- Event Edit History Modal Content Template -->
<!-- This template is loaded via AJAX and inserted into the modal -->

<div class="edit-history-content">
  {% if not edit_data.has_history %}
    <!-- No History State -->
    <div class="edit-history-empty">
      <i class="bi bi-clock-history"></i>
      <h4>No Edit History</h4>
      <p>This event hasn't been edited yet. Any future changes made by staff will appear here.</p>
    </div>
  {% else %}
    {% if edit_data.is_premium or edit_data.is_staff %}
      <!-- Full edit history for premium users and staff -->
      <div class="edit-timeline">
        {% for edit in edit_data.edit_history %}
          <div class="edit-timeline-item">
            <div class="edit-timeline-badge">
              <i class="bi bi-pencil"></i>
            </div>
            <div class="edit-timeline-panel">
              <div class="edit-timeline-header">
                <h6 class="edit-timeline-title">Edit by {{ edit.editor_username }}</h6>
                <div class="edit-timeline-meta">
                  <i class="bi bi-clock"></i>
                  <span>{{ edit.created_at }}</span>
                </div>
              </div>
              <div class="edit-timeline-body">
                <div class="edit-reason">
                  <strong>Reason:</strong> {{ edit.reason }}
                </div>
                
                <!-- Field Changes Table -->
                {% if edit.field_changes and edit.field_changes|length > 0 %}
                  <div class="changes-section">
                    <div class="changes-title">
                      <i class="bi bi-list-ul"></i>
                      <span>Changes</span>
                    </div>
                    <div class="table-responsive">
                      <table class="table changes-table">
                        <colgroup>
                          <col style="width: 20%;">
                          <col style="width: 40%;">
                          <col style="width: 40%;">
                        </colgroup>
                        <thead>
                          <tr>
                            <th>Field</th>
                            <th>Before</th>
                            <th>After</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for change in edit.field_changes %}
                            <tr>
                              <td class="field-name">{{ change.field_name|replace('_', ' ')|title }}</td>
                              <td style="word-wrap: break-word; white-space: normal;"><span class="old-value">{{ change.old_value|format_edit_value(change.field_name) }}</span></td>
                              <td style="word-wrap: break-word; white-space: normal;"><span class="new-value">{{ change.new_value|format_edit_value(change.field_name) }}</span></td>
                            </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                {% else %}
                  <p class="text-muted small">No field changes recorded.</p>
                {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    {% else %}
      <!-- Greyed out content for free users with premium prompt -->
      <div class="position-relative">
        <div class="edit-timeline" style="opacity: 0.3; pointer-events: none;">
          {% for edit in edit_data.edit_history %}
            <div class="edit-timeline-item">
              <div class="edit-timeline-badge bg-secondary">
                <i class="bi bi-pencil"></i>
              </div>
              <div class="edit-timeline-panel">
                <div class="edit-timeline-header">
                  <h6 class="edit-timeline-title">Edit by {{ edit.editor_username }}</h6>
                  <div class="edit-timeline-meta">
                    <i class="bi bi-clock"></i>
                    <span>{{ edit.created_at }}</span>
                  </div>
                </div>
                <div class="edit-timeline-body">
                  <div class="edit-reason">
                    <strong>Reason:</strong> {{ edit.reason }}
                  </div>
                  
                  <!-- Field Changes Table -->
                  {% if edit.field_changes and edit.field_changes|length > 0 %}
                    <div class="changes-section">
                      <div class="changes-title">
                        <i class="bi bi-list-ul"></i>
                        <span>Changes</span>
                      </div>
                      <div class="table-responsive">
                        <table class="table changes-table">
                          <colgroup>
                            <col style="width: 20%;">
                            <col style="width: 40%;">
                            <col style="width: 40%;">
                          </colgroup>
                          <thead>
                            <tr>
                              <th>Field</th>
                              <th>Before</th>
                              <th>After</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% for change in edit.field_changes %}
                              <tr>
                                <td class="field-name">{{ change.field_name|replace('_', ' ')|title }}</td>
                                <td style="word-wrap: break-word; white-space: normal;"><span class="old-value">{{ change.old_value|format_edit_value(change.field_name) }}</span></td>
                                <td style="word-wrap: break-word; white-space: normal;"><span class="new-value">{{ change.new_value|format_edit_value(change.field_name) }}</span></td>
                              </tr>
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  {% else %}
                    <p class="text-muted small">No field changes recorded.</p>
                  {% endif %}
                </div>
              </div>
            </div>
          {% endfor %}
        </div>

        <!-- Premium upgrade overlay -->
        <div class="position-absolute top-50 start-50 translate-middle text-center bg-white p-4 rounded shadow-lg border" style="z-index: 10;">
          <div class="mb-3">
            <i class="bi bi-star-fill fs-1 text-warning"></i>
          </div>
          <h4 class="mb-3">Premium Feature</h4>
          <p class="text-muted mb-3">Upgrade to Premium to access complete edit history for all your content.</p>
          <a href="http://127.0.0.1:5000/account/profile?active_tab=subscription" class="btn btn-warning">
            <i class="bi bi-star me-2"></i>Upgrade to Premium
          </a>
        </div>
      </div>
    {% endif %}
  {% endif %}
</div>
