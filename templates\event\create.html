<!-- Create Event Modal Content - CSS and JS files are included in the parent page -->

<div class="create-event-modal" id="createEventModal">
  <form method="post" action="{{ url_for('event.create_event', journey_id=journey.id) }}" enctype="multipart/form-data"
    novalidate id="createEventForm" class="needs-validation modern-form">
    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid">
        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-journal-text"></i>
                  Event Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title"
                  value="{{ request.form.get('title', '') }}" required minlength="5" maxlength="50"
                  placeholder="Enter event title" />
                <div class="invalid-feedback">
                  Title is required and must be at least 5 characters long.
                </div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="2" placeholder="Describe your event...">
{{ request.form.get('description', '') }}</textarea>
                <div class="invalid-feedback">
                  Description is required and must be at least 5 characters
                  long.
                </div>
              </div>
            </div>
          </div>

          <!-- Date & Time Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Date & Time</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="startDatetime" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date & Time *
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="startDatetime"
                  name="start_datetime" value="{{ request.form.get('start_datetime', '') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              <div class="form-group">
                <label for="endDatetime" class="modern-label">
                  <i class="bi bi-calendar-check"></i>
                  End Date & Time
                </label>
                <input type="datetime-local" class="modern-input datetime-input" id="endDatetime" name="end_datetime"
                  value="{{ request.form.get('end_datetime', '') }}" />
                <div class="invalid-feedback">
                  End date cannot be before start date.
                </div>
              </div>
            </div>
          </div>

          <!-- Images Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Event Images</span>
            </div>

            <div class="form-group">
              <label for="images" class="modern-label">
                <i class="bi bi-camera"></i>
                Images (optional)
              </label>
              <input type="file" class="modern-input" id="images" name="images" {% if premium_access %}multiple{% endif
                %} accept="{{ file_config.allowedExtensionsHtml }}"
                data-premium="{% if premium_access %}true{% else %}false{% endif %}" />
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                {% if premium_access %} Upload up to 10 images. Maximum {{
                file_config.maxFileSizeMB }}MB each. Allowed formats: {{
                file_config.allowedFormatsText }}. {% else %} Upload one image.
                Maximum {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{
                file_config.allowedFormatsText }}. {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Location Search Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Search Location</span>
            </div>

            <div class="form-group">
              <label for="locationSearch" class="modern-label">
                <i class="bi bi-search"></i>
                Search for Location *
              </label>
              <div class="location-search-container">
                <div class="input-group">
                  <input type="text" class="form-control" id="locationSearch"
                    placeholder="Type location name or address (min 3 characters)..." />
                  <button type="button" class="btn btn-primary" id="searchLocationBtn">
                    <i class="bi bi-search me-1"></i>
                    <span class="d-none d-sm-inline">Search</span>
                  </button>
                </div>
              </div>
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                Enter at least 3 characters to search for existing locations or new addresses
              </div>
            </div>

            <!-- Search Results -->
            <div id="searchResults" class="location-results" style="display: none">
              <div class="results-header">
                <h6 class="mb-0">Search Results</h6>
              </div>
              <div id="resultsList" class="results-list">
                <!-- Results will be populated here -->
              </div>
            </div>
          </div>

          <!-- Selected Location Section -->
          <div class="form-section compact" id="selectedLocationSection" style="display: none">
            <div class="section-header">
              <span class="section-title">Selected Location</span>
            </div>

            <div class="selected-location-info">
              <div class="form-group">
                <label for="location" class="modern-label">
                  <i class="bi bi-tag"></i>
                  Location Name *
                </label>
                <input type="text" class="modern-input" id="location" name="location" required readonly />
                <div class="invalid-feedback">Location is required.</div>
              </div>

              <div class="d-flex gap-2 location-actions mt-2 flex-wrap">
                <div class="flex-grow-1">
                  <button type="button" class="btn btn-sm btn-outline-secondary w-100" id="changeLocationBtn">
                    <i class="bi bi-arrow-left me-1"></i>
                    <span class="d-none d-sm-inline">Back to </span>Search
                  </button>
                </div>
                <div class="flex-grow-1">
                  <button type="button" class="btn btn-sm btn-outline-primary w-100" id="editMapLocationBtn"
                    style="display: none">
                    <i class="bi bi-geo-alt me-1"></i>
                    <span class="d-none d-sm-inline">Change </span>Map Location
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Map Section -->
          <div class="form-section map-section" id="mapSection" style="display: none">
            <div class="section-header">
              <span class="section-title">Map Preview</span>
            </div>

            <div class="location-content">
              <!-- Interactive Map -->
              <div class="map-container desktop-optimized">
                <div id="map" class="modern-map"></div>
              </div>

              <!-- Map Search (for new locations) -->
              <div id="mapSearchGroup" class="form-group mt-3" style="display: none">
                <label for="mapSearch" class="modern-label">
                  <i class="bi bi-search"></i>
                  Search Address on Map
                </label>
                <div class="map-search-container">
                  <input type="text" class="modern-input" id="mapSearch" placeholder="Search for address..." />
                  <div id="mapSuggestions" class="modern-suggestions"></div>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  Search for address and click on map to set location
                </div>

                <!-- Coordinates Status (Hidden from users) -->
                <div id="coordinatesStatus" class="mt-2" style="display: none !important">
                  <div class="alert alert-success py-2 px-3 mb-0">
                    <i class="bi bi-check-circle me-2"></i>
                    <span id="coordinatesText">Location coordinates set</span>
                  </div>
                </div>

                <!-- New Location Name Input -->
                <div id="newLocationNameGroup" class="mt-3" style="display: none">
                  <label for="newLocationName" class="modern-label">
                    <i class="bi bi-tag"></i>
                    Name for New Location *
                  </label>
                  <input type="text" class="modern-input" id="newLocationName"
                    placeholder="Enter unique name for this location" />
                  <div class="input-help">
                    <i class="bi bi-info-circle"></i>
                    This name will be validated and created when you submit the
                    event
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Hidden coordinate fields -->
      <input type="hidden" id="latitude" name="latitude" />
      <input type="hidden" id="longitude" name="longitude" />
    </div>
  </form>

  <style>
    /* Modern Create Event Modal Styles */
    .create-event-modal {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0;
    }

    .modern-form {
      background: #ffffff;
      overflow: hidden;
    }

    /* Form Content */

    /* Desktop Grid Layout */
    .desktop-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      align-items: start;
    }

    .left-column,
    .right-column {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    /* Form Sections */
    .form-section {
      background: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 0;
      border: 1px solid #e1e8ed;
      transition: all 0.3s ease;
      height: fit-content;
    }

    .form-section.compact {
      padding: 12px;
    }

    .form-section.map-section {
      padding: 12px;
      height: fit-content;
    }

    /* Section Headers */
    .section-header {
      display: flex;
      align-items: center;
      gap: 10px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f1f3f4;
    }

    .section-icon {
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;
      flex: 1;
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      gap: 8px;
    }

    /* Form Groups */
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    /* Modern Labels */
    .modern-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
    }

    .modern-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Modern Inputs */
    .modern-input,
    .modern-textarea {
      width: 100%;
      padding: 6px 10px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      color: #2d3748;
      background: #ffffff;
      transition: all 0.3s ease;
      outline: none;
    }

    .modern-input:focus,
    .modern-textarea:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    .modern-input:disabled,
    .modern-input:read-only,
    .modern-textarea:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    .modern-input::placeholder,
    .modern-textarea::placeholder {
      color: #a0aec0;
    }

    /* Location Search Container */
    .location-search-container {
      position: relative;
    }

    .location-search-container .input-group {
      margin-bottom: 0;
      display: flex;
      width: 100%;
    }

    .location-search-container .form-control {
      border-right: none;
      flex: 1;
      min-width: 0; /* Allow input to shrink properly */
    }

    .location-search-container .btn {
      border-left: none;
      border-color: #ced4da;
      flex-shrink: 0; /* Prevent button from shrinking */
      white-space: nowrap;
    }

    .location-search-container .btn:hover {
      border-color: #86b7fe;
    }

    /* Ensure input group works well on all screen sizes */
    .location-search-container .input-group .form-control:focus {
      z-index: 3;
    }

    /* Search button responsive behavior */
    .location-search-container .btn {
      min-width: 44px; /* Ensure touch-friendly minimum size */
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Location actions responsive styling */
    .location-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .location-actions .flex-grow-1 {
      min-width: 120px; /* Minimum width for buttons */
    }

    /* Location Results */
    .location-results {
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      margin-top: 12px;
      max-height: 300px;
      overflow-y: auto;
    }

    .results-header {
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 600;
      color: #495057;
    }

    .results-list {
      padding: 0;
    }

    .result-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f1f3f4;
      cursor: pointer;
      transition: all 0.2s ease;
      justify-content: space-between;
    }

    .result-item:hover {
      background: #f8f9fa;
    }

    .result-item:last-child {
      border-bottom: none;
    }

    .result-item.selected {
      background: #e3f2fd;
      border-color: #2196f3;
    }

    .result-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .result-icon {
      width: 32px;
      height: 32px;
      background: #e3f2fd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #2196f3;
    }

    .result-details h6 {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .result-details small {
      color: #666;
      font-size: 12px;
    }

    .result-type {
      background: #e3f2fd;
      color: #2196f3;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }

    /* Selected Location Info */
    .selected-location-info {
      background: #e8f5e8;
      border: 1px solid #4caf50;
      border-radius: 8px;
      padding: 16px;
    }

    /* Map Styles */
    .map-container {
      margin: 12px 0 0 0;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #e2e8f0;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .modern-map {
      height: 175px;
      width: 100%;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .map-container:hover {
      border-color: #667eea;
    }

    /* Map Search Container */
    .map-search-container {
      position: relative;
    }

    /* Map Suggestions Dropdown - Restored from Old Implementation */
    .modern-suggestions {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 2px solid #e2e8f0;
      border-top: none;
      border-radius: 0 0 8px 8px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      display: none;
    }

    .modern-suggestions.show {
      display: block;
    }

    .suggestion-item {
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-bottom: 1px solid #f1f3f4;
      background: white;
      color: #2d3748;
      font-size: 14px;
      line-height: 1.4;
    }

    .suggestion-item:hover {
      background: #667eea;
      color: white;
    }

    .suggestion-item:last-child {
      border-bottom: none;
    }

    /* Help Text */
    .input-help {
      font-size: 12px;
      color: #718096;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .input-help i {
      color: #667eea;
    }

    /* Validation feedback states */
    .has-error .modern-input,
    .has-error .modern-textarea {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .has-success .modern-input,
    .has-success .modern-textarea {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .has-error .input-help {
      color: #e53e3e;
    }

    .has-success .input-help {
      color: #38a169;
    }

    .has-error .input-help i {
      color: #e53e3e;
    }

    .has-success .input-help i {
      color: #38a169;
    }

    /* File Validation Feedback Styles */
    .validation-feedback {
      margin-top: 8px;
    }

    .validation-feedback .alert {
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border: none;
    }

    .validation-feedback .alert-danger {
      background-color: #fdf2f2;
      color: #c53030;
      border-left: 4px solid #e53e3e;
    }

    .validation-feedback .alert-success {
      background-color: #f0fff4;
      color: #22543d;
      border-left: 4px solid #38a169;
    }

    .validation-feedback ul {
      margin-bottom: 0;
      font-size: 11px;
    }

    .validation-feedback ul li {
      margin-bottom: 2px;
    }

    .validation-feedback ul li:last-child {
      margin-bottom: 0;
    }

    /* Enhanced file input error states */
    .form-group.has-error .modern-input {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .form-group.has-success .modern-input {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    /* Validation Styles */
    .modern-input.is-invalid,
    .modern-textarea.is-invalid {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .modern-input.is-valid,
    .modern-textarea.is-valid {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .invalid-feedback {
      color: #e53e3e;
      font-size: 0.75rem !important;
      display: none;
      align-items: center;
    }

    .modern-input.is-invalid+.invalid-feedback,
    .modern-textarea.is-invalid+.invalid-feedback {
      display: flex;
    }

    .invalid-feedback::before {
      font-size: 4px !important;
      margin-top: 0px;
    }

    /* Loading States */
    .loading {
      position: relative;
      pointer-events: none;
    }

    .loading::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Search button loading state */
    .btn.loading {
      opacity: 0.8;
      cursor: not-allowed;
    }

    .btn.loading i {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* Responsive Design */
    @media (max-width: 992px) {
      .desktop-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .create-event-modal {
        max-width: 800px;
      }

      .modern-map {
        height: 140px;
      }

      /* Better button sizing on tablets */
      .location-actions .flex-grow-1 {
        min-width: 140px;
      }

      .location-search-container .btn {
        padding: 8px 12px;
      }
    }

    @media (max-width: 768px) {
      .form-content {
        padding: 20px 16px;
      }

      .form-section {
        padding: 16px;
      }

      .form-section.compact {
        padding: 12px;
      }

      .location-search-container .input-group {
        flex-direction: column;
        gap: 8px;
      }

      .location-search-container .form-control {
        border-right: 2px solid #e2e8f0;
        border-radius: 8px;
        width: 100%;
      }

      .location-search-container .btn {
        border-left: 2px solid #e2e8f0;
        border-radius: 8px;
        width: 100%;
        justify-content: center;
      }

      /* Ensure proper spacing for location actions */
      .location-actions {
        flex-direction: column;
        gap: 8px;
      }

      .location-actions .btn {
        width: 100%;
      }

      /* Better map sizing on mobile */
      .modern-map {
        height: 180px;
      }

      /* Improve search results display on mobile */
      .location-results {
        max-height: 250px;
      }

      .result-item {
        padding: 16px 12px;
      }

      .result-details h6 {
        font-size: 13px;
      }

      .result-details small {
        font-size: 11px;
      }
    }

    /* Additional breakpoint for very small screens */
    @media (max-width: 576px) {
      .create-event-modal {
        margin: 0;
        border-radius: 0;
      }

      .form-section {
        border-radius: 8px;
        padding: 12px;
      }

      .form-section.compact {
        padding: 8px;
      }

      .section-title {
        font-size: 14px;
      }

      .modern-label {
        font-size: 13px;
      }

      .modern-input,
      .modern-textarea {
        font-size: 14px;
        padding: 8px;
      }

      .location-search-container .btn {
        padding: 8px 16px;
        font-size: 14px;
      }

      .modern-map {
        height: 160px;
      }

      /* Stack location actions vertically on very small screens */
      .d-flex.gap-2.location-actions {
        flex-direction: column;
        gap: 6px;
      }

      .d-flex.gap-2.location-actions .btn {
        width: 100%;
        text-align: center;
      }

      /* Better handling of search results on small screens */
      .location-results {
        border-radius: 6px;
        max-height: 200px;
      }

      .result-item {
        padding: 12px 8px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .result-info {
        width: 100%;
      }

      .result-type {
        margin-top: 4px;
        align-self: flex-start;
      }
    }
  </style>

  <script>
    // Initialize Event Create Form
    document.addEventListener("DOMContentLoaded", function () {
      const form = document.getElementById("createEventForm");
      if (!form) return;

      // Initialize basic form validation
      initializeFormValidation(form);

      // Location operations will be initialized by event-operations.js when modal is shown
      console.log(
        "📝 Create event form loaded - waiting for location operations initialization"
      );
      
    });

    // Basic form validation function
    function initializeFormValidation(form) {
      const inputs = form.querySelectorAll(".modern-input, .modern-textarea");

      inputs.forEach((input) => {
        input.addEventListener("input", function () {
          validateField(this);
        });

        input.addEventListener("blur", function () {
          validateField(this);
        });
      });
    }

    function validateField(field) {
      const isValid = field.checkValidity();
      const formGroup = field.closest(".form-group");

      if (isValid) {
        field.classList.remove("is-invalid");
        field.classList.add("is-valid");
        if (formGroup) {
          formGroup.classList.remove("has-error");
          formGroup.classList.add("has-success");
        }
      } else {
        field.classList.remove("is-valid");
        field.classList.add("is-invalid");
        if (formGroup) {
          formGroup.classList.remove("has-success");
          formGroup.classList.add("has-error");
        }
      }
    }
  </script>
</div>