{% extends "base.html" %}

{% block title %}{{ event.title }} - Footprints{% endblock %}

{% block content %}
<!-- Back Navigation -->
<a href="javascript:void(0)" onclick="smartBack()"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back</span>
</a>

<!-- Main Content: Journey Detail Style Layout -->
<div class="row g-4" id="eventContainer">
  <!-- Event Detail Panel (Left) -->
  <div class="col-12 col-xl-7">
    <div class="card shadow-sm border-0 rounded-3 journey-detail-card" style="height: calc(100vh - 250px);">
      <div class="card-body">
        <div class="event-content-container">
          <div class="event-content" id="pageData" data-event-id="{{ event.id }}"
            data-current-user-id="{{ session.get('user_id', '') }}"
            data-premium-access="{% if premium_access %}true{% else %}false{% endif %}"
            data-upload-url="{{ url_for('event.upload_event_images', event_id=event.id) }}"
            data-plans-url="{{ url_for('subscription.get_plans') }}"
            data-is-logged-in="{{ 'true' if g.current_user else 'false' }}"
            data-journey-visibility="{{ journey.visibility }}" data-journey-id="{{ journey.id }}"
            data-journey-page="{{ session.get('journey_page', 'public') }}"
            data-published-journey-url="{{ url_for('journey.get_published_journey_detail', journey_id=journey.id) }}"
            data-public-journey-url="{{ url_for('journey.get_public_journey', journey_id=journey.id) }}"
            data-private-journey-url="{{ url_for('journey.get_private_journey', journey_id=journey.id) }}"
            data-published-list-url="{{ url_for('main.get_published_journey') }}"
            data-landing-url="{{ url_for('main.get_landing_page') }}"
            data-private-journeys-url="{{ url_for('journey.get_private_journeys') }}"
            data-public-journeys-url="{{ url_for('journey.get_public_journeys') }}">
            <!-- BEGIN EVENT DETAIL CONTENT -->
            <div class="title-actions-header">
              <div class="title-content">
                <!-- Journey Breadcrumb -->
                <div class="journey-breadcrumb">
                  <i class="bi bi-journal-bookmark"></i>
                  <span>{{ journey.title }}</span>
                </div>
                <!-- Event Title -->
                <h1 class="event-title">{{ event.title }}</h1>
              </div>

              <div class="header-actions">
                <!-- Status Badges -->
                <div class="status-badges">
                  {% if journey.visibility == 'private' %}
                  <span class="status-badge private">
                    <i class="bi bi-lock-fill"></i>Private
                  </span>
                  {% endif %}
                  {% if journey.is_hidden %}
                  <span class="status-badge hidden">
                    <i class="bi bi-eye-slash"></i>Hidden
                  </span>
                  {% endif %}
                  {% if journey.no_edits and can_manage_content() %}
                  <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1"
                    title="This journey is protected from staff edits">
                    <i class="bi bi-shield-lock me-1"></i>No Edits
                  </span>
                  {% endif %}
                </div>

                <!-- Like Button -->
                {% if journey.visibility != 'private' %}
                {% if session.get('user_id') %}
                <button type="button" class="like-btn{% if user_liked_event %} liked{% endif %}"
                  data-action="toggle-like" data-event-id="{{ event.id }}">
                  <i class="bi bi-heart{% if user_liked_event %}-fill{% endif %}"></i>
                  <span>{{ event_likes|default(0) }}</span>
                </button>
                {% else %}
                <!-- Non-logged-in users see like count but cannot interact -->
                <div class="like-btn disabled">
                  <i class="bi bi-heart"></i>
                  <span>{{ event_likes|default(0) }}</span>
                </div>
                {% endif %}
                {% endif %}

                <!-- Menu -->
                {% if session.get('user_id') and (journey.user_id == session.get('user_id') or can_manage_content()) %}
                <div class="dropdown">
                  <button class="menu-btn" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots-vertical"></i>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                      {% if journey.no_edits and journey.user_id != session.get('user_id') %}
                      <a href="#" class="dropdown-item disabled" onclick="showProtectedEventMessage()">
                        <i class="bi bi-shield-lock"></i> Edit Event (Protected)
                      </a>
                      {% else %}
                      <button class="dropdown-item editEventBtn" data-event-id="{{ event.id }}">
                        <i class="bi bi-pencil"></i> Edit Event
                      </button>
                      {% endif %}
                    </li>
                    {% if can_access_edit_history() or journey.user_id == session.get('user_id') %}
                    <li>
                      <button type="button" class="dropdown-item" data-action="view-edit-history"
                        data-event-id="{{ event.id }}" data-event-title="{{ event.title }}">
                        <i class="bi bi-clock-history"></i> View Edit History
                      </button>
                    </li>
                    {% endif %}
                    {% if journey.user_id == session.get('user_id') %}
                    <li>
                      <hr class="dropdown-divider">
                    </li>
                    <li>
                      <form method="post" action="{{ url_for('event.delete_event', event_id=event.id) }}"
                        id="deleteEventForm_{{ event.id }}">
                        <button type="button" class="dropdown-item text-danger"
                          onclick="confirmDeleteEventModal('{{ event.id }}')">
                          <i class="bi bi-trash"></i> Delete Event
                        </button>
                      </form>
                    </li>
                    {% endif %}
                  </ul>
                </div>
                {% endif %}
              </div>
            </div>

            <!-- Content Layout Section -->
            <div class="event-info">
              <div class="event-info-container overflow-auto">
                <div class="card-content-layout">
                  <!-- Left Column: Meta + Content -->
                  <div class="left-column" style="max-height: 420px; overflow-y: auto;">
                    <!-- Author and Update Time Section -->
                    <div class="content-section meta-section">
                      <div class="event-meta">
                        <div class="author-info">
                          <div class="author-avatar">
                            <img
                              src="{{ url_for('static', filename=get_safe_image_url(journey.profile_image, 'profile')) }}"
                              alt="{{ journey.username }}">
                          </div>
                          <div class="author-details">
                            <span class="author-name">{{ journey.username }}</span>
                          </div>
                        </div>
                        <div class="update-time">
                          <i class="bi bi-clock-history"></i>
                          <span>Updated {{ journey.updated_at | timeago }}</span>
                        </div>
                      </div>
                    </div>

                    <!-- DateTime Section -->
                    <div class="content-section datetime-section">
                      <div class="section-header">
                        <h3>Date & Time</h3>
                      </div>
                      <div class="section-content">
                        <div class="d-flex flex-row gap-4">
                          <div class="flex-fill">
                            <span class="modern-label"><i class="bi bi-calendar-plus"></i>Starts</span>
                            <span class="value" id="eventStartDate">{{ event.start_datetime | datetime }}</span>
                          </div>
                          {% if event.end_datetime %}
                          <div class="flex-fill">
                            <span class="modern-label"><i class="bi bi-calendar-check"></i>Ends</span>
                            <span class="value" id="eventEndDate">{{ event.end_datetime | datetime }}</span>
                          </div>
                          {% endif %}
                        </div>
                      </div>
                    </div>

                    <!-- Description Section -->
                    <div class="content-section description-section">
                      <div class="section-header">
                        <h3>Description</h3>
                      </div>
                      <div class="section-content"
                        style="min-height: 30px; max-height: 40px; overflow-y: auto; margin-right: 10px;">
                        <p id="eventDescription">{{ event.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- END EVENT DETAIL CONTENT -->
                <div class="right-column mt-4">
                  <div class="row g-4 mb-4 align-items-stretch w-100">
                    <div class="col-12 col-lg-6">
                      <!-- Images Section -->
                      <div class="content-section images-section h-100">
                        <div class="section-header d-flex justify-content-between align-items-center">
                          <h3>Images</h3>
                        </div>
                        <div class="section-content">
                          {% if images and images|length > 0 %}
                          <div class="image-gallery">
                            <div class="main-image">
                              <img
                                src="{{ url_for('static', filename='uploads/event_images/' + images[0].image_filename) }}"
                                alt="Event image" class="event-image-button event-image" event-id="{{ event.id }}"
                                onerror="this.parentElement.parentElement.parentElement.style.display='none'; document.getElementById('noImagesSection').style.display='block';">
                              {% if images|length > 1 %}
                              <div class="image-count">+{{ images|length - 1 }}</div>
                              {% endif %}
                              {% if session.get('user_id') and journey.user_id == session.user_id %}
                              <button type="button" class="manage-btn-inside" id="manageImagesBtn">
                                <i class="bi bi-camera"></i>Manage Images
                              </button>
                              {% elif session.get('user_id') and can_manage_content() and journey.user_id !=
                              session.user_id
                              and journey.no_edits %}
                              <!-- Protected journey - show disabled button with click handler -->
                              <button type="button" class="manage-btn-inside staff-manage-btn disabled"
                                id="staffManageImagesBtn" title="This journey is protected from content manager edits"
                                data-protected="true" style="pointer-events: auto;">
                                <i class="bi bi-shield-check"></i>Staff Manage
                              </button>
                              {% elif session.get('user_id') and can_manage_content() and journey.user_id !=
                              session.user_id
                              %}
                              <!-- Normal staff manage button -->
                              <button type="button" class="manage-btn-inside staff-manage-btn"
                                id="staffManageImagesBtn">
                                <i class="bi bi-shield-check"></i>Staff Manage
                              </button>
                              {% endif %}
                            </div>
                          </div>
                          {% else %}
                          <div class="empty-images bg-light rounded p-3 text-center" id="noImagesSection">
                            <i class="bi bi-images fs-2 text-secondary"></i>
                            <p class="text-muted mt-2 mb-2 small">No Event Images Added</p>
                            {% if session.get('user_id') and journey.user_id == session.user_id %}
                            <button type="button" class="btn btn-primary btn-sm" id="addFirstImageBtn">
                              <i class="bi bi-plus-lg me-1"></i>Add Image
                            </button>
                            {% endif %}
                          </div>
                          {% endif %}
                        </div>
                      </div>
                    </div>
                    <div class="col-12 col-lg-6">
                      <!-- Location Section -->
                      <div class="content-section location-section h-100">
                        <div class="section-header d-flex justify-content-between align-items-center">
                          <h3>Location</h3>
                          {% if session.get('user_id') and event.latitude and event.longitude %}
                          <button type="button" class="btn btn-outline-primary btn-sm rounded-pill px-3 flex-shrink-0"
                            data-action="open-location-modal" data-latitude="{{ event.latitude }}"
                            data-longitude="{{ event.longitude }}" data-location-name="{{ event.location_name }}"
                            data-location-id="{{ event.location_id }}">
                            <i class="bi bi-geo-alt me-1"></i>
                            Map & Follow
                          </button>
                          {% endif %}
                        </div>
                        <div class="section-content">
                          {% if event.latitude and event.longitude %}
                          <div class="map-container">
                            <div id="map" class="event-map" data-lat="{{ event.latitude }}"
                              data-lng="{{ event.longitude }}" data-location-name="{{ event.location_name }}"></div>
                          </div>
                          {% endif %}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Comments Panel (Right) -->
  <div class="col-12 col-xl-5">
    <div class="card shadow-sm border-0 rounded-3"
      style="max-height: calc(100vh - 250px); overflow-y: auto; height: 100%;">
      <div class=" card-body">
        <div class="comments-content-container">
          <div class="comments-section">
            <div class="comments-header">
              <h2><i class="bi bi-chat-dots"></i>Comments</h2>
              <span class="comment-count">{{ comments|length }}</span>
            </div>
            <div class="comments-list">
              {% if comments|length > 0 %}
              {% for comment in comments %}
              <div class="comment-item" data-comment-id="{{ comment.id }}">
                <div class="comment-avatar">
                  <img src="{{ url_for('static', filename=get_safe_image_url(comment.profile_image, 'profile')) }}"
                    alt="{{ comment.username }}">
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.username }}</span>
                    <span class="comment-time">{{ comment.created_at | timeago }}</span>
                  </div>
                  {% if not comment.is_hidden or comment.user_id == session.user_id or can_manage_content() %}
                  <div class="comment-text">
                    {{ comment.content }}
                    {% if comment.is_hidden %}
                    <div class="hidden-notice">This comment is hidden from public view.</div>
                    {% endif %}
                  </div>
                  <div class="comment-actions">
                    {% if session.get('user_id') %}
                    <form
                      action="{{ url_for('event.interact_with_event_comment', event_id=event.id, comment_id=comment['id']) }}"
                      method="post">
                      <input type="hidden" name="action" value="like">
                      <button type="submit" class="action-btn like-btn">
                        <i class="bi bi-hand-thumbs-up{% if comment.user_has_liked[0] %}-fill{% endif %}"></i>
                        <span>{{ comment.like_count|default(0) }}</span>
                      </button>
                    </form>
                    <form
                      action="{{ url_for('event.interact_with_event_comment', event_id=event.id, comment_id=comment['id']) }}"
                      method="post">
                      <input type="hidden" name="action" value="dislike">
                      <button type="submit" class="action-btn dislike-btn">
                        <i class="bi bi-hand-thumbs-down{% if comment.user_has_disliked[0] %}-fill{% endif %}"></i>
                        <span>{{ comment.dislike_count|default(0) }}</span>
                      </button>
                    </form>
                    {% else %}
                    <!-- Non-logged-in users see counts but cannot interact -->
                    <div class="action-btn disabled">
                      <i class="bi bi-hand-thumbs-up"></i>
                      <span>{{ comment.like_count|default(0) }}</span>
                    </div>
                    <div class="action-btn disabled">
                      <i class="bi bi-hand-thumbs-down"></i>
                      <span>{{ comment.dislike_count|default(0) }}</span>
                    </div>
                    {% endif %}
                    {% if session.get('user_id') %}
                    {% if comment.user_id == session.user_id %}
                    <form method="post"
                      action="{{ url_for('event.delete_event_comment', event_id=event.id, comment_id=comment.id) }}">
                      <button type="button" class="action-btn delete-btn"
                        onclick="confirmDeleteComment('{{ comment.id}},{{ event.id }}')">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                    {% else %}
                    {% if comment.user_has_reported %}
                    <div class="action-btn reported">
                      <i class="bi bi-flag-fill"></i>
                    </div>
                    {% else %}
                    <button type="button" class="action-btn report-btn report-comment-button"
                      data-comment-id="{{ comment.id }}">
                      <i class="bi bi-flag"></i>
                    </button>
                    {% endif %}
                    {% endif %}
                    {% endif %}
                  </div>
                  {% else %}
                  <div class="comment-text hidden">
                    This comment has been hidden by a staff member.
                  </div>
                  {% endif %}
                </div>
              </div>
              {% endfor %}
              {% else %}
              <!-- No Comments State -->
              <div class="no-comments-state">
                <div class="no-comments-icon">
                  <i class="bi bi-chat-dots"></i>
                </div>
                <div class="no-comments-content">
                  <h4>No comments yet</h4>
                  <p>Be the first to share your thoughts about this event!</p>
                </div>
              </div>
              {% endif %}
            </div>
            {% if session.get('user_id') %}
            <div class="comment-form-container">
              <form action="{{ url_for('event.add_event_comment', event_id=event.id) }}" method="post"
                class="comment-form">
                <input type="text" name="content" class="comment-input" placeholder="Add a comment...">
                <button type="submit" class="submit-btn">
                  <i class="bi bi-send"></i>
                </button>
              </form>
            </div>
            {% elif journey.visibility in ['public', 'published'] and not session.get('user_id') %}
            <div class="comment-form-container">
              <div class="login-prompt text-center p-3">
                <p class="text-muted mb-2">Please log in to add a comment</p>
                <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-sm">Log In</a>
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Event Location Modal -->
<div class="modal fade" id="eventLocationModal" tabindex="-1" aria-labelledby="eventLocationModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content rounded-4 border-0">
      <div class="modal-header border-0 pb-2">
        <div class="d-flex align-items-center w-100">
          <div class="d-flex align-items-center flex-grow-1">
            <i class="bi bi-geo-alt-fill text-primary me-2"></i>
            <h5 class="modal-title fw-bold mb-0" id="eventLocationModalLabel">Event Location</h5>
          </div>
          <div class="d-flex align-items-center gap-2">
            {% if session.get('user_id') %}
            <button type="button" class="btn btn-outline-primary btn-sm rounded-pill px-3"
              data-action="toggle-location-follow" data-location-id="">
              <i class="bi bi-heart me-1"></i>
              <span class="btn-text">Follow</span>
            </button>
            {% endif %}
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
        </div>
      </div>
      <div class="modal-body pt-2">
        <div class="map-container rounded-3 overflow-hidden" style="border: 1px solid #e9ecef;">
          <div id="fullEventMap" style="height: 45vh; width: 100%;"></div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block head %}
<!-- Event Detail Bundle CSS - Single file for maximum performance -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/event-detail-bundle.css') }}">

<!-- Responsive improvements for mobile -->
<style>
  /* Mobile responsive improvements */
  @media (max-width: 1199.98px) {

    /* Stack layout vertically on smaller screens */
    /* Give comments section better height on mobile */
    #eventContainer .col-12.col-xl-5 .journey-detail-card {
      min-height: 600px !important;
    }

    /* Adjust spacing for mobile */
    .right-column {
      margin-top: 1rem !important;
    }

    /* Make images and location sections more mobile-friendly */
    .content-section.images-section,
    .content-section.location-section {
      margin-bottom: 1rem;
    }

    /* Improve button layout on mobile */
    .d-flex.flex-column.flex-sm-row {
      gap: 0.75rem;
    }

    /* Better map sizing on mobile */
    .map-container {
      height: 200px;
    }

    .event-map {
      height: 200px !important;
    }
  }

  @media (max-width: 575.98px) {

    /* Extra small screens - stack everything */
    .d-flex.flex-column.flex-sm-row {
      flex-direction: column !important;
      align-items: flex-start !important;
    }

    /* Full width button on very small screens */
    .btn.flex-shrink-0 {
      width: 100%;
      justify-content: center;
    }

    /* Smaller map on very small screens */
    .map-container {
      height: 180px;
    }

    .event-map {
      height: 180px !important;
    }

    /* Even better comment section height on very small screens */
    #eventContainer .col-12.col-xl-5 .journey-detail-card {
      min-height: 70vh !important;
    }

    /* Improve comment list scrolling on mobile */
    .comments-list {
      max-height: calc(70vh - 200px);
      overflow-y: auto;
    }

    /* Better spacing for comment form on mobile */
    .comment-form-container {
      margin-top: auto;
      padding-top: 1rem;
      border-top: 1px solid #e9ecef;
    }
  }
</style>

<!-- Event operations for edit/delete/like functionality -->
<script src="{{ url_for('static', filename='js/event-operations.js') }}"></script>

<!-- Comment interactions for AJAX like/dislike/delete/add -->
<script src="{{ url_for('static', filename='js/comment-interactions.js') }}"></script>

<!-- JavaScript files for edit modal functionality -->
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/location-operations.js') }}"></script>

<script>
  // Essential utilities only - most functionality moved to modular utilities

  // Simple and reliable back button function
  function smartBack() {
    // Get page data for session info
    const pageData = document.getElementById("pageData");
    const journeyPage = pageData?.dataset.journeyPage;
    const isLoggedIn = pageData?.dataset.isLoggedIn === 'true';

    // First check session data - this persists through page reloads
    if (journeyPage === 'departure_board') {
      window.location.href = "/departure_board";
      return;
    } else if (journeyPage === 'published') {
      // For events in published journeys, go back to the published journey detail
      const journeyId = pageData?.dataset.journeyId;
      if (journeyId) {
        window.location.href = `/journey/published/${journeyId}`;
        return;
      }

      // Fallback to published journey list or landing page
      if (!isLoggedIn) {
        window.location.href = "/";
      } else {
        window.location.href = "/published_journey";
      }
      return;
    } else if (journeyPage === 'notifications') {
      // For events accessed from notifications, go back to the journey detail
      const journeyId = pageData?.dataset.journeyId;
      const journeyVisibility = pageData?.dataset.journeyVisibility;

      if (journeyId && journeyVisibility === 'published') {
        window.location.href = `/journey/published/${journeyId}`;
        return;
      } else if (journeyId && journeyVisibility === 'public') {
        window.location.href = `/journey/public/${journeyId}`;
        return;
      } else if (journeyId) {
        // Default to private for private journeys or any other case
        window.location.href = `/journey/private/${journeyId}`;
        return;
      }

      // Fallback to notifications page
      window.location.href = "/notifications/all";
      return;
    }

    // Then check referrer as fallback
    const referrer = document.referrer;
    if (referrer && referrer.includes("/departure_board")) {
      window.location.href = "/departure_board";
      return;
    } else if (referrer && referrer.includes("/discovery")) {
      window.location.href = "/discovery";
      return;
    } else if (referrer && referrer.includes("/dashboard")) {
      window.location.href = "/dashboard";
      return;
    }

    // For events, try to go back to the journey
    if (pageData) {
      const journeyId = pageData.dataset.journeyId;

      if (journeyId && isLoggedIn) {
        window.location.href = `/journey/public/${journeyId}`;
        return;
      }
    }

    // Fallback to public journeys or landing page
    if (isLoggedIn) {
      window.location.href = "/journey/public";
    } else {
      window.location.href = "/";
    }
  }

  // Simple back button text update
  function updateBackButtonText() {
    const backButtonText = document.getElementById('backButtonText');

    // Get page data for session info
    const pageData = document.getElementById("pageData");
    const journeyPage = pageData?.dataset.journeyPage;
    const isLoggedIn = pageData?.dataset.isLoggedIn === 'true';

    // Check referrer to determine back button text
    const referrer = document.referrer;

    // First check session data - this persists through page reloads
    if (journeyPage === 'departure_board') {
      backButtonText.textContent = 'Back to Departure Board';
    } else if (journeyPage === 'published') {
      backButtonText.textContent = 'Back to Journey';
    } else if (journeyPage === 'notifications') {
      backButtonText.textContent = 'Back to Journey';
    } else if (referrer && referrer.includes('/departure_board')) {
      backButtonText.textContent = 'Back to Departure Board';
    } else if (referrer && referrer.includes('/discovery')) {
      backButtonText.textContent = 'Back to Discovery';
    } else if (referrer && referrer.includes('/dashboard')) {
      backButtonText.textContent = 'Back to Dashboard';
    } else if (referrer && referrer.includes('/journey/')) {
      backButtonText.textContent = 'Back to Journey';
    } else if (referrer && referrer.includes('/manage')) {
      backButtonText.textContent = 'Back to Management';
    } else {
      backButtonText.textContent = 'Back to Journey';
    }
  }

  document.addEventListener('DOMContentLoaded', function () {
    updateBackButtonText();
  });

  // Event deletion confirmation with modal
  function confirmDeleteEventModal(eventId) {
    if (typeof showModal === 'function') {
      showModal(
        'Delete Event',
        'Are you sure you want to delete this event? This action cannot be undone.',
        {
          actionText: 'Delete',
          onAction: function () {
            const deleteForm = document.getElementById('deleteEventForm_' + eventId);
            if (deleteForm) {
              deleteForm.submit();
            } else {
              console.error('Delete form not found');
              if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage('Delete form not found. Please refresh and try again.', 'danger');
              }
            }
            return true;
          }
        }
      );
    } else {
      // Fallback to browser confirm if modal system is not available
      if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        document.getElementById('deleteEventForm_' + eventId).submit();
      }
    }
  }

  // Comment deletion confirmation
  function confirmDeleteComment(commentEventIds) {
    const [commentId, eventId] = commentEventIds.split(',');

    showModal('Delete Comment', 'Are you sure you want to delete this comment? This action cannot be undone.', {
      actionText: 'Delete',
      onAction: function () {
        const form = document.createElement('form');
        form.method = 'post';
        form.action = `/event/${eventId}/comment/${commentId}/delete`;
        document.body.appendChild(form);
        form.submit();
        return true;
      }
    });
  }

  // Protected event message
  function showProtectedEventMessage() {
    alert('This event is protected from editing by staff members.');
  }

  // Edit Event Modal Handler
  document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".editEventBtn").forEach(button => {
      button.addEventListener("click", async function () {
        let eventId = this.getAttribute("data-event-id");
        const editUrl = `/event/${eventId}/edit`

        const response = await fetch(editUrl);
        const html = await response.text();

        // Parse the response to extract the modal content
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const modalContent = doc.querySelector('.modal-content');

        if (modalContent) {
          // Show the modal with the fetched content
          showModal('Edit Event', modalContent.innerHTML, {
            size: 'large',
            hideActionButton: true
          });

          // Initialize the edit modal functionality with proper timing
          // Wait for modal to be fully rendered and visible
          setTimeout(() => {
            initializeNewEditModal(eventId);
          }, 500); // Increased delay to ensure modal is fully rendered
        } else {
          console.error('Modal content not found in response');
          showModal('Error', 'Failed to load edit form. Please try again.', {
            actionText: 'OK',
            onAction: function () { return true; }
          });
        }
      });
    });
  });

  // Initialize Edit Modal functionality
  function initializeEditModal(eventId) {
    console.log('Initializing edit modal for event:', eventId);

    // Initialize map if coordinates exist
    const latInput = document.getElementById('latitude');
    const lngInput = document.getElementById('longitude');

    if (latInput && lngInput && latInput.value && lngInput.value) {
      const lat = parseFloat(latInput.value);
      const lng = parseFloat(lngInput.value);

      if (!isNaN(lat) && !isNaN(lng)) {
        setTimeout(() => {
          initializeEditModalMap(lat, lng);
        }, 100);
      }
    }

    // Initialize location search functionality
    initializeEditModalLocationSearch();

    // Handle form submission
    const editForm = document.getElementById('editEventForm');
    if (editForm) {
      editForm.addEventListener('submit', async function (e) {
        e.preventDefault();

        const formData = new FormData(this);

        try {
          const response = await fetch(this.action, {
            method: 'POST',
            body: formData
          });

          if (response.ok) {
            // Check if response is a redirect (successful form submission)
            if (response.redirected || response.url !== this.action) {
              // Store success message and reload
              storeFlashMessage('Event updated successfully!', 'success');
              window.location.reload();
              return;
            }

            // If not redirected, check for JSON response
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              const data = await response.json();
              if (data.success) {
                // Handle "no changes" case differently - don't reload page
                if (data.no_changes) {
                  showFlashMessage(data.message, 'info');
                  return; // Don't reload the page
                }
                storeFlashMessage(data.message || 'Event updated successfully!', 'success');
                window.location.reload();
              } else {
                showFlashMessage(data.message || 'Failed to update event', 'danger');
              }
            } else {
              // Handle HTML response (form with validation errors)
              const html = await response.text();
              const parser = new DOMParser();
              const doc = parser.parseFromString(html, 'text/html');
              const newModalContent = doc.querySelector('.modal-content');

              if (newModalContent) {
                // Update modal content with validation errors
                const currentModal = document.querySelector('.modal-content');
                if (currentModal) {
                  currentModal.innerHTML = newModalContent.innerHTML;
                  // Re-initialize the modal functionality
                  initializeNewEditModal(eventId);
                }
              }
            }
          } else {
            showFlashMessage('Failed to update event. Please try again.', 'danger');
          }
        } catch (error) {
          console.error('Error submitting form:', error);
          showFlashMessage('An error occurred. Please try again.', 'danger');
        }
      });
    }
  }

  // NEW: Comprehensive Edit Modal Initialization
  function initializeNewEditModal(eventId) {
    console.log('Initializing edit modal for event:', eventId);

    // Note: Edit modal scripts are not executed when loaded via AJAX/innerHTML
    // So we use our own initialization functions directly

    // Initialize form validation
    const form = document.getElementById('editEventForm');
    if (form) {
      // Initialize enhanced form validation if available
      if (window.EnhancedFormValidation) {
        window.EnhancedFormValidation.initializeModernForm(form, {
          validateOnInput: true,
          validateOnBlur: true,
          showSuccessStates: true,
        });
      }

      // Initialize basic form validation
      initializeBasicFormValidation(form);
    }

    // Initialize location functionality
    initializeNewLocationFunctionality();

    // Initialize map if coordinates exist
    const latInput = document.getElementById('latitude');
    const lngInput = document.getElementById('longitude');

    if (latInput && lngInput && latInput.value && lngInput.value) {
      const lat = parseFloat(latInput.value);
      const lng = parseFloat(lngInput.value);

      if (!isNaN(lat) && !isNaN(lng)) {
        setTimeout(() => {
          if (typeof initializeEditModalMap === 'function') {
            initializeEditModalMap(lat, lng);
          } else {
            initializeEditModalMapFallback(lat, lng);
          }
        }, 200);
      }
    }

    // Initialize form submission handling
    if (form) {
      form.addEventListener('submit', async function (e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
          const response = await fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          });

          if (response.ok) {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              const data = await response.json();

              if (data.success) {
                // Close modal
                const editModal = document.querySelector('#editEventModal');
                if (editModal) {
                  const modalInstance = bootstrap.Modal.getInstance(editModal);
                  if (modalInstance) {
                    modalInstance.hide();
                  }
                }

                // Handle "no changes" case differently - don't reload page
                if (data.no_changes) {

                  showFlashMessage(data.message, 'info');
                  return; // Don't reload the page
                }

                // Show the exact message from server (no fallback needed)
                showFlashMessage(data.message, 'success');

                // Reload page after a short delay to show the flash message
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              } else {
                showFlashMessage(data.message || 'Failed to update event', 'danger');
              }
            } else {
              // Handle form validation errors by reloading modal content
              const html = await response.text();
              const parser = new DOMParser();
              const doc = parser.parseFromString(html, 'text/html');
              const newModalContent = doc.querySelector('.modal-content');

              if (newModalContent) {
                const currentModal = document.querySelector('.modal-content');
                if (currentModal) {
                  currentModal.innerHTML = newModalContent.innerHTML;
                  initializeNewEditModal(eventId);
                }
              }
            }
          } else {
            showFlashMessage('Failed to update event. Please try again.', 'danger');
          }
        } catch (error) {
          console.error('Error submitting form:', error);
          showFlashMessage('An error occurred. Please try again.', 'danger');
        }
      });
    }

  }

  // Fallback map initialization function
  function initializeEditModalMapFallback(lat, lng) {
    const mapContainer = document.getElementById('editModalMap');
    if (!mapContainer) return;

    try {
      // Initialize map
      window.editModalMap = L.map('editModalMap').setView([lat, lng], 15);

      // Add tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(window.editModalMap);

      // Add marker (non-draggable for preview only)
      window.editModalMarker = L.marker([lat, lng], {
        draggable: false
      }).addTo(window.editModalMap);
    } catch (error) {
      console.error('Error initializing edit modal map fallback:', error);
    }
  }

  // Initialize basic form validation
  function initializeBasicFormValidation(form) {
    const inputs = form.querySelectorAll('.modern-input, .modern-textarea');

    inputs.forEach((input) => {
      input.addEventListener('input', function () {
        validateEditField(this);
      });

      input.addEventListener('blur', function () {
        validateEditField(this);
      });
    });
  }

  function validateEditField(field) {
    const isValid = field.checkValidity();
    const formGroup = field.closest('.form-group');

    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
      if (formGroup) {
        formGroup.classList.remove('has-error');
        formGroup.classList.add('has-success');
      }
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');
      if (formGroup) {
        formGroup.classList.remove('has-success');
        formGroup.classList.add('has-error');
      }
    }
  }

  // Initialize new location functionality
  function initializeNewLocationFunctionality() {
    // Wait for DOM elements to be available in modal context
    setTimeout(() => {
      // Use our own initialization since edit modal scripts aren't executed via AJAX
      console.log('Initializing location functionality');
      initializeChangeLocationButtonFallback();

      initializeBackToSearchButton();

      // Initialize location operations with proper timing for modal context
      setTimeout(() => {
        initializeLocationOperationsForEdit();
      }, 200); // Additional delay for location operations
    }, 100); // Initial delay for modal DOM rendering
  }

  // Fallback initialization for location buttons (handles both owner and staff buttons)
  function initializeChangeLocationButtonFallback(retryCount = 0) {
    const changeLocationBtn = document.getElementById('changeLocationBtn');
    const editLocationNameBtn = document.getElementById('editLocationNameBtn');
    const cancelLocationEditBtn = document.getElementById('cancelLocationEditBtn');

    let buttonFound = false;

    // Handle owner's change location button
    if (changeLocationBtn) {

      // Remove any existing listeners to prevent duplicates
      changeLocationBtn.replaceWith(changeLocationBtn.cloneNode(true));
      const newBtn = document.getElementById('changeLocationBtn');

      newBtn.addEventListener('click', function () {
        showNewLocationSearchSection();
      });
      buttonFound = true;
    }

    // Handle content manager's edit location name button
    if (editLocationNameBtn) {
      console.log('Found editLocationNameBtn (content manager)');
      // Remove any existing listeners to prevent duplicates
      editLocationNameBtn.replaceWith(editLocationNameBtn.cloneNode(true));
      const newBtn = document.getElementById('editLocationNameBtn');

      newBtn.addEventListener('click', function () {
        showStaffLocationEditSection();
      });
      buttonFound = true;
    }

    // Handle cancel location edit button
    if (cancelLocationEditBtn) {
      console.log('Found cancelLocationEditBtn');
      // Remove any existing listeners to prevent duplicates
      cancelLocationEditBtn.replaceWith(cancelLocationEditBtn.cloneNode(true));
      const newBtn = document.getElementById('cancelLocationEditBtn');

      newBtn.addEventListener('click', function () {
        hideStaffLocationEditSection();
      });
      buttonFound = true;
    }

    if (!buttonFound && retryCount < 3) {
      // Retry up to 3 times with increasing delays
      setTimeout(() => {
        initializeChangeLocationButtonFallback(retryCount + 1);
      }, 200 * (retryCount + 1));
    } else if (!buttonFound) {
      console.error('No location change buttons found after multiple attempts');
    }
  }

  // Initialize back to search button
  function initializeBackToSearchButton(retryCount = 0) {
    const backToSearchBtn = document.getElementById('backToSearchBtn');

    if (backToSearchBtn) {
      // Remove any existing listeners to prevent duplicates
      backToSearchBtn.replaceWith(backToSearchBtn.cloneNode(true));
      const newBtn = document.getElementById('backToSearchBtn');

      newBtn.addEventListener('click', function () {
        // Hide selected location section
        const selectedLocationSection = document.getElementById('selectedLocationSection');
        const mapSection = document.getElementById('mapSection');
        const locationSearchSection = document.getElementById('locationSearchSection');

        if (selectedLocationSection) selectedLocationSection.style.display = 'none';
        if (mapSection) mapSection.style.display = 'none';
        if (locationSearchSection) locationSearchSection.style.display = 'block';

        // Clear and focus search input
        const searchInput = document.getElementById('locationSearch');
        if (searchInput) {
          searchInput.value = '';
          searchInput.focus();
        }
      });
    } else if (retryCount < 3) {
      // Retry up to 3 times with increasing delays
      setTimeout(() => {
        initializeBackToSearchButton(retryCount + 1);
      }, 200 * (retryCount + 1));
    } else {
      console.error('Back to search button not found after multiple attempts');
    }
  }

  // Location operations initialization for edit modal
  function initializeLocationOperationsForEdit() {
    // Check if LocationOperations class is available
    if (typeof LocationOperations !== 'undefined') {
      try {
        // Create new instance with error handling
        window.editLocationOperations = new LocationOperations();
      } catch (error) {
        console.error('Failed to initialize LocationOperations:', error);
        initializeEditModalLocationSearch();
      }
    } else {
      initializeEditModalLocationSearch();
    }
  }

  function showNewLocationSearchSection() {
    const currentLocationSection = document.getElementById('currentLocationSection');
    const currentMapSection = document.getElementById('currentMapSection');
    const locationSearchSection = document.getElementById('locationSearchSection');

    if (currentLocationSection) currentLocationSection.style.display = 'none';
    if (currentMapSection) currentMapSection.style.display = 'none';
    if (locationSearchSection) {
      locationSearchSection.style.display = 'block';
    } else {
      console.error('Location search section not found in modal');
      return;
    }

    // Focus on search input with delay for modal rendering
    setTimeout(() => {
      const searchInput = document.getElementById('locationSearch');
      if (searchInput) {
        searchInput.focus();
      }
    }, 150);

    // Re-initialize location operations if needed with proper timing
    if (!window.editLocationOperations) {
      setTimeout(() => {
        initializeLocationOperationsForEdit();
      }, 300);
    }
  }

  // Show staff location edit section (for content managers)
  function showStaffLocationEditSection() {
    const staffLocationEditSection = document.getElementById('staffLocationEditSection');

    if (staffLocationEditSection) {
      // Hide current location card when editing (but keep the map visible)
      const currentLocationSection = document.getElementById('currentLocationSection');

      if (currentLocationSection) currentLocationSection.style.display = 'none';
      // Keep currentMapSection visible for spatial context

      // Show staff edit section
      staffLocationEditSection.style.display = 'block';

      // Focus on location name input
      const nameInput = document.getElementById('staffLocationName');
      if (nameInput) {
        nameInput.focus();
      }
    } else {
      console.error('Staff location edit section not found in modal');
    }
  }

  // Hide staff location edit section and restore current location display
  function hideStaffLocationEditSection() {
    const staffLocationEditSection = document.getElementById('staffLocationEditSection');

    if (staffLocationEditSection) {
      // Hide staff edit section
      staffLocationEditSection.style.display = 'none';

      // Show current location card again (map was never hidden)
      const currentLocationSection = document.getElementById('currentLocationSection');

      if (currentLocationSection) currentLocationSection.style.display = 'block';
    }
  }

  // Initialize Edit Modal Location Search
  function initializeEditModalLocationSearch() {
    const locationInput = document.getElementById('location');
    const suggestionBox = document.getElementById('locationSuggestions');

    if (!locationInput || !suggestionBox) return;

    let searchTimeout;

    locationInput.addEventListener('input', function () {
      clearTimeout(searchTimeout);
      const query = this.value.trim();

      if (query.length < 2) {
        hideEditModalLocationDropdown();
        return;
      }

      searchTimeout = setTimeout(() => {
        editModalSearchLocations(query);
      }, 300);
    });

    // Hide dropdown when clicking outside
    document.addEventListener('click', function (e) {
      if (!locationInput.contains(e.target) && !suggestionBox.contains(e.target)) {
        hideEditModalLocationDropdown();
      }
    });
  }

  function hideEditModalLocationDropdown() {
    const suggestionBox = document.getElementById('locationSuggestions');
    if (suggestionBox) {
      suggestionBox.classList.add('d-none');
    }
  }

  async function editModalSearchLocations(query) {
    const suggestionBox = document.getElementById('locationSuggestions');
    if (!suggestionBox) return;

    try {
      console.log(`Searching database for: "${query}"`);
      let response = await fetch(`/location/search?query=${encodeURIComponent(query)}`);
      let data = await response.json();
      suggestionBox.innerHTML = '';

      if (data.length === 0) {
        hideEditModalLocationDropdown();
        console.log('No database results found');
        return;
      }

      console.log(`Found ${data.length} database locations`);

      data.forEach(location => {
        const item = document.createElement('div');
        item.className = 'dropdown-item location-suggestion';
        item.textContent = location.name;
        item.addEventListener('click', function () {
          document.getElementById('location').value = location.name;
          hideEditModalLocationDropdown();
          editModalLoadLocationCoordinates(location.name);
        });
        suggestionBox.appendChild(item);
      });

      suggestionBox.classList.remove('d-none');
    } catch (err) {
      console.error("Error during location search:", err);
    }
  }

  // Load coordinates for a database location and update map
  async function editModalLoadLocationCoordinates(locationName) {
    try {
      console.log(`Loading coordinates for: ${locationName}`);
      const response = await fetch(`/api/location-coords?name=${encodeURIComponent(locationName)}`);
      const data = await response.json();

      if (data.lat && data.lng) {
        console.log(`Found coordinates: ${data.lat}, ${data.lng}`);

        // Update coordinate fields
        document.getElementById('latitude').value = data.lat;
        document.getElementById('longitude').value = data.lng;

        // Update map if it exists
        if (window.editModalMap) {
          const lat = parseFloat(data.lat);
          const lng = parseFloat(data.lng);

          window.editModalMap.setView([lat, lng], 15);

          if (window.editModalMarker) {
            window.editModalMarker.setLatLng([lat, lng]);
          } else {
            // Check if this is a preview-only map
            const mapContainer = document.getElementById('editModalMap');
            const isPreviewOnly = mapContainer && mapContainer.classList.contains('map-preview-only');

            window.editModalMarker = L.marker([lat, lng], {
              draggable: !isPreviewOnly
            }).addTo(window.editModalMap);
          }
        }
      } else {
        console.log('No coordinates found for location');
      }
    } catch (error) {
      console.error('Error loading coordinates:', error);
    }
  }

  // Initialize Edit Modal Map
  function initializeEditModalMap(lat, lng) {
    const mapContainer = document.getElementById('editModalMap');
    if (!mapContainer) return;

    try {
      // Initialize map
      window.editModalMap = L.map('editModalMap').setView([lat, lng], 15);

      // Add tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(window.editModalMap);

      // Add marker (non-draggable for preview only)
      window.editModalMarker = L.marker([lat, lng], {
        draggable: false
      }).addTo(window.editModalMap);

      console.log('Edit modal map initialized');
    } catch (error) {
      console.error('Error initializing edit modal map:', error);
    }
  }

  // Reverse geocode coordinates to address
  async function reverseGeocode(lat, lng) {
    try {
      const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`);
      const data = await response.json();

      const locationInput = document.getElementById('location');
      if (locationInput) {
        locationInput.value = data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
  }

  // Protected journey message for event images
  function showProtectedJourneyMessage() {
    const modalContent = `
    <div class="text-center mb-4">
      <div class="mb-3">
        <i class="bi bi-shield-lock text-warning" style="font-size: 3rem;"></i>
      </div>
      <h4 class="fw-bold text-dark mb-3">Journey Protected</h4>
    </div>
    <div class="alert alert-info border-0 rounded-3">
      <div class="d-flex align-items-start">
        <i class="bi bi-info-circle me-2 mt-1"></i>
        <div>
          <strong>Note:</strong> This protection was enabled by the journey owner to maintain content integrity.
          However, as a staff member, you can still hide the journey if needed for moderation purposes.
        </div>
      </div>
    </div>
  `;

    // Check if showModal function is available (from modal.html)
    if (typeof window.showModal === "function") {
      window.showModal("Journey Protection Information", modalContent, {
        hideActionButton: true,
        modalSize: "medium",
      });
    } else {
      // Fallback to alert if modal system is not available
      alert("This journey is protected from editing by staff members.");
    }
  }

  // Premium upgrade modal
  function showPremiumUpgradeModal(feature) {
    const featureNames = {
      'edit-history': 'Edit History'
    };

    const featureName = featureNames[feature] || 'Premium Feature';

    showModal(`${featureName} - Premium Feature`,
      `<div class="premium-upgrade">
      <i class="bi bi-star-fill"></i>
      <h4>Premium Feature</h4>
      <p>Upgrade to Premium to access complete edit history for all your content.</p>
    </div>`, {
      actionText: 'Upgrade to Premium',
      onAction: function () {
        window.location.href = '/account/profile?active_tab=subscription';
        return true;
      },
      cancelText: 'Maybe Later'
    });
  }

  // Image management and report comment functionality
  document.addEventListener('DOMContentLoaded', function () {
    // Handle owner manage images button
    const manageImagesBtn = document.getElementById('manageImagesBtn');
    if (manageImagesBtn) {
      manageImagesBtn.addEventListener('click', function () {
        const eventContent = document.querySelector('.event-content');
        const eventId = parseInt(eventContent.getAttribute('data-event-id'));
        const isPremiumAccess = eventContent.getAttribute('data-premium-access') === 'true';
        const uploadUrl = eventContent.getAttribute('data-upload-url');
        const plansUrl = eventContent.getAttribute('data-plans-url');

        // Fetch current images and show management modal
        fetch(`/event/${eventId}/images-data`)
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showEventImageManager(eventId, isPremiumAccess, uploadUrl, plansUrl);
            } else {
              if (typeof window.showFlashMessage === 'function') {
                window.showFlashMessage(data.message || 'Failed to load images', 'danger');
              }
            }
          })
          .catch(error => {
            console.error('Error fetching images:', error);
            if (typeof window.showFlashMessage === 'function') {
              window.showFlashMessage('Failed to load images. Please try again.', 'danger');
            }
          });
      });
    }

    // Handle add first image button
    const addFirstImageBtn = document.getElementById('addFirstImageBtn');
    if (addFirstImageBtn) {
      addFirstImageBtn.addEventListener('click', function () {
        const eventContent = document.querySelector('.event-content');
        const eventId = parseInt(eventContent.getAttribute('data-event-id'));
        const isPremiumAccess = eventContent.getAttribute('data-premium-access') === 'true';
        const uploadUrl = eventContent.getAttribute('data-upload-url');
        const plansUrl = eventContent.getAttribute('data-plans-url');

        showEventImageUploader(eventId, isPremiumAccess, uploadUrl);
      });
    }

    // Handle staff manage images button (non-protected)
    const staffManageBtn = document.getElementById('staffManageImagesBtn');
    if (staffManageBtn) {
      if (staffManageBtn.dataset.protected === 'true') {
        // Protected journey - show modal
        staffManageBtn.addEventListener('click', function (e) {
          e.preventDefault();
          e.stopPropagation();
          showProtectedJourneyMessage();
        });
      } else {
        // Normal staff management
        staffManageBtn.addEventListener('click', function () {
          const eventContent = document.querySelector('.event-content');
          const eventId = parseInt(eventContent.getAttribute('data-event-id'));
          showStaffImageManager(eventId);
        });
      }
    }

    // Report comment functionality
    document.querySelectorAll('.report-comment-button').forEach(button => {
      button.addEventListener('click', async function () {
        const commentId = this.getAttribute('data-comment-id');

        const formHtml = `
        <form id="reportCommentForm" action="/event/comment/${commentId}/report" method="post" class="needs-validation" novalidate>
          <div class="mb-3">
            <label for="reportReason" class="modern-label">
              <i class="bi bi-flag"></i>
              Reason for reporting:
            </label>
            <textarea class="form-control report-input" id="reportReason" name="reason" required minlength="5" maxlength="250"></textarea>
            <div class="invalid-feedback">Reason is required and must be at least 5 characters long.</div>
            </div>
        </form>
      `;

        showModal('Report Comment', formHtml, {
          actionText: 'Submit',
          onAction: function () {
            const form = document.getElementById('reportCommentForm');
            form.classList.add('was-validated');

            if (form.checkValidity()) {
              form.submit();
              return true;
            } else {
              return false;
            }
          }

        });
      });
    });
  });

</script>
{% endblock %}

{% block scripts %}
<!-- Event Detail Bundle - Comprehensive JavaScript for all event detail functionality -->
<script src="{{ url_for('static', filename='js/event-detail-bundle.js') }}"></script>
{% endblock %}