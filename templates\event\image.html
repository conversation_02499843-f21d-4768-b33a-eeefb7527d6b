{% if event_images and event_images|length > 0 %}
<div class="image-gallery-modal">
    <div class="image-container">
        <img src="{{ url_for('static', filename='uploads/event_images/' + event_images[0].image_filename) }}"
            id="mainImage" alt="{{ event.title }}" />

        {% if event_images|length > 1 %}
        <div class="nav-arrow prev" id="prevImage">
            <i class="bi bi-chevron-left"></i>
        </div>
        <div class="nav-arrow next" id="nextImage">
            <i class="bi bi-chevron-right"></i>
        </div>
        {% endif %}

        <!-- Image not found message -->
        <div id="imageNotFoundMessage" style="display: none" class="text-center p-4">
            <i class="bi bi-exclamation-triangle fs-1 text-warning"></i>
            <p class="text-muted mt-2">Image not found</p>
        </div>
    </div>

    {% if event_images|length > 1 %}
    <div class="dots-indicator">
        {% for image in event_images %}
        <span class="dot {% if loop.index0 == 0 %}active{% endif %}" data-index="{{ loop.index0 }}">•</span>
        {% endfor %}
    </div>

    <div class="thumbnails-container" id="galleryThumbnails">
        {% for image in event_images%}
        <div class="thumbnail {% if loop.index0 == 0 %}active{% endif %}" data-index="{{ loop.index0 }}">
            <img src="{{ url_for('static', filename='uploads/event_images/' + image.image_filename) }}"
                alt="{{ event.title }} - Image {{ loop.index + 1 }}" />
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% else %}
<div class="text-center p-4">
    <i class="bi bi-images fs-1 text-secondary"></i>
    <p class="text-muted mt-2">No images available for this event</p>
</div>
{% endif %}

<style>
    .modal-dialog-image-gallery {
        max-width: 800px;
    }

    .image-gallery-modal {
        width: 100%;
    }

    .image-container {
        position: relative;
        width: 100%;
        height: 400px;
        border-radius: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-container img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 10px;
    }

    .nav-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .nav-arrow:hover {
        background-color: rgba(0, 0, 0, 0.9);
        transform: translateY(-50%) scale(1.1);
    }

    .nav-arrow.prev {
        left: 10px;
    }

    .nav-arrow.next {
        right: 10px;
    }

    .nav-arrow i {
        font-size: 18px;
    }

    .thumbnails-container {
        display: flex;
        gap: 10px;
        overflow-x: auto;
        padding: 10px 0;
        justify-content: center;
        flex-wrap: wrap;
    }

    .thumbnail {
        flex-shrink: 0;
        width: 80px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .thumbnail:hover {
        border-color: #007bff;
        transform: scale(1.05);
    }

    .thumbnail.active {
        border-color: #007bff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
    }

    .thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .image-container {
            height: 300px;
        }

        .nav-arrow {
            width: 35px;
            height: 35px;
        }

        .nav-arrow i {
            font-size: 16px;
        }

        .thumbnail {
            width: 60px;
            height: 45px;
        }
    }

    /* Scrollbar styling for thumbnails */
    .thumbnails-container::-webkit-scrollbar {
        height: 6px;
    }

    .thumbnails-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .thumbnails-container::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .thumbnails-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Dots indicator styling */
    .dots-indicator {
        text-align: center;
        margin: 15px 0;
    }

    .dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #ccc;
        margin: 0 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0;
    }

    .dot:hover {
        background-color: #999;
        transform: scale(1.2);
    }

    .dot.active {
        background-color: #007bff;
        transform: scale(1.3);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Handle image loading errors in gallery
        const mainImage = document.getElementById('mainImage');
        if (mainImage) {
            mainImage.addEventListener('error', function () {
                // Hide the failed image and show error message
                this.style.display = 'none';
                const errorMessage = document.getElementById('imageNotFoundMessage');
                if (errorMessage) {
                    errorMessage.style.display = 'block';
                }
            });
        }

        // Handle thumbnail image errors
        const thumbnails = document.querySelectorAll('.thumbnail img');
        thumbnails.forEach(function (img) {
            img.addEventListener('error', function () {
                // Hide the thumbnail if image fails to load
                this.closest('.thumbnail').style.display = 'none';
            });
        });
    });
</script>