<div id="locationModal">
  <div class="d-flex justify-content-between align-items-center p-3 bg-white">
    <div class="text-primary fw-medium">
      <i class="bi bi-geo-alt"></i> {{event.location_name}}
    </div>
    {% if is_following_location %}
    <form method="POST"
      action="{{ url_for('departure_board.unfollow_location', location_id=event.location_id, journey_id=event.journey_id) }}"
      class="d-inline">
      <button class="btn btn-dark rounded-pill px-4">
        Unfollow
      </button>
    </form>
    {% else %}
    <form method="POST"
      action="{{ url_for('departure_board.follow_location', location_id=event.location_id, journey_id=event.journey_id) }}"
      class="d-inline">
      <button class="btn btn-dark rounded-pill px-4">
        Follow
      </button>
    </form>
    {% endif %}
  </div>

  <div id="map" style="height: 300px; width: 100%; border-radius: 8px;" data-longitude="{{event.location_longitude}}"
    data-latitude="{{event.location_latitude}}"></div>

  <style>
    .location-popup h3 {
      margin-top: 0;
      margin-bottom: 8px;
      color: #2c3e50;
    }

    .location-popup p {
      margin: 5px 0;
    }

    .leaflet-popup-content {
      min-width: 200px;
    }
  </style>
</div>