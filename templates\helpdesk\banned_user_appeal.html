{% extends "base.html" %}

{% block title %}
Appeal Banned Account - Footprints
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <a href="{{ url_for('main.get_landing_page') }}"
        class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
        <i class="bi bi-arrow-left me-2"></i>
        <span>Back to Home</span>
      </a>

      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-white border-0 py-3 px-4">
          <h1 class="fs-3 fw-bold mb-0">
            Appeal Banned Account
          </h1>
        </div>

        <div class="card-body p-4">

          <!-- User Details -->
          <div class="row mb-4">
            <div class="col-md-6">
              <h6 class="text-uppercase text-muted small fw-bold">Username</h6>
              <p class="mb-3">{{ user.username }}</p>
            </div>
            <div class="col-md-6">
              <h6 class="text-uppercase text-muted small fw-bold">Account Status</h6>
              <p class="mb-3">
                <span class="badge bg-danger rounded-pill px-3 py-2">
                  <i class="bi bi-ban me-1"></i>Banned
                </span>
              </p>
            </div>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">What does this mean?</h6>
            <p class="mb-0">This account has been banned from the system. You cannot log in or access any features until
              this restriction is lifted. You can submit an appeal below to request that the ban be reviewed.</p>
          </div>

          <!-- Appeal Status Section -->
          {% if appeal_status %}
          <div
            class="alert {% if appeal_status.status == 'approved' %}alert-success{% elif appeal_status.status == 'rejected' %}alert-danger{% else %}alert-info{% endif %} mb-4">
            <div class="d-flex">
              <i
                class="bi {% if appeal_status.status == 'approved' %}bi-check-circle{% elif appeal_status.status == 'rejected' %}bi-x-circle{% else %}bi-clock{% endif %} fs-5 me-3"></i>
              <div class="flex-grow-1">
                <h6 class="fw-bold mb-2">
                  {% if appeal_status.status == 'approved' %}
                  Appeal Approved
                  {% elif appeal_status.status == 'rejected' %}
                  Appeal Rejected
                  {% elif appeal_status.status in ['new', 'open'] %}
                  Appeal Under Review
                  {% else %}
                  Appeal {{ appeal_status.status|title }}
                  {% endif %}
                </h6>
                <p class="mb-2 small">
                  {% if appeal_status.status == 'approved' %}
                  Your appeal has been approved and your account should be unbanned. <br> If you're still seeing this
                  page,
                  please contact support.
                  {% elif appeal_status.status == 'rejected' %}
                  Your appeal has been reviewed and rejected.
                  {% elif appeal_status.status in ['new', 'open'] %}
                  Your appeal is currently being reviewed by our content management team. <br> You will be notified once
                  a
                  decision is made.
                  {% endif %}
                </p>
                {% if appeal_status.admin_response %}
                <div class="mt-2">
                  <strong>Staff Response:</strong>
                  <p class="mb-0 mt-1">{{ appeal_status.admin_response }}</p>
                </div>
                {% endif %}
                <small class="text-muted">
                  Submitted: {{ appeal_status.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                  {% if appeal_status.resolved_at %}
                  | Resolved: {{ appeal_status.resolved_at.strftime('%B %d, %Y at %I:%M %p') }}
                  {% endif %}
                </small>
              </div>
            </div>
          </div>
          {% endif %}

          {% if appeal_status and appeal_status.status in ['new', 'open'] %}
          <!-- Pending Appeal - Show Status Only -->
          {% elif appeal_status and appeal_status.status == 'rejected' %}
          <!-- Rejected Appeal - Allow New Appeal -->
          <hr class="my-4">
          <h5 class="fw-bold mb-3">Submit New Appeal</h5>
          {% elif appeal_status and appeal_status.status == 'approved' %}
          <!-- Approved Appeal - Show Contact Support -->
          <div class="text-center py-4">
            <p class="text-warning mb-3">Your appeal was approved but your account is still banned. Please contact
              support for assistance.</p>
            <a href="{{ url_for('helpdesk.create_ticket') }}" class="btn btn-warning">
              <i class="bi bi-headset me-2"></i>Contact Support
            </a>
          </div>
          {% else %}
          {% endif %}

          {% if not appeal_status or appeal_status.status == 'rejected' %}
          <!-- Appeal Form -->
          <form method="post" class="needs-validation" novalidate>
            <div class="mb-4">
              <label for="reason" class="form-label fw-bold">
                Reason for Appeal <span class="text-danger">*</span>
              </label>
              <textarea class="form-control" id="reason" name="reason" rows="8" required minlength="20" maxlength="1000"
                placeholder="Please explain why you believe your account should be unbanned.">{{ request.form.get('reason', '') }}</textarea>
              <div class="invalid-feedback">
                Please provide a detailed reason for your appeal (minimum 20 characters).
              </div>
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>
                Please provide a clear and detailed explanation. Appeals are reviewed by content management staff.
              </div>
            </div>

            <div class="d-flex justify-content-center gap-3">
              <a href="{{ url_for('main.get_landing_page') }}"
                class="btn btn-outline-secondary rounded-pill px-4 py-2 fs-6 shadow-sm">
                Cancel
              </a>
              <button type="submit" class="btn btn-dark rounded-pill px-4 py-2 fs-6 shadow-sm">
                Submit
              </button>

            </div>
          </form>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Bootstrap form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
      form.addEventListener('submit', event => {
        if (!form.checkValidity()) {
          event.preventDefault();
          event.stopPropagation();

          // Focus the first invalid field
          const firstInvalidField = form.querySelector(':invalid');
          if (firstInvalidField) {
            firstInvalidField.focus();
          }
        }

        form.classList.add('was-validated');
      }, false);
    });
  });
</script>

{% endblock %}