{% extends "base.html" %}

{% block title %}
Appeal Blocked Status - Footprints
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <a href="javascript:void(0)" onclick="smartBack()" class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
        <i class="bi bi-arrow-left me-2"></i>
        <span>Back</span>
      </a>

      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-white border-0 py-3">
          <h1 class="fs-3 fw-bold mb-0">
            <i class="bi bi-flag me-2 text-warning"></i>Appeal Blocked Status
          </h1>
        </div>
        
        <div class="card-body p-4">

          <!-- User Details -->
          <div class="row mb-4">
            <div class="col-md-6">
              <h6 class="text-uppercase text-muted small fw-bold">Username</h6>
              <p class="mb-3">{{ user.username }}</p>
            </div>
            <div class="col-md-6">
              <h6 class="text-uppercase text-muted small fw-bold">Account Status</h6>
              <p class="mb-3">
                <span class="badge bg-warning text-dark">
                  <i class="bi bi-lock-fill me-1"></i>Blocked from Sharing
                </span>
              </p>
            </div>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">What does this mean?</h6>
            <p class="mb-0">Your account has been blocked from sharing journeys publicly. You can still use the system and manage your private journeys, but you cannot make them public or published until this restriction is lifted.</p>
          </div>

          <!-- Appeal Status Section -->
          {% if appeal_status %}
          <div class="alert {% if appeal_status.status == 'approved' %}alert-success{% elif appeal_status.status == 'rejected' %}alert-danger{% else %}alert-info{% endif %} mb-4">
            <div class="d-flex align-items-start">
              <i class="bi {% if appeal_status.status == 'approved' %}bi-check-circle{% elif appeal_status.status == 'rejected' %}bi-x-circle{% else %}bi-clock{% endif %} fs-5 me-3 mt-1"></i>
              <div class="flex-grow-1">
                <h6 class="fw-bold mb-2">
                  {% if appeal_status.status == 'approved' %}
                    Appeal Approved
                  {% elif appeal_status.status == 'rejected' %}
                    Appeal Rejected
                  {% elif appeal_status.status in ['new', 'open'] %}
                    Appeal Under Review
                  {% else %}
                    Appeal {{ appeal_status.status|title }}
                  {% endif %}
                </h6>
                <p class="mb-2 small">
                  {% if appeal_status.status == 'approved' %}
                    Your appeal has been approved and your sharing restrictions have been lifted.
                  {% elif appeal_status.status == 'rejected' %}
                    Your appeal has been reviewed and rejected.
                  {% elif appeal_status.status in ['new', 'open'] %}
                    Your appeal is currently being reviewed by our content management team. You will be notified once a decision is made.
                  {% endif %}
                </p>
                {% if appeal_status.admin_response %}
                <div class="mt-2">
                  <strong>Staff Response:</strong>
                  <p class="mb-0 mt-1">{{ appeal_status.admin_response }}</p>
                </div>
                {% endif %}
                <small class="text-muted">
                  Submitted: {{ appeal_status.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                  {% if appeal_status.resolved_at %}
                  | Resolved: {{ appeal_status.resolved_at.strftime('%B %d, %Y at %I:%M %p') }}
                  {% endif %}
                </small>
              </div>
            </div>
          </div>
          {% endif %}

          {% if appeal_status and appeal_status.status in ['new', 'open'] %}
          <!-- Pending Appeal - Show View Ticket Link -->
          <div class="text-center">
            <a href="{{ url_for('helpdesk.query_ticket', ticket_id=appeal_status.id) }}" class="btn btn-primary rounded-pill px-4">
              <i class="bi bi-ticket-detailed me-2"></i>View Appeal Ticket
            </a>
          </div>
          {% elif appeal_status and appeal_status.status == 'rejected' %}
          <!-- Rejected Appeal - Allow New Appeal -->
          <hr class="my-4">
          <h5 class="fw-bold mb-3">Submit New Appeal</h5>
          {% else %}
          <h5 class="fw-bold mb-3">Submit Appeal</h5>
          {% endif %}

          {% if not appeal_status or appeal_status.status == 'rejected' %}
          <!-- Appeal Form -->
          <form method="post" class="needs-validation" novalidate>
            <div class="mb-4">
              <label for="reason" class="form-label fw-bold">
                Reason for Appeal <span class="text-danger">*</span>
              </label>
              <textarea 
                class="form-control" 
                id="reason" 
                name="reason" 
                rows="6" 
                required 
                minlength="20" 
                maxlength="1000"
                placeholder="Please explain why you believe your account should not be blocked from sharing journeys."
              >{{ request.form.get('reason', '') }}</textarea>
              <div class="invalid-feedback">
                Please provide a detailed reason for your appeal (minimum 20 characters).
              </div>
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>
                Please provide a clear and detailed explanation. Appeals are reviewed by content management staff.
              </div>
            </div>

            <div class="d-flex gap-3">
              <button type="submit" class="btn btn-warning px-4">
                <i class="bi bi-flag me-2"></i>Submit Appeal
              </button>
              <a href="{{ url_for('main.get_user_dashboard') }}" class="btn btn-outline-secondary px-4">
                Cancel
              </a>
            </div>
          </form>
          {% elif appeal_status.status == 'approved' %}
          <!-- Approved Appeal Message -->
          <div class="text-center py-4">
            <p class="text-success mb-3">Your appeal has been approved! You can now share journeys again.</p>
            <a href="{{ url_for('main.get_user_dashboard') }}" class="btn btn-success">
              <i class="bi bi-house me-2"></i>Return to Dashboard
            </a>
          </div>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
</div>

<!-- Smart Back Button Script -->
<script>
  function smartBack() {
    // Method 1: Check for referrer within the same domain
    const referrer = document.referrer;
    const currentDomain = window.location.origin;
    
    if (referrer && referrer.startsWith(currentDomain)) {
      window.history.back();
      return;
    }

    // Method 2: Check for back URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get('back');
    if (backUrl) {
      window.location.href = backUrl;
      return;
    }

    // Method 3: Fallback based on context
    const isLoggedIn = {{ 'true' if g.current_user else 'false' }};

    if (isLoggedIn) {
      window.location.href = '{{ url_for("main.get_user_dashboard") }}';
    } else {
      window.location.href = '{{ url_for("main.get_landing_page") }}';
    }
  }
</script>

<!-- Bootstrap Form Validation -->
<script>
  (function() {
    'use strict';
    window.addEventListener('load', function() {
      var forms = document.getElementsByClassName('needs-validation');
      var validation = Array.prototype.filter.call(forms, function(form) {
        form.addEventListener('submit', function(event) {
          if (form.checkValidity() === false) {
            event.preventDefault();
            event.stopPropagation();
          }
          form.classList.add('was-validated');
        }, false);
      });
    }, false);
  })();
</script>
{% endblock %}
