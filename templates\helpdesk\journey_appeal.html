{% extends "base.html" %}

{% block title %}
Appeal Hidden Journey - Footprints
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <a href="javascript:void(0)" onclick="smartBack()" class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
        <i class="bi bi-arrow-left me-2"></i>
        <span>Back</span>
      </a>

      <div class="card shadow-sm border-0 rounded-3">
        <div class="card-header bg-white border-0 py-3">
          <h1 class="fs-3 fw-bold mb-0">
            <i class="bi bi-flag me-2 text-warning"></i>Appeal Hidden Journey
          </h1>
        </div>
        
        <div class="card-body p-4">

          <!-- Journey Details -->
          <div class="row mb-4">
            <div class="col-md-6">
              <h6 class="text-uppercase text-muted small fw-bold">Journey Title</h6>
              <p class="mb-3">{{ journey.title }}</p>
            </div>
            <div class="col-md-6">
              <h6 class="text-uppercase text-muted small fw-bold">Start Date</h6>
              <p class="mb-3">{{ journey.start_date.strftime('%B %d, %Y') }}</p>
            </div>
          </div>

          <div class="mb-4">
            <h6 class="text-uppercase text-muted small fw-bold">Description</h6>
            <p class="mb-0">{{ journey.description }}</p>
          </div>

          {% if appeal_status %}
          <!-- Existing Appeal Status -->
          <div class="alert {% if appeal_status.status == 'rejected' %}alert-danger{% else %}alert-info{% endif %} rounded-3 mb-4">
            <div class="d-flex align-items-start">
              <i class="bi {% if appeal_status.status == 'rejected' %}bi-x-circle{% else %}bi-clock{% endif %} fs-4 me-3 mt-1"></i>
              <div class="flex-grow-1">
                <h6 class="fw-bold mb-2">
                  {% if appeal_status.status == 'new' %}Current Appeal Status: Submitted
                  {% elif appeal_status.status == 'open' %}Current Appeal Status: Under Review
                  {% elif appeal_status.status == 'rejected' %}Previous Appeal: Rejected
                  {% else %}Appeal Status: {{ appeal_status.status|title }}
                  {% endif %}
                </h6>
                <p class="mb-2 small text-muted">Submitted on {{ appeal_status.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>

                {% if appeal_status.status in ['new', 'open'] %}
                <p class="mb-0">Your appeal is being reviewed by our content management team. You will be notified when a decision is made.</p>
                {% elif appeal_status.status == 'rejected' %}
                <p class="mb-2">Your previous appeal was reviewed and rejected.</p>
                {% if appeal_status.admin_response %}
                <div class="bg-light rounded p-3">
                  <h6 class="small fw-bold mb-1">Staff Response:</h6>
                  <p class="mb-0 small">{{ appeal_status.admin_response }}</p>
                </div>
                {% endif %}
                {% endif %}
              </div>
            </div>
          </div>

          {% if appeal_status.status in ['new', 'open'] %}
          <!-- Pending Appeal - Show View Ticket Link -->
          <div class="text-center">
            <a href="{{ url_for('helpdesk.query_ticket', ticket_id=appeal_status.id) }}" class="btn btn-primary rounded-pill px-4">
              <i class="bi bi-ticket-detailed me-2"></i>View Appeal Ticket
            </a>
          </div>
          {% elif appeal_status.status == 'rejected' %}
          <!-- Rejected Appeal - Allow New Appeal -->
          <hr class="my-4">
          <h5 class="fw-bold mb-3">Submit New Appeal</h5>
          <p class="text-muted mb-4">Your journey is currently hidden. You may submit a new appeal with additional information.</p>
          {% endif %}
          {% else %}
          <h5 class="fw-bold mb-3">Submit Appeal</h5>
          {% endif %}

          {% if not appeal_status or appeal_status.status == 'rejected' %}
          <!-- Appeal Form -->
          <form method="post" class="needs-validation" novalidate>
            <div class="mb-4">
              <label for="reason" class="form-label fw-bold">
                Reason for Appeal <span class="text-danger">*</span>
              </label>
              <textarea 
                class="form-control" 
                id="reason" 
                name="reason" 
                rows="6" 
                required 
                minlength="20" 
                maxlength="1000"
                placeholder="Please explain why you believe your journey should not be hidden."
              >{{ request.form.get('reason', '') }}</textarea>
              <div class="invalid-feedback">
                Please provide a detailed reason for your appeal (minimum 20 characters).
              </div>
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>
                Be specific and provide context. Appeals with detailed explanations are more likely to be approved.
              </div>
            </div>

            <div class="alert alert-info rounded-3 mb-4">
              <div class="d-flex align-items-start">
                <i class="bi bi-lightbulb fs-4 me-3 mt-1"></i>
                <div>
                  <h6 class="fw-bold mb-2">Appeal Guidelines</h6>
                  <ul class="mb-0 small">
                    <li>Explain why your content follows community guidelines</li>
                    <li>Provide context that may have been missed in the initial review</li>
                    <li>Be respectful and professional in your communication</li>
                    <li>Include any relevant information that supports your case</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="d-flex justify-content-between align-items-center">
              <a href="{{ url_for('journey.get_private_journey', journey_id=journey.id) }}" class="btn btn-outline-secondary rounded-pill px-4">
                <i class="bi bi-arrow-left me-2"></i>Back to Journey
              </a>
              <button type="submit" class="btn btn-warning rounded-pill px-4">
                <i class="bi bi-flag me-2"></i>Submit Appeal
              </button>
            </div>
          </form>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Bootstrap form validation
(function() {
  'use strict';
  window.addEventListener('load', function() {
    var forms = document.getElementsByClassName('needs-validation');
    var validation = Array.prototype.filter.call(forms, function(form) {
      form.addEventListener('submit', function(event) {
        if (form.checkValidity() === false) {
          event.preventDefault();
          event.stopPropagation();
        }
        form.classList.add('was-validated');
      }, false);
    });
  }, false);
})();

// Simple and reliable back button function
function smartBack() {
  // Method 1: Check for explicit back URL parameter (most reliable)
  const urlParams = new URLSearchParams(window.location.search);
  const backUrl = urlParams.get('back');

  if (backUrl) {
    try {
      const decodedUrl = decodeURIComponent(backUrl);
      // Only allow internal URLs for security
      if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
        window.location.href = decodedUrl;
        return;
      }
    } catch (e) {
      // Invalid URL, continue to next method
    }
  }

  // Method 2: Simple history back (works most of the time)
  if (window.history.length > 1) {
    window.history.back();
    return;
  }

  // Method 3: Fallback to private journeys
  window.location.href = "{{ url_for('journey.get_private_journeys') }}";
}
</script>
{% endblock %}
