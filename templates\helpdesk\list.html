{% extends "base.html" %} {% from "components/pagination.html" import
render_pagination %} {% block title %}Help desk - Footprints{% endblock %} {%
block content %}
<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="display-6 fw-bold">
      <span class="position-relative">
        {% if 'manage' in request.path %} Helpdesk Management {% else %} My
        Helpdesk Tickets {% endif %}
        <span class="position-absolute start-0 bottom-0" style="
            height: 6px;
            width: 60%;
            background-color: #4e6bff;
            opacity: 0.2;
            border-radius: 3px;
          "></span>
      </span>
    </h1>
    <button id="createTicketBtn" class="btn btn-dark rounded-pill px-3 py-2">
      <i class="bi bi-plus-lg me-2"></i>Add Ticket
    </button>
  </div>
  <!-- Tab Navigation for Staff Management Only -->
  {% if can_manage_helpdesk() and 'manage' in request.path %}
  <div class="mb-4">
    <div class="border-bottom position-relative">
      <div class="d-flex">
        <div class="me-4 position-relative">
          <a href="{{ url_for('helpdesk.get_tickets', tab='all', search=request.args.get('search', ''), category=request.args.get('category', '')) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if current_tab == 'all' %}text-primary{% else %}text-secondary{% endif %}">
            <span class="d-inline d-sm-none">All</span><span class="d-none d-sm-inline">All Tickets</span>
          </a>
          {% if current_tab == 'all' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1"></div>
          {% endif %}
        </div>
        <div class="me-4 position-relative">
          <a href="{{ url_for('helpdesk.get_tickets', tab='new', search=request.args.get('search', ''), category=request.args.get('category', '')) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if current_tab == 'new' %}text-primary{% else %}text-secondary{% endif %}">
            <span class="d-inline d-sm-none">New</span><span class="d-none d-sm-inline">New Tickets</span>
          </a>
          {% if current_tab == 'new' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1"></div>
          {% endif %}
        </div>
        <div class="me-4 position-relative">
          <a href="{{ url_for('helpdesk.get_tickets', tab='active', search=request.args.get('search', ''), category=request.args.get('category', '')) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if current_tab == 'active' %}text-primary{% else %}text-secondary{% endif %}">
            <span class="d-inline d-sm-none">Active</span><span class="d-none d-sm-inline">Active Tickets</span>
          </a>
          {% if current_tab == 'active' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1"></div>
          {% endif %}
        </div>
        <div class="me-4 position-relative">
          <a href="{{ url_for('helpdesk.get_tickets', tab='assigned_to_me', search=request.args.get('search', ''), category=request.args.get('category', '')) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if current_tab == 'assigned_to_me' %}text-primary{% else %}text-secondary{% endif %}">
            <span class="d-inline d-sm-none">Me</span><span class="d-none d-sm-inline">Assigned to Me</span>
          </a>
          {% if current_tab == 'assigned_to_me' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1"></div>
          {% endif %}
        </div>
        <div class="position-relative">
          <a href="{{ url_for('helpdesk.get_tickets', tab='past', search=request.args.get('search', ''), category=request.args.get('category', '')) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if current_tab == 'past' %}text-primary{% else %}text-secondary{% endif %}">
            <span class="d-inline d-sm-none">Past</span><span class="d-none d-sm-inline">Past Tickets</span>
          </a>
          {% if current_tab == 'past' %}
          <div class="position-absolute bottom-0 start-0 w-100" style="height: 3px; background-color: #6366f1"></div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold mb-0">Total ({{ total_count|default(0) }})</h5>
    <div class="d-flex gap-2 align-items-center">
      <form
        action="{% if 'manage' in request.path %}{{ url_for('helpdesk.get_tickets') }}{% else %}{{ url_for('helpdesk.get_user_tickets') }}{% endif %}"
        method="get" class="d-flex gap-2 align-items-center">
        {% if can_manage_helpdesk() and current_tab %}
        <input type="hidden" name="tab" value="{{ current_tab }}" />
        {% endif %}
        <div class="dropdown">
          <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button"
            data-bs-toggle="dropdown" aria-expanded="false">
            {% if request.args.get('category') %} {% set current_category =
            request.args.get('category') %} {% if current_category == 'help' %}
            <i class="bi bi-question-circle me-2"></i>
            <span class="d-none d-sm-inline">Help</span>
            {% elif current_category == 'bug' %}
            <i class="bi bi-bug-fill me-2"></i>
            <span class="d-none d-sm-inline">Bug</span>
            {% elif current_category == 'appeal' %}
            <i class="bi bi-megaphone-fill me-2"></i>
            <span class="d-none d-sm-inline">Appeal</span>
            {% elif current_category == 'other' %}
            <i class="bi bi-three-dots me-2"></i>
            <span class="d-none d-sm-inline">Other</span>
            {% endif %} {% else %}
            <i class="bi bi-funnel me-2"></i>
            <span class="d-none d-sm-inline">Filter</span>
            {% endif %}
          </button>
          <ul class="dropdown-menu">
            <li>
              <a class="dropdown-item {% if not request.args.get('category') %}active{% endif %}"
                href="javascript:void(0)" onclick="filterByCategory('')">
                <i class="bi bi-list me-2"></i>All Categories
              </a>
            </li>
            <li>
              <hr class="dropdown-divider" />
            </li>
            <li>
              <a class="dropdown-item {% if request.args.get('category') == 'help' %}active{% endif %}"
                href="javascript:void(0)" onclick="filterByCategory('help')">
                <i class="bi bi-question-circle me-2"></i>Help
              </a>
            </li>
            <li>
              <a class="dropdown-item {% if request.args.get('category') == 'bug' %}active{% endif %}"
                href="javascript:void(0)" onclick="filterByCategory('bug')">
                <i class="bi bi-bug-fill me-2"></i>Bug
              </a>
            </li>
            <li>
              <a class="dropdown-item {% if request.args.get('category') == 'appeal' %}active{% endif %}"
                href="javascript:void(0)" onclick="filterByCategory('appeal')">
                <i class="bi bi-megaphone-fill me-2"></i>Appeal
              </a>
            </li>
            <li>
              <a class="dropdown-item {% if request.args.get('category') == 'other' %}active{% endif %}"
                href="javascript:void(0)" onclick="filterByCategory('other')">
                <i class="bi bi-three-dots me-2"></i>Other
              </a>
            </li>
          </ul>
        </div>
        <input type="hidden" name="category" id="categoryFilter" value="{{ request.args.get('category', '') }}" />
        <div class="input-group">
          <input type="text" class="form-control" name="search" placeholder="Search tickets..."
            value="{{ request.args.get('search', '') }}" />
          {% if 'manage' not in request.path %}
          <a href="{{ url_for('helpdesk.get_user_tickets') }}"
            class="btn btn-outline-secondary search-clear-btn {% if not request.args.get('search') and not request.args.get('category') %}d-none{% endif %}">
            <i class="bi bi-x"></i>
          </a>
          {% elif can_manage_helpdesk() %}
          <a href="{{ url_for('helpdesk.get_tickets', tab=current_tab) }}"
            class="btn btn-outline-secondary search-clear-btn {% if not request.args.get('search') and not request.args.get('category') %}d-none{% endif %}">
            <i class="bi bi-x"></i>
          </a>
          {% else %}
          <a href="{{ url_for('helpdesk.get_tickets') }}"
            class="btn btn-outline-secondary search-clear-btn {% if not request.args.get('search') and not request.args.get('category') %}d-none{% endif %}">
            <i class="bi bi-x"></i>
          </a>
          {% endif %}
        </div>
        <button type="submit" class="btn" style="background-color: black; color: white; border: none">
          Search
        </button>
      </form>
    </div>
  </div>

  {% if tickets %}
  <div class="table-responsive">
    <table class="table align-middle table-hover">
      <thead>
        <tr>
          <th>No.</th>
          {% if can_manage_helpdesk() %}
          <th class="d-none d-sm-table-cell">Tag</th>
          {% endif %}
          <th class="d-none d-sm-table-cell">Category</th>
          <th>User</th>
          <th style="width: 250px;">Title</th>
          {% if can_manage_helpdesk() %}
          <th class="d-none d-sm-table-cell">Created</th>
          <th class="d-none d-sm-table-cell">Comments</th>
          <th class="d-none d-sm-table-cell">Status</th>
          <th class="d-none d-sm-table-cell">Assigned To</th>
          {% else %}
          <th class="d-none d-sm-table-cell">Created</th>
          <th class="d-none d-sm-table-cell">Comments</th>
          <th class="d-none d-sm-table-cell">Status</th>
          {% endif %}
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {% for ticket in tickets %}
        <tr>
          <td>{{ (page - 1) * 10 + loop.index }}</td>
          {% if can_manage_helpdesk() %}
          <td class="d-none d-sm-table-cell">
            {% if ticket.is_admin %}
            <span class="badge rounded-pill bg-primary-subtle text-primary border-0 px-3 py-2" style="font-size: 0.8em; min-width: 70px; text-align: center;">
              <i class="bi bi-shield-fill me-1"></i>Staff
            </span>
            {% elif ticket.is_member %}
            <span class="badge rounded-pill bg-warning-subtle text-warning border-0 px-3 py-2" style="font-size: 0.8em; min-width: 70px; text-align: center;">
              <i class="bi bi-star-fill me-1"></i>Priority
            </span>
            {% else %}
            <span class="badge rounded-pill bg-secondary-subtle text-secondary border-0 px-3 py-2" style="font-size: 0.8em; min-width: 70px; text-align: center;">-
            </span>
            {% endif %}
          </td>
          {% endif %}
          <td class="d-none d-sm-table-cell">
            {% if ticket.request_type == 'help' %}
            <span class="badge rounded-pill bg-info-subtle text-info p-2" style="min-width: 90px; text-align: center">
              <i class="bi bi-question-circle me-1"></i>Help
            </span>
            {% elif ticket.request_type == 'bug' %}
            <span class="badge rounded-pill bg-danger-subtle text-danger p-2"
              style="min-width: 90px; text-align: center">
              <i class="bi bi-bug-fill me-1"></i>Bug
            </span>
            {% elif ticket.request_type == 'appeal' %}
            <span class="badge rounded-pill bg-warning-subtle text-warning p-2"
              style="min-width: 90px; text-align: center">
              <i class="bi bi-megaphone-fill me-1"></i>Appeal
            </span>
            {% elif ticket.request_type == 'other' %}
            <span class="badge rounded-pill bg-light text-dark border p-2" style="min-width: 90px; text-align: center">
              <i class="bi bi-three-dots me-1"></i>Other
            </span>
            {% endif %}
          </td>
          <td>
            {% if ticket.user_id == -1 %}
            <span class="badge bg-dark-subtle text-dark p-2" style="min-width: 90px; text-align: center">
              <i class="bi bi-person-fill-slash me-1"></i>Guest
            </span>
            {% else %}
            <span>{{ ticket.username }}</span>
            {% endif %}
          </td>
          <td style="width: 250px;">
            <span class="text-truncate d-block" style="max-width: 180px" title="{{ ticket.subject }}">{{ ticket.subject
              }}</span>
          </td>
          {% if can_manage_helpdesk() %}
          <td class="d-none d-sm-table-cell">
            {{ ticket.created_at|formatdate }}
          </td>
          <td class="d-none d-sm-table-cell">
            <span class="badge rounded-pill bg-light text-dark px-3 py-2">{{ticket.reply_count}}</span>
          </td>
          <td class="d-none d-sm-table-cell">
            <span class="badge rounded-pill px-3 py-2 {% if ticket.status == 'new' %}bg-primary-subtle text-primary{% elif ticket.status == 'open' %}bg-success-subtle text-success{% elif ticket.status == 'stalled' %}bg-warning-subtle text-warning{% elif ticket.status == 'resolved' %}bg-info-subtle text-info{% elif ticket.status == 'approved' %}bg-success-subtle text-success{% elif ticket.status == 'rejected' %}bg-secondary-subtle text-secondary{% endif %}" style="min-width: 90px; text-align: center">
              {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>New{% elif ticket.status == 'open' %}<i class="bi bi-unlock me-1"></i>Open{% elif ticket.status == 'stalled' %}<i class="bi bi-pause me-1"></i>Stalled{% elif ticket.status == 'resolved' %}<i class="bi bi-check2-circle me-1"></i>Resolved{% elif ticket.status == 'approved' %}<i class="bi bi-check-lg me-1"></i>Approved{% elif ticket.status == 'rejected' %}<i class="bi bi-x-circle me-1"></i>Rejected{% endif %}
            </span>
          </td>
          <td class="d-none d-sm-table-cell" style="max-width: 150px; min-width: 100px">
            {% if ticket.assigned_to %} {% if ticket.assigned_to ==
            session.user_id %}
            <!-- Assigned to current user - highlight in dark blue for distinction -->
            <span class="badge rounded-pill bg-primary text-white px-3 py-2" style="min-width: 80px; text-align: center">
              <i class="bi bi-person-check-fill me-1"></i>{{
              ticket.assigned_username or 'You' }}
            </span>
            {% else %}
            <!-- Assigned to other staff - use actual role from database -->
            {% set assigned_user_role = ticket.assigned_user_role %}
            <span class="badge rounded-pill {{ get_role_badge_class(assigned_user_role) }} border-0 px-3 py-2"
              style="min-width: 80px; text-align: center">
              <i class="{{ get_role_icon(assigned_user_role) }} me-1"></i>{{
              ticket.assigned_username or 'Unknown' }}
            </span>
            {% endif %} {% else %}
            <!-- Unassigned - highlight in red to show urgency -->
            <span class="badge bg-danger-subtle text-danger px-3 py-2 rounded-pill"
              style="min-width: 80px; text-align: center">
              <i class="bi bi-person-exclamation me-1"></i>Unassigned
            </span>
            {% endif %}
          </td>
          {% else %}
          <td class="d-none d-sm-table-cell">
            {{ ticket.created_at|formatdate }}
          </td>
          <td class="d-none d-sm-table-cell">
            <span class="badge rounded-pill bg-light text-dark px-3 py-2">{{ticket.reply_count}}</span>
          </td>
          <td class="d-none d-sm-table-cell">
            <span class="badge rounded-pill px-3 py-2 {% if ticket.status == 'new' %}bg-primary-subtle text-primary{% elif ticket.status == 'open' %}bg-success-subtle text-success{% elif ticket.status == 'stalled' %}bg-warning-subtle text-warning{% elif ticket.status == 'resolved' %}bg-info-subtle text-info{% elif ticket.status == 'approved' %}bg-success-subtle text-success{% elif ticket.status == 'rejected' %}bg-secondary-subtle text-secondary{% endif %}" style="min-width: 90px; text-align: center">
              {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>New{% elif ticket.status == 'open' %}<i class="bi bi-unlock me-1"></i>Open{% elif ticket.status == 'stalled' %}<i class="bi bi-pause me-1"></i>Stalled{% elif ticket.status == 'resolved' %}<i class="bi bi-check2-circle me-1"></i>Resolved{% elif ticket.status == 'approved' %}<i class="bi bi-check-lg me-1"></i>Approved{% elif ticket.status == 'rejected' %}<i class="bi bi-x-circle me-1"></i>Rejected{% endif %}
            </span>
          </td>
          {% endif %}
          <td>
            {% if 'manage' in request.path %}
            <a href="{{ url_for('helpdesk.query_ticket', ticket_id=ticket.id, back=url_for('helpdesk.get_tickets')) }}"
              class="btn btn-outline-dark btn-sm">View</a>
            {% else %}
            <a href="{{ url_for('helpdesk.query_ticket', ticket_id=ticket.id, back=url_for('helpdesk.get_user_tickets')) }}"
              class="btn btn-outline-dark btn-sm">View</a>
            {% endif %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <!-- Pagination -->
  {% if can_manage_helpdesk() %} {% set extra_params = { 'tab': current_tab,
  'category': request.args.get('category', '') } %} {{ render_pagination(page,
  total_pages, 'helpdesk.get_tickets', extra_params=extra_params,
  q=request.args.get('search', ''), filter=request.args.get('filter')) }} {%
  else %} {% set extra_params = { 'category': request.args.get('category', '') }
  %} {{ render_pagination(page, total_pages, 'helpdesk.get_tickets',
  extra_params=extra_params, q=request.args.get('search', ''),
  filter=request.args.get('filter')) }} {% endif %} {% else %}
  <div class="alert rounded-4" style="
      background-color: rgba(78, 107, 255, 0.1);
      color: #4e6bff;
      border: 1px solid rgba(78, 107, 255, 0.2);
    ">
    {% if request.args.get('search') or request.args.get('category') %}
    <p class="mb-0">
      <i class="bi bi-info-circle me-2"></i>No matching tickets found.
    </p>
    {% else %} {% if 'manage' in request.path %} {% if current_tab == 'all' %}
    <p class="mb-0">
      <i class="bi bi-inbox me-2"></i>No tickets in the system yet.
    </p>
    {% elif current_tab == 'new' %}
    <p class="mb-0">
      <i class="bi bi-check-circle me-2"></i>No new tickets waiting for
      assignment.
    </p>
    {% elif current_tab == 'active' %}
    <p class="mb-0">
      <i class="bi bi-check-circle me-2"></i>No active tickets at the moment.
    </p>
    {% elif current_tab == 'assigned_to_me' %}
    <p class="mb-0">
      <i class="bi bi-person-check me-2"></i>No tickets assigned to you
      currently.
    </p>
    {% elif current_tab == 'past' %}
    <p class="mb-0">
      <i class="bi bi-archive me-2"></i>No completed tickets found.
    </p>
    {% else %}
    <p class="mb-0"><i class="bi bi-inbox me-2"></i>No tickets found.</p>
    {% endif %} {% else %}
    <p class="mb-0">
      <i class="bi bi-journal-plus me-2"></i>You haven't created any tickets
      yet.
    </p>
    {% endif %} {% endif %}
  </div>
  {% endif %}
</div>
<script>
  // shows modal for creating new ticket
  document
    .getElementById("createTicketBtn")
    .addEventListener("click", async function (event) {
      event.preventDefault();
      const response = await fetch("{{ url_for('helpdesk.get_ticket_form')}}");
      const formHtml = await response.text();

      showModal("Add New Ticket", formHtml, {
        actionText: "Create",

        onShow: function () {
          const categorySelect = document.getElementById("category");
          const usernameField = document.getElementById("usernameField");
          const usernameInput = document.getElementById("username");
          const appealHelpText = document.getElementById("appealHelpText");
          function toggleUsernameField() {
            if (categorySelect.value === "appeal") {
              usernameField.style.display = "block";
              usernameInput.required = true;
              appealHelpText.style.display = "block";
            } else {
              usernameField.style.display = "none";
              usernameInput.required = false;
              appealHelpText.style.display = "none";
            }
          }

          toggleUsernameField();

          categorySelect.addEventListener("change", toggleUsernameField);
        },

        onAction: function () {
          const form = document.getElementById("create-ticket-form");
          form.classList.add("was-validated");

          if (!form.checkValidity()) {
            // Focus the first invalid field instead of showing browser popup
            const firstInvalidField = form.querySelector(":invalid");
            if (firstInvalidField) {
              firstInvalidField.focus();
            }
            return false; // Prevent modal from closing
          }

          form.submit();
          return true; // Allow modal to close
        },
      });
    });

  // Handle category filtering
  function filterByCategory(category) {
    const categoryInput = document.getElementById("categoryFilter");
    categoryInput.value = category;

    // Submit the form to apply the filter
    const form = categoryInput.closest("form");
    form.submit();
  }

  // Update clear button visibility when filters change
  function updateClearButtonVisibility() {
    const searchValue = document.querySelector('input[name="search"]').value;
    const categoryValue = document.getElementById("categoryFilter").value;
    const clearBtn = document.querySelector(".search-clear-btn");

    if (searchValue || categoryValue) {
      clearBtn.classList.remove("d-none");
    } else {
      clearBtn.classList.add("d-none");
    }
  }

  // Initialize clear button visibility on page load
  document.addEventListener("DOMContentLoaded", function () {
    updateClearButtonVisibility();
  });
</script>

<style>
  .text-truncate {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .dropdown-menu {
    background-color: #fff !important;
    border: 1px solid #ccc !important;
    text-align: left !important;
  }

  .dropdown-item {
    text-align: left !important;
  }

  /* Title column for management view (5th column) */
  body:has(.table th:nth-child(9)) .table th:nth-child(5),
  body:has(.table th:nth-child(9)) .table td:nth-child(5) {
    width: 250px;
    min-width: 180px;
    max-width: 400px;
  }

  /* Title column for normal user view (4th column) - Give more space */
  body:not(:has(.table th:nth-child(9))) .table th:nth-child(4),
  body:not(:has(.table th:nth-child(9))) .table td:nth-child(4) {
    width: 350px;
    min-width: 260px;
    max-width: 480px;
  }

  /* Created column for normal user view (5th column) - Make narrower */
  body:not(:has(.table th:nth-child(9))) .table th:nth-child(5),
  body:not(:has(.table th:nth-child(9))) .table td:nth-child(5) {
    width: 110px;
    min-width: 90px;
    max-width: 130px;
  }

  @media (max-width: 991.98px) {

    .table th:nth-child(2),
    .table td:nth-child(2),
    .table th:nth-child(3),
    .table td:nth-child(3),
    .table th:nth-child(7),
    .table td:nth-child(7) {
      display: none !important;
    }

    /* Title column adjustments for tablets - Normal user view */
    body:not(:has(.table th:nth-child(9))) .table th:nth-child(4),
    body:not(:has(.table th:nth-child(9))) .table td:nth-child(4) {
      width: 160px;
      min-width: 120px;
      max-width: 200px;
    }

    /* Created column adjustments for tablets - Normal user view */
    body:not(:has(.table th:nth-child(9))) .table th:nth-child(5),
    body:not(:has(.table th:nth-child(9))) .table td:nth-child(5) {
      width: 80px;
      min-width: 70px;
      max-width: 100px;
    }

    /* Management view title column adjustments for tablets */
    body:has(.table th:nth-child(9)) .table th:nth-child(5),
    body:has(.table th:nth-child(9)) .table td:nth-child(5) {
      width: 90px;
      min-width: 60px;
      max-width: 120px;
    }
  }

  @media (max-width: 767.98px) {

    /* Title column adjustments for mobile - Normal user view */
    body:not(:has(.table th:nth-child(9))) .table th:nth-child(4),
    body:not(:has(.table th:nth-child(9))) .table td:nth-child(4) {
      width: 120px;
      min-width: 100px;
      max-width: 150px;
    }

    /* Created column adjustments for mobile - Normal user view */
    body:not(:has(.table th:nth-child(9))) .table th:nth-child(5),
    body:not(:has(.table th:nth-child(9))) .table td:nth-child(5) {
      width: 70px;
      min-width: 60px;
      max-width: 80px;
    }

    /* Management view title column adjustments for mobile */
    body:has(.table th:nth-child(9)) .table th:nth-child(5),
    body:has(.table th:nth-child(9)) .table td:nth-child(5) {
      width: 60px;
      min-width: 50px;
      max-width: 90px;
    }
  }
</style>
{% endblock %}