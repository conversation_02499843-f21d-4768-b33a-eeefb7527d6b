{% extends "base.html" %}

{% block head %}
<style>
    .text-primary,
    .bi-camera,
    .bi-collection,
    .bi-share {
        color: var(--primary-color) !important;
    }

    .btn-primary {
        background-color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
    }

    .btn-primary:hover {
        background-color: var(--primary-darker) !important;
        border-color: var(--primary-darker) !important;
    }

    /* Animation for content fade-in */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-up {
        animation: fadeInUp 0.8s ease-out forwards;
        opacity: 0;
    }

    /* Full screen hero section with navbar adjustment */
    .hero-section {
        position: relative;
        height: calc(100vh - 80px);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        margin-top: -15px;
    }

    .hero-container {
        width: 100%;
        max-width: 1200px;
        text-align: center;
        padding: 0 2rem;
        z-index: 2;
        opacity: 0;
        animation: fadeInUp 1s ease-out forwards;
    }

    /* Container for the typing effect */
    .typing-container {
        display: inline-block;
        margin: 0 auto;
        overflow: hidden;
    }

    .typing-effect {
        border-right: 3px solid #000;
        white-space: nowrap;
        overflow: hidden;
        margin: 0 auto;
        width: 0;
        animation: typing 1.5s steps(40, end) forwards, blink-caret 0.75s step-end infinite;
        display: inline-block;
    }

    /* Typing animation */
    @keyframes typing {
        from {
            width: 0
        }

        to {
            width: 100%
        }
    }

    /* Blinking cursor animation */
    @keyframes blink-caret {

        from,
        to {
            border-color: transparent
        }

        50% {
            border-color: #000
        }
    }

    .typing-effect-p {
        opacity: 0;
        animation: appear 1s ease forwards;
        animation-delay: 0s;
        max-width: 700px;
        margin: 0 auto;
    }

    .typing-buttons {
        opacity: 0;
        animation: appear 1s ease forwards;
        animation-delay: 1.5s;
    }

    @keyframes appear {
        from {
            opacity: 0
        }

        to {
            opacity: 1
        }
    }

    /* Footprint animation */
    .footprints-container {
        position: relative;
        width: 100%;
        margin: 40px auto;
    }

    .footprint {
        position: absolute;
        width: 40px;
        height: 80px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        opacity: 0;
        background-image: url("/static/images/footprint.png");
    }

    /* Footprint positioning - with wider spacing */
    .footprint-1 {
        left: calc(50% - 40px);
        top: 10px;
        transform: rotate(-15deg);
    }

    .footprint-2 {
        left: calc(50% + 10px);
        top: 50px;
        transform: rotate(15deg);
    }

    /* Updated footprint animation to come from bottom */
    @keyframes footprintAppear {
        0% {
            opacity: 0;
            transform: translateY(30px) scale(0) rotate(var(--rotation));
        }

        20% {
            opacity: 1;
            transform: translateY(15px) scale(1.2) rotate(var(--rotation));
        }

        40% {
            transform: translateY(0) scale(1) rotate(var(--rotation));
        }

        100% {
            opacity: 0.8;
            transform: translateY(0) scale(1) rotate(var(--rotation));
        }
    }

    .footprint-1 {
        animation: footprintAppear 1s forwards;
        animation-delay: 2s;
        --rotation: -15deg;
    }

    .footprint-2 {
        animation: footprintAppear 1s forwards;
        animation-delay: 2.2s;
        --rotation: 15deg;
    }

    /* Testimonials slider */
    .testimonials-wrapper {
        width: 100%;
        overflow: hidden;
        position: relative;
        margin-bottom: 3rem;
        margin-top: 3rem;
    }

    .testimonials-ticker {
        display: flex;
        animation: scrollLeft 30s linear infinite;
        width: fit-content;
    }

    /* Pause animation on hover */
    .testimonials-wrapper:hover .testimonials-ticker {
        animation-play-state: paused;
    }

    .testimonial-card {
        width: 350px;
        flex-shrink: 0;
        margin-right: 2rem;
        transition: transform 0.3s ease;
    }

    .testimonial-card:hover {
        transform: translateY(-5px);
    }

    @keyframes scrollLeft {
        0% {
            transform: translateX(0);
        }

        100% {
            transform: translateX(-100%);
        }
    }

    /* Keep blinking cursor after animation completes */
    .animation-completed {
        width: 100% !important;
        opacity: 1 !important;
        animation: none !important;
        border-right: 3px solid #000;
        animation: blink-caret 0.75s step-end infinite !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .typing-container {
            display: block;
            width: 100%;
        }

        .typing-effect {
            display: inline-block;
            border-right: 3px solid #000;
            white-space: nowrap;
            overflow: hidden;
            margin: 0 auto;
            width: 0;
            animation: typing 1.5s steps(40, end) forwards, blink-caret 0.75s step-end infinite;
        }

        /* Speed up the paragraph and button animations for mobile */
        .typing-effect-p {
            animation-delay: 1s;
        }

        .typing-buttons {
            animation-delay: 2s;
        }

        /* Speed up footprint animations for mobile */
        .footprint-1 {
            animation-delay: 2.2s;
        }

        .footprint-2 {
            animation-delay: 2.5s;
        }

        /* Adjust footprint size for mobile */
        .footprint-1,
        .footprint-2 {
            height: 70px;
            width: 35px;
        }

        /* Adjust footprint positioning for mobile */
        .footprint-1 {
            top: calc(50% - 35px);
            left: calc(50% - 35px);
        }

        .footprint-2 {
            top: calc(50%);
            left: calc(50% + 5px);
        }
    }

    @media (max-width: 576px) {
        .hero-title {
            font-size: 2rem;
        }

        .hero-container {
            padding: 0 1rem;
        }

        .typing-buttons {
            flex-direction: column;
            gap: 0.5rem !important;
        }

        .typing-buttons .btn {
            width: 100%;
        }
    }

    @media (max-width: 600px) {
        .journey-cta-text {
            text-align: center !important;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .journey-cta-text h2,
        .journey-cta-text p {
            text-align: center !important;
        }

        .journey-cta-text a {
            margin-left: auto;
            margin-right: auto;
            display: inline-block;
        }
    }

    #chatbot-fab {
        position: fixed;
        right: 32px;
        bottom: 32px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #7b2ff2 0%, #4e6bff 100%);
        color: #fff;
        box-shadow: 0 6px 24px rgba(123, 47, 242, 0.18), 0 1.5px 6px rgba(0, 0, 0, 0.10);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        cursor: pointer;
        border: none;
        outline: none;
        transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
    }

    #chatbot-fab:hover {
        background: linear-gradient(135deg, #4e6bff 0%, #7b2ff2 100%);
        box-shadow: 0 10px 32px rgba(123, 47, 242, 0.32), 0 2px 8px rgba(0, 0, 0, 0.13);
        transform: translateY(-2px) scale(1.06);
    }

    #chatbot-fab i {
        color: #fff !important;
        font-size: 1.7rem;
    }

    #chatbot-window {
        position: fixed;
        right: 40px;
        bottom: 100px;
        width: 290px;
        max-width: 95vw;
        height: 450px;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border: 1px solid #000;
        z-index: 10000;
        display: none;
        flex-direction: column;
        overflow: hidden;
        animation: chatbot-pop 0.2s cubic-bezier(.4, 1.4, .6, 1) 1;
    }

    @keyframes chatbot-pop {
        0% {
            opacity: 0;
            transform: translateY(20px) scale(0.96);
        }

        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    #chatbot-header {
        background: #fff;
        color: #000;
        padding: 1rem 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #000;
        flex-shrink: 0;
    }

    #chatbot-close {
        background: none;
        border: none;
        color: #000;
        font-size: 1.4rem;
        cursor: pointer;
        opacity: 0.9;
        transition: opacity 0.15s;
        padding: 4px;
        line-height: 1;
    }

    #chatbot-close:hover {
        opacity: 0.7;
    }

    #chatbot-body {
        padding: 1.2rem;
        background: #f8f9fa;
        flex: 1;
        font-size: 0.95rem;
        color: #333;
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
        overflow-y: auto;
        min-height: 0;
    }

    #chatbot-messages {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
        overflow-y: auto;
    }

    .chatbot-message {
        max-width: 85%;
        padding: 0.8em 1.1em;
        border-radius: 12px;
        font-size: 0.95rem;
        line-height: 1.4;
        word-break: break-word;
    }

    .chatbot-message.bot {
        background: #fff;
        color: #333;
        align-self: flex-start;
        border: 1px solid #000;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
    }

    .chatbot-message.user {
        background: #000;
        color: #fff;
        align-self: flex-end;
    }

    #chatbot-input {
        flex: 1 1 auto;
        border-radius: 20px;
        border: 1px solid #e3eaff;
        padding: 0.7rem 1rem;
        font-size: 0.95rem;
        outline: none;
        transition: all 0.2s;
        background: #fff;
        margin: 0;
    }

    #chatbot-input:focus {
        border-color: #000;
        background: #fff;
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    }

    #chatbot-send {
        background: #000;
        color: #fff;
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        cursor: pointer;
        transition: all 0.2s;
        padding: 0;
        margin: 0;
    }

    #chatbot-send:hover {
        background: #333;
        transform: scale(1.05);
    }

    /* Custom scrollbar for chatbot messages */
    #chatbot-body::-webkit-scrollbar {
        width: 6px;
    }

    #chatbot-body::-webkit-scrollbar-track {
        background: transparent;
    }

    #chatbot-body::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }

    #chatbot-body::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
    }

    /* Destination card hover effects */
    .destination-card {
        transition: box-shadow 0.2s, transform 0.2s;
    }

    .destination-card:hover {
        box-shadow: 0 8px 32px rgba(78, 107, 255, 0.13) !important;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="container-fluid p-0">
    <div class="hero-section">
        <div class="hero-container">
            <h1 class="display-4 fw-bold mb-4 hero-title">
                <span class="typing-container">
                    <span class="typing-effect" id="typingEffect">Your Travel Journey Starts Here</span>
                </span>
            </h1>
            <p class="mb-4 typing-effect-p lead">Document your adventures, share your experiences, and connect with
                travelers worldwide through our digital travel journal platform.</p>
            <div class="d-flex justify-content-center gap-3 typing-buttons mb-3">
                <a href="{{ url_for('auth.login') }}" class="btn btn-primary rounded-pill px-4 py-2">
                    Login
                </a>
                <a href="{{ url_for('auth.register') }}" class="btn btn-outline-dark rounded-pill px-4 py-2">Sign Up</a>
            </div>

            <!-- Footprints Animation (Only 2) -->
            <div class="footprints-container">
                <div class="footprint footprint-1"></div>
                <div class="footprint footprint-2"></div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Destinations Section -->
<div class="container mb-5">
    <div class="row justify-content-center mb-3">
        <div class="col-12 text-center">
            <a href="{{ url_for('main.get_published_journey') }}" class="fw-semibold text-dark text-decoration-none">
                <h5 class="fw-bold mb-5">All Published Journeys &rarr;</h5>
            </a>
        </div>
    </div>
    <div class="row justify-content-center g-2">
        {% for journey in journeys[:6] %}
        <div class="col-md-4 col-sm-6 col-12 mb-2">
            <a href="{{ url_for('journey.get_published_journey_detail', journey_id=journey.id) }}"
                class="rounded-3 position-relative overflow-hidden destination-card d-block p-0"
                style="height: 180px; text-decoration: none;">
                <img src="{% if journey.cover_image %}{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}{% else %}{{ url_for('static', filename='images/main_image' ~ ((loop.index % 6) + 1) ~ '.jpg') }}{% endif %}"
                    alt="{{ journey.title }}" style="width: 100%; height: 180px; object-fit: cover; display: block;"
                    data-fallback="{{ url_for('static', filename='images/main_image' ~ ((loop.index % 6) + 1) ~ '.jpg') }}"
                    onload="this.style.opacity='1'" onerror="handleImageError(this)">
                <div class="position-absolute bottom-0 start-0 w-100 d-flex align-items-center justify-content-center"
                    style="height: 100%;">
                    <h5 class="text-white fw-bold text-center"
                        style="width: 100%; text-shadow: 0 2px 8px rgba(0,0,0,0.25);">
                        {{ journey.title }}</h5>
                </div>
            </a>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Testimonials Section -->
<div class="row mb-5">
    <div class="col-12 text-center">
        <h2 class="fw-bold mt-5 mb-5">What Our Travelers Say</h2>
    </div>

    <div class="col-12">
        <div class="testimonials-wrapper">
            <div class="testimonials-ticker" id="testimonialsTicker">
                {% set testimonials = [
                {'name': 'Vasili Ilmaz', 'stars': 5, 'text': 'The journey planning features are incredible! I\'ve
                been able to document my travels across Europe with such detail and ease.'},
                {'name': 'Veronica Lee', 'stars': 5, 'text': 'As a travel photographer, I love how I can showcase my
                journey stories with beautiful layouts. The platform is simply perfect!'},
                {'name': 'Lion Johnson', 'stars': 4.5, 'text': 'The community aspect is amazing. I\'ve connected
                with so many fellow travelers and found inspiration for my next adventures.'},
                {'name': 'Sophia Rodriguez', 'stars': 5, 'text': 'I love how easy it is to record my daily
                experiences while traveling. The location tracking feature is absolutely brilliant!'},
                {'name': 'Hiroshi Tanaka', 'stars': 4.5, 'text': 'This app has transformed how I document my
                travels. The image organization features are exactly what I\'ve been looking for.'},
                {'name': 'Emma Wilson', 'stars': 5, 'text': 'I\'ve tried several travel journals, but this one
                stands out with its intuitive interface and beautiful design. Highly recommend!'},
                {'name': 'Michael Chang', 'stars': 4, 'text': 'Great platform for keeping memories organized. I wish
                I had discovered this before my world tour last year!'}
                ] %}

                {% for testimonial in testimonials %}
                <div class="testimonial-card">
                    <div class="card bg-light rounded-4 border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center mb-3">
                                <img src="{{ url_for('static', filename='images/main_user' ~ (loop.index % 3 + 1) ~ '.png') }}"
                                    alt="{{ testimonial.name }}" class="rounded-circle me-3" width="60" height="60">
                                <div>
                                    <h5 class="mb-1">{{ testimonial.name }}</h5>
                                    <div class="text-warning">
                                        {% for i in range(testimonial.stars|int) %}
                                        <i class="bi bi-star-fill"></i>
                                        {% endfor %}
                                        {% if testimonial.stars % 1 != 0 %}
                                        <i class="bi bi-star-half"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <p class="mb-0 text-muted">"{{ testimonial.text }}"</p>
                        </div>
                    </div>
                </div>
                {% endfor %}

                {% for testimonial in testimonials %}
                <div class="testimonial-card">
                    <div class="card bg-light rounded-4 border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-center mb-3">
                                <img src="{{ url_for('static', filename='images/main_user' ~ (loop.index % 3 + 1) ~ '.png') }}"
                                    alt="{{ testimonial.name }}" class="rounded-circle me-3" width="60" height="60">
                                <div>
                                    <h5 class="mb-1">{{ testimonial.name }}</h5>
                                    <div class="text-warning">
                                        {% for i in range(testimonial.stars|int) %}
                                        <i class="bi bi-star-fill"></i>
                                        {% endfor %}
                                        {% if testimonial.stars % 1 != 0 %}
                                        <i class="bi bi-star-half"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <p class="mb-0 text-muted">"{{ testimonial.text }}"</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<div class="row">
    <div class="col-12">
        <div class="card bg-dark text-white rounded-4 border-0">
            <div class="card-body p-5">
                <div class="row align-items-center">
                    <div class="col-lg-6 mb-4 mb-lg-0 journey-cta-text">
                        <h2 class="display-6 mb-4 fw-bold">Begin Your Journey Journal Today</h2>
                        <p class="text-white-50 mb-4">Join our global community of travelers and create your
                            personalized digital journey
                            journal with stunning visuals, memorable moments, and immersive travel stories.</p>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-primary btn-lg rounded-pill px-5 py-3">
                            Start Your Travel Journal
                        </a>
                    </div>
                    <div class="col-lg-6 journey-cta-text">
                        <h5 class="mb-3 fw-semibold">Document Every Step of Your Journey</h5>
                        <p class="text-white-50">Capture your adventures in a beautiful digital journal—from
                            breathtaking photographs to
                            precise locations and personal reflections. Whether you share your journey log with our
                            community or keep it as
                            a private travel diary, your experiences unfold exactly as you lived them.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% include 'components/chatbot.html' %}
{% endblock %}

{% block scripts %}
<script>
    // Handle image fallback using data attribute
    function handleImageError(img) {
        if (img.dataset.fallback && img.src !== img.dataset.fallback) {
            img.onerror = null; // Prevent infinite loop
            img.src = img.dataset.fallback;
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Update the animation-completed class to maintain cursor blinking
        const typingEffect = document.getElementById('typingEffect');

        // After animation completes, add class that preserves blinking cursor
        setTimeout(() => {
            if (typingEffect) {
                typingEffect.classList.add('animation-completed');
            }
        }, 3600); // slightly longer than the animation duration

        // Intersection Observer for animating cards when they come into view
        const animateOnScroll = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target.classList.contains('slide-in-left')) {
                        entry.target.style.animation = 'slideInLeft 0.8s ease forwards';
                    } else if (entry.target.classList.contains('slide-in-right')) {
                        entry.target.style.animation = 'slideInRight 0.8s ease forwards';
                    }
                    // Unobserve after animation
                    animateOnScroll.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.2 // Trigger when 20% of the element is visible
        });

        // Observe all slide-in elements
        document.querySelectorAll('.slide-in-left, .slide-in-right').forEach(element => {
            animateOnScroll.observe(element);
        });
    });
</script>
{% endblock %}