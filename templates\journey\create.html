<!-- Create Journey Modal Content - CSS and JS files are included inline for modal compatibility -->

<div class="create-journey-modal" id="createJourneyModal">
  <form method="post" action="{{ url_for('journey.create_journey') }}" enctype="multipart/form-data" novalidate
    id="create-journey-form" class="needs-validation modern-form">
    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid{% if not premium_access %} single-column{% endif %}">
        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-journal-text"></i>
                  Journey Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title"
                  value="{{ request.form.get('title', '') }}" required minlength="5" maxlength="50"
                  placeholder="Enter journey title" />
                <div class="invalid-feedback">
                  Title is required and must be at least 5 characters long.
                </div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="3" placeholder="Describe your journey...">
{{ request.form.get('description', '') }}</textarea>
                <div class="invalid-feedback">
                  Description is required and must be at least 5 characters
                  long.
                </div>
              </div>
            </div>
          </div>

          {% if premium_access %}
          <!-- Cover Image Section (moved to left column) -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Cover Image</span>
            </div>

            <div class="form-group">
              <label for="image" class="modern-label">
                <i class="bi bi-camera"></i>
                Add Cover Image (optional)
              </label>
              <input type="file" class="modern-input" id="image" name="image"
                accept="{{ file_config.allowedExtensionsHtml }}" data-premium="false" />
              <div class="input-help">
                <i class="bi bi-info-circle"></i>
                Maximum {{ file_config.maxFileSizeMB }}MB. Allowed formats: {{
                file_config.allowedFormatsText }}.
              </div>
              <div class="invalid-feedback" id="imageFeedback">
                Image cannot exceed {{ file_config.maxFileSizeMB }}MB. Allowed
                formats: {{ file_config.allowedFormatsText }}.
              </div>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Journey Settings Section (moved to right column) -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Journey Settings</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="start_date" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date *
                </label>
                <input type="date" class="modern-input date-input" id="start_date" name="start_date"
                  value="{{ request.form.get('start_date', '') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>

              {% if user_blocked %}
              <div class="form-group">
                <div class="blocked-alert modern-alert">
                  <div class="alert-content">
                    <i class="bi bi-info-circle-fill alert-icon"></i>
                    <div class="alert-text">
                      <strong>Account Restricted</strong>
                      <p>
                        You have been blocked from sharing journeys publicly, so
                        your journey will remain private.
                      </p>
                    </div>
                  </div>
                </div>
                <input type="hidden" name="visibility" value="private" />
              </div>
              {% else %}
              <div class="form-group">
                <label for="visibility" class="modern-label">
                  <i class="bi bi-eye"></i>
                  Visibility
                </label>
                <select id="visibility" name="visibility" class="modern-input">
                  <option value="private" selected>Private</option>
                  <option value="public">Public</option>
                  {% if premium_access %}
                  <option value="published">Published</option>
                  {% endif %}
                </select>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  <div class="visibility-help">
                    <strong>Private:</strong> Only visible to you<br />
                    <strong>Public:</strong> Visible to all logged in users<br />
                    {% if premium_access %}
                    <strong>Published:</strong> Visible to everyone, including
                    non-logged in users {% endif %}
                  </div>
                </div>
              </div>
              {% endif %} {% if premium_access %}
              <div class="form-group">
                <div class="modern-checkbox">
                  <input type="checkbox" class="modern-checkbox-input" id="no_edits" name="no_edits" />
                  <label class="modern-checkbox-label" for="no_edits">
                    <i class="bi bi-shield-lock"></i>
                    Prevent staff from editing
                  </label>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  When checked, content manager staff cannot edit your journey
                  content, but they can still hide it if needed.
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>

  <style>
    /* Modern Create Journey Modal Styles */
    .create-journey-modal {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0;
    }

    .modern-form {
      background: #ffffff;
      overflow: hidden;
    }

    /* Desktop Grid Layout */
    .desktop-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      align-items: start;
    }

    .desktop-grid.single-column {
      grid-template-columns: 1fr;
      max-width: 600px;
      margin: 0 auto;
    }

    .left-column,
    .right-column {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* Form Sections */
    .form-section {
      background: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 0;
      border: 1px solid #e1e8ed;
      transition: all 0.3s ease;
      height: fit-content;
    }

    .form-section.compact {
      padding: 18px;
    }

    /* Section Headers */
    .section-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f1f3f4;
    }

    .section-icon {
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;
      flex: 1;
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      gap: 16px;
    }

    /* Form Groups */
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    /* Modern Labels */
    .modern-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .modern-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Modern Inputs */
    .modern-input,
    .modern-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      color: #2d3748;
      background: #ffffff;
      transition: all 0.3s ease;
      outline: none;
    }

    .modern-input:focus,
    .modern-textarea:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    /* Select-specific styling */
    select.modern-input {
      cursor: pointer;
      background: #ffffff;
      color: #2d3748;
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 12px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
    }

    select.modern-input:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    select.modern-input:hover {
      border-color: #cbd5e0;
    }

    /* Disabled/readonly styles (excluding select) */
    input.modern-input:disabled,
    input.modern-input:read-only,
    .modern-textarea:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    /* Disabled select styling */
    select.modern-input:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    .modern-input::placeholder,
    .modern-textarea::placeholder {
      color: #a0aec0;
    }

    /* Modern Alert */
    .modern-alert {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
    }

    .alert-content {
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .alert-icon {
      color: #856404;
      font-size: 18px;
      margin-top: 2px;
    }

    .alert-text strong {
      color: #856404;
      font-weight: 600;
    }

    .alert-text p {
      margin: 4px 0 0 0;
      color: #856404;
      font-size: 14px;
    }

    /* Modern Checkbox */
    .modern-checkbox {
      display: flex;
      align-items: flex;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .modern-checkbox:hover {
      border-color: #667eea;
      background: #f0f4ff;
    }

    .modern-checkbox-input {
      margin: 0;
      transform: scale(1.2);
    }

    .modern-checkbox-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin: 0;
      cursor: pointer;
      flex: 1;
    }

    .modern-checkbox-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Help Text */
    .input-help {
      font-size: 12px;
      color: #718096;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .input-help i {
      color: #667eea;
    }

    .visibility-help {
      margin-left: 16px;
    }

    /* Image Preview Styles */

    .image-preview-grid {
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
    }

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f1f3f4;
    }

    .preview-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
    }

    /* Validation feedback states */
    .has-error .modern-input,
    .has-error .modern-textarea {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .has-success .modern-input,
    .has-success .modern-textarea {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .has-error .input-help {
      color: #e53e3e;
    }

    .has-success .input-help {
      color: #38a169;
    }

    .has-error .input-help i {
      color: #e53e3e;
    }

    .has-success .input-help i {
      color: #38a169;
    }

    /* Validation Styles */
    .modern-input.is-invalid,
    .modern-textarea.is-invalid {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .modern-input.is-valid,
    .modern-textarea.is-valid {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .invalid-feedback {
      color: #e53e3e;
      font-size: 12px;
      margin-top: 4px;
      display: none;
      align-items: center;
      gap: 6px;
    }

    .modern-input.is-invalid+.invalid-feedback,
    .modern-textarea.is-invalid+.invalid-feedback {
      display: flex;
    }

    .invalid-feedback::before {
      content: "⚠";
      font-size: 14px;
    }

    /* Responsive Design */
    @media (max-width: 992px) {
      .desktop-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .desktop-grid.single-column {
        max-width: 100%;
      }

      .create-journey-modal {
        max-width: 800px;
      }
    }

    @media (max-width: 768px) {
      .create-journey-modal {
        margin: 0;
        border-radius: 0;
      }

      .form-content {
        padding: 20px 16px;
      }

      .form-section {
        padding: 16px;
        border-radius: 8px;
      }

      .form-section.compact {
        padding: 14px;
      }

      .desktop-grid {
        gap: 16px;
      }

      .left-column,
      .right-column {
        gap: 16px;
      }

      .section-header {
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 14px;
        padding-bottom: 10px;
      }

      .section-icon {
        width: 24px;
        height: 24px;
        font-size: 12px;
      }

      .section-title {
        font-size: 14px;
      }

      .modern-input,
      .modern-textarea {
        padding: 10px 12px;
        font-size: 13px;
      }

      select.modern-input {
        padding-right: 36px;
        background-size: 14px;
      }

      .modern-label {
        font-size: 13px;
        gap: 6px;
      }

      .modern-checkbox-label {
        padding: 10px 12px;
        font-size: 13px;
      }
    }

    @media (max-width: 480px) {
      .form-content {
        padding: 16px 12px;
      }

      .form-section {
        padding: 12px;
      }

      .form-section.compact {
        padding: 10px;
      }

      .desktop-grid {
        gap: 12px;
      }

      .left-column,
      .right-column {
        gap: 12px;
      }

      .section-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
      }

      .section-title {
        font-size: 13px;
      }

      .modern-input,
      .modern-textarea {
        padding: 8px 10px;
        font-size: 12px;
      }

      select.modern-input {
        padding-right: 32px;
        background-size: 12px;
      }

      .modern-label {
        font-size: 12px;
      }

      .modern-checkbox-label {
        padding: 8px 10px;
        font-size: 12px;
      }
    }
  </style>

  <script>
    // Initialize Journey Create Form
    document.addEventListener("DOMContentLoaded", function () {
      const form = document.getElementById("create-journey-form");
      if (!form) return;

      // Initialize basic form validation
      initializeFormValidation(form);


    });

    // Basic form validation function
    function initializeFormValidation(form) {
      const inputs = form.querySelectorAll(".modern-input, .modern-textarea");

      inputs.forEach((input) => {
        input.addEventListener("input", function () {
          validateField(this);
        });

        input.addEventListener("blur", function () {
          validateField(this);
        });
      });
    }

    function validateField(field) {
      const isValid = field.checkValidity();
      const formGroup = field.closest(".form-group");

      if (isValid) {
        field.classList.remove("is-invalid");
        field.classList.add("is-valid");
        if (formGroup) {
          formGroup.classList.remove("has-error");
          formGroup.classList.add("has-success");
        }
      } else {
        field.classList.remove("is-valid");
        field.classList.add("is-invalid");
        if (formGroup) {
          formGroup.classList.remove("has-success");
          formGroup.classList.add("has-error");
        }
      }
    }
  </script>
</div>