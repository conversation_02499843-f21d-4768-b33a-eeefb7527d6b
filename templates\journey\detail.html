{% extends "base.html" %}

{% block title %}
{{ journey.title }} - Footprints
{% endblock %}

{% block head %}
<!-- Production Bundle CSS - Single file for maximum performance -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/production-bundle.css') }}">
<!-- Journey Detail Styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/journey-detail.css') }}">
<style>
  /* Harmonize journey detail with event detail styles */
  .journey-detail-card h1,
  .journey-detail-card h2,
  .journey-detail-card h3,
  .journey-detail-card h4,
  .journey-detail-card h5,
  .journey-detail-card h6 {
    font-family: inherit;
    font-weight: 700;
    color: #212529;
    margin-bottom: 0.5rem;
    line-height: 1.3;
  }

  .journey-detail-card .section-header h3,
  .journey-detail-card .section-header h6 {
    font-size: 15px;
    font-weight: 600;
    color: #212529;
    margin: 0;
    text-transform: none;
    letter-spacing: 0;
  }

  .journey-detail-card .section-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f8f9fa;
    flex-shrink: 0;
  }

  .journey-detail-card .section-content {
    color: #495057;
    line-height: 1.5;
    font-size: 14px;
    flex: 1;
    overflow: hidden;
  }

  .journey-detail-card .description-section .section-content p {
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
  }

  .journey-detail-card .location-section .location-name {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;
  }

  .journey-detail-card .datetime-section .modern-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    font-weight: 600;
    color: #2d3748;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .journey-detail-card .datetime-section .modern-label i {
    color: #667eea;
    font-size: 12px;
  }

  .journey-detail-card .datetime-section .value {
    font-size: 12px;
    font-weight: 600;
    color: #212529;
  }

  .journey-detail-card .status-badge {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    padding: 2px 8px;
    border-radius: 12px;
  }

  .journey-detail-card .event-title,
  .journey-detail-card .fs-3.fw-bold.mb-0 {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
    margin: 0;
    line-height: 1.3;
  }

  .journey-detail-card .text-muted.small.fw-bold {
    font-size: 13px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .journey-detail-card .badge {
    font-size: 11px;
    font-weight: 600;
    border-radius: 12px;
    padding: 3px 10px;
  }

  /* Journey detail headings 강제 harmonize */
  .journey-detail-card h1 {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: #212529 !important;
    margin: 0 0 0.5rem 0 !important;
    line-height: 1.3 !important;
  }

  .journey-detail-card h2,
  .journey-detail-card h3 {
    font-size: 15px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    margin: 0 0 0.5rem 0 !important;
    line-height: 1.3 !important;
  }

  .journey-detail-card h6 {
    font-size: 13px !important;
    font-weight: 600 !important;
    color: #6c757d !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin: 0 0 0.5rem 0 !important;
  }

  .journey-detail-card .section-header h3,
  .journey-detail-card .section-header h6 {
    font-size: 15px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    margin: 0 !important;
    text-transform: none !important;
    letter-spacing: 0 !important;
  }

  .journey-title {
    font-size: 24px;
    font-weight: 700;
    color: #212529;
    margin: 0;
  }
</style>
{% endblock %}

{% block content %}
<!-- Data attributes for JavaScript -->
<div id="pageData" data-journey-user-id="{{ journey.user_id }}"
  data-session-user-id="{{ session.user_id if session.user_id else '' }}"
  data-can-manage-content="{{ 'true' if can_manage_content() else 'false' }}"
  data-back-url="{{ url_for('journey.get_public_journeys') }}" data-journey-id="{{ journey.id }}"
  data-journey-title="{{ journey.title }}"
  data-can-edit="{{ 'true' if journey.user_id == session.user_id or can_manage_content() else 'false' }}"
  data-upload-url="{{ url_for('journey.upload_cover_image', journey_id=journey.id) }}"
  data-remove-url="{{ url_for('journey.remove_cover_image', journey_id=journey.id) }}"
  data-has-locations="{{ 'true' if locations and locations|length > 0 else 'false' }}"
  data-is-logged-in="{{ 'true' if g.current_user else 'false' }}"
  data-journey-page="{{ session.get('journey_page', 'public') }}"
  data-published-list-url="{{ url_for('main.get_published_journey') }}"
  data-landing-url="{{ url_for('main.get_landing_page') }}"
  data-premium-access="{{ 'true' if premium_access else 'false' }}" style="display: none;"></div>

<!-- Locations data in script tag to avoid HTML attribute length limits -->
<script type="application/json" id="locationsData">{{ locations | tojson | safe }}</script>

<a href="javascript:void(0)" onclick="smartBack()"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back</span>
</a>
<div class="row g-4" id="journeyContainer">
  <!-- Journey details panel -->
  <div class="col-md-6">
    <div class="card shadow-sm border-0 rounded-3 journey-detail-card" style="height: calc(100vh - 240px);">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
          <div class="title-content d-flex align-items-center gap-3">
            <div class="journey-breadcrumb mb-2"><i class="bi bi-journal-bookmark"></i> <span>Journey Details</span>
            </div>
          </div>
          <div class="d-flex align-items-center gap-2">
            <!-- Follow button (visible for non-owners) -->
            {% if session.get('user_id') and journey.user_id != session.user_id %}
            <button type="button"
              class="btn {% if is_following_journey %}btn-primary{% else %}btn-outline-primary{% endif %} btn-sm d-flex align-items-center px-3"
              data-action="toggle-follow" data-journey-id="{{ journey.id }}">
              <i class="bi bi-heart{% if is_following_journey %}-fill{% endif %} me-1"></i>
              <span class="btn-text d-none d-sm-inline">{% if is_following_journey %}Following{% else %}Follow{% endif
                %}</span>
            </button>
            {% endif %}
            <!-- Journey menu -->
            {% if journey.user_id == session.user_id or can_manage_content() %}
            <div class="dropdown">
              <button class="menu-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-three-dots-vertical"></i>
              </button>
              <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                {% if journey.user_id != session.user_id %}
                <!-- Follow button moved out of dropdown -->
                {% endif %}
                {% if journey.user_id == session.user_id or can_manage_content() %}
                <li>
                  {% if journey.no_edits and journey.user_id != session.user_id %}
                  <a href="#" class="dropdown-item text-muted" onclick="showProtectedJourneyMessage()"
                    style="cursor: not-allowed;">
                    <i class="bi bi-shield-lock me-2"></i>Edit Journey (Protected)
                  </a>
                  {% else %}
                  <button type="button" class="dropdown-item" data-action="edit-journey"
                    data-journey-id="{{ journey.id }}">
                    <i class="bi bi-pencil me-2"></i>Edit Journey
                  </button>
                  {% endif %}
                </li>
                {% if can_access_edit_history() or journey.user_id == session.get('user_id') %}
                <li>
                  <button type="button" class="dropdown-item" data-action="view-edit-history"
                    data-journey-id="{{ journey.id }}" data-journey-title="{{ journey.title }}">
                    <i class="bi bi-clock-history me-2"></i>View Edit History
                  </button>
                </li>
                {% endif %}
                {% if can_manage_content() and not journey.user_id == session.user_id %}
                <li>
                  <button type="button" class="dropdown-item text-warning" data-action="toggle-hidden"
                    data-journey-id="{{ journey.id }}">
                    <i class="bi bi-slash-circle me-2"></i>
                    <span>{% if not journey.is_hidden %}Hide{% else %}Unhide{% endif %} Journey</span>
                  </button>
                </li>
                {% endif %}
                {% if journey.user_id == session.user_id %}
                <li>
                  <hr class="dropdown-divider">
                </li>
                <li>
                  <form method="post"
                    action="{{ url_for('journey.delete_' + ('admin_' if is_admin_view else '') + 'journey', journey_id=journey.id) }}"
                    id="deleteJourneyForm" class="d-inline">
                    <button type="button" class="dropdown-item text-danger" data-action="delete-journey"
                      data-journey-id="{{ journey.id }}">
                      <i class="bi bi-trash me-2"></i>Delete Journey
                    </button>
                  </form>
                </li>
                {% endif %}
                {% endif %}
              </ul>
            </div>
            {% endif %}
          </div>
        </div>
        <div class="journey-title" id="journeyTitle">{{ journey.title }}</div>
        <div class="journey-info">
          <div class="journey-info-container overflow-auto">
            <div class="mb-2">
              <span
                class="badge visibility-badge {% if journey.visibility in ('public', 'published') %}bg-success{% else %}bg-secondary{% endif %} rounded-pill py-1 px-3">
                {% if journey.visibility in ('public', 'published') %}{{ journey.visibility|capitalize }}{% else
                %}Private{% endif %}
              </span>
              {% if journey.is_hidden %}
              <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 hidden-badge">Hidden</span>
              {% endif %}
              {% if journey.no_edits and (journey.user_id == session.user_id or can_manage_content()) %}
              <span class="badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 no-edits-badge"
                title="This journey is protected from staff edits">
                <i class="bi bi-shield-lock me-1"></i>No Edits
              </span>
              {% endif %}
            </div>

            <!-- Hidden Journey Appeal Section -->
            {% if journey.is_hidden and journey.user_id == session.user_id %}
            <div class="alert alert-warning rounded-3 mb-2" id="appealSection">
              <div class="d-flex align-items-start">
                <i class="bi bi-eye-slash fs-5 me-3 mt-1"></i>
                <div class="flex-grow-1">
                  <h6 class="fw-bold mb-2">Journey Hidden by Staff</h6>
                  <p class="mb-2 small">This journey has been hidden by content management staff and is not visible to
                    other
                    users.</p>

                  <!-- Show rejection reason if appeal was rejected -->
                  {% if appeal_status and appeal_status.status == 'rejected' and appeal_status.admin_response %}
                  <div class="bg-light rounded p-3 mb-3" id="rejectionReason">
                    <h6 class="small fw-bold mb-1 text-danger">
                      <i class="bi bi-x-circle me-1"></i>Appeal Rejected - Staff Response:
                    </h6>
                    <p class="mb-0 small">{{ appeal_status.admin_response }}</p>
                  </div>
                  {% endif %}

                  <a href="{{ url_for('helpdesk.journey_appeal', journey_id=journey.id) }}"
                    class="btn btn-sm btn-warning rounded-pill">
                    <i class="bi bi-flag me-1"></i>
                    {% if not appeal_status %}
                    Submit Appeal
                    {% elif appeal_status.status == 'rejected' %}
                    Submit New Appeal
                    {% elif appeal_status.status in ['new', 'open'] %}
                    View Appeal Status
                    {% else %}
                    Appeal Decision
                    {% endif %}
                  </a>
                </div>
              </div>
            </div>
            {% endif %}


            <!-- Journey info -->
            <div class="journey-info d-flex flex-column" style="gap: 24px;">
              <!-- Author info harmonized with event detail -->
              <div class="content-section meta-section">
                <div class="event-meta">
                  <div class="author-info">
                    <div class="author-avatar">
                      <img
                        src="{% if journey.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + journey.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                        alt="{{ journey.username }}">
                    </div>
                    <div class="author-details">
                      <span class="author-name">{{ journey.username }}</span>
                    </div>
                  </div>
                  <div class="update-time">
                    <i class="bi bi-clock-history"></i>
                    <span>
                      {% if journey.updated_at and journey.updated_at != journey.created_at %}
                      Updated {{ journey.updated_at.strftime('%B %d, %Y') }}
                      {% else %}
                      Created {{ journey.created_at.strftime('%B %d, %Y') }}
                      {% endif %}
                    </span>
                  </div>
                </div>
              </div>

              <div class="content-section datetime-section">
                <div class="section-header">
                  <h3>Date & Time</h3>
                </div>
                <div class="section-content">
                  <div class="d-flex flex-row gap-4">
                    <div class="flex-fill">
                      <span class="modern-label"><i class="bi bi-calendar-plus"></i>Starts</span>
                      <span class="value" id="journeyStartDate">{{ journey.start_date.strftime('%B %d, %Y') }}</span>
                    </div>
                    {% if journey.end_date %}
                    <div class="flex-fill">
                      <span class="modern-label"><i class="bi bi-calendar-check"></i>Ends</span>
                      <span class="value" id="journeyEndDate">{{ journey.end_date.strftime('%B %d, %Y') }}</span>
                    </div>
                    {% endif %}
                  </div>
                </div>
              </div>
              <div class="content-section description-section">
                <div class="section-header">
                  <h3>Description</h3>
                </div>
                <div class="section-content mb-0 journey-description-truncate" id="journeyDescription"
                  style="margin-right: 10px; min-height:30px; max-height:40px; overflow-y:auto;">{{ journey.description
                  }}</div>
              </div>
              <div class="row g-4 mb-4 align-items-stretch w-100">
                <div class="col-12 col-lg-6">
                  <div class="content-section images-section h-100">
                    <div class="section-header">
                      <h3>Cover Image</h3>
                    </div>
                    <div class="section-content">
                      {% if journey.cover_image %}
                      <div class="cover-image-container mb-2">
                        <div class="cover-image position-relative" id="coverImageContainer">
                          <div class="cover-image-clickable"
                            data-image-url="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                            data-title="{{ journey.title }}"
                            onclick="showCoverImageModal(this.dataset.imageUrl, this.dataset.title)">
                            <img
                              src="{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}"
                              alt="Cover image" class="cover-image-img" id="coverImageImg">
                            <div class="cover-image-overlay">
                              <i class="bi bi-zoom-in"></i>
                              <span>Click to enlarge</span>
                            </div>
                          </div>
                          {% if journey.user_id == session.user_id or can_manage_content() %}
                          <div class="cover-image-controls position-absolute bottom-0 end-0 m-2">
                            {% if journey.user_id == session.user_id %}
                            <button class="btn btn-sm btn-light rounded-pill me-2" id="changeCoverImageBtn">
                              <i class="bi bi-image me-1"></i> Change
                            </button>
                            {% endif %}
                            {% if can_manage_content() and journey.user_id != session.user_id and journey.no_edits %}
                            <!-- Protected journey - show disabled button with click handler -->
                            <button class="btn btn-sm btn-light rounded-pill disabled" id="removeCoverImageBtn"
                              type="button" title="This journey is protected from content manager edits"
                              data-protected="true" style="pointer-events: auto;">
                              <i class="bi bi-trash me-1"></i> Remove
                            </button>
                            {% else %}
                            <!-- Normal button -->
                            <button class="btn btn-sm btn-light rounded-pill" id="removeCoverImageBtn">
                              <i class="bi bi-trash me-1"></i> Remove
                            </button>
                            {% endif %}
                          </div>
                          {% endif %}
                        </div>
                      </div>
                      {% else %}
                      <div class="cover-image cover-image-placeholder d-flex align-items-center justify-content-center"
                        id="coverImagePlaceholder">
                        <div class="text-center">
                          {% if journey.user_id == session.user_id %}
                          {% if premium_access %}
                          <button class="btn btn-light btn-sm rounded-pill" id="addCoverImageBtn">
                            <i class="bi bi-image me-2"></i> Add Cover Image
                          </button>
                          {% else %}
                          <div class="text-muted small mb-2">Cover images are available for premium users</div>
                          <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
                            class="btn btn-primary btn-sm rounded-pill">
                            <i class="bi bi-star me-1"></i> Upgrade to Premium
                          </a>
                          {% endif %}
                          {% else %}
                          <div class="text-muted">No cover image</div>
                          {% endif %}
                        </div>
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <div class="col-12 col-lg-6">
                  <div class="content-section location-section h-100">
                    <div class="section-header d-flex justify-content-between align-items-center">
                      <h3>Journey Map</h3>
                      {% if locations and locations|length > 0 %}
                      <button type="button" class="btn btn-sm btn-outline-primary rounded-pill"
                        onclick="openJourneyMapModal()">
                        <i class="bi bi-arrows-fullscreen me-1"></i>Full Map
                      </button>
                      {% endif %}
                    </div>
                    <div class="section-content">
                      {% if locations and locations|length > 0 %}
                      <div class="journey-map-container">
                        <div id="journeyMap"
                          style="height: 200px; width: 100%; border-radius: 8px; background: #f8f9fa;"></div>
                      </div>
                      {% else %}
                      <div class="journey-map-empty-state d-flex flex-column justify-content-center align-items-center text-center"
                        style="height: 200px; width: 100%; border-radius: 8px; background: #f8f9fa; border: 2px dashed #dee2e6;">
                        <div class="empty-state-icon mb-3">
                          <i class="bi bi-map" style="font-size: 3rem; color: #adb5bd;"></i>
                        </div>
                        <h6 class="mb-2 text-muted">No locations to display</h6>
                        <p class="mb-0 small text-muted">
                          {% if journey.user_id == session.user_id %}
                          Add events with locations to see your journey map
                          {% else %}
                          This journey doesn't have any located events yet
                          {% endif %}
                        </p>
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Events panel -->
  <div class="col-md-6">
    <div class="card shadow-sm border-0 rounded-3 journey-detail-card" style="height: calc(100vh - 240px);">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h2 class="fs-3 fw-bold mb-0">Events <span class="badge bg-dark rounded-pill" id="eventCount">{{
              events|length }}</span></h2>
          {% if session.get('user_id') and journey.user_id == session.user_id %}
          <button class="btn btn-dark rounded-pill px-4 py-2" data-action="create-event"
            data-journey-id="{{ journey.id }}">
            <i class="bi bi-plus-lg me-2"></i> Add Event
          </button>
          {% endif %}
        </div>

        {% if events %}
        <div class="event-content">
          <div class="timeline-container overflow-auto">
            <div class="timeline">
              {% set ns = namespace(displayed_dates={}) %}
              {% for event in events %}
              {% set current_date = event.start_datetime.strftime('%Y-%m-%d') %}

              <div class="timeline-item">
                {% if current_date not in ns.displayed_dates %}
                <div class="timeline-date-marker">
                  <div class="timeline-date">
                    <span class="badge bg-dark rounded-pill py-1 px-3">{{ event.start_datetime.strftime('%b %d, %Y')
                      }}</span>
                  </div>
                </div>
                {% set _ = ns.displayed_dates.update({current_date: true}) %}
                {% endif %}

                <div class="timeline-content-wrapper event-card" data-event-id="{{ event.id }}">
                  <div class="timeline-content card border-0 shadow-sm">
                    <div class="card-body p-0">
                      <!-- Main event content -->
                      <div class="d-flex event-main-content"
                        data-event-url="{{url_for('event.get_event_details', event_id=event.id)}}"
                        onclick="window.location.href=this.dataset.eventUrl" style="cursor: pointer;">
                        {% if event.image %}
                        <div class="timeline-image-container">
                          <img src="{{ url_for('static', filename=get_safe_image_url(event.image, 'event')) }}"
                            class="timeline-image rounded-start" alt="{{ event.title }}">
                        </div>
                        {% else %}
                        <div class="timeline-image-container">
                          <img
                            src="{{ url_for('static', filename='uploads/event_images/event_image_placeholder.jpg') }}"
                            class="timeline-image rounded-start" alt="Event placeholder" />
                        </div>
                        {% endif %}

                        <div class="py-2 px-3 w-100 position-relative">
                          <h5 class="mb-2 fw-semibold text-truncate">{{ event.title }}</h5>
                          <p class="mb-2 text-muted small event-description-truncate">{{ event.description }}
                          </p>

                          <div class="d-flex justify-content-between align-items-center">
                            {% if session.get('user_id') %}
                            <div class="d-flex align-items-center text-primary small event-location-button"
                              style="cursor: pointer;" data-event-id="{{ event.id }}"
                              data-latitude="{{ event.latitude }}" data-longitude="{{ event.longitude }}"
                              data-location-name="{{ event.location_name }}" data-location-id="{{ event.location_id }}"
                              onclick="event.stopPropagation(); openEventLocationModal(this.dataset.latitude || null, this.dataset.longitude || null, this.dataset.locationName, this.dataset.locationId)">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                              <i class="bi bi-chevron-down ms-2"></i>
                            </div>
                            {% else %}
                            <!-- Non-logged-in users see location but cannot interact -->
                            <div class="d-flex align-items-center text-muted small">
                              <i class="bi bi-geo-alt-fill me-2"></i>
                              <span class="text-truncate">{{ event.location_name }}</span>
                            </div>
                            {% endif %}
                            <small class="text-muted">{{ event.start_datetime.strftime('%I:%M %p') }}</small>
                          </div>
                        </div>
                      </div>


                    </div>
                  </div>
                </div>
              </div>

              {% endfor %}
            </div>
          </div>
        </div>
        {% else %}
        <div class="event-content d-flex flex-column justify-content-center align-items-center">
          <div class="empty-state text-center">
            <div class="empty-state-icon mb-4">
              <i class="bi bi-calendar-x" style="font-size: 5rem; color: #d1d1d1;"></i>
            </div>
            <h3 class="mb-3 fw-bold" id="noEventsMessage">No events yet</h3>
            <p class="text-muted mb-4">{% if journey.user_id == session.user_id %}Start your journey by adding your
              first
              event!{% else %}This journey doesn't have any events yet.{% endif %}</p>
            {% if session.get('user_id') and journey.user_id == session.user_id %}
            <button class="btn btn-primary rounded-pill px-4 py-2" data-action="create-event"
              data-journey-id="{{ journey.id }}">
              <i class="bi bi-plus-lg me-2"></i> Add your first event
            </button>
            {% endif %}
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Hidden file input for cover image uploads -->
<input type="file" id="coverImageInput" accept=".png,.jpg,.jpeg,.gif" style="display: none;">

<!-- Full Map Modal -->
<div class="modal fade" id="fullMapModal" tabindex="-1" aria-labelledby="fullMapModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl modal-dialog-centered">
    <div class="modal-content rounded-4 border-0">
      <div class="modal-header border-0 pb-2">
        <div class="d-flex align-items-center">
          <i class="bi bi-map text-primary me-2"></i>
          <h5 class="modal-title fw-bold mb-0" id="fullMapModalLabel">Journey Map</h5>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body pt-2">
        <div class="map-container rounded-3 overflow-hidden" style="border: 1px solid #e9ecef;">
          <div id="fullJourneyMap" style="height: 65vh; width: 100%;"></div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Event Location Modal -->
<div class="modal fade" id="eventLocationModal" tabindex="-1" aria-labelledby="eventLocationModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content rounded-4 border-0">
      <div class="modal-header border-0 pb-2">
        <div class="d-flex align-items-center w-100">
          <div class="d-flex align-items-center flex-grow-1">
            <i class="bi bi-geo-alt-fill text-primary me-2"></i>
            <h5 class="modal-title fw-bold mb-0" id="eventLocationModalLabel">Event Location</h5>
          </div>
          <div class="d-flex align-items-center gap-2">
            {% if session.get('user_id') %}
            <button type="button" class="btn btn-outline-primary btn-sm rounded-pill px-3"
              data-action="toggle-location-follow" data-location-id="">
              <i class="bi bi-heart me-1"></i>
              <span class="btn-text">Follow</span>
            </button>
            {% endif %}
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
        </div>
      </div>
      <div class="modal-body pt-2">
        <div class="map-container rounded-3 overflow-hidden" style="border: 1px solid #e9ecef;">
          <div id="fullEventMap" style="height: 45vh; width: 100%;"></div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block scripts %}
<!-- Journey Detail Bundle - Single unified JavaScript file -->
<!-- Loaded at end of body to ensure flash messages are available -->
<script src="{{ url_for('static', filename='js/journey-detail-bundle.js') }}"></script>
{% endblock %}