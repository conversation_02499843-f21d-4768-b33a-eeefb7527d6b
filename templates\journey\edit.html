{% block head %}
<!-- Modular CSS files -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/form-layouts.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff-permissions.css') }}">

<!-- Modular JavaScript utilities -->
<script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/journey-operations.js') }}"></script>
{% endblock %}

{% block content %}
<!-- Edit Journey Modal Content - CSS and JS files are included inline for modal compatibility -->

<div class="edit-journey-modal" id="editJourneyModal">
  <form method="post" action="{{ url_for('journey.update_journey', journey_id=journey.id) }}"
    enctype="multipart/form-data" novalidate id="editJourneyForm" class="needs-validation modern-form">
    <!-- Form Content -->
    <div class="form-content">
      <!-- Desktop Two-Column Layout -->
      <div class="desktop-grid">
        <!-- Left Column -->
        <div class="left-column">
          <!-- Basic Information Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Basic Information</span>
            </div>

            <div class="form-grid">
              <div class="form-group">
                <label for="title" class="modern-label">
                  <i class="bi bi-journal-text"></i>
                  Journey Title *
                </label>
                <input type="text" class="modern-input" id="title" name="title" value="{{ journey.title }}" required
                  minlength="5" maxlength="50" placeholder="Enter journey title" />
                <div class="invalid-feedback">
                  Title is required and must be at least 5 characters long.
                </div>
              </div>

              <div class="form-group">
                <label for="description" class="modern-label">
                  <i class="bi bi-card-text"></i>
                  Description *
                </label>
                <textarea class="modern-textarea" id="description" name="description" required minlength="5"
                  maxlength="250" rows="4" placeholder="Describe your journey...">{{ journey.description }}</textarea>
                <div class="invalid-feedback">
                  Description is required and must be at least 5 characters long.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="right-column">
          <!-- Journey Settings Section -->
          <div class="form-section compact">
            <div class="section-header">
              <span class="section-title">Journey Settings</span>
            </div>

            {% if journey.user_id != session.user_id and session.get('role') in ['editor', 'admin', 'support_tech'] %}
            <div class="alert"
              style="background-color: rgba(78, 107, 255, 0.1); color: #4e6bff; border: 1px solid rgba(78, 107, 255, 0.2); border-radius: 8px;">
              <i class="bi bi-info-circle-fill"></i>
              <span class="small">Only journey owner can edit these.</span>
            </div>
            {% endif %}

            <div class="form-grid">
              {% if journey.user_id == session.user_id %}
              <div class="form-group">
                <label for="start_date" class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date *
                </label>
                <input type="date" class="modern-input date-input" id="start_date" name="start_date"
                  value="{{ journey.start_date.strftime('%Y-%m-%d') }}" required />
                <div class="invalid-feedback">Start date is required.</div>
              </div>
              {% else %}
              <div class="form-group">
                <label class="modern-label">
                  <i class="bi bi-calendar-plus"></i>
                  Start Date
                </label>
                <div class="modern-input" style="background: #f7fafc; color: #a0aec0; cursor: not-allowed;">
                  {{ journey.start_date|date }}
                </div>
                <input type="hidden" id="start_date" name="start_date"
                  value="{{ journey.start_date.strftime('%Y-%m-%d') }}" readonly />
              </div>
              {% endif %}

              {% if journey.user_id == session.user_id and not user_blocked %}
              <div class="form-group">
                <label for="visibility" class="modern-label">
                  <i class="bi bi-eye"></i>
                  Visibility
                </label>
                <select id="visibility" name="visibility" class="modern-input">
                  <option value="private" {% if journey.visibility=='private' %}selected{% endif %}>Private</option>
                  <option value="public" {% if journey.visibility=='public' %}selected{% endif %}>Public</option>
                  {% if premium_access or journey.visibility=='published' %}
                  <option value="published" {% if journey.visibility=='published' %}selected{% endif %}>Published
                  </option>
                  {% endif %}
                </select>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  <div class="visibility-help">
                    <strong>Private:</strong> Only visible to you<br />
                    <strong>Public:</strong> Visible to all logged in users<br />
                    {% if premium_access or journey.visibility=='published' %}
                    <strong>Published:</strong> Visible to everyone, including non-logged in users
                    {% endif %}
                  </div>
                </div>
              </div>
              {% elif user_blocked %}
              <div class="form-group">
                <div class="blocked-alert modern-alert">
                  <div class="alert-content">
                    <i class="bi bi-info-circle-fill alert-icon"></i>
                    <div class="alert-text">
                      <strong>Account Restricted</strong>
                      <p>You have been blocked from sharing journeys publicly.</p>
                    </div>
                  </div>
                </div>
                <input type="hidden" name="visibility" value="{{ journey.visibility }}" />
              </div>
              {% else %}
              <div class="form-group">
                <label class="modern-label">
                  <i class="bi bi-eye"></i>
                  Visibility
                </label>
                <div class="modern-input" style="background: #f7fafc; color: #a0aec0; cursor: not-allowed;">
                  <span
                    class="badge rounded-pill {% if journey.visibility in ('public', 'published') %}text-bg-success{% else %}text-bg-secondary{% endif %} px-3 py-2">
                    {{ journey.visibility|capitalize }}
                  </span>
                </div>
                <input type="hidden" name="visibility" value="{{ journey.visibility }}" />
              </div>
              {% endif %}

              {% if journey.user_id == session.user_id and (premium_access or (ever_had_premium and journey.no_edits))
              %}
              <div class="form-group">
                <div class="modern-checkbox">
                  <input type="checkbox" class="modern-checkbox-input" id="no_edits" name="no_edits" {% if
                    journey.no_edits %}checked{% endif %} {% if not premium_access and journey.no_edits %}disabled{%
                    endif %} />
                  <label class="modern-checkbox-label" for="no_edits">
                    <i class="bi bi-shield-lock"></i>
                    Prevent staff from editing
                  </label>
                </div>
                <div class="input-help">
                  <i class="bi bi-info-circle"></i>
                  {% if premium_access %}
                  When checked, content manager staff cannot edit your journey content, but they can still hide it if
                  needed.
                  {% elif ever_had_premium and journey.no_edits %}
                  This journey is protected from edits. To change this setting, you need an active premium subscription.
                  {% endif %}
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      {% if journey.user_id != session.user_id and session.get('role') in ['editor', 'admin', 'support_tech'] %}
      <!-- Staff Edit Reason Section -->
      <div class="form-section compact" style="margin-top: 20px;">
        <div class="section-header">
          <span class="section-title">Staff Edit Reason</span>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label for="edit_reason" class="modern-label">
              <i class="bi bi-pencil-square"></i>
              Edit Reason *
            </label>
            <textarea class="modern-textarea" id="edit_reason" name="edit_reason" rows="2" required maxlength="500"
              placeholder="Please provide a reason for this edit"></textarea>
            <div class="invalid-feedback">Staff must provide a reason for editing user content</div>
            <div class="input-help">
              <i class="bi bi-info-circle"></i>
              As a staff member, you must provide a reason when editing user content.
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
  </form>

  <style>
    /* Modern Edit Journey Modal Styles */
    .edit-journey-modal {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0;
    }

    .modern-form {
      background: #ffffff;
      overflow: hidden;
    }

    /* Form Content */
    /* Desktop Grid Layout */
    .desktop-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      align-items: stretch;
      height: 100%;
    }

    .left-column,
    .right-column {
      display: flex;
      flex-direction: column;
      gap: 20px;
      height: 100%;
    }

    .form-section {
      background: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 0;
      border: 1px solid #e1e8ed;
      transition: all 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .form-section.compact {
      padding: 18px;
    }

    /* Section Headers */
    .section-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f1f3f4;
    }

    .section-icon {
      width: 28px;
      height: 28px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #1a202c;
      flex: 1;
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      gap: 16px;
    }

    /* Form Groups */
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    /* Modern Labels */
    .modern-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .modern-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Modern Inputs */
    .modern-input,
    .modern-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      color: #2d3748;
      background: #ffffff;
      transition: all 0.3s ease;
      outline: none;
    }

    .modern-input:focus,
    .modern-textarea:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    /* Select-specific styling */
    select.modern-input {
      cursor: pointer;
      background: #ffffff;
      color: #2d3748;
      appearance: none;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
      background-position: right 12px center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
    }

    select.modern-input:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      transform: translateY(-1px);
    }

    select.modern-input:hover {
      border-color: #cbd5e0;
    }

    /* Disabled/readonly styles (excluding select) */
    input.modern-input:disabled,
    input.modern-input:read-only,
    .modern-textarea:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    /* Disabled select styling */
    select.modern-input:disabled {
      background: #f7fafc;
      color: #a0aec0;
      border-color: #e2e8f0;
      cursor: not-allowed;
    }

    .modern-input::placeholder,
    .modern-textarea::placeholder {
      color: #a0aec0;
    }

    /* Modern Alert */
    .modern-alert {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
    }

    .alert-content {
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .alert-icon {
      color: #856404;
      font-size: 18px;
      margin-top: 2px;
    }

    .alert-text strong {
      color: #856404;
      font-weight: 600;
    }

    .alert-text p {
      margin: 4px 0 0 0;
      color: #856404;
      font-size: 14px;
    }

    /* Modern Checkbox */
    .modern-checkbox {
      display: flex;
      align-items: flex;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .modern-checkbox:hover {
      border-color: #667eea;
      background: #f0f4ff;
    }

    .modern-checkbox-input {
      margin: 0;
      transform: scale(1.2);
    }

    .modern-checkbox-label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      color: #2d3748;
      margin: 0;
      cursor: pointer;
      flex: 1;
    }

    .modern-checkbox-label i {
      color: #667eea;
      font-size: 16px;
    }

    /* Help Text */
    .input-help {
      font-size: 12px;
      color: #718096;
      margin-top: 4px;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .input-help i {
      color: #667eea;
    }

    .visibility-help {
      margin-left: 16px;
    }

    /* Validation feedback states */
    .has-error .modern-input,
    .has-error .modern-textarea {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .has-success .modern-input,
    .has-success .modern-textarea {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .has-error .input-help {
      color: #e53e3e;
    }

    .has-success .input-help {
      color: #38a169;
    }

    .has-error .input-help i {
      color: #e53e3e;
    }

    .has-success .input-help i {
      color: #38a169;
    }

    /* Validation Styles */
    .modern-input.is-invalid,
    .modern-textarea.is-invalid,
    .was-validated .modern-input:invalid,
    .was-validated .modern-textarea:invalid {
      border-color: #e53e3e;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    .modern-input.is-valid,
    .modern-textarea.is-valid,
    .was-validated .modern-input:valid,
    .was-validated .modern-textarea:valid {
      border-color: #38a169;
      box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    .invalid-feedback {
      color: #e53e3e;
      font-size: 12px;
      margin-top: 4px;
      display: none;
      align-items: center;
      gap: 6px;
    }

    .modern-input.is-invalid+.invalid-feedback,
    .modern-textarea.is-invalid+.invalid-feedback,
    .was-validated .modern-input:invalid+.invalid-feedback,
    .was-validated .modern-textarea:invalid+.invalid-feedback {
      display: flex;
    }

    .invalid-feedback::before {
      content: "⚠";
      font-size: 14px;
    }

    /* Responsive Design */
    @media (max-width: 992px) {
      .desktop-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .edit-journey-modal {
        max-width: 800px;
      }
    }

    @media (max-width: 768px) {
      .edit-journey-modal {
        margin: 0;
        border-radius: 0;
      }

      .form-content {
        padding: 20px 16px;
      }

      .form-section {
        padding: 16px;
        border-radius: 8px;
      }

      .form-section.compact {
        padding: 14px;
      }

      .desktop-grid {
        gap: 16px;
      }

      .left-column,
      .right-column {
        gap: 16px;
      }

      .section-header {
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 14px;
        padding-bottom: 10px;
      }

      .section-icon {
        width: 24px;
        height: 24px;
        font-size: 12px;
      }

      .section-title {
        font-size: 14px;
      }

      .modern-input,
      .modern-textarea {
        padding: 10px 12px;
        font-size: 13px;
      }

      select.modern-input {
        padding-right: 36px;
        background-size: 14px;
      }

      .modern-label {
        font-size: 13px;
        gap: 6px;
      }

      .modern-checkbox-label {
        padding: 10px 12px;
        font-size: 13px;
      }
    }

    @media (max-width: 576px) {
      .edit-journey-modal {
        max-height: 60vh;
        overflow-y: auto;
      }

      .form-section {
        padding: 10px;
        min-height: unset;
      }

      .form-content {
        padding: 8px 4px;
      }

      .modern-input,
      .modern-textarea {
        padding: 8px 8px;
        font-size: 13px;
      }
    }
  </style>

  <script>
    // Initialize Journey Edit Form with Bootstrap validation
    document.addEventListener("DOMContentLoaded", function () {
      const form = document.getElementById("editJourneyForm");
      if (!form) return;

      // Initialize enhanced form validation (from form-validation.js)
      if (window.EnhancedFormValidation) {
        window.EnhancedFormValidation.initializeModernForm(form, {
          validateOnInput: true,
          validateOnBlur: true,
          showSuccessStates: true
        });
      }


    });
  </script>
</div>

{% endblock %}