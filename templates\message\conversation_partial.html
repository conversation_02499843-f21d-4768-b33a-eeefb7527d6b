<style>
  /* Clean Chat Interface Styles */
  .chat-container {
    max-width: 900px;
    margin: 0 auto;
    height: calc(100vh - 300px);
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .chat-header {
    background: white;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  .chat-header .user-info {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .chat-header .user-info a:hover .user-avatar {
    transform: scale(1.05);
    border-color: var(--primary-color);
  }

  .chat-header .user-info a:hover .user-name {
    color: var(--primary-color) !important;
  }

  .chat-header .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
  }

  .chat-header .user-avatar:hover {
    transform: scale(1.05);
    border-color: rgba(102, 126, 234, 0.5);
  }

  .chat-header .user-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  .chat-header .action-buttons {
    display: flex;
    gap: 10px;
  }

  .chat-header .btn {
    border-radius: 12px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
  }

  .chat-header .btn-back {
    background: var(--light-color);
    color: var(--dark-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .chat-header .btn-back:hover {
    background: var(--dark-color);
    color: var(--light-color);
    transform: translateY(-1px);
  }

  .chat-header .btn-refresh {
    background: var(--light-color);
    color: var(--dark-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .chat-header .btn-refresh:hover {
    background: var(--dark-color);
    color: var(--light-color);
    transform: rotate(180deg);
  }

  .chat-header .btn-profile {
    background: var(--primary-color);
    color: white;
    border: none;
  }

  .chat-header .btn-profile:hover {
    background: var(--primary-darker);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px 16px;
    background: #fafafa;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
  }

  .messages-container::-webkit-scrollbar {
    width: 6px;
  }

  .messages-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .messages-container::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  .date-divider {
    text-align: center;
    margin: 16px 0 24px 0;
  }

  .date-badge {
    background: white;
    color: #6c757d;
    padding: 6px 14px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .message-wrapper {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-end;
  }

  .message-wrapper.sent {
    justify-content: flex-end;
  }

  .message-bubble {
    max-width: 70%;
    padding: 10px 16px;
    border-radius: 20px;
    position: relative;
    word-wrap: break-word;
    animation: messageSlideIn 0.3s ease-out;
  }

  .message-bubble.sent {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 8px;
    margin-left: auto;
  }

  .message-bubble.received {
    background: white;
    color: #2d3748;
    border-bottom-left-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .message-content {
    margin: 0 0 8px 0;
    line-height: 1.5;
    font-size: 0.95rem;
  }

  .message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .message-bubble.sent .message-time {
    color: rgba(255, 255, 255, 0.8);
  }

  .message-bubble.received .message-time {
    color: #718096;
  }

  .read-indicator {
    color: #48bb78;
    font-size: 0.7rem;
  }

  .chat-input-container {
    background: white;
    padding: 20px 25px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }

  .chat-input-form {
    display: flex;
    gap: 15px;
    align-items: flex-end;
  }

  .chat-input {
    flex: 1;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 0.95rem;
    background: white;
    transition: all 0.3s ease;
    resize: none;
    min-height: 45px;
    max-height: 120px;
  }

  .chat-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
  }

  .send-button {
    background: var(--primary-color);
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .send-button:hover {
    background: var(--primary-darker);
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .send-button:disabled {
    opacity: 0.6;
    transform: none;
    cursor: not-allowed;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    text-align: center;
  }

  .empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.4;
  }

  .premium-alert {
    background: #f8f9fa;
    border: 1px solid #f8f9fa;
    border-radius: 15px;
    padding: 20px;
  }

  @keyframes messageSlideIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(100%);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideOutRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }

    to {
      opacity: 0;
      transform: translateX(100%);
    }
  }

  /* Mobile Responsive */
  @media (max-width: 768px) {
    .chat-container {
      height: calc(100vh - 300px);
      border-radius: 0;
      margin: 0;
    }

    .message-bubble {
      max-width: 85%;
    }

    .chat-header {
      padding: 15px 20px;
    }

    .messages-container {
      padding: 20px 15px;
    }

    .chat-input-container {
      padding: 15px 20px;
    }
  }

  .message-bubble {
    position: relative;
  }

  .delete-message-btn {
    position: absolute;
    top: -10px;
    right: -5px;
    background: #f1f1f1;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 1.2rem;
    color: #333;
    cursor: pointer;
    opacity: 0.8;
    display: block;
    z-index: 2;
    transition: background 0.2s, color 0.2s, opacity 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 0;
    line-height: 1;
  }

  .delete-message-btn:hover {
    background: #dc3545;
    color: #fff;
    opacity: 1;
  }
</style>
<div class="container-fluid">
  <div class="chat-container">
    <!-- Chat Header -->
    <div class="chat-header">
      <div class="d-flex justify-content-between align-items-center">
        <div class="user-info">
          <a href="{{ url_for('account.get_profile', username=other_user.username) }}"
            class="d-flex align-items-center text-decoration-none" style="color: inherit;">
            <img src="{{ url_for('static', filename=get_safe_image_url(other_user.profile_image, 'profile')) }}"
              alt="{{ other_user.username }}" class="user-avatar me-3">
            <h1 class="user-name mb-0">{{ other_user.username }}</h1>
          </a>
        </div>
        <div class="action-buttons">
          <button id="refresh-messages" class="btn btn-refresh" title="Refresh messages">
            <i class="bi bi-arrow-clockwise"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Messages Container -->
    <div class="messages-container" id="messages-container">
      {% if messages %}
      {% set ns = namespace(last_date='') %}
      {% for message in messages %}
      {% set message_date = message.created_at|formatdate %}
      {% if message_date != ns.last_date %}
      <div class="date-divider">
        <span class="date-badge">{{ message_date }}</span>
      </div>
      {% set ns.last_date = message_date %}
      {% endif %}

      <div class="message-wrapper {% if message.sender_id == current_user.id %}sent{% endif %}"
        data-message-id="{{ message.id }}">
        <div class="message-bubble {% if message.sender_id == current_user.id %}sent{% else %}received{% endif %}">
          <button class="delete-message-btn" data-message-id="{{ message.id }}" title="Delete message">&times;</button>
          <p class="message-content">{{ message.content }}</p>
          <div class="message-time">
            <span>{{ message.created_at|formattime }}</span>
            {% if message.sender_id == current_user.id and message.is_read %}
            <span class="read-indicator">
              <i class="bi bi-check2-all"></i> Read
            </span>
            {% endif %}
          </div>
        </div>
      </div>
      {% endfor %}
      {% else %}
      <div class="empty-state">
        <i class="bi bi-chat-dots"></i>
        <h3>No messages yet</h3>
        <p>Start the conversation by sending a message!</p>
      </div>
      {% endif %}
    </div>

    <!-- Chat Input -->
    <div class="chat-input-container">
      {% if premium_access %}
      <form id="message-form" class="chat-input-form needs-validation" method="post" action="/messages/send" novalidate>
        <input type="hidden" name="recipient_id" value="{{ other_user.id }}">
        <textarea name="content" id="message-input" class="chat-input" placeholder="Type your message..." required
          autocomplete="off" rows="1"></textarea>
        <div class="invalid-feedback">Please enter a message.</div>
        <button type="submit" class="send-button">
          <i class="bi bi-send-fill"></i>
        </button>
      </form>
      {% else %}
      <div class="premium-alert">
        <div class="d-flex align-items-center">
          <div>
            <strong>Premium Feature</strong>
            <p class="mb-0">You need a premium subscription to send messages.</p>
          </div>
          <a href="{{ url_for('account.get_profile', active_tab='subscription') }}"
            class="btn btn-dark ms-auto rounded-pill px-4 py-2">
            <i class="bi bi-star me-2"></i> Upgrade to Premium
          </a>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>