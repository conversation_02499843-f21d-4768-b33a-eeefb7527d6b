{% extends 'base.html' %}

{% block title %}Notifications | Footprints{% endblock %}

{% block content %}
<div class="container">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">Your Notifications</h1>
        <div>
          <button id="markAllReadButton" class="btn btn-primary rounded-pill">Mark As All Read</button>
        </div>
      </div>

      <div class="card shadow-sm">
        <div class="card-body p-0">
          {% if notifications %}
          <div class="list-group">
            {% for notification in notifications %}
            <div
              class="list-group-item notification-item p-3 {% if not notification.is_read %}unread-notification{% endif %}"
              data-notification-id="{{ notification.id }}" data-related-id="{{ notification.related_id }}"
              data-notification-type="{{ notification.notification_type }}">
              <div class="d-flex gap-3">
                {% if not notification.is_read %}
                <div class="notification-indicator">
                  <span class="badge bg-primary rounded-circle"></span>
                </div>
                {% endif %}
                <div class="flex-grow-1">
                  <h6 class="fw-bold mb-1">{{ notification.notification_type|capitalize }}</h6>
                  <p class="mb-1">{{ notification.content }}</p>
                  <small class="text-muted">{{ notification.created_at|datetime }}</small>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>

          <!-- Pagination -->
          {% if total_pages > 1 %}
          <div class="d-flex justify-content-center p-3">
            <nav aria-label="Notifications pagination">
              <ul class="pagination">
                <li class="page-item {% if page == 1 %}disabled{% endif %}">
                  <a class="page-link" href="{{ url_for('main.view_all_notifications', page=page-1) }}"
                    aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>

                {% for p in range(1, total_pages + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                  <a class="page-link" href="{{ url_for('main.view_all_notifications', page=p) }}">{{ p }}</a>
                </li>
                {% endfor %}

                <li class="page-item {% if page == total_pages %}disabled{% endif %}">
                  <a class="page-link" href="{{ url_for('main.view_all_notifications', page=page+1) }}"
                    aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
          {% endif %}
          {% else %}
          <div class="p-4 text-center">
            <p class="text-muted mb-0">No notifications to display</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Style for unread notifications
    document.head.insertAdjacentHTML('beforeend', `
            <style>
                .unread-notification {
                    background-color: rgba(13, 110, 253, 0.05);
                }
                .notification-item {
                    cursor: pointer;
                    transition: background-color 0.2s;
                }
                .notification-item:hover {
                    background-color: rgba(13, 110, 253, 0.1);
                }
                .notification-indicator {
                    display: block;
                    width: 10px;
                    height: 10px;
                    background: #1976d2;
                    border-radius: 50%;
                    margin-right: 10px;
                    margin-top: 10px;
                    flex-shrink: 0;
                }
                .notification-item p.mb-1 {
                    white-space: normal;
                    word-break: break-word;
                    overflow-wrap: break-word;
                    margin-bottom: 0.5rem;
                }
            </style>
        `);

    // Mark individual notification as read and navigate to related content
    document.querySelectorAll('.notification-item').forEach(item => {
      item.addEventListener('click', function () {
        const notificationId = this.getAttribute('data-notification-id');
        const relatedId = this.getAttribute('data-related-id');
        const notificationType = this.getAttribute('data-notification-type');

        // Mark as read
        fetch(`/notifications/${notificationId}/read`, {
          method: 'POST'
        }).then(response => response.json())
          .then(data => {
            // Remove unread styling
            this.classList.remove('unread-notification');
            const indicator = this.querySelector('.notification-indicator');
            if (indicator) indicator.remove();

            // Update the notification count
            updateNotificationCount();

            // Navigate to related content if available
            if (relatedId) {
              let targetUrl = '';

              // Handle notifications by type first for accuracy
              if (notificationType === 'report') {
                targetUrl = `/report/detail/${relatedId}`;
              } else if (notificationType === 'helpdesk' || notificationType === 'appeal') {
                targetUrl = `/helpdesk/query?ticket_id=${relatedId}&back=${encodeURIComponent('/notifications/all')}`;
              } else if (notificationType === 'message') {
                targetUrl = `/messages?user=${relatedId}`;
              } else if (notificationType === 'edit') {
                // For edit notifications, check content to determine target
                if (this.textContent.includes('event')) {
                  // Event edit: related_id is event_id
                  targetUrl = `/event/${relatedId}/detail`;
                } else if (this.textContent.includes('journey')) {
                  // Journey edit: related_id is journey_id
                  // Check if journey is published first
                  fetch(`/api/journey-visibility/${relatedId}`)
                    .then(response => response.json())
                    .then(data => {
                      if (data.success && data.visibility === 'published') {
                        window.location.href = `/journey/published/${relatedId}`;
                      } else {
                        window.location.href = `/journey/private/${relatedId}`;
                      }
                    })
                    .catch(() => {
                      // Fallback to private if API fails
                      window.location.href = `/journey/private/${relatedId}`;
                    });
                  return; // Exit early to avoid setting targetUrl
                }
              } else if (notificationType === 'like') {
                // Like notifications: related_id is always event_id
                targetUrl = `/event/${relatedId}/detail`;
              } else if (notificationType === 'comment') {
                // All comment notifications: related_id is event_id
                // This includes regular comments and hidden comment notifications
                targetUrl = `/event/${relatedId}/detail`;
              }

              if (targetUrl) {
                window.location.href = targetUrl;
              }
            }
          });
      });
    });

    // Mark all as read button
    document.getElementById('markAllReadButton').addEventListener('click', function () {
      fetch('/notifications/mark-all-read', {
        method: 'POST'
      }).then(response => response.json())
        .then(data => {
          // Remove all unread styling
          window.location.href = '/notifications/all';
          window.location.reload();
          document.querySelectorAll('.unread-notification').forEach(item => {
            item.classList.remove('unread-notification');
          });
          document.querySelectorAll('.notification-indicator').forEach(indicator => {
            indicator.remove();
          });

          // Update notification count to zero
          updateNotificationCount();
        });
    });
  });

  // Function to update notification count in navbar
  function updateNotificationCount() {
    fetch('/api/notifications/unread-count')
      .then(response => response.json())
      .then(data => {
        document.getElementById('notificationCount').innerText = data.count;
      });
  }
</script>
{% endblock %}