{% extends "base.html" %}

{% block title %}Published Journeys - Footprints{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="banner mb-5 text-center">
    <h2 class="fw-bold mb-3" style="font-size:2.4rem;">It's your story</h2>
    <div class="mb-3 text-muted" style="max-width:600px; margin:0 auto; font-size:1.1rem;">
      Footprints showcases fabulous travel stories and places.<br>
      Discover, get inspired, and share your own journey.
    </div>
    <a href="{{ url_for('main.get_landing_page') }}" class="btn btn-primary rounded-pill px-4 py-2 mt-3 back-home-btn">
      <i class="bi bi-house-door me-2"></i> Back to Home
    </a>
  </div>

  <div class="row g-3 justify-content-center">
    {% for journey in journeys %}
    <div class="col-md-4 col-sm-6 col-12 mb-2">
      <a href="{{ url_for('journey.get_published_journey_detail', journey_id=journey.id) }}"
        class="rounded-3 position-relative overflow-hidden destination-card d-block p-0"
        style="height: 180px; text-decoration: none;">
        <img
          src="{% if journey.cover_image %}{{ url_for('static', filename=get_safe_image_url(journey.cover_image, 'journey_cover')) }}{% else %}{{ url_for('static', filename='images/main_image' ~ ((loop.index % 6) + 1) ~ '.jpg') }}{% endif %}"
          alt="{{ journey.title }}" style="width: 100%; height: 180px; object-fit: cover; display: block;"
          data-fallback="{{ url_for('static', filename='images/main_image' ~ ((loop.index % 6) + 1) ~ '.jpg') }}"
          onload="this.style.opacity='1'" onerror="handleImageError(this)">
        <div class="position-absolute bottom-0 start-0 w-100 d-flex align-items-center justify-content-center"
          style="height: 100%;">
          <h5 class="text-white fw-bold text-center" style="width: 100%; text-shadow: 0 2px 8px rgba(0,0,0,0.25);">
            {{ journey.title }}</h5>
        </div>
      </a>
    </div>
    {% endfor %}
  </div>

  {% if total_count > 6 %}
  <div class="text-center mt-5 mb-4">
    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-dark rounded-pill px-4 py-2 fs-5">
      Show More
    </a>
  </div>
  {% endif %}
</div>

<style>
  .destination-card {
    transition: box-shadow 0.2s, transform 0.2s;
  }

  .destination-card:hover {
    box-shadow: 0 8px 32px rgba(78, 107, 255, 0.13) !important;
    transform: translateY(-2px);
  }

  .back-home-btn {
    box-shadow: 0 2px 12px rgba(78, 107, 255, 0.08);
  }
</style>

<script>
  // Handle image fallback using data attribute
  function handleImageError(img) {
    if (img.dataset.fallback && img.src !== img.dataset.fallback) {
      img.onerror = null; // Prevent infinite loop
      img.src = img.dataset.fallback;
    }
  }
</script>
{% endblock %}