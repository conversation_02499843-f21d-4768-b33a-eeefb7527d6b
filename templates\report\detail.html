{% extends "base.html" %}

{% block title %} {{ event.title }} - Footprints {%endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/event-detail-bundle.css') }}">
<style>
  .journey-detail-card,
  .card.shadow-sm {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f3f4;
  }

  .section-header,
  .comments-header,
  .fs-4.fw-bold.text-dark {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f8f9fa;
    font-size: 18px;
    font-weight: 700;
    color: #212529;
    flex-shrink: 0;
  }

  .comments-header h2,
  .fs-4.fw-bold.text-dark {
    font-size: 18px;
    font-weight: 700;
    color: #212529;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .comment-count {
    background: #f8f9fa;
    color: #495057;
    padding: 3px 10px;
    border-radius: 14px;
    font-size: 11px;
    font-weight: 600;
  }

  .comments-scroll {
    font-size: 14px;
    color: #495057;
    line-height: 1.5;
    padding-bottom: 0;
  }

  .mb-3.p-3.rounded-3.bg-light,
  .mb-3.p-3.rounded-3 {
    background: #f8f9fa !important;
    border: 1px solid #f1f3f4;
    border-radius: 12px;
    margin-bottom: 16px !important;
    font-size: 14px;
    color: #495057;
  }

  .fw-semibold,
  .fw-bold {
    font-weight: 600 !important;
  }

  .badge {
    font-size: 11px;
    font-weight: 600;
    border-radius: 12px;
    padding: 3px 10px;
  }

  .text-muted,
  .small {
    color: #6c757d !important;
    font-size: 12px !important;
  }

  .rounded-circle {
    border: 2px solid #e9ecef;
  }

  .report-reason-scroll {
    font-size: 14px;
    color: #495057;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #f1f3f4;
  }

  .btn,
  .btn-sm {
    border-radius: 24px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    padding: 8px 18px !important;
  }

  /* Comment section height and scroll */
  .card.journey-detail-card.shadow-sm.border-0.rounded-3.h-100 .comments-section {
    height: 250px;
    min-height: 250px;
    max-height: 300px;
    display: flex;
    flex-direction: column;
  }

  .comments-list {
    flex: 1 1 auto;
    overflow-y: auto;
    min-height: 0;
  }

  /* Report Details title font harmonize with comments-header */
  .section-header.mb-3 h2.fs-4.fw-bold.text-dark.mb-0 {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #212529 !important;
    margin: 0 !important;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  @media (max-width: 1024px) {

    .row.g-4.mb-4.align-items-stretch.w-100.mt-2>.col-md-6,
    .row.g-4.mb-4.align-items-stretch.w-100.mt-2>.col-6 {
      flex: 0 0 100% !important;
      max-width: 100% !important;
      width: 100% !important;
      display: block !important;
    }
  }

  @media (max-width: 767.98px) {
    .report-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 100px);
      overflow: hidden;
    }
  }

  @media (min-width: 768px) and (max-width: 899px) {
    .report-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 200px);
      overflow: hidden;
    }
  }

  @media (min-width: 899px) and (max-width: 1439.98px) {
    .report-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 230px);
      overflow: hidden;
    }
  }


  @media (min-width: 1440px) {
    .report-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 250px);
      overflow: hidden;
    }
  }


  @media (max-width: 769.98px) {
    .report-info-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 200px);
      overflow: hidden;
    }
  }

  @media (min-width: 768px) and (max-width: 1439.98px) {
    .report-info-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 200px);
      overflow: hidden;
    }
  }

  @media (min-width: 1440px) {
    .report-info-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: calc(100vh - 200px);
      overflow: hidden;
    }
  }

  @media (min-width: 900px) and (max-width: 1366px) {

    .report-info-container {
      max-height: calc(100vh - 0px);
      overflow: hidden;
    }
  }

  .report-info-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 200px);
    overflow: hidden;
  }
</style>
{% endblock %}

{% block content %}
<a href="javascript:void(0)" onclick="smartBack()" data-back="{{ back | urlencode }}" id="backButton"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back to Reports</span>
</a>
<div class="row g-4" id="eventContainer">
  <div class="col-md-6 col-12 mb-4 mb-md-0 {% if journey.visibility == 'private' %}mx-auto{% endif %}">
    <div class="card shadow-sm border-0 rounded-3 journey-detail-card h-100">
      <div class="card-body">
        <div class="report-content-container">
          <div class="report-content" id="pageData" data-event-id="{{ event.id }}">
            <!-- BEGIN EVENT DETAIL CONTENT  -->
            <div class="title-actions-header">
              <div class="title-content">
                <div class="journey-breadcrumb mb-2">
                  <i class="bi bi-journal-bookmark"></i>
                  <span>Report Details</span>
                </div>
                <h1 class="event-title mb-1">{{ event.title }}</h1>
                <div class="mt-1 mb-3">
                  <span
                    class="status-badge private {% if journey.visibility == 'public' %}bg-success text-white{% endif %}">
                    <i class="bi bi-lock-fill"></i>{{ journey.visibility|capitalize }}
                  </span>
                </div>
              </div>
              <div class="header-actions">
                <div class="like-btn disabled">
                  <i class="bi bi-hand-thumbs-up{% if user_liked_event %}-fill{% endif %}"></i>
                  <span>{{ event_likes|length|default(0) }}</span>
                </div>
              </div>
            </div>
            <div class="report-info">
              <div class="report-info-container overflow-auto">
                <div class="meta-section">
                  <div class="event-meta">
                    <div class="author-info">
                      <div class="author-avatar">
                        <img
                          src="{{ url_for('static', filename='uploads/profile_images/' + (journey.profile_image if journey.profile_image else 'profile_placeholder.png')) }}"
                          alt="{{ journey.username }}"
                          onerror="this.onerror=null;this.src='{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}';" />
                      </div>
                      <div class="author-details">
                        <span class="author-name">{{ journey.username }}</span>
                      </div>
                    </div>
                    <div class="update-time">
                      <i class="bi bi-clock-history"></i>
                      <span>Created on {{ event.updated_at|date }}</span>
                    </div>
                  </div>
                </div>
                <!-- Journey Info Card -->
                <div class="mt-2">
                  <div class="section-header">
                    <h3>Journey</h3>
                  </div>
                  <div class="section-content">
                    <a href="{{ url_for('journey.get_public_journey', journey_id=journey.id) }}" class="journey-link">
                      <div class="card bg-light border-0 rounded-3 mb-3 border">
                        <div class="card-body py-2 px-3">
                          <div class="fw-semibold mb-1">{{ journey.title }}</div>
                          <div class="text-muted small mb-1">{{ journey.description }}</div>
                          <div class="d-flex align-items-center text-muted small">Start Date: {{ journey.start_date|date
                            }}
                          </div>
                        </div>
                      </div>
                    </a>
                  </div>
                </div>
                <!-- DateTime Section -->
                <div class="datetime-section mb-4">
                  <div class="section-header">
                    <h3>Date & Time</h3>
                  </div>
                  <div class="section-content">
                    <div class="d-flex flex-row gap-4">
                      <div class="flex-fill">
                        <span class="modern-label"><i class="bi bi-calendar-plus"></i>Starts</span>
                        <span class="value" id="eventStartDate">{{ event.start_datetime | datetime }}</span>
                      </div>
                      {% if event.end_datetime %}
                      <div class="flex-fill">
                        <span class="modern-label"><i class="bi bi-calendar-check"></i>Ends</span>
                        <span class="value" id="eventEndDate">{{ event.end_datetime | datetime }}</span>
                      </div>
                      {% endif %}
                    </div>
                  </div>
                </div>
                <!-- Description Section -->
                <div class="description-section">
                  <div class="section-header">
                    <h3>Description</h3>
                  </div>
                  <div class="section-content">
                    <div class="section-content"><span style="font-size: 14px; color: #495057;">{{ event.description }}
                    </div>
                  </div>
                </div>
                <!-- Images & Location Section  -->
                <div class="row g-4 mb-4 align-items-stretch w-100 mt-2">
                  <div class="col-12 col-md-6">
                    <div class="images-section h-100">
                      <div class="section-header d-flex justify-content-between align-items-center">
                        <h3>Images</h3>
                      </div>
                      <div class="section-content">
                        {% if images %}
                        <div class="image-wrapper position-relative">
                          <button class="event-image-button" event-id="{{ event.id }}">
                            <div class="event-image">
                              <img
                                src="{{ url_for('static', filename='uploads/event_images/' + images[0].image_filename) }}"
                                alt="Event image">
                            </div>
                          </button>
                          {% if images|length > 1 %}
                          <div class="image-indicator badge text-white rounded-circle">+{{ images|length - 1 }}</div>
                          {% endif %}
                        </div>
                        {% else %}
                        <div id="noImagesMessage-{{ event.id }}" class="text-center p-3"
                          style="background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 12px">
                          <i class="bi bi-images fs-3 text-secondary"></i>
                          <p class="text-muted mt-2 mb-0">No images available</p>
                        </div>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6">
                    <div class="location-section h-100">
                      <div class="section-header d-flex justify-content-between align-items-center">
                        <h3>Location</h3>
                      </div>
                      <div class="section-content">
                        {% if event.latitude and event.longitude %}
                        <div class="map-container">
                          <div id="reportEventMap" class="event-map" data-lat="{{ event.latitude }}"
                            data-lng="{{ event.longitude }}" data-location-name="{{ event.location_name }}"
                            style="height: 200px; border-radius: 8px"></div>
                        </div>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- END EVENT DETAIL CONTENT -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 col-12 d-flex flex-column h-100">
    <div class="card shadow-sm border-0 rounded-3 journey-detail-card flex-fill mb-4">
      <div class="card-body p-0 d-flex flex-column h-100">
        <div class="card journey-detail-card shadow-sm border-0 rounded-3 h-100">
          <div class="card-body comments-section">
            <div class="comments-header">
              <h2><i class="bi bi-chat-dots"></i>Comments</h2>
              <span class="comment-count">{{ comments|length }}</span>
            </div>
            <div class="comments-list">
              {% if comments|length > 0 %}
              {% for comment in comments %}
              {% if comment.id == report.content_id %}
              <div id="reported-comment" class="comment-item rounded-3 px-2" style="background: #fbeaee !important;">
                <div class="comment-avatar">
                  <img
                    src="{% if comment.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + comment.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                    alt="{{ comment.username }}"
                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.username }}</span>
                    <span class="comment-time"><i class="bi bi-clock me-1"></i>{{ comment.created_at|timeago }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                  <!-- 좋아요/싫어요 등 기존 로직/데이터/조건문은 그대로 아래에 위치 -->
                  <div class="d-flex align-items-center gap-3 mt-1">
                    <i class="bi bi-hand-thumbs-up"></i> {{ comment.like_count|default(0) }}
                    <i class="bi bi-hand-thumbs-down"></i> {{ comment.dislike_count|default(0) }}
                    <span class="badge bg-danger ms-auto"><i class="bi bi-flag-fill me-1"></i>Reported</span>
                  </div>
                </div>
              </div>
              {% else %}
              <div class="comment-item">
                <div class="comment-avatar">
                  <img
                    src="{% if comment.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + comment.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                    alt="{{ comment.username }}"
                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                </div>
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.username }}</span>
                    <span class="comment-time"><i class="bi bi-clock me-1"></i>{{ comment.created_at|timeago }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                  <!-- 좋아요/싫어요 등 기존 로직/데이터/조건문은 그대로 아래에 위치 -->
                  <div class="d-flex align-items-center gap-3 mt-1">
                    <i class="bi bi-hand-thumbs-up"></i> {{ comment.like_count|default(0) }}
                    <i class="bi bi-hand-thumbs-down"></i> {{ comment.dislike_count|default(0) }}
                  </div>
                </div>
              </div>
              {% endif %}
              {% endfor %}
              {% else %}
              <div class="empty-state text-center w-100">
                <div class="empty-state-icon">
                  <i class="bi bi-chat-left-dots"></i>
                </div>
                <h4>No comments yet</h4>
                <p>Be the first to share your thoughts about this event!</p>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card shadow-sm border-0 rounded-3 flex-fill" style="min-height: 400px">
      <div class="card-body">
        <div class="section-header mb-3">
          <h2 class="fs-4 fw-bold text-dark mb-0">Report Details</h2>
        </div>
        <!-- Report Status Section -->
        <div class="mb-3 p-3 bg-light rounded border d-flex align-items-center gap-3">
          <i
            class="fs-3 {% if report.status == 'dismissed' %}bi bi-x-octagon-fill text-danger{% elif report.status == 'resolved' %}bi bi-check-circle-fill text-success{% elif report.escalated_to_admin %}bi bi-flag-fill text-warning{% elif report.is_banned %}bi bi-person-x-fill text-danger{% elif report.is_hidden %}bi bi-eye-slash-fill text-secondary{% else %}bi bi-clock-fill text-primary{% endif %}"></i>
          <div class="flex-grow-1">
            <h6 class="fw-bold mb-1 d-flex align-items-center gap-2" style="font-size: 1.1rem;">
              {% if report.status == 'dismissed' %}Report Dismissed
              {% elif report.status == 'resolved' %}Report Resolved
              {% elif report.escalated_to_admin %}Escalated for Review
              {% elif report.is_banned %}Action Taken - User Banned
              {% elif report.is_hidden %}Action Taken - Comment Hidden
              {% else %}Under Review{% endif %}

              {% if is_reporter and not is_report_manager %}
              <span class="badge bg-primary ms-2" style="font-size: 0.7em;">Your Report</span>
              {% endif %}
            </h6>
            <div class="text-muted small">
              {% if report.status == 'dismissed' %}
              {% if is_reporter %}Your report has been reviewed and dismissed. No further action will be taken.{% else
              %}This report has been dismissed and no further action will be taken.{% endif %}
              {% elif report.status == 'resolved' %}
              {% if is_reporter %}Your report has been reviewed and resolved. Thank you for helping keep our community
              safe.{% else %}This report has been reviewed and resolved.{% endif %}
              {% elif report.escalated_to_admin %}
              {% if is_reporter %}Your report has been escalated to an administrator for further review.{% else %}This
              report has been escalated to an administrator for further review.{% endif %}
              {% elif report.is_banned %}
              {% if is_reporter %}Action has been taken - the user associated with your report has been banned.{% else
              %}The user associated with this report has been banned.{% endif %}
              {% elif report.is_hidden %}
              {% if is_reporter %}Action has been taken - the reported comment has been hidden from public view.{% else
              %}The reported comment has been hidden from public view.{% endif %}
              {% else %}
              {% if is_reporter %}Your report is currently being reviewed by our moderation team. We'll update you once
              it's resolved.{% else %}This report is currently under review.{% endif %}
              {% endif %}
            </div>

            {% if is_reporter and report.reviewed_by and report.reviewed_at %}
            <div class="text-muted small mt-1">
              <i class="bi bi-person-check me-1"></i>Reviewed by staff on {{ report.reviewed_at | datetime }}
            </div>
            {% endif %}
          </div>
        </div>
        <div class="mb-3 p-3 bg-light rounded border">
          <div class="mb-2">
            <span class="fw-semibold">Reason:</span>
            <div class="bg-white border rounded px-3 py-2 mt-1" style="font-size: 15px;">
              {{ report.reason }}
            </div>
          </div>
          <div class="d-flex align-items-center gap-2 mb-2" style="font-size: 13px;">
            <span class="fw-semibold">Reported by:</span>
            <span class="fw-semibold" style="font-size: 14px;">{{ report.reporter_username }}</span>
            <span class="text-muted d-flex align-items-center" style="font-size: 12px;">
              <i class="bi bi-clock-history me-1"></i>{{ report.created_at | datetime }}
            </span>
          </div>
          {% if report.is_hidden or report.is_banned %}
          <div class="mt-2">
            <span class="fw-semibold">Actions Taken:</span>
            <div class="d-flex gap-2 mt-1">
              {% if report.is_hidden %}
              <span class="badge bg-warning text-dark"><i class="bi bi-eye-slash me-1"></i>Comment Hidden</span>
              {% endif %}
              {% if report.is_banned %}
              <span class="badge bg-danger"><i class="bi bi-person-x me-1"></i>User Banned</span>
              {% endif %}
            </div>
          </div>
          {% endif %}
        </div>
        {% if is_report_manager %}
        <div class="d-flex gap-2 mt-3 flex-wrap justify-content-end flex-row">
          {% if report.status == 'resolved' %}
          <button type="button" style="background: #28a745; color: white" class="btn btn-sm px-3 py-2 rounded-pill"
            onclick="submitReportForm('reopen')">
            <i class="bi bi-arrow-clockwise me-1"></i>Reopen Report
          </button>
          {% elif report.status == 'dismissed' %}
          <button type="button" style="background: #f0f4ff; color: #2d3a5b" class="btn btn-sm px-3 py-2 rounded-pill"
            onclick="submitReportForm('undismiss')">
            Undismiss Report
          </button>
          {% else %} {% if not report.is_hidden and not report.is_banned %}
          <button type="button" style="background: #f0f4ff; color: #2d3a5b" class="btn btn-sm px-3 py-2 rounded-pill"
            onclick="submitReportForm('dismiss')">
            Dismiss
          </button>
          {% endif %}

          <button type="button" style="background: #e9e9e9; color: #444" class="btn btn-sm px-3 py-2 rounded-pill"
            onclick="submitReportForm('hide')">
            {{ 'Unhide Comment' if report.is_hidden else 'Hide Comment' }}
          </button>

          {% if not can_change_user_roles() %}
          <button type="button" style="background: #000000; color: #fff" class="btn btn-sm px-3 py-2 rounded-pill"
            onclick="submitReportForm('escalate')">
            {{'Unreport to Admin' if report.escalated_to_admin else 'Report to
            admin'}}
          </button>
          {% else %}
          <button type="button" class="btn btn-sm px-3 py-2 btn-dark rounded-pill" onclick="submitReportForm('ban')">
            {{ 'Unban User' if report.is_banned else 'Ban User' }}
          </button>
          {% endif %} {% if report.is_hidden or report.is_banned %}
          <button type="button" style="background: #28a745; color: white"
            class="btn btn-sm px-3 py-2 rounded-pill fw-semibold" onclick="submitReportForm('resolve')">
            <i class="bi bi-check-circle me-1"></i>Mark as Resolved
          </button>
          {% endif %} {% endif %}
        </div>
        <form id="form-dismiss" action="{{ url_for('report.dismiss_comment_report', report_id=report.id) }}"
          method="post" style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>
        <form id="form-undismiss" action="{{ url_for('report.undismiss_comment_report', report_id=report.id) }}"
          method="post" style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>
        <form id="form-hide"
          action="{{ url_for('report.' + ('unhide_comment' if report.is_hidden else 'hide_comment'), comment_id=report.content_id, report_id=report.id) }}"
          method="post" style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>
        <form id="form-escalate"
          action="{{ url_for('report.' + ('unescalate_comment' if report.escalated_to_admin else 'escalate_comment'), report_id=report.id) }}"
          method="post" style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>
        <form id="form-ban"
          action="{{ url_for('report.' + ('unban_user' if report.is_banned else 'ban_user'), report_id=report.id) }}"
          method="post" style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>
        <form id="form-resolve" action="{{ url_for('report.resolve_report', report_id=report.id) }}" method="post"
          style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>

        <form id="form-reopen" action="{{ url_for('report.reopen_report', report_id=report.id) }}" method="post"
          style="display: none">
          <input type="hidden" name="back_url" value="{{ back_url | urlencode }}" />
        </form>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<script>
  function smartBack() {
    const btn = document.getElementById("backButton");

    function safeDecodeURIComponent(url) {
      try {
        return decodeURIComponent(decodeURIComponent(url));
      } catch {
        try {
          return decodeURIComponent(url);
        } catch {
          return url;
        }
      }
    }

    function isValidBackUrl(url) {
      return url && (url.startsWith("/") || url.startsWith(window.location.origin));
    }

    function navigateToUrl(url) {
      window.location.href = url;
    }

    // 1. First try the data-back attribute from the button
    const encodedAttrUrl = btn?.dataset.back;
    if (encodedAttrUrl) {
      const decodedAttrUrl = safeDecodeURIComponent(encodedAttrUrl);
      if (isValidBackUrl(decodedAttrUrl)) {
        navigateToUrl(decodedAttrUrl);
        return;
      }
    }

    // 2. Try URL parameter 'back'
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get("back");
    if (backUrl) {
      const decodedUrl = safeDecodeURIComponent(backUrl);
      if (isValidBackUrl(decodedUrl)) {
        navigateToUrl(decodedUrl);
        return;
      }
    }

    // 3. Check sessionStorage for preserved back URL (fallback for after form submissions)
    const preservedBackUrl = sessionStorage.getItem('reportDetailBackUrl');
    if (preservedBackUrl) {
      const decodedUrl = safeDecodeURIComponent(preservedBackUrl);
      if (isValidBackUrl(decodedUrl)) {
        sessionStorage.removeItem('reportDetailBackUrl'); // Clean up
        navigateToUrl(decodedUrl);
        return;
      }
    }

    // 4. Intelligent fallback based on user permissions and referrer
    const referrer = document.referrer;
    const currentDomain = window.location.origin;

    if (referrer && referrer.startsWith(currentDomain)) {
      if (referrer.includes("/report/manage") || referrer.includes("/admin")) {
        navigateToUrl('{{ url_for("report.get_reports") }}');
        return;
      } else if (referrer.includes("/report/list") || referrer.includes("/report")) {
        navigateToUrl('{{ url_for("report.get_my_reports") }}');
        return;
      }
    }

    // 5. Final fallback - default to reports page
    navigateToUrl('{{ url_for("report.get_my_reports") }}');
  }

  // Update back button text based on context
  function updateBackButtonText() {
    const backButtonText = document.getElementById("backButtonText");
    const btn = document.getElementById("backButton");

    function checkUrlForReportType(url) {
      if (url.includes("/report/manage") || url.includes("/admin")) {
        return "manage";
      } else if (url.includes("/report") && !url.includes("/report/detail") && !url.includes("/report/manage")) {
        return "my";
      }
      return null;
    }

    // First try to determine from the back URL data
    const encodedAttrUrl = btn?.dataset.back;
    if (encodedAttrUrl) {
      try {
        const decodedUrl = decodeURIComponent(decodeURIComponent(encodedAttrUrl));
        const reportType = checkUrlForReportType(decodedUrl);
        if (reportType === "manage") {
          backButtonText.textContent = "Back to Manage Reports";
          return;
        } else if (reportType === "my") {
          backButtonText.textContent = "Back to My Reports";
          return;
        }
      } catch (e) {
        // Fall through to other methods
      }
    }

    // Check URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get("back");
    if (backUrl) {
      try {
        const decodedUrl = decodeURIComponent(decodeURIComponent(backUrl));
        const reportType = checkUrlForReportType(decodedUrl);
        if (reportType === "manage") {
          backButtonText.textContent = "Back to Manage Reports";
          return;
        } else if (reportType === "my") {
          backButtonText.textContent = "Back to My Reports";
          return;
        }
      } catch (e) {
        // Fall through to other methods
      }
    }

    // Check sessionStorage for preserved back URL
    const preservedBackUrl = sessionStorage.getItem('reportDetailBackUrl');
    if (preservedBackUrl) {
      try {
        const decodedUrl = decodeURIComponent(decodeURIComponent(preservedBackUrl));
        const reportType = checkUrlForReportType(decodedUrl);
        if (reportType === "manage") {
          backButtonText.textContent = "Back to Manage Reports";
          return;
        } else if (reportType === "my") {
          backButtonText.textContent = "Back to My Reports";
          return;
        }
      } catch (e) {
        // Fall through to referrer check
      }
    }

    // Finally check referrer as fallback
    const referrer = document.referrer;
    const currentDomain = window.location.origin;

    if (referrer && referrer.startsWith(currentDomain)) {
      const reportType = checkUrlForReportType(referrer);
      if (reportType === "manage") {
        backButtonText.textContent = "Back to Manage Reports";
      } else if (reportType === "my") {
        backButtonText.textContent = "Back to My Reports";
      } else {
        backButtonText.textContent = "Back";
      }
    } else {
      // Default fallback
      backButtonText.textContent = "Back to Reports";
    }
  }

  // Preserve back URL before form submission to maintain it after page refresh
  function preserveBackUrlBeforeSubmission() {
    const btn = document.getElementById("backButton");
    const encodedAttrUrl = btn?.dataset.back;

    if (encodedAttrUrl) {
      sessionStorage.setItem('reportDetailBackUrl', encodedAttrUrl);
    } else {
      const urlParams = new URLSearchParams(window.location.search);
      const backUrl = urlParams.get("back");
      if (backUrl) {
        sessionStorage.setItem('reportDetailBackUrl', backUrl);
      }
    }
  }

  // Initialize on page load
  document.addEventListener("DOMContentLoaded", function () {
    updateBackButtonText();
    const reported = document.getElementById("reported-comment");
    if (reported) {
      reported.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    // Initialize map if coordinates exist
    initializeReportEventMap();

    // Initialize image modal functionality
    initializeImageModal();

    // Debug information for back URL handling (can be removed in production)
    if (window.location.search.includes('debug=1')) {
      console.log('Report Detail Debug Info:');
      console.log('- Referrer:', document.referrer);
      console.log('- Back URL param:', new URLSearchParams(window.location.search).get("back"));
      console.log('- Button data-back:', document.getElementById("backButton")?.dataset.back);
      console.log('- Preserved back URL:', sessionStorage.getItem('reportDetailBackUrl'));
    }
  });

  function submitReportForm(action) {
    // Preserve back URL before form submission to maintain it after page refresh
    preserveBackUrlBeforeSubmission();
    document.getElementById("form-" + action).submit();
  }

  // Initialize map for event location
  function initializeReportEventMap() {
    const mapContainer = document.getElementById("reportEventMap");
    if (!mapContainer) return;

    const lat = parseFloat(mapContainer.dataset.lat);
    const lng = parseFloat(mapContainer.dataset.lng);
    const locationName = mapContainer.dataset.locationName;

    if (!lat || !lng) return;

    try {
      // Initialize map
      const map = L.map("reportEventMap").setView([lat, lng], 15);

      // Add tile layer
      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "© OpenStreetMap contributors",
      }).addTo(map);

      // Add marker
      L.marker([lat, lng])
        .addTo(map)
        .bindPopup(locationName || "Event Location")
        .openPopup();
    } catch (error) {
      console.error("Error initializing map:", error);
      // Hide map container if initialization fails
      mapContainer.style.display = "none";
    }
  }

  // Initialize image modal functionality
  function initializeImageModal() {
    const imageButtons = document.querySelectorAll(".event-image-button");

    imageButtons.forEach((button) => {
      button.addEventListener("click", function () {
        const eventId = this.getAttribute("event-id");
        if (eventId) {
          showEventImagesModal(eventId);
        }
      });
    });
  }

  // Show event images in modal (using the same approach as event detail)
  async function showEventImagesModal(eventId) {
    try {
      const response = await fetch(`/event/image/${eventId}`);
      const html = await response.text();

      const modalResult = showModal("Event Images", html, {
        hideCloseButton: false,
        size: "large",
        hideActionButton: true,
      });

      // Configure modal for image gallery
      const modalDialog = document.querySelector("#commonModal .modal-dialog");
      if (modalDialog) {
        modalDialog.classList.add("modal-lg", "modal-dialog-image-gallery");
      }

      // Initialize gallery functionality
      setTimeout(() => {
        initializeImageGallery(document.getElementById("commonModal"));
      }, 200);
    } catch (error) {
      console.error("Error loading images:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Error loading images. Please try again.",
          "danger"
        );
      }
    }
  }

  // Initialize image gallery functionality (matching event detail implementation)
  function initializeImageGallery(container) {
    if (!container) return;

    const images = container.querySelectorAll(".thumbnail img");
    const mainImage = container.querySelector("#mainImage");
    const prevButton = container.querySelector("#prevImage");
    const nextButton = container.querySelector("#nextImage");
    const thumbnails = container.querySelectorAll(".thumbnail");
    const dots = container.querySelectorAll(".dot");

    let currentIndex = 0;

    // Thumbnail click handlers
    thumbnails.forEach((thumbnail, index) => {
      thumbnail.addEventListener("click", () => {
        showImage(index);
      });
    });

    // Dot click handlers
    dots.forEach((dot, index) => {
      dot.addEventListener("click", () => {
        showImage(index);
      });
    });

    // Navigation button handlers
    if (prevButton) {
      prevButton.addEventListener("click", () => {
        showImage(currentIndex > 0 ? currentIndex - 1 : images.length - 1);
      });
    }

    if (nextButton) {
      nextButton.addEventListener("click", () => {
        showImage(currentIndex < images.length - 1 ? currentIndex + 1 : 0);
      });
    }

    // Keyboard navigation
    container.addEventListener("keydown", (e) => {
      if (e.key === "ArrowLeft") {
        showImage(currentIndex > 0 ? currentIndex - 1 : images.length - 1);
      } else if (e.key === "ArrowRight") {
        showImage(currentIndex < images.length - 1 ? currentIndex + 1 : 0);
      }
    });

    function showImage(index) {
      if (index < 0 || index >= images.length) return;

      currentIndex = index;

      // Update main image
      if (mainImage && images[index]) {
        mainImage.src = images[index].src;
        mainImage.alt = images[index].alt;
      }

      // Update active thumbnail
      thumbnails.forEach((thumb, i) => {
        thumb.classList.toggle("active", i === index);
      });

      // Update active dot
      dots.forEach((dot, i) => {
        dot.classList.toggle("active", i === index);
      });
    }
  }
</script>

<style>
  .journey-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: all 0.2s ease;
  }

  .journey-link:hover {
    text-decoration: none;
    color: inherit;
  }

  .journey-link .border {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .journey-link:hover .border {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(78, 107, 255, 0.15) !important;
    background-color: rgba(0, 0, 0, 0.02);
  }

  .comments-scroll {
    max-height: 300px;
    overflow-y: auto;
  }

  @media (max-width: 600px) {
    .btn-sm.px-3 {
      padding-left: 0.5rem !important;
      padding-right: 0.5rem !important;
    }
  }

  .report-reason-scroll {
    max-height: 300px;
    overflow-y: auto;
  }

  /* Map styles */
  .event-map {
    border: 1px solid #ddd;
    border-radius: 8px;
  }

  .location-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
  }

  /* Event image styles */
  .image-wrapper {
    position: relative;
    display: block;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .image-wrapper:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .event-image-button {
    border: none;
    background: none;
    padding: 0;
    cursor: pointer;
    display: block;
    width: 100%;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
  }

  .event-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 8px;
  }

  .event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .event-image-button:hover .event-image img {
    transform: scale(1.05);
  }

  .image-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7) !important;
    color: white !important;
    font-size: 12px;
    font-weight: 600;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  /* No images message styling */
  [id^="noImagesMessage-"] {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 2rem;
  }
</style>

{% endblock %}