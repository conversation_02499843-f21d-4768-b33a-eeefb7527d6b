{% extends "base.html" %}

{% block title %}Report - Journey Unavailable - Footprints{% endblock %}

{% block content %}
<a href="javascript:void(0)" onclick="smartBack()" 
  data-back="{{ back | urlencode }}" 
  data-reports-url="{{ url_for('report.get_reports') }}"
  data-landing-url="{{ url_for('main.get_landing_page') }}"
  data-is-logged-in="{{ 'true' if g.current_user else 'false' }}"
  id="backButton"
  class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
  <i class="bi bi-arrow-left me-2"></i>
  <span id="backButtonText">Back to Reports</span>
</a>

<div class="row justify-content-center">
  <div class="col-lg-8 col-md-10 col-12">
    <div class="card shadow-sm border-0 rounded-3">
      <div class="card-body p-5 text-center">
        <!-- Icon -->
        <div class="mb-4">
          <i class="bi bi-lock-fill text-warning" style="font-size: 4rem;"></i>
        </div>
        
        <!-- Title -->
        <h1 class="fs-2 fw-bold text-dark mb-3">Journey Made Private</h1>
        
        <!-- Description -->
        <div class="mb-4">
          <p class="fs-5 text-muted mb-3">
            The journey containing this reported event has been made private by the user.
          </p>
          <p class="text-muted">
            Since the content is no longer publicly accessible, this report may no longer be relevant. 
            {% if can_manage_content() %}
            You can dismiss this report to remove it from your active reports list.
            {% endif %}
          </p>
        </div>

        <!-- Report Info Card -->
        <div class="card bg-light border-0 rounded-3 mb-4">
          <div class="card-body p-4">
            <h5 class="fw-bold mb-3">Report Information</h5>
            <div class="row text-start">
              <div class="col-md-6 mb-3">
                <h6 class="text-uppercase text-muted small fw-bold mb-1">Report ID</h6>
                <p class="mb-0">#{{ report.id }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <h6 class="text-uppercase text-muted small fw-bold mb-1">Reported By</h6>
                <p class="mb-0">{{ report.reporter_username }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <h6 class="text-uppercase text-muted small fw-bold mb-1">Report Date</h6>
                <p class="mb-0">{{ report.created_at | date }}</p>
              </div>
              <div class="col-md-6 mb-3">
                <h6 class="text-uppercase text-muted small fw-bold mb-1">Status</h6>
                {% if report.status == 'dismissed' %}
                <span class="badge bg-secondary text-white py-1 px-2">
                  <i class="bi bi-x-circle-fill me-1"></i>Dismissed
                </span>
                {% else %}
                <span class="badge bg-warning text-dark py-1 px-2">
                  <i class="bi bi-lock-fill me-1"></i>Journey Private
                </span>
                {% endif %}
              </div>
              {% if report.reason %}
              <div class="col-12">
                <h6 class="text-uppercase text-muted small fw-bold mb-1">Report Reason</h6>
                <p class="mb-0">{{ report.reason }}</p>
              </div>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Action Buttons or Status -->
        {% if report.status == 'dismissed' %}
        <!-- Report Already Dismissed - Show Status -->
        <div class="text-center mb-4">
          <div class="alert alert-secondary d-inline-block px-4 py-3 rounded-3">
            <i class="bi bi-check-circle-fill me-2 text-success"></i>
            <strong>This report has been dismissed</strong>
          </div>
        </div>
        
        <!-- Only Back Button -->
        {% else %}
        <!-- Action Buttons for Active Reports -->
        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
          <!-- Dismiss Report Button -->
          <!-- Only show the form if the user is a content manager -->
          {% if can_manage_content() %}
          <form method="POST" action="{{ url_for('report.dismiss_comment_report', report_id=report.id) }}" 
                onsubmit="return confirm('Are you sure you want to dismiss this report? This action cannot be undone.')" 
                class="d-inline">
            <input type="hidden" name="back_url" value="{{ back | urlencode }}">
            <button type="submit" class="btn btn-danger btn-lg px-4">
              <i class="bi bi-x-circle me-2"></i>Dismiss Report
            </button>
          </form>
          {% endif %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<script>
  function smartBack() {
    const btn = document.getElementById("backButton");
    const encodedAttrUrl = btn?.dataset.back;

    function safeDecodeURIComponent(url) {
      try {
        return decodeURIComponent(decodeURIComponent(url));
      } catch {
        try {
          return decodeURIComponent(url);
        } catch {
          return url;
        }
      }
    }

    if (encodedAttrUrl) {
      const decodedAttrUrl = safeDecodeURIComponent(encodedAttrUrl);
      if (
        decodedAttrUrl.startsWith("/") ||
        decodedAttrUrl.startsWith(window.location.origin)
      ) {
        window.location.href = decodedAttrUrl;
        return;
      }
    }

    const urlParams = new URLSearchParams(window.location.search);
    const backUrl = urlParams.get("back");
    if (backUrl) {
      const decodedUrl = safeDecodeURIComponent(backUrl);
      if (
        decodedUrl.startsWith("/") ||
        decodedUrl.startsWith(window.location.origin)
      ) {
        window.location.href = decodedUrl;
        return;
      }
    }

    if (window.history.length > 1) {
      window.history.back();
      setTimeout(() => {
        window.history.back();
      }, 50);
      return;
    }

    // Default fallback
    const isLoggedIn = btn.dataset.isLoggedIn === 'true';
    if (isLoggedIn) {
      window.location.href = btn.dataset.reportsUrl;
    } else {
      window.location.href = btn.dataset.landingUrl;
    }
  }

  // Update back button text based on context
  function updateBackButtonText() {
    const backButtonText = document.getElementById("backButtonText");
    const backButtonText2 = document.getElementById("backButtonText2");
    const referrer = document.referrer;
    const currentDomain = window.location.origin;

    let buttonText = "Back to Reports";

    if (referrer && referrer.startsWith(currentDomain)) {
      if (referrer.includes("/report/list") || referrer.includes("/report")) {
        if (referrer.includes("my_reports")) {
          buttonText = "Back to My Reports";
        } else {
          buttonText = "Back to Reports";
        }
      } else if (
        referrer.includes("/admin") ||
        referrer.includes("/dashboard")
      ) {
        buttonText = "Back to Dashboard";
      } else {
        buttonText = "Back";
      }
    }

    if (backButtonText) backButtonText.textContent = buttonText;
    if (backButtonText2) backButtonText2.textContent = buttonText;
  }

  // Initialize on page load
  document.addEventListener("DOMContentLoaded", function () {
    updateBackButtonText();
  });
</script>
{% endblock %} 