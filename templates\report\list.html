{% extends "base.html" %} {% block title %}Reports Management{% endblock %} {%
from "components/pagination.html" import render_pagination %} {% block content
%} {% include 'components/modal.html' %} {% if not active_tab %} {% if
can_change_user_roles() %} {% set active_tab = 'escalated' %} {% else %} {% set
active_tab = 'active' %} {% endif %} {% endif %}

<div class="container">
  <div class="row">
    <div class="col-12 mb-4">
      <div class="d-flex align-items-center">
        <h1 class="display-6 fw-bold">
          <span class="position-relative">
            {% if 'manage' in request.path %} Reports Management {% else %} My
            Reports {% endif %}
            <span
              class="position-absolute start-0 bottom-0"
              style="
                height: 6px;
                width: 60%;
                background-color: #4e6bff;
                opacity: 0.2;
                border-radius: 3px;
              "
            ></span>
          </span>
        </h1>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-between align-items-center mb-4">
    <h5 class="fw-bold">Total ({{ total_count | default(0) }})</h5>

    <!-- Search Form -->
    <div class="d-flex gap-2 align-items-center">
      <form
        action="{{ url_for('report.get_reports') }}"
        method="get"
        class="d-flex gap-2"
      >
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            name="q"
            placeholder="Search reports..."
            value="{{ search_term }}"
          />
          <input type="hidden" name="active_tab" value="{{ active_tab }}" />
          {% if search_term %}
          <a
            href="{{ url_for('report.get_reports', active_tab=active_tab) }}"
            class="btn btn-outline-secondary border-start-0"
          >
            <i class="bi bi-x"></i>
          </a>
          {% endif %}
        </div>
        <button
          type="submit"
          class="btn"
          style="background-color: black; color: white; border: none"
        >
          Search
        </button>
      </form>
    </div>
  </div>

  <div class="mb-4">
    <div class="border-bottom position-relative">
      <div class="d-flex">
        <!-- All tab -->
        <div class="me-4 position-relative">
          <a
            href="{{ my_report_tab_url('all') if is_my_report_page else url_for('report.get_reports', active_tab='all', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'all' %}text-primary{% else %}text-secondary{% endif %}"
          >
            All
          </a>
          {% if active_tab == 'all' %}
          <div
            class="position-absolute bottom-0 start-0 w-100"
            style="height: 3px; background-color: #6366f1"
          ></div>
          {% endif %}
        </div>
        {% if not is_my_report_page and can_change_user_roles() %}
        <div class="me-4 position-relative">
          <a
            href="{{ url_for('report.get_reports', active_tab='escalated', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'escalated' %}text-primary{% else %}text-secondary{% endif %}"
          >
            <span class="d-sm-none">Admin</span>
            <span class="d-none d-sm-inline">Escalated to Admin</span>
          </a>
          {% if active_tab == 'escalated' %}
          <div
            class="position-absolute bottom-0 start-0 w-100"
            style="height: 3px; background-color: #6366f1"
          ></div>
          {% endif %}
        </div>
        {% endif %}

        <div class="me-4 position-relative">
          <a
            href="{{ my_report_tab_url('active') if is_my_report_page else url_for('report.get_reports', active_tab='active', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'active' %}text-primary{% else %}text-secondary{% endif %}"
          >
            Active
          </a>
          {% if active_tab == 'active' %}
          <div
            class="position-absolute bottom-0 start-0 w-100"
            style="height: 3px; background-color: #6366f1"
          ></div>
          {% endif %}
        </div>

        <div class="me-4 position-relative">
          <a
            href="{{ my_report_tab_url('resolved') if is_my_report_page else url_for('report.get_reports', active_tab='resolved', q=search_term) }}"
            class="text-decoration-none fw-medium px-2 py-3 d-inline-block {% if active_tab == 'resolved' %}text-primary{% else %}text-secondary{% endif %}"
          >
            Resolved
          </a>
          {% if active_tab == 'resolved' %}
          <div
            class="position-absolute bottom-0 start-0 w-100"
            style="height: 3px; background-color: #6366f1"
          ></div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  {% if comment_reports %}
  <div class="row g-4">
    <div class="col-12">
      <div class="table-responsive">
        <table class="table align-middle">
          <thead>
            <tr>
              <th>No.</th>
              <th>Reason</th>
              <th>Created</th>
              <th class="d-none d-sm-table-cell">Reporter</th>
              <th class="d-none d-sm-table-cell">Status</th>
              <th class="d-none d-sm-table-cell">Moderation</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {% for comment_report in comment_reports %}
            <tr>
              <td>{{ (offset or 0) + loop.index }}</td>
              <td
                class="text-truncate reason-td"
                title="{{ comment_report.reason }}"
              >
                {{ comment_report.reason }}
              </td>
              <td>
                <span class="created-formatdate"
                  >{{ comment_report.created_at|formatdate }}</span
                >
                <span class="created-datetime"
                  >{{ comment_report.created_at|datetime }}</span
                >
              </td>
              <td class="d-none d-sm-table-cell">
                {{ comment_report.reporter_username }}
              </td>
              <td class="d-none d-sm-table-cell">
                {% if comment_report['status'] == 'resolved' %}
                <span
                  class="badge bg-success-subtle text-success p-2 rounded-pill"
                  style="min-width: 90px; text-align: center"
                >
                  <i class="bi bi-check-circle-fill me-1"></i>Resolved
                </span>
                {% elif comment_report['escalated_to_admin'] == 1 %}
                <span
                  class="badge bg-primary-subtle text-primary p-2 rounded-pill"
                  style="min-width: 90px; text-align: center"
                >
                  <i class="bi bi-arrow-up-circle me-1"></i>To Admin
                </span>
                {% elif comment_report['status'] == 'active' %}
                <span
                  class="badge bg-info-subtle text-info p-2 rounded-pill"
                  style="min-width: 90px; text-align: center"
                >
                  <i class="bi bi-star-fill me-1"></i>Active
                </span>

                {% elif comment_report['status'] == 'open' %}
                <span
                  class="badge bg-secondary-subtle text-secondary p-2 rounded-pill"
                  style="min-width: 90px; text-align: center"
                >
                  <i class="bi bi-envelope-open me-1"></i>Open
                </span>
                {% elif comment_report['status'] == 'dismissed' %}
                <span
                  class="badge bg-secondary-subtle text-secondary p-2 rounded-pill"
                  style="min-width: 90px; text-align: center"
                >
                  <i class="bi bi-x-circle me-1"></i>Dismissed
                </span>
                {% else %}
                <span
                  class="badge bg-secondary-subtle text-secondary p-2 rounded-pill"
                  style="min-width: 90px; text-align: center"
                >
                  <i class="bi bi-question-circle me-1"></i>{{
                  comment_report['status']|capitalize }}
                </span>
                {% endif %}
              </td>
              <td class="d-none d-sm-table-cell">
                <div
                  class="d-flex flex-column align-items-start gap-1"
                  style="min-width: 90px"
                >
                  {% if comment_report.is_banned %}
                  <span
                    class="badge bg-danger-subtle text-danger p-2 rounded-pill"
                  >
                    <i class="bi bi-person-x me-1"></i>User Banned
                  </span>
                  {% endif %} {% if comment_report.is_hidden %}
                  <span
                    class="badge bg-warning-subtle text-warning p-2 rounded-pill"
                  >
                    <i class="bi bi-eye-slash me-1"></i>Hidden
                  </span>
                  {% endif %} {% if not comment_report.is_hidden and not
                  comment_report.is_banned %}
                  <span class="text-center">-</span>
                  {% endif %}
                </div>
              </td>
              <td>
                <a
                  href="{{ url_for('report.get_report_details', report_id=comment_report['id'], back=request.url | urlencode) }}"
                  ><button class="btn btn-sm btn-outline-dark">View</button></a
                >
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      {{ render_pagination(page, total_pages, 'report.get_reports',
      q=search_term, filter=filter, active_tab=active_tab) }}
    </div>
  </div>
  {% else %}
  <div
    class="alert rounded-4"
    style="
      background-color: rgba(78, 107, 255, 0.1);
      color: #4e6bff;
      border: 1px solid rgba(78, 107, 255, 0.2);
    "
  >
    <div class="d-flex align-items-center">
      <i class="bi bi-info-circle-fill me-3 fs-4"></i>
      <p class="mb-0">No matching reports found.</p>
    </div>
  </div>
  {% endif %}
</div>

<style>
  .reason-td {
    width: 400px;
    max-width: 400px;
  }

  @media (max-width: 799.9px) {
    .reason-td {
      max-width: 100px;
    }
  }

  @media (min-width: 800px) and (max-width: 1400px) {
    .reason-td {
      max-width: 200px;
    }
  }

  .created-formatdate {
    display: none;
  }

  .created-datetime {
    display: inline;
  }

  @media (max-width: 991.98px) {
    .created-formatdate {
      display: inline;
    }

    .created-datetime {
      display: none;
    }
  }
</style>

{% endblock %}
