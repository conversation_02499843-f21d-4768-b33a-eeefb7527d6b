from datetime import datetime
import pytest
from flask import Flask
from app import create_app 

@pytest.fixture
def app():
    """
    Provides the Flask app instance for testing.
    """
    app = create_app()
    app.config.update({
        "TESTING": True,
        "SERVER_NAME": "localhost",
    })
    with app.app_context():
        yield app

@pytest.fixture
def client(app):
    """
    Provides a test client to simulate HTTP requests.
    """
    return app.test_client()

@pytest.fixture
def runner(app):
    """
    Provides a CLI runner if you're testing Flask CLI commands.
    """
    return app.test_cli_runner()

@pytest.fixture
def mock_execute_query(mocker):
    """Fixture to mock the `execute_query` function"""
    return mocker.patch('utils.db_utils.execute_query', autospec=True)

@pytest.fixture
def mock_journey_data(mocker):
    return mocker.patch('services.journey_service.journey_data')


@pytest.fixture
def mock_user_data(mocker):
    return mocker.patch('services.journey_service.user_data')

@pytest.fixture
def mock_event_data(mocker):
    return mocker.patch('data.event_data')

@pytest.fixture
def mock_location_data(mocker):
    return mocker.patch('data.location_data')

@pytest.fixture
def mock_journey():
    return {
        "id": 1,
        "title": "Test Journey",
        "description": "Test Description",
        "start_date": datetime.now(),
        "is_public": False,
        "user_id": 1
    }

@pytest.fixture
def mock_event():
    return {
        "id": 1,
        "title": "Test Event",
        "description": "Test Description",
        "location_name": "Test Location",
        "start_datetime": datetime.now(),
        "end_datetime": datetime.now(),
        "journey_id": 1,
        "user_id": 1
    }
    