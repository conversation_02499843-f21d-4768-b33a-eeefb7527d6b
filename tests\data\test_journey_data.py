from datetime import date

from data.journey_data import (
    create_journey,
    get_journey,
    get_private_journeys,
    get_public_journeys,
    get_published_journey,
    get_published_journeys,
    get_all_journeys,
    get_public_journey_count,
    get_published_journey_count,
    get_hidden_journeys,
    update_journey,
    update_journey_visibility,
    update_journey_hidden_status,
    update_journey_no_edits,
    update_journey_cover_image,
    delete_journey,
    search_my_journeys,
    search_journeys,
    search_journeys_by_location,
    count_journey_events,
    check_journey_exists,
    check_journey_owner,
    check_journey_no_edits,
    get_user_public_journeys,
)


# =============================================================================
# Create journey tests
# =============================================================================

def test_create_journey_default_params(mocker):
    """Test creating a journey with default parameters."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=123)
    
    result = create_journey(1, "New Journey", "Journey Description", date(2025, 1, 1))
    
    assert result == 123
    mock_execute_query.assert_called_once_with(
        """
    INSERT INTO journeys
    (user_id, title, description, start_date, visibility, cover_image, no_edits)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """,
        (1, "New Journey", "Journey Description", date(2025, 1, 1), 'private', None, False)
    )

def test_create_journey_all_params(mocker):
    """Test creating a journey with all parameters."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=456)
    
    result = create_journey(
        user_id=2,
        title="Public Journey",
        description="A public journey",
        start_date=date(2025, 2, 1),
        visibility='public',
        cover_image='path/to/image.jpg',
        no_edits=True
    )
    
    assert result == 456
    mock_execute_query.assert_called_once_with(
        """
    INSERT INTO journeys
    (user_id, title, description, start_date, visibility, cover_image, no_edits)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """,
        (2, "Public Journey", "A public journey", date(2025, 2, 1), 'public', 'path/to/image.jpg', True)
    )


# =============================================================================
# Get journey tests
# =============================================================================

def test_get_journey_found(mocker):
    """Test getting an existing journey."""
    expected_result = {
        'id': 1,
        'title': 'Test Journey',
        'username': 'testuser',
        'profile_image': 'profile.jpg',
        'cover_image': 'cover.jpg',
        'event_image': 'event.jpg'
    }
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = get_journey(1)
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.profile_image,
           j.cover_image AS cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC
            LIMIT 1) AS event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.id = %s
    """,
        (1,),
        fetch_one=True
    )

def test_get_journey_not_found(mocker):
    """Test getting a non-existent journey."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=None)
    
    result = get_journey(999)
    
    assert result is None


# =============================================================================
# Get private journey tests
# =============================================================================

def test_get_private_journeys_success(mocker):
    """Test getting private journeys for a user."""
    expected_result = [
        {
            'id': 1,
            'title': 'Private Journey 1',
            'username': 'testuser',
            'cover_image': 'cover1.jpg',
            'event_image': 'event1.jpg',
            'event_count': 5
        },
        {
            'id': 2,
            'title': 'Private Journey 2',
            'username': 'testuser',
            'cover_image': None,
            'event_image': None,
            'event_count': 0
        }
    ]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = get_private_journeys(1)
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) AS event_image,
           (SELECT COUNT(*) FROM events e WHERE e.journey_id = j.id) AS event_count
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.user_id = %s
    ORDER BY j.start_date DESC
    """,
        (1,),
        fetch_all=True
    )

def test_get_private_journeys_empty(mocker):
    """Test getting private journeys when none exist."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=None)
    
    result = get_private_journeys(1)
    
    assert result == []


# =============================================================================
# Get public journey tests
# =============================================================================

def test_get_public_journeys_default_params(mocker):
    """Test getting public journeys with default parameters."""
    expected_result = [{'id': 1, 'title': 'Public Journey'}]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = get_public_journeys()
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (50, 0),
        fetch_all=True
    )

def test_get_public_journeys_custom_params(mocker):
    """Test getting public journeys with custom limit and offset."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=[])
    
    result = get_public_journeys(limit=20, offset=10)
    
    assert result == []
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (20, 10),
        fetch_all=True
    )


# =============================================================================
# Get published journey tests
# =============================================================================

def test_get_published_journey_found(mocker):
    """Test getting a published journey with premium filter."""
    expected_result = {
        'id': 1,
        'title': 'Published Journey',
        'username': 'publisher',
        'event_image': 'published_event.jpg'
    }
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)

    # Mock the PermissionGroups import at the utils.permissions level
    mock_permission_groups = mocker.patch('utils.permissions.PermissionGroups')
    mock_permission_groups.STAFF = ['admin', 'editor', 'moderator', 'support_tech']

    result = get_published_journey(1)

    assert result == expected_result
    # Note: We can't easily test the exact query due to f-string formatting with staff roles
    # but we can verify the function was called with the journey_id as the second argument
    mock_execute_query.assert_called_once()
    call_args = mock_execute_query.call_args
    assert call_args[0][1] == (1,)  # Second argument should be the parameters tuple
    assert call_args[1]['fetch_one'] == True


# =============================================================================
# Get published journeys tests
# =============================================================================

def test_get_published_journeys(mocker):
    """Test getting all published journeys with premium filter."""
    expected_result = [{'id': 1, 'title': 'Published Journey'}]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)

    # Mock the PermissionGroups import at the utils.permissions level
    mock_permission_groups = mocker.patch('utils.permissions.PermissionGroups')
    mock_permission_groups.STAFF = ['admin', 'editor', 'moderator', 'support_tech']

    result = get_published_journeys(limit=25, offset=5)

    assert result == expected_result
    # Note: We can't easily test the exact query due to f-string formatting with staff roles
    # but we can verify the function was called with correct parameters
    mock_execute_query.assert_called_once()
    call_args = mock_execute_query.call_args
    assert call_args[0][1] == (25, 5)  # Second argument should be the parameters tuple
    assert call_args[1]['fetch_all'] == True


# =============================================================================
# Get all journeys tests
# =============================================================================

def test_get_all_journeys(mocker):
    """Test getting all journeys."""
    expected_result = [{'id': 1, 'title': 'Any Journey'}]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = get_all_journeys(100, 25)
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (100, 25),
        fetch_all=True
    )


# =============================================================================
# Journey count tests
# =============================================================================

def test_get_public_journey_count(mocker):
    """Test getting public journey count."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 42})
    
    result = get_public_journey_count()
    
    assert result == 42
    mock_execute_query.assert_called_once_with(
        """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE (visibility = 'public' OR visibility = 'published') AND is_hidden = FALSE
    """,
        fetch_one=True
    )

def test_get_published_journey_count(mocker):
    """Test getting published journey count with premium filter."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 15})

    # Mock the PermissionGroups import at the utils.permissions level
    mock_permission_groups = mocker.patch('utils.permissions.PermissionGroups')
    mock_permission_groups.STAFF = ['admin', 'editor', 'moderator', 'support_tech']

    result = get_published_journey_count()

    assert result == 15
    # Note: We can't easily test the exact query due to f-string formatting with staff roles
    # but we can verify the function was called
    mock_execute_query.assert_called_once()
    call_args = mock_execute_query.call_args
    assert call_args[1]['fetch_one'] == True

def test_get_journey_count_none_result(mocker):
    """Test getting journey count when query returns None."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=None)
    
    result = get_public_journey_count()
    
    assert result == 0


# =============================================================================
# Get hidden journeys tests
# =============================================================================

def test_get_hidden_journeys(mocker):
    """Test getting hidden journeys."""
    expected_result = [
        {'id': 1, 'title': 'Hidden Journey 1', 'username': 'user1'},
        {'id': 2, 'title': 'Hidden Journey 2', 'username': 'user2'}
    ]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = get_hidden_journeys()
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.is_hidden = TRUE
    ORDER BY j.user_id, j.updated_at DESC
    """,
        fetch_all=True
    )


# =============================================================================
# Update journeys tests
# =============================================================================

def test_update_journey_single_field(mocker):
    """Test updating a single journey field."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey(1, title="New Title")
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET title = %s
    WHERE id = %s
    """,
        ["New Title", 1]
    )

def test_update_journey_multiple_fields(mocker):
    """Test updating multiple journey fields."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey(
        journey_id=1,
        title="Updated Title",
        description="Updated Description",
        visibility='public',
        cover_image='new_cover.jpg',
        no_edits=True
    )
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET title = %s, description = %s, visibility = %s, cover_image = %s, no_edits = %s
    WHERE id = %s
    """,
        ["Updated Title", "Updated Description", 'public', 'new_cover.jpg', True, 1]
    )

def test_update_journey_no_updates(mocker):
    """Test updating journey with no parameters."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query')
    
    result = update_journey(1)
    
    assert result == 0
    mock_execute_query.assert_not_called()

def test_update_journey_with_start_date(mocker):
    """Test updating journey with start date."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey(1, start_date=date(2025, 3, 15))
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET start_date = %s
    WHERE id = %s
    """,
        [date(2025, 3, 15), 1]
    )


# =============================================================================
# Update journey fields tests
# =============================================================================

def test_update_journey_visibility(mocker):
    """Test updating journey visibility."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey_visibility(1, 'published')
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET visibility = %s
    WHERE id = %s
    """,
        ('published', 1)
    )

def test_update_journey_hidden_status(mocker):
    """Test updating journey hidden status."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey_hidden_status(1, True)
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET is_hidden = %s
    WHERE id = %s
    """,
        (True, 1)
    )

def test_update_journey_no_edits(mocker):
    """Test updating journey no_edits flag."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey_no_edits(1, True)
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET no_edits = %s
    WHERE id = %s
    """,
        (True, 1)
    )

def test_update_journey_cover_image(mocker):
    """Test updating journey cover image."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey_cover_image(1, 'new_cover.jpg')
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET cover_image = %s
    WHERE id = %s
    """,
        ('new_cover.jpg', 1)
    )

def test_update_journey_cover_image_remove(mocker):
    """Test removing journey cover image."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = update_journey_cover_image(1, None)
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    UPDATE journeys
    SET cover_image = %s
    WHERE id = %s
    """,
        (None, 1)
    )


# =============================================================================
# Delete journey tests
# =============================================================================

def test_delete_journey(mocker):
    """Test deleting a journey."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=1)
    
    result = delete_journey(1)
    
    assert result == 1
    mock_execute_query.assert_called_once_with(
        """
    DELETE FROM journeys
    WHERE id = %s
    """,
        (1,)
    )


# =============================================================================
# Search journey tests
# =============================================================================

def test_search_my_journeys(mocker):
    """Test searching user's own journeys."""
    expected_result = [{'id': 1, 'title': 'My Test Journey'}]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = search_my_journeys(1, "Test", limit=25, offset=5)
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT DISTINCT j.*,
           u.username,
           u.first_name,
           u.last_name,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) AS event_image,
           (SELECT COUNT(*)
            FROM events e
            WHERE e.journey_id = j.id) AS event_count
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    LEFT JOIN events e ON j.id = e.journey_id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE j.user_id = %s
      AND j.is_hidden = FALSE
      AND (
        j.title LIKE %s OR
        j.description LIKE %s OR
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
      )
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (1, '%Test%', '%Test%', '%Test%', 25, 5),
        fetch_all=True
    )

def test_search_journeys(mocker):
    """Test searching public journeys."""
    expected_result = [{'id': 1, 'title': 'Public Test Journey'}]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = search_journeys("Test")
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT DISTINCT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    LEFT JOIN events e ON j.id = e.journey_id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
      AND (
        j.title LIKE %s OR
        j.description LIKE %s OR
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
      )
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        ('%Test%', '%Test%', '%Test%', 50, 0),
        fetch_all=True
    )

def test_search_journeys_by_location(mocker):
    """Test searching journeys by location."""
    expected_result = [{'id': 1, 'title': 'Beach Journey'}]
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    result = search_journeys_by_location(123, limit=25, offset=10)
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT DISTINCT j.*, u.username, u.first_name, u.last_name, j.cover_image
    FROM journeys j
    JOIN events e ON j.id = e.journey_id
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
      AND e.location_id = %s
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (123, 25, 10),
        fetch_all=True
    )


# =============================================================================
# Count journey events tests
# =============================================================================

def test_count_journey_events(mocker):
    """Test counting events in a journey."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 7})
    
    result = count_journey_events(1)
    
    assert result == 7
    mock_execute_query.assert_called_once_with(
        """
    SELECT COUNT(*) as count
    FROM events
    WHERE journey_id = %s
    """,
        (1,),
        fetch_one=True
    )

def test_count_journey_events_none_result(mocker):
    """Test counting events when query returns None."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=None)
    
    result = count_journey_events(1)
    
    assert result == 0


# =============================================================================
# Journey validation tests
# =============================================================================

def test_check_journey_exists_true(mocker):
    """Test checking if journey exists - found."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 1})
    
    result = check_journey_exists(1)
    
    assert result is True
    mock_execute_query.assert_called_once_with(
        """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE id = %s
    """,
        (1,),
        fetch_one=True
    )

def test_check_journey_exists_false(mocker):
    """Test checking if journey exists - not found."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 0})
    
    result = check_journey_exists(999)
    
    assert result is False

def test_check_journey_owner_true(mocker):
    """Test checking if user owns journey - is owner."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 1})
    
    result = check_journey_owner(1, 2)
    
    assert result is True
    mock_execute_query.assert_called_once_with(
        """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE id = %s AND user_id = %s
    """,
        (1, 2),
        fetch_one=True
    )

def test_check_journey_owner_false(mocker):
    """Test checking if user owns journey - not owner."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'count': 0})
    
    result = check_journey_owner(1, 3)
    
    assert result is False

def test_check_journey_no_edits_true(mocker):
    """Test checking if journey has no_edits flag - is set."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'no_edits': True})
    
    result = check_journey_no_edits(1)
    
    assert result is True
    mock_execute_query.assert_called_once_with(
        """
    SELECT no_edits
    FROM journeys
    WHERE id = %s
    """,
        (1,),
        fetch_one=True
    )

def test_check_journey_no_edits_false(mocker):
    """Test checking if journey has no_edits flag - not set."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value={'no_edits': False})
    
    result = check_journey_no_edits(1)
    
    assert result is False

def test_check_journey_no_edits_none_result(mocker):
    """Test checking no_edits flag when query returns None."""
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=None)
    
    result = check_journey_no_edits(999)
    
    assert result is False


# =============================================================================
# User specific public/published journey tests
# =============================================================================



def test_get_user_public_journeys(mocker):
    """Test getting public or published journeys for a specific user."""
    expected_result = [
        {
            'id': 1,
            'username': 'testuser',
            'first_name': 'Test',
            'last_name': 'User',
            'profile_image': 'profile.jpg',
            'cover_image': 'cover.jpg',
            'event_image': 'event.jpg',
        }
    ]
    
    mock_execute_query = mocker.patch('data.journey_data.execute_query', return_value=expected_result)
    
    user_id = 42
    limit = 10
    offset = 0
    
    result = get_user_public_journeys(user_id, limit, offset)
    
    assert result == expected_result
    mock_execute_query.assert_called_once_with(
        """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published')
      AND j.is_hidden = FALSE
      AND j.user_id = %s
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """,
        (user_id, limit, offset),
        fetch_all=True
    )
