# from flask import url_for
# from datetime import date, datetime

# def test_get_private_journeys(client, mocker):

#     with client.session_transaction() as session:
#         session['user_id'] = 1

#     mock_journey = {
#         "id": 1,
#         "title": "Test Journey",
#         "start_date": datetime.now(),
#         "event_count": 2,
#         "updated_at": datetime.now(),
#         "description": "Test description",
#         "is_public": False,
#         "event_photo": None
#     }

#     mock_get_journeys = mocker.patch(
#         'services.journey_service.get_private_journeys',  
#         return_value=[mock_journey]
#     )

#     response = client.get(url_for('journey.get_private_journeys', page=1))

#     mock_get_journeys.assert_called_once()
#     assert response.status_code == 200
#     assert b'Test Journey' in response.data


# # Test for /journey/private/new route (POST)
# def test_create_journey(client, mocker):
#     with client.session_transaction() as session:
#         session['user_id'] = 1
    
#     mock_journey_service = mocker.patch('services.journey_service.create_journey')
#     mock_journey_service.return_value = (True, "Journey created successfully!", 1)
    
#     response = client.post(url_for('journey.create_journey'), data={
#         'title': 'New Journey',
#         'description': 'A new journey description',
#         'start_date': '2025-01-01',
#         'is_public': 'True'
#     })
    
#     assert response.status_code == 302
#     assert response.headers['Location'] in url_for('journey.get_private_journeys') 
#     mock_journey_service.assert_called_once_with(
#         user_id=1,
#         title='New Journey',
#         description='A new journey description',
#         start_date='2025-01-01',
#         is_public=True
#     )

# # Test for /journey/private/<journey_id> route (GET)
# def test_get_private_journey(client, mocker):
#     with client.session_transaction() as session:
#         session['user_id'] = 1

#     mock_journey_service = mocker.patch('services.journey_service.get_journey')
#     mock_event_service = mocker.patch('services.event_service.get_journey_events')

#     mock_journey_service.return_value = (True, "Journey fetched", {"id": 1, "title": "Test Journey", "start_date": date.today()})
#     mock_event_service.return_value = (True, "Events fetched", [{"id": 1, "name": "Test Event", "start_datetime": datetime.now()}])
    
#     response = client.get(url_for('journey.get_private_journey', journey_id=1))
    
#     assert response.status_code == 200
#     assert b'Test Journey' in response.data
#     mock_journey_service.assert_called_once_with(journey_id=1, user_id=1)
#     mock_event_service.assert_called_once_with(journey_id=1, user_id=1)


# # Test for /journey/private/<journey_id>/edit route (GET)
# def test_get_journey_edit_form(client, mocker):
#     mock_journey_service = mocker.patch('routes.journey_routes.journey_service.get_journey')
#     mock_event_service = mocker.patch('routes.journey_routes.event_service.get_journey_events')
#     mock_is_user_blocked = mocker.patch('routes.journey_routes.user_service.is_user_blocked')

#     mock_journey_service.return_value = (True, "Journey fetched", {
#         "id": 1, 
#         "title": "Test Journey", 
#         "start_date": date.today(), 
#         "description": "Test Description", 
#         "user_id": 1,
#         "is_public": True
#     })
#     mock_event_service.return_value = (True, "Events fetched", [{"id": 1, "name": "Test Event", "start_datetime": datetime.now()}])

#     mock_is_user_blocked.return_value = False
#     with client.session_transaction() as session:
#         session['user_id'] = 1 
#         session['role'] = 'traveller'  

#     response = client.get(url_for('journey.get_journey_edit_form', journey_id=1))

#     expected_date = f"{date.today()}"

#     assert response.status_code == 200
#     assert b'Test Journey' in response.data
#     assert b'Test Description' in response.data
#     assert expected_date.encode() in response.data 

#     assert b'Public' in response.data

#     mock_journey_service.assert_called_once_with(journey_id=1, user_id=1)

#     assert b'<select id="is_public"' in response.data

#     # Test if blocked user sees a warning message
#     mock_is_user_blocked.return_value = True
#     with client.session_transaction() as session:
#         session['user_id'] = 1 
#         session['role'] = 'editor'
#         response = client.get(url_for('journey.get_journey_edit_form', journey_id=1))
#         assert b'You have been blocked from sharing journeys publicly.' in response.data



# # Test for /journey/private/<journey_id>/edit route (POST)
# def test_update_journey(client, mocker):
#     mock_journey_service = mocker.patch('routes.journey_routes.journey_service')
#     mock_user_service = mocker.patch('routes.journey_routes.user_service')

#     mock_journey_service.get_journey.return_value = (
#         True, "Journey fetched", {"id": 1, "title": "Test Journey", "user_id": 1}
#     )
#     mock_journey_service.update_journey.return_value = (True, "Journey updated successfully!")
#     mock_user_service.is_user_blocked.return_value = False

#     test_date = date.today().isoformat()

#     with client.session_transaction() as session:
#         session['user_id'] = 1
#         session['role'] = 'traveller'
#         session['journey_page'] = 'private'

#     response = client.post(url_for('journey.update_journey', journey_id=1), data={
#         'title': 'Updated Journey',
#         'description': 'Updated description',
#         'start_date': test_date,
#         'is_public': '1'
#     })

#     assert response.status_code == 302
#     assert response.headers['Location'] in url_for('journey.get_private_journey', journey_id=1)

#     mock_journey_service.update_journey.assert_called_once_with(
#         journey_id=1,
#         user_id=1,
#         title='Updated Journey',
#         description='Updated description',
#         start_date=test_date,
#         is_public=True
#     )



# # Test for /journey/private/<journey_id>/delete route (POST)
# def test_delete_journey(client, mocker):
#     with client.session_transaction() as session:
#         session['user_id'] = 1
#         session['journey_page'] = 'private' 

#     mock_journey_service = mocker.patch('routes.journey_routes.journey_service.delete_journey')
#     mock_journey_service.return_value = (True, "Journey deleted successfully!")

#     delete_url = url_for('journey.delete_journey', journey_id=1)

#     response = client.post(url_for('journey.delete_journey', journey_id=1))

#     assert response.status_code == 302
#     assert response.headers['Location'] in url_for('journey.get_private_journeys', journey_id=1, _external=True)
#     mock_journey_service.assert_called_once_with(journey_id=1, user_id=1)
