# 1) Re-add the missing import of `date` (it was present in your first snippet but missing in the second).
from datetime import date  # ← CHANGE: Add this import back so that `date(…)` is defined.

from services.journey_service import (
    create_journey,
    get_journey,
    update_journey,
    delete_journey,
    update_journey_visibility,
    update_journey_hidden_status,
    get_private_journeys,
    get_public_journeys
)

class MockFileStorage:
    def __init__(self, filename):
        self.filename = filename

    def save(self, destination):
        pass


def test_create_journey_success(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_journey_data.create_journey.return_value = 1  # journey_id
    mock_subscription_service.check_can_use_premium_features.return_value = True  # Assume premium for image upload
    mocker.patch('utils.file_utils.save_journey_cover_image', return_value='test_image.jpg')

    success, message, journey_id = create_journey(
        1,
        "Test Journey",
        "Description",
        date(2025, 1, 1),
        'public',
        cover_image=MockFileStorage('test.jpg')
    )

    assert success
    assert message == "Journey created successfully"
    assert journey_id == 1
    mock_journey_data.create_journey.assert_called_once_with(
        user_id=1,
        title="Test Journey",
        description="Description",
        start_date=date(2025, 1, 1),
        visibility='public',
        cover_image='test_image.jpg',
        no_edits=False
    )


def test_create_journey_blocked_user(mock_journey_data, mock_user_data, mock_subscription_service):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': True, 'username': 'blockeduser'}

    success, message, journey_id = create_journey(
        1,
        "Test Journey",
        "Description",
        date(2025, 1, 1),
        'public'
    )

    assert not success
    assert message == "You have been blocked from sharing journeys"
    assert journey_id is None
    mock_journey_data.create_journey.assert_not_called()


def test_create_journey_premium_visibility_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False

    success, message, journey_id = create_journey(
        1,
        "Test Journey",
        "Description",
        date(2025, 1, 1),
        'published'
    )

    assert not success
    assert message == "Publishing journeys requires a premium subscription"
    assert journey_id is None
    mock_journey_data.create_journey.assert_not_called()


def test_create_journey_no_edits_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False

    success, message, journey_id = create_journey(
        1,
        "Test Journey",
        "Description",
        date(2025, 1, 1),
        'private',
        no_edits=True
    )

    assert not success
    assert message == "Protecting journeys from edits requires a premium subscription"
    assert journey_id is None
    mock_journey_data.create_journey.assert_not_called()


def test_create_journey_cover_image_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False
    mocker.patch('utils.file_utils.save_journey_cover_image', return_value='test_image.jpg')

    success, message, journey_id = create_journey(
        1,
        "Test Journey",
        "Description",
        date(2025, 1, 1),
        'private',
        cover_image=MockFileStorage('test.jpg')
    )

    assert not success
    assert message == "Adding cover images requires a premium subscription"
    assert journey_id is None
    mock_journey_data.create_journey.assert_not_called()


def test_get_journey_success(mock_journey_data, mock_user_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'user_id': 1,
        'visibility': 'public',
        'is_hidden': False
    }

    success, message, journey = get_journey(1, 1)

    assert success
    assert message == "Journey retrieved successfully"
    assert journey['journey_id'] == 1


def test_get_journey_not_found(mock_journey_data):
    mock_journey_data.get_journey.return_value = None

    success, message, journey = get_journey(1, 1)

    assert not success
    assert message == "Journey not found"
    assert journey is None


def test_get_journey_permission_denied_private(mock_journey_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'title': 'Private Journey',
        'description': 'Description',
        'user_id': 2,
        'visibility': 'private',
        'is_hidden': False
    }

    success, message, journey = get_journey(1, 1)  # User 1 trying to access user 2's private journey

    assert not success
    assert message == "You do not have permission to view this journey"
    assert journey is None


def test_get_journey_permission_denied_hidden(mock_journey_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'title': 'Hidden Journey',
        'description': 'Description',
        'user_id': 2,
        'visibility': 'public',
        'is_hidden': True
    }

    success, message, journey = get_journey(1, 1)  # User 1 trying to access a hidden public journey

    assert not success
    assert message == "You do not have permission to view this journey"
    assert journey is None


def test_update_journey_success(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False
    }

    mock_journey_data.update_journey.return_value = 1
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}  # Required for _is_privileged_user check
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=False)

    success, message = update_journey(1, 1, title="Updated Journey")

    assert success
    assert message == "Journey updated successfully"
    mock_journey_data.update_journey.assert_called_once_with(
        journey_id=1,
        title="Updated Journey",
        description=None,
        start_date=None,
        visibility=None,
        cover_image=None,
        no_edits=None
    )


def test_update_journey_permission_error_not_owner_or_staff(mock_journey_data, mock_user_data, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Owned by user 2
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False
    }
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=False)  # Not staff

    success, message = update_journey(1, 1, title="Updated Journey")  # User 1 trying to update

    assert not success
    assert message == "You do not have permission to update this journey"
    mock_journey_data.update_journey.assert_not_called()


def test_update_journey_staff_edit_no_reason(mock_journey_data, mock_user_data, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Owned by user 2
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False
    }
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=True)  # Is staff
    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}  # Staff user

    success, message = update_journey(1, 1, title="Updated Journey", edit_reason="")  # Staff user 1 updating without reason

    assert not success
    assert message == "Staff must provide a reason when editing a user's journey"
    mock_journey_data.update_journey.assert_not_called()


def test_update_journey_blocked_user_visibility_change(mock_journey_data, mock_user_data, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': True, 'username': 'blockeduser'}
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=False)

    success, message = update_journey(1, 1, visibility='public')

    assert not success
    assert message == "You have been blocked from sharing journeys"
    mock_journey_data.update_journey.assert_not_called()


def test_update_journey_premium_visibility_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=False)

    success, message = update_journey(1, 1, visibility='published')

    assert not success
    assert message == "Publishing journeys requires a premium subscription"
    mock_journey_data.update_journey.assert_not_called()


def test_update_journey_cover_image_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=False)
    mocker.patch('utils.file_utils.save_journey_cover_image', return_value='new_image.jpg')

    success, message = update_journey(1, 1, cover_image=MockFileStorage('new.jpg'))

    assert not success
    assert message == "Adding cover images requires a premium subscription"
    mock_journey_data.update_journey.assert_not_called()


def test_update_journey_no_edits_flag_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'cover_image': None,
        'no_edits': False  # Currently false
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False  # No premium
    mocker.patch('utils.permissions.PermissionChecker.can_manage_content', return_value=False)

    success, message = update_journey(1, 1, no_edits=True)  # Attempt to enable no_edits

    assert not success
    assert message == "Protecting journeys from edits requires a premium subscription"
    mock_journey_data.update_journey.assert_not_called()


def test_delete_journey_success(mock_journey_data, mock_user_data, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'is_hidden': False,
        'cover_image': 'image.jpg'
    }
    mock_journey_data.delete_journey.return_value = 1  # rows affected

    # 2) Capture patches in local variables so that we can assert on them later.
    mock_join = mocker.patch('os.path.join', return_value='mock_path/image.jpg')  # ← CHANGE: assign patch to `mock_join`
    mock_delete = mocker.patch('utils.file_utils.delete_file', return_value=True)  # ← CHANGE: assign patch to `mock_delete`

    success, message = delete_journey(1, 1)

    assert success
    assert message == "Journey deleted successfully"
    mock_journey_data.delete_journey.assert_called_once_with(1)
    mock_join.assert_called_once_with('journey_images', 'image.jpg')  # ← CHANGE: explicitly assert how `os.path.join` was called
    mock_delete.assert_called_once_with('mock_path/image.jpg')  # ← CHANGE: assert on the captured `mock_delete`


def test_delete_journey_permission_error(mock_journey_data, mock_user_data):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Owned by user 2
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'is_hidden': False
    }

    success, message = delete_journey(1, 1)  # User 1 trying to delete

    assert not success
    assert message == "Only the owner can delete a journey"
    mock_journey_data.delete_journey.assert_not_called()


def test_update_journey_visibility_success(mock_journey_data, mock_user_data, mock_subscription_service, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'is_hidden': False
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = True
    mock_journey_data.update_journey_visibility.return_value = 1

    success, message = update_journey_visibility(1, 1, 'public')

    assert success
    assert message == "Journey visibility set to public successfully"
    mock_journey_data.update_journey_visibility.assert_called_once_with(1, 'public')


def test_update_journey_visibility_blocked_user(mock_journey_data, mock_user_data, mock_subscription_service):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'is_hidden': False
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': True, 'username': 'blockeduser'}

    success, message = update_journey_visibility(1, 1, 'public')

    assert not success
    assert message == "You have been blocked from sharing journeys"
    mock_journey_data.update_journey_visibility.assert_not_called()


def test_update_journey_visibility_premium_no_subscription(mock_journey_data, mock_user_data, mock_subscription_service):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'private',
        'is_hidden': False
    }
    mock_user_data.get_user_by_id.return_value = {'is_blocked': False, 'username': 'testuser'}
    mock_subscription_service.check_can_use_premium_features.return_value = False

    success, message = update_journey_visibility(1, 1, 'published')

    assert not success
    assert message == "Publishing journeys requires a premium subscription"
    mock_journey_data.update_journey_visibility.assert_not_called()


def test_update_journey_hidden_status_success_admin(mock_journey_data, mock_user_data, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Owned by user 2
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'public',
        'is_hidden': False
    }
    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}  # User 1 is admin
    mock_journey_data.update_journey_hidden_status.return_value = 1

    # 3) Capture the notification mock so we can assert on it
    mock_notification = mocker.patch(
        'services.notification_service.create_notification',
        return_value=(True, "Notification sent", 123)
    )  # ← CHANGE: assign this patch to `mock_notification`

    success, message = update_journey_hidden_status(1, 1, True)  # Admin user 1 hides journey

    assert success
    assert message == "Journey set to hidden successfully"
    mock_journey_data.update_journey_hidden_status.assert_called_once_with(1, True)

    # Check if notification was sent using the captured `mock_notification`
    mock_notification.assert_called_once_with(
        user_id=2,
        notification_type='edit',
        content="Your journey 'Test Journey' has been hidden by admin_user. It is no longer visible to other users.",
        related_id=1
    )


def test_update_journey_hidden_status_non_privileged_user(mock_journey_data, mock_user_data, mocker):
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'public',
        'is_hidden': False
    }
    mock_user_data.get_user_by_id.return_value = {'role': 'traveller', 'username': 'normal_user'}  # User 1 is a regular user

    mock_notification = mocker.patch('services.notification_service.create_notification')  # ← CHANGE: capture this mock too

    success, message = update_journey_hidden_status(1, 1, True)

    assert not success
    assert message == "Only editors and admins can hide journeys"
    mock_journey_data.update_journey_hidden_status.assert_not_called()
    mock_notification.assert_not_called()  # ← CHANGE: assert on the captured `mock_notification`


def test_update_journey_hidden_status_no_notification_for_owner_change(mock_journey_data, mock_user_data, mocker):
    # Owner (user 1) is also an admin and hides their own journey
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 1,  # Owned by user 1
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'public',
        'is_hidden': False
    }
    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}  # User 1 is admin
    mock_journey_data.update_journey_hidden_status.return_value = 1

    mock_notification = mocker.patch('services.notification_service.create_notification')  # ← CHANGE: capture this mock

    success, message = update_journey_hidden_status(1, 1, True)  # User 1 hides their own journey

    assert success
    assert message == "Journey set to hidden successfully"
    mock_journey_data.update_journey_hidden_status.assert_called_once_with(1, True)
    # Verify no notification was sent since the owner made the change
    mock_notification.assert_not_called()  # ← CHANGE


def test_update_journey_hidden_status_no_notification_if_status_unchanged(mock_journey_data, mock_user_data, mocker):
    # Admin (user 1) tries to hide an already hidden journey owned by user 2
    mock_journey_data.get_journey.return_value = {
        'journey_id': 1,
        'user_id': 2,  # Owned by user 2
        'title': 'Test Journey',
        'description': 'Description',
        'start_date': date(2025, 1, 1),
        'visibility': 'public',
        'is_hidden': True  # Already hidden
    }
    mock_user_data.get_user_by_id.return_value = {'role': 'admin', 'username': 'admin_user'}
    mock_journey_data.update_journey_hidden_status.return_value = 1

    mock_notification = mocker.patch('services.notification_service.create_notification')  # ← CHANGE: capture this mock

    success, message = update_journey_hidden_status(1, 1, True)  # Admin user 1 tries to hide, but it's already hidden

    assert success
    assert message == "Journey set to hidden successfully"
    mock_journey_data.update_journey_hidden_status.assert_called_once_with(1, True)
    # Verify no notification was sent because the status didn't actually change
    mock_notification.assert_not_called()  # ← CHANGE
