import mysql.connector
from mysql.connector import Error as MySQLError
from flask import g, current_app
import sys
import time

def get_db_connection():
    """
    Get a database connection from the Flask g object or create a new one
    """
    if 'db' not in g:
        # Import here to avoid circular imports
        from connect import DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT
        
        try:
            g.db = mysql.connector.connect(
                host=DB_HOST,
                user=DB_USER,
                password=DB_PASSWORD,
                database=DB_NAME,
                port=DB_PORT,
                charset='utf8mb4',
                use_pure=True,
                autocommit=True,
            )
        except Exception as e:
            current_app.logger.error(f"Database connection error: {e}")
            sys.exit(1)
    
    return g.db

def close_db_connection(e=None):
    """
    Close the database connection if it exists
    This function should be registered as a teardown function with Flask
    """
    db = g.pop('db', None)
    
    if db is not None:
        db.close()

def init_db_connection(app):
    """
    Initialize the database connection for the Flask app
    """
    app.teardown_appcontext(close_db_connection)

def execute_query(query, params=None, fetch_one=False, fetch_all=False, commit=False):
    """
    Execute a database query with error handling and retries
    
    Args:
        query (str): SQL query to execute
        params (tuple, dict, optional): Parameters for the query
        fetch_one (bool): Whether to fetch one result
        fetch_all (bool): Whether to fetch all results
        commit (bool): Whether to commit the transaction
        
    Returns:
        The query results or None if no fetch was requested
    """
    conn = get_db_connection()
    max_retries = 3
    retry_count = 0
    cursor = None
    
    while retry_count < max_retries:
        try:
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params)
            
            if commit:
                conn.commit()
            
            if fetch_one:
                result = cursor.fetchone()
                cursor.close()
                return result
            
            if fetch_all:
                result = cursor.fetchall()
                cursor.close()
                return result
            
            # For INSERT operations, return the last inserted ID
            if query.strip().upper().startswith('INSERT'):
                last_id = cursor.lastrowid
                cursor.close()
                return last_id
            
            # For UPDATE, DELETE operations, return the number of affected rows
            affected_rows = cursor.rowcount
            
            # Always consume any unread results before closing cursor
            if cursor.with_rows:
                cursor.fetchall()
                
            cursor.close()
            return affected_rows
                
        except mysql.connector.errors.OperationalError as e:
            # Handle connection errors by reconnecting
            retry_count += 1
            
            # Close cursor if it exists
            if cursor:
                try:
                    cursor.close()
                except:
                    pass
            
            # Log the error
            current_app.logger.warning(f"Database connection error: {e}. Retrying {retry_count}/{max_retries}...")
            
            # Close the current connection if possible
            if 'db' in g:
                close_db_connection()
            
            # Wait before retrying
            time.sleep(0.5)
            
            # Get a new connection
            conn = get_db_connection()
        
        except Exception as e:
            # Ensure cursor is closed on any exception
            if cursor:
                try:
                    # Consume any pending results
                    if cursor.with_rows:
                        cursor.fetchall()
                    cursor.close()
                except:
                    pass
            
            current_app.logger.error(f"Database error: {e}")
            raise
            
    # If we've exhausted retries
    if cursor and not cursor.closed:
        cursor.close()
    raise Exception("Database operation failed after multiple retries")