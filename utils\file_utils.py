import os
from werkzeug.utils import secure_filename
from flask import current_app
import uuid
from PIL import Image
from venv import logger

# Define constants for file handling
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB in bytes

# Frontend-friendly formats for HTML accept attribute
ALLOWED_EXTENSIONS_HTML = '.png,.jpg,.jpeg,.gif'

# MIME types for validation
ALLOWED_MIME_TYPES = {
    'image/png', 'image/jpg', 'image/jpeg', 'image/gif'
}

# Human-readable format list
ALLOWED_FORMATS_TEXT = 'PNG, JPG, JPEG, GIF'

def allowed_file(filename):
    """Check if a file is allowed based on its extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def allowed_mime_type(mime_type):
    """Check if a MIME type is allowed"""
    return mime_type in ALLOWED_MIME_TYPES

def validate_file_size(file_size):
    """Validate file size against maximum limit"""
    return file_size <= MAX_FILE_SIZE

def get_file_validation_error(filename, file_size, mime_type=None):
    """
    Get validation error message for a file, or None if valid

    Args:
        filename: Name of the file
        file_size: Size of the file in bytes
        mime_type: MIME type of the file (optional)

    Returns:
        str or None: Error message if invalid, None if valid
    """
    if not filename:
        return "No file provided"

    if not allowed_file(filename):
        return f"File type not allowed. Only {ALLOWED_FORMATS_TEXT} are accepted."

    if not validate_file_size(file_size):
        return f"File size exceeds maximum limit of {MAX_FILE_SIZE // (1024 * 1024)}MB."

    if mime_type and not allowed_mime_type(mime_type):
        return f"Invalid file type. Only {ALLOWED_FORMATS_TEXT} are accepted."

    return None

def get_frontend_config():
    """
    Get configuration for frontend file validation

    Returns:
        dict: Configuration object for JavaScript
    """
    return {
        'allowedExtensions': list(ALLOWED_EXTENSIONS),
        'allowedExtensionsHtml': ALLOWED_EXTENSIONS_HTML,
        'allowedMimeTypes': list(ALLOWED_MIME_TYPES),
        'maxFileSize': MAX_FILE_SIZE,
        'maxFileSizeMB': MAX_FILE_SIZE // (1024 * 1024),
        'allowedFormatsText': ALLOWED_FORMATS_TEXT
    }

def get_safe_image_url(image_filename, image_type='event'):
    """
    Get a safe image URL that falls back to placeholder if file doesn't exist

    Args:
        image_filename: Name of the image file
        image_type: Type of image ('event', 'profile', 'journey_cover')

    Returns:
        str: URL to the image or placeholder
    """
    if not image_filename:
        return get_placeholder_url(image_type)

    # Check if file exists
    if image_type == 'event':
        file_path = os.path.join('static', 'uploads', 'event_images', image_filename)
        placeholder = 'uploads/event_images/event_image_placeholder.jpg'
    elif image_type == 'profile':
        file_path = os.path.join('static', 'uploads', 'profile_images', image_filename)
        placeholder = 'uploads/profile_images/profile_placeholder.png'
    elif image_type == 'journey_cover':
        file_path = os.path.join('static', 'uploads', 'journey_covers', image_filename)
        placeholder = 'uploads/journey_covers/cover_image_placeholder.jpg'
    else:
        return get_placeholder_url(image_type)

    # Check if the actual file exists
    if os.path.exists(file_path):
        # Return the correct URL path based on image type
        if image_type == 'event':
            return f'uploads/event_images/{image_filename}'
        elif image_type == 'profile':
            return f'uploads/profile_images/{image_filename}'
        elif image_type == 'journey_cover':
            return f'uploads/journey_covers/{image_filename}'
    else:
        # Log missing file for debugging
        print(f"Warning: Image file not found: {file_path}")
        return placeholder

def get_placeholder_url(image_type='event'):
    """
    Get placeholder URL for different image types

    Args:
        image_type: Type of image ('event', 'profile', 'journey_cover')

    Returns:
        str: URL to the appropriate placeholder
    """
    placeholders = {
        'event': 'uploads/event_images/event_image_placeholder.jpg',
        'profile': 'uploads/profile_images/profile_placeholder.png',
        'journey_cover': 'uploads/journey_covers/cover_image_placeholder.jpg'
    }
    return placeholders.get(image_type, placeholders['event'])

def get_unique_filename(filename):
    """Generate a unique filename to prevent overwrite"""
    # Get the file extension
    ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    # Generate a UUID for the filename
    unique_filename = f"{uuid.uuid4().hex}.{ext}"

    return unique_filename

def save_profile_image(file):
    """
    Save a profile image to the file system

    Args:
        file: File object from request.files

    Returns:
        str: Filename of the saved file
    """
    if not file or not hasattr(file, 'filename') or not file.filename:
        raise ValueError("No valid file provided")

    # Check file size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)

    # Use centralized validation
    error_message = get_file_validation_error(file.filename, file_size)
    if error_message:
        raise ValueError(error_message)

    # Create a unique filename
    filename = get_unique_filename(secure_filename(file.filename))

    # Ensure directory exists
    profile_dir = os.path.join(current_app.static_folder, 'uploads', 'profile_images')
    os.makedirs(profile_dir, exist_ok=True)

    # Save the file
    filepath = os.path.join(profile_dir, filename)

    # Resize and save the image
    img = Image.open(file)
    img = img.resize((300, 300))  # Resize to standard size
    img.save(filepath)

    return filename

def delete_profile_image(filename):
    """
    Delete a profile image from the file system

    Args:
        filename: Name of the file to delete

    Returns:
        bool: True if successful, False otherwise
    """
    if not filename:
        return False

    try:
        # Get the absolute path
        profile_dir = os.path.join(current_app.static_folder, 'uploads', 'profile_images')
        filepath = os.path.join(profile_dir, filename)

        # Check if file exists and delete it
        if os.path.exists(filepath):
            os.remove(filepath)
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting profile image: {str(e)}")
        return False

def save_event_image(file):
    """
    Save an event image to the file system

    Args:
        file: File object from request.files

    Returns:
        str: The filename of the saved image
    """
    if not file or not hasattr(file, 'filename') or not file.filename:
        raise ValueError("No valid file provided")

    # Check file size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)

    # Use centralized validation
    error_message = get_file_validation_error(file.filename, file_size)
    if error_message:
        raise ValueError(error_message)

    # Create a unique filename
    filename = get_unique_filename(secure_filename(file.filename))

    # Ensure directory exists
    event_dir = os.path.join(current_app.static_folder, 'uploads', 'event_images')
    os.makedirs(event_dir, exist_ok=True)

    # Save the file
    filepath = os.path.join(event_dir, filename)

    # Resize and save the image (keeping aspect ratio)
    img = Image.open(file)

    # Calculate new dimensions while preserving aspect ratio
    max_size = (800, 600)
    img.thumbnail(max_size)

    img.save(filepath)

    # Return only the filename
    return filename



def delete_file(file_path):
    """
    Delete a file from the file system

    Args:
        file_path: Path to the file relative to static folder
    """
    if not file_path:
        return

    # Get the absolute path
    abs_path = os.path.join(current_app.static_folder, file_path)

    # Check if file exists
    if os.path.exists(abs_path):
        os.remove(abs_path)

def save_journey_cover_image(file):
    """
    Save a journey cover image to the file system

    Args:
        file: File object from request.files

    Returns:
        str: Filename of the saved file
    """
    if not file or not hasattr(file, 'filename') or not file.filename:
        raise ValueError("No valid file provided")

    # Check file size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)

    # Use centralized validation
    error_message = get_file_validation_error(file.filename, file_size)
    if error_message:
        raise ValueError(error_message)

    # Create a unique filename
    filename = get_unique_filename(secure_filename(file.filename))

    # Ensure directory exists
    journey_covers_dir = os.path.join(current_app.static_folder, 'uploads', 'journey_covers')
    os.makedirs(journey_covers_dir, exist_ok=True)

    # Save the file
    filepath = os.path.join(journey_covers_dir, filename)

    # Resize and save the image (keeping aspect ratio)
    img = Image.open(file)

    # Calculate new dimensions while preserving aspect ratio
    max_size = (1200, 800)  # Larger dimensions for cover images
    img.thumbnail(max_size)

    img.save(filepath)

    # Return only the filename
    return filename

def validate_profile_image(filename):
    """
    Check if a profile image file exists

    Args:
        filename (str): The filename to check

    Returns:
        bool: True if file exists, False otherwise
    """
    if not filename:
        return False

    try:
        image_path = os.path.join(
            current_app.static_folder,
            'uploads/profile_images',
            filename
        )
        return os.path.isfile(image_path)
    except Exception:
        return False