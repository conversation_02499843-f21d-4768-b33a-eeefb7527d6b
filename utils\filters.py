"""
Custom Jinja2 template filters

This module provides custom Jinja2 template filters for formatting dates, times, and relative times.

Filters:
    - datetime: Format a datetime object to a string using a specified format
    - date: Format a date object to a string using a specified format
    - time: Format a time object to a string using a specified format
    - relative_time: Format a datetime object to a relative time string

"""
from datetime import datetime, date, timedelta
from flask import Flask

# Function to format a datetime as a relative time string (e.g., "5 minutes ago")
def format_time_ago(value):
    """
    Format a datetime object or string as a relative time (e.g., "5 minutes ago").
    
    Args:
        value: The datetime object or string to format.
        
    Returns:
        str: The relative time string.
    """
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value.replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            try:
                value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
            except (ValueError, AttributeError):
                return value
    
    if not isinstance(value, datetime):
        return value
        
    now = datetime.now()
    diff = now - value
    
    seconds = diff.total_seconds()
    minutes = seconds // 60
    hours = minutes // 60
    days = diff.days
    
    if seconds < 60:
        return "just now"
    elif minutes < 60:
        return f"{int(minutes)}m ago"
    elif hours < 24:
        return f"{int(hours)}h ago"
    elif days < 7:
        return f"{int(days)}d ago"
    else:
        return value.strftime("%d %b %Y")

def init_filters(app: Flask) -> None:
    """
    Initialize custom filters for templates.
    
    Args:
        app (Flask): The Flask application instance.
    """
    
    @app.template_filter('formatdate')
    def format_date(value):
        """
        Format a datetime/date object or string as a date.
        """
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                try:
                    value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                except (ValueError, AttributeError):
                    try:
                        value = datetime.strptime(value, '%Y-%m-%d')
                    except (ValueError, AttributeError):
                        return value
        elif isinstance(value, date) and not isinstance(value, datetime):
            value = datetime.combine(value, datetime.min.time())
        if not isinstance(value, datetime):
            return value
        return value.strftime("%d/%m/%Y")
    
    @app.template_filter('formattime')
    def format_time(value):
        """
        Format a datetime/date object or string as a time.
        """
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except (ValueError, AttributeError):
                try:
                    value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                except (ValueError, AttributeError):
                    return value
        elif isinstance(value, date) and not isinstance(value, datetime):
            value = datetime.combine(value, datetime.min.time())
        if not isinstance(value, datetime):
            return value
        return value.strftime("%I:%M %p")
    
    @app.template_filter('timeago')
    def time_ago(value):
        return format_time_ago(value)

    @app.template_filter('format_edit_value')
    def format_edit_value(value, field_name):
        """
        Format edit history values to be more user-friendly.
        Especially useful for image-related fields.
        """
        if not value or value in ['None', 'Empty', '']:
            return 'None'

        # Handle image count fields
        if field_name in ['event_images', 'event_image'] and 'images' in str(value):
            return value  # Already formatted as "X images"

        # Handle cover image status (already meaningful)
        if field_name == 'cover_image' and str(value) in ['Has cover image', 'No cover image']:
            return value  # Already user-friendly

        # Handle secure filenames - make them more user-friendly
        if field_name in ['event_image', 'cover_image'] and str(value).startswith(('event_img_', 'cover_img_')):
            return 'Image file'

        # Handle deleted values
        if str(value).lower() == 'deleted':
            return 'Removed'

        # Return the full value without truncation for edit history
        # User specifically requested to see full values in edit history modals
        return str(value)
    
    @app.template_filter('datetime')
    def format_datetime(value, format='%d %b %Y %I:%M %p'):
        """
        Format a datetime object to a string using NZ format (day month year).
        Default format: '25 Dec 2023 2:30 PM'
        """
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    value = datetime.fromisoformat(value.replace('Z', '+00:00'))
                except ValueError:
                    return value
        elif isinstance(value, date) and not isinstance(value, datetime):
            value = datetime.combine(value, datetime.min.time())
        return value.strftime(format)
    
    @app.template_filter('date')
    def format_date_only(value, format='%d %b %Y'):
        """
        Format a date object to a string using NZ format (day month year).
        Default format: '25 Dec 2023'
        """
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.strptime(value, '%d-%m-%Y')
            except ValueError:
                try:
                    value = datetime.strptime(value, '%Y-%m-%d')
                except ValueError:
                    return value
        elif isinstance(value, date) and not isinstance(value, datetime):
            value = datetime.combine(value, datetime.min.time())
        return value.strftime(format)
    
    @app.template_filter('time')
    def format_time_only(value, format='%I:%M %p'):
        """Format a time object to string.
        
        Args:
            value: The time value to format (can be time object or string)
            format: The strftime format to use (default: '%I:%M %p')
            
        Returns:
            String: Formatted time string
        """
        if value is None:
            return ""
            
        if isinstance(value, str):
            try:
                value = datetime.strptime(value, '%H:%M:%S')
            except ValueError:
                return value
                
        return value.strftime(format)

    @app.template_filter('relative_time')
    def relative_time(value):
        """Format a datetime object to a relative time string.
        
        Args:
            value: The datetime value to format (can be datetime object or string)
            
        Returns:
            String: Relative time string (e.g. '1 hour ago', '2 days ago', '1 month ago')
        """
        if value is None:
            return ""
        
        now = datetime.now()
        delta = now - value
        
        if delta.days > 0:
            return f"{delta.days} days ago"
        elif delta.seconds > 3600:
            return f"{delta.seconds // 3600} hours ago"
        elif delta.seconds > 60:
            return f"{delta.seconds // 60} minutes ago"
        else:
            return "just now"  
