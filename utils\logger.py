"""
Logger Module

This module provides standardized logging configuration for the application.
It ensures consistent formatting and behavior across all modules.
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime

# Configure log directory
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
os.makedirs(LOG_DIR, exist_ok=True)

# Log file naming
LOG_FILE = os.path.join(LOG_DIR, f"application.log")
ERROR_LOG_FILE = os.path.join(LOG_DIR, f"error.log")

# Log format
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Global logger registry to avoid duplicated handlers
loggers = {}

def get_logger(name: str) -> logging.Logger:
    """Get a logger with standardized configuration.
    
    Args:
        name: The name of the logger, typically __name__
        
    Returns:
        logging.Logger: Configured logger instance
    """
    # Return existing logger if already configured
    if name in loggers:
        return loggers[name]
    
    # Create new logger
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)  # Set to lowest level, handlers will filter
    
    # Avoid duplicate handlers if logger already exists
    if logger.handlers:
        return logger
    
    # Create formatters
    formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    
    # Console handler (INFO+)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # File handlers - different behavior for Windows vs other OS
    if sys.platform == 'win32':
        # On Windows, use regular FileHandler to avoid rotation issues
        file_handler = logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        error_file_handler = logging.FileHandler(ERROR_LOG_FILE, mode='a', encoding='utf-8')
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(formatter)
    else:
        # On other OS, use RotatingFileHandler
        file_handler = RotatingFileHandler(
            LOG_FILE, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        error_file_handler = RotatingFileHandler(
            ERROR_LOG_FILE, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.addHandler(error_file_handler)
    
    # Add to registry
    loggers[name] = logger
    
    return logger 