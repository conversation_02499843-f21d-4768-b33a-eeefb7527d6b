"""
Centralized permission management system for role-based access control.

This module provides a single source of truth for all role permissions,
making it easier to maintain and update role-based access control throughout the application.
"""

from typing import List, Set
from flask import session

# Role definitions
class Roles:
    TRAVELLER = 'traveller'
    MODERATOR = 'moderator'
    EDITOR = 'editor'
    SUPPORT_TECH = 'support_tech'
    ADMIN = 'admin'

# Permission groups - centralized role lists for different permission levels
class PermissionGroups:
    """Centralized permission groups for consistent role checking"""

    # Basic staff roles (can moderate content)
    STAFF = [Roles.MODERATOR, Roles.EDITOR, Roles.SUPPORT_TECH, Roles.ADMIN]

    # Content management roles (can edit journeys, events, etc.)
    CONTENT_MANAGERS = [Roles.EDITOR, Roles.SUPPORT_TECH, Roles.ADMIN]

    # Administrative roles (can manage users, but not necessarily change roles)
    ADMINISTRATORS = [Roles.SUPPORT_TECH, Roles.ADMIN]

    # Full admin (can change user roles)
    FULL_ADMIN = [Roles.ADMIN]

    # Premium feature access (staff get premium features automatically)
    PREMIUM_ACCESS = STAFF  # Staff roles get premium features



    # Report management (moderator manages reported comments, support_tech has admin permissions)
    REPORT_MANAGERS = [Roles.MODERATOR, Roles.EDITOR, Roles.SUPPORT_TECH, Roles.ADMIN]

    # Edit history access
    EDIT_HISTORY_ACCESS = CONTENT_MANAGERS


class PermissionChecker:
    """Utility class for checking user permissions"""

    @staticmethod
    def get_current_user_role() -> str:
        """Get the current user's role from session"""
        return session.get('role', Roles.TRAVELLER)

    @staticmethod
    def has_role(required_roles: List[str]) -> bool:
        """Check if current user has any of the required roles"""
        current_role = PermissionChecker.get_current_user_role()
        return current_role in required_roles

    @staticmethod
    def is_staff() -> bool:
        """Check if current user is staff"""
        return PermissionChecker.has_role(PermissionGroups.STAFF)

    @staticmethod
    def can_manage_content() -> bool:
        """Check if current user can manage content (edit journeys, events, etc.)"""
        return PermissionChecker.has_role(PermissionGroups.CONTENT_MANAGERS)

    @staticmethod
    def can_administrate() -> bool:
        """Check if current user has administrative privileges"""
        return PermissionChecker.has_role(PermissionGroups.ADMINISTRATORS)

    @staticmethod
    def can_change_user_roles() -> bool:
        """Check if current user can change other users' roles"""
        return PermissionChecker.has_role(PermissionGroups.FULL_ADMIN)

    @staticmethod
    def can_manage_reports() -> bool:
        """Check if current user can manage reports"""
        return PermissionChecker.has_role(PermissionGroups.REPORT_MANAGERS)



    @staticmethod
    def can_access_edit_history() -> bool:
        """Check if current user can access edit history"""
        return PermissionChecker.has_role(PermissionGroups.EDIT_HISTORY_ACCESS)

    @staticmethod
    def is_owner_or_can_manage_content(user_id: int, resource_user_id: int) -> bool:
        """Check if user owns resource or can manage content"""
        # User owns the resource
        if int(user_id) == int(resource_user_id):
            return True

        # User can manage content
        return PermissionChecker.can_manage_content()


# Template helper functions for Jinja2
def register_permission_helpers(app):
    """Register permission helper functions for use in templates"""

    @app.template_global()
    def can_manage_content():
        return PermissionChecker.can_manage_content()

    @app.template_global()
    def is_staff():
        return PermissionChecker.is_staff()

    @app.template_global()
    def can_administrate():
        return PermissionChecker.can_administrate()

    @app.template_global()
    def can_change_user_roles():
        return PermissionChecker.can_change_user_roles()

    @app.template_global()
    def can_manage_helpdesk():
        """Check if current user can manage helpdesk (content managers only)"""
        return PermissionChecker.can_manage_content()

    @app.template_global()
    def can_manage_reports():
        return PermissionChecker.can_manage_reports()

    @app.template_global()
    def has_role(required_roles):
        return PermissionChecker.has_role(required_roles)

    @app.template_global()
    def can_access_edit_history():
        return PermissionChecker.can_access_edit_history()

    @app.template_global()
    def get_role_badge_class(role: str) -> str:
        """Get Bootstrap badge class for role"""
        role_classes = {
            Roles.ADMIN: 'bg-primary-subtle text-primary',
            Roles.SUPPORT_TECH: 'bg-warning-subtle text-warning',
            Roles.EDITOR: 'bg-success-subtle text-success',
            Roles.MODERATOR: 'bg-info-subtle text-info',
            Roles.TRAVELLER: 'bg-secondary-subtle text-secondary'
        }
        return role_classes.get(role, 'bg-secondary-subtle text-secondary')

    @app.template_global()
    def get_role_icon(role: str) -> str:
        """Get Bootstrap icon for role"""
        role_icons = {
            Roles.ADMIN: 'bi-shield-fill',
            Roles.SUPPORT_TECH: 'bi-tools',
            Roles.EDITOR: 'bi-pencil-fill',
            Roles.MODERATOR: 'bi-eye-fill',
            Roles.TRAVELLER: 'bi-person-fill'
        }
        return role_icons.get(role, 'bi-person')


# Backward compatibility - maintain existing function names
def is_staff_role(role: str) -> bool:
    """Check if a role is a staff role"""
    return role in PermissionGroups.STAFF

def can_manage_content_role(role: str) -> bool:
    """Check if a role can manage content"""
    return role in PermissionGroups.CONTENT_MANAGERS

def is_admin_role(role: str) -> bool:
    """Check if a role has admin privileges"""
    return role in PermissionGroups.ADMINISTRATORS
