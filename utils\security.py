from flask import session, redirect, url_for, flash, render_template
from flask_bcrypt import Bcrypt
from functools import wraps
import re

from services import subscription_service
from utils.permissions import PermissionChecker

bcrypt = Bcrypt()


def init_bcrypt(app):
    """Initialize Flask-Bcrypt with the app"""
    global bcrypt
    bcrypt = Bcrypt(app)


def hash_password(password):
    """Hash a password using bcrypt"""
    return bcrypt.generate_password_hash(password).decode('utf-8')


def check_password(hashed_password, password):
    """Check if a password matches the hash"""
    return bcrypt.check_password_hash(hashed_password, password)


def validate_password_strength(password):
    """
    Validate password strength

    Requirements:
    - At least 8 characters
    - Contains at least one digit
    - Contains at least one lowercase letter
    - Contains at least one uppercase letter
    - Contains at least one special character

    Returns:
        (bool, str): (is_valid, error_message)
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"

    if not re.search(r'\d', password):
        return False, "Password must contain at least one digit"

    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"

    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"

    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        return False, "Password must contain at least one special character"

    return True, ""


def validate_username(username):
    """
    Validate username format

    Requirements:
    - Between 3 and 20 characters
    - Contains only letters, numbers
    - No spaces or special characters

    Returns:
        (bool, str): (is_valid, error_message)
    """
    if not username or len(username) < 3:
        return False, "Username must be at least 3 characters long"

    if len(username) > 20:
        return False, "Username must be at most 20 characters long"

    if not re.match(r'^[a-zA-Z0-9]+$', username):
        return False, "Username can only contain letters and numbers"

    return True, ""


def validate_email(email):
    """
    Validate email format

    Requirements:
    - Must be a valid email format
    - Maximum length of 254 characters (RFC 5321)

    Returns:
        (bool, str): (is_valid, error_message)
    """
    if not email or len(email) > 254:
        return False, "Email address is too long"

    # Basic email format validation
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_regex, email):
        return False, "Please enter a valid email address"

    return True, ""


def validate_name(name):
    """
    Validate name format (for first_name and last_name)

    Requirements:
    - Between 2 and 50 characters if provided
    - Contains only letters, spaces, hyphens, and apostrophes
    - Optional (can be None or empty string)

    Returns:
        (bool, str): (is_valid, error_message)
    """
    # Skip validation if name is None or empty (it's optional)
    if not name:
        return True, ""

    if len(name) < 2:
        return False, "Name must be at least 2 characters long"

    if len(name) > 50:
        return False, "Name must be at most 50 characters long"

    if not re.match(r'^[a-zA-Z\s\-\']+$', name):
        return False, "Name can only contain letters, spaces, hyphens and apostrophes"

    return True, ""


def login_required(f):
    """Decorator to require login for a route"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


def role_required(roles):
    """
    Decorator to require specific role(s) for a route

    Args:
        roles (str or list): Required role(s) for access
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # First ensure the user is logged in
            if 'user_id' not in session:
                flash('Please log in to access this page.', 'warning')
                return redirect(url_for('auth.login'))

            # Then check if the user has the required role
            if isinstance(roles, list):
                if session.get('role') not in roles:
                    return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403
            else:
                if session.get('role') != roles:
                    return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator


def admin_required(f):
    """Decorator to require admin role for a route"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        from utils.permissions import Roles
        if session.get('role') != Roles.ADMIN:
            return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

        return f(*args, **kwargs)
    return decorated_function


def get_current_user():
    """Get the current user from the session"""
    if 'user_id' not in session:
        return None

    return {
        'id': session['user_id'],
        'username': session.get('username'),
        'role': session.get('role')
    }


def set_user_session(user):
    """Set user session data after login"""
    session['user_id'] = user['id']
    session['username'] = user['username']
    session['role'] = user['role']
    session['profile_image'] = user['profile_image']
    session['is_blocked'] = user.get('is_blocked', False)
    session['is_premium'] = subscription_service.check_can_use_premium_features(user['id'])


def clear_user_session():
    """Clear user session data on logout"""
    session.pop('user_id', None)
    session.pop('username', None)
    session.pop('role', None)
    session.pop('is_blocked', None)
    session.pop('profile_image', None)
    session.pop('is_premium', None)


def premium_required(f):
    """Decorator to require premium subscription for a route"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        premium_access = subscription_service.check_can_use_premium_features(
            user_id=session['user_id']
        )

        if not premium_access:
            flash('This feature requires a premium subscription. Please upgrade to continue.', 'warning')
            return redirect(url_for('account.get_profile', active_tab='subscription'))

        return f(*args, **kwargs)
    return decorated_function


def staff_required(f):
    """Decorator to require staff role (moderator, editor, support_tech, or admin)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        if not PermissionChecker.is_staff():
            return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

        return f(*args, **kwargs)
    return decorated_function


def admin_or_support_tech_required(f):
    """Decorator to require administrative permissions (support_tech or admin role)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        if not PermissionChecker.can_administrate():
            return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

        return f(*args, **kwargs)
    return decorated_function


def content_manager_required(f):
    """Decorator to require content management permissions (editor, support_tech, or admin role)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        if not PermissionChecker.can_manage_content():
            return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

        return f(*args, **kwargs)
    return decorated_function


def report_manager_required(f):
    """Decorator to require report management permissions (moderator, editor, support_tech, or admin role)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))

        if not PermissionChecker.can_manage_reports():
            return render_template('error.html', error_code='Access Denied', message="You are not authorized to access this page."), 403

        return f(*args, **kwargs)
    return decorated_function